<template>
  <div :ref="el => curTableRef = el" v-show="!field.hidden">
    <div style="display: flex; justify-content: space-between;">
      <p class="form-p" style="margin: 9px 0px; width: 100%;">
        {{ field.config.label }}
      </p>
      <div v-if="tableTools && tableTools.length > 0" class="tools-wrap">
        <div v-for="(eld, pindex) in tableTools" :key="pindex" class="form-table-item">
          <component :is="eld.config.ctype" :field="eld" :index="index" :key="index" v-model="eld.vModel.value"
            v-model:optionValue="eld.vModel.optionValue" :isMainBtn="false" />
        </div>
      </div>
    </div>
    <!-- {{ vModel }} -->
    <div v-if="!field.hidden" class="card-table" v-for="(item, index) in paginateArray(vModel)" :key="index">
      <!-- 这里的v-show是为了处理分页下标，从index 到index+limit的长度才可显示 -->
      <van-collapse v-model="activates" :accordion="false">
        <van-collapse-item :title="`#${index + 1}`" :name="index">
          <template v-if="field.getAllAddRow() !== false" #title>
            <div>
              <van-icon name="delete-o" @click.stop="deleteRow(index)" style="color: #f30; padding-right: 15px;" />
              #{{ enablePaging ? ((page - 1) * limit) + index + 1 : (index + 1) }}
            </div>
            <!-- {{ index >= (page - 1 ) * limit  && index < ((page - 1 ) * limit) + limit  }}?????
       下标：{{ index + 1 }} -->
          </template>
          <div v-for="(el, pindex) in item.colums" :key="el.columnName" class="form-table-item">
            <component :is="el.field?.config.ctype" :field="el.field" :index="index" :key="index"
              v-model="el.field.vModel.value" v-model:optionValue="el.field.vModel.optionValue" />
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <van-sticky v-if="field.getAllAddRow() !== false || enablePaging" class="sticky-btn" :container="curTableRef"
      :offset-bottom="50" position="bottom">
      <!-- :offset-bottom="50" position="bottom" YZ + -->
      <div class="btn-div">
        <van-button type="primary" plain icon="plus" size="mini" @click="addRow('click')" native-type="button"
          v-if="field.getAllAddRow() !== false">{{ $t('Form.addRow') }}</van-button>
        <div class="page-area" v-if="enablePaging">
          <van-pagination v-if="enablePaging" @change="onPageChange" v-model="page" :total-items="total" force-ellipses
            :show-page-size="5" :page-count="pageCount"></van-pagination>
        </div>
      </div>
    </van-sticky>
  </div>
</template>

<script lang="ts" setup>
import { url } from "@/api/url";
import { formBuilder } from "@/logic/forms/formBuilder";
import { YZField } from "@/logic/forms/formModel";
import { currencyFormatValue, currencyTurnToNum } from "@/logic/forms/processHelper";
import { IYZColumn, YZTable, YZTableConfig } from "@/logic/forms/YZTable";
import { useCore } from "@/store/core";
import { useParamsFormatter, useUUID, useFormData, useStrToUnicode, useIsChinese, deepCopy } from "@/utils";
import { eventBus } from "@/utils/eventBus";
import { usePostBody } from "@/utils/request";
import { to } from "mathjs";
import { computed, onMounted, PropType, ref, getCurrentInstance, nextTick } from "vue";
export interface ITableData {
  colums: IYZColumn[];
}
const activates = ref<Array<number>>([0]);
const core = useCore()
const props = defineProps({
  field: {
    type: Object as PropType<YZTable>,
    required: true,
  },
  modelValue: {
    type: Array,
    default: [],
  },
  optionValue: {
    type: [String],
    default: "",
  },
  index: {
    type: Number,
    default: -1
  }
});
const emit = defineEmits(["update:modelValue", "update:optionValue"]);
const isFirstLoad = ref<boolean>(true)
const page = ref<number>(1)
const limit = ref<number>(props.field.getPageSieze())
const total = ref<number>(0)
const pageCount = ref<number>(0)
const enablePaging = ref<boolean>(props.field.getEnablePaging())
const curTableRef = ref<any>(null)
const vModel: any = computed({
  get() {
    // props.field.expressTest(props.field, props.index, isFirstLoad.value)
    // props.field.disableTest(props.field, props.index, isFirstLoad.value)
    // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    return props.modelValue;
  },
  set(val) {
    emit("update:modelValue", val);
  },
});
const tableData = ref<Array<ITableData>>([]);
const tableTools = ref<Array<YZField>>([])
onMounted(() => {
  isFirstLoad.value = false
  createTools()
  setPage()
  if (props.modelValue.length <= 0) {
    initAutoMap('init')
  } else {
    // 展开所有
    const length = vModel.value.length
    for (let i = 0; i < length; i++) {
      activates.value.push(i)
    }
  }
  const instance = getCurrentInstance()
  core.setControlInstance(instance)
});
const setPage = () => {
  total.value = vModel.value.length
  pageCount.value = Math.ceil(total.value / limit.value)
}
const paginateArray = (array: any[]) => {
  if (enablePaging.value) {
    const pageSize = limit.value
    let index = (page.value - 1) * pageSize
    return array.slice(index, index + pageSize);
  } else {
    return array
  }
}
const addRow = (type?: string): void => {
  createRows()
  setPage()
  if (type == 'click') {
    // 添加的时候往后添加按总页数走
    page.value = pageCount.value
    nextTick(() => {
      setPageScroll()
    })

  }
};
const setPageScroll = (n?: number) => {
  let warpDom = curTableRef.value
  let domArr = warpDom.getElementsByClassName('card-table')
  let dom = domArr[n != undefined ? n : domArr.length - 1]
  dom.scrollIntoView({ behavior: 'smooth', block: "start", inline: "nearest" })
}
const onPageChange = () => {
  // showLoading.value = true
  nextTick(() => {
    setPageScroll(0)
  })
}
const createRows = (beforeAddVmodel?: (tb: ITableData) => IYZColumn[], data?: any) => {

  const config = props.field.config as YZTableConfig;
  // console.log("%c [ config ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", config)
  const cols = config.colunms
  const columns: IYZColumn[] = [];
  const count = vModel.value.length
  cols.forEach((el, index) => {
    let item = deepCopy(el) as YZField | any
    // 产生新的唯一标识给到字段
    item['uuid'] = useUUID()
    item['table'] = `${props.field.vModel.model}.${item['__vModel__'].model}`
    item['tableName'] = props.field.vModel.model
    item['pindex'] = count
    item['__vModel__'].modelValue = '' //YZ +
    item['__vModel__'].optionValue = ''//YZ +
    const newField = formBuilder.builderField(
      item['__config__'].ctype,
      item
    ) as YZField;

    if (item['__config__'].defualtValue != undefined) { //YZ +
      newField.vModel.optionValue = item['__config__'].defualtValue
      newField.vModel.value = item['__config__'].defualtValue
    } else {
      // newField.vModel.optionValue = ''
      // newField.vModel.value = ''
    }
    if (newField.config.extends.$express) {
      newField.expressTest(newField, newField.pindex, true)
    }
    // console.log(item.viewModel,'data', data)
    newField.disabled = newField.$returnSeltDisabled(newField, newField.pindex, newField.disabled, item.viewModel, data)
    // newField.readonly = props.field.$returnSeltDisabled(item,item.pindex,item.disabled,item.viewModel)
    const value: IYZColumn = {
      columnName: newField.config.label,
      columnCode: newField.vModel.model,
      field: newField,
      renderKey: newField.config.formId,
    };
    config.setCols(newField)
    columns.push(value);
  });
  const table: ITableData = {
    colums: columns,
  };
  if (beforeAddVmodel) {
    const beforeModel = beforeAddVmodel(table)
    table.colums = beforeModel
    vModel.value.push(table);
  } else {
    vModel.value.push(table);
  }
  const length = vModel.value.length - 1;
  activates.value.push(...[length]);
}
const createTools = () => {
  tableTools.value = []
  const tableConfig = props.field.config as YZTableConfig
  const tools = tableConfig.getTools()
  if (!tools) return
  tools.forEach(item => {
    item['uuid'] = useUUID()
    item['table'] = `${props.field.vModel.model}.${item['__config__'].extends.fieldName}`
    item['tableName'] = props.field.vModel.model
    item['disabled'] = props.field.disabled
    item['readonly'] = props.field.readonly
    const newField = formBuilder.builderField(
      item['__config__'].ctype,
      item
    ) as YZField;
    tableTools.value.push(newField)
  })
}
const initAutoMap = async (type: string) => {
  const config = props.field.config as YZTableConfig;
  const map = config.getMap()
  const ds = config.getDs()
  const fields = formBuilder.formConfig?.getFields()
  if (ds) {
    const filter = ds.filter
    const fobj: any = {}
    const arrys: Array<string> = []
    let strs = '{'
    let ff = 'e30='
    if (filter) {
      let strs = '{'
      Object.keys(ds.filter).forEach(key => {
        const item = ds.filter[key]
        if (fields && fields.length > 0) {
          let valueData = item.value
          if (item.field) {
            if (item.field.indexOf('.') > -1) {
              // 读取明细表
              const fieldItem = fields.find(x => x.tableName == item.field)
              if (fieldItem) {
                valueData = fieldItem.vModel.value
              }
            } else {
              const fieldItem = fields.find(x => x.vModel.model == item.field)
              if (fieldItem) {
                valueData = fieldItem.vModel.optionValue ? fieldItem.vModel.optionValue : fieldItem.vModel.value
              }
            }
          }

          const nkey: string = buildFilterStr(key)
          strs += `"${nkey}":{"op":"${item.op}","value":"${buildFilterStr(valueData)}"},`
        }
      })
      if (strs.lastIndexOf(',') > -1)
        strs = strs.substring(0, strs.lastIndexOf(','))
      strs += "}"
      ff = btoa(strs)
    }


    // Object.keys(ds.filter).forEach(key => {
    //   arrys.push(key)
    //   fobj[key] = {}
    //   fobj[key].op = filter[key].op
    //   if (filter[key].value)
    //     fobj[key].value = filter[key].value
    //   if (filter[key].field) {
    //     const fieldBind = filter[key].field
    //     if (fieldBind.indexOf('.') > -1) {
    //       // 明细表，暂不考虑 2023.09.12 
    //     } else {
    //       // 获取当前表单字段的值
    //       if (fields) {
    //         const bind = fields.find(x => x.vModel.model === fieldBind)
    //         if (bind) {
    //           fobj[key].value = buildFilterStr(bind.vModel.value)
    //         } else {
    //           fobj[key].value = ''
    //         }
    //       }
    //     }
    //   }
    // })
    let newUrl = ''
    if (ds.type === 'table' && ds.tableName) {
      if (ds.public) { //YZ +
        newUrl = useParamsFormatter(url.dataSource.getTableData, {
          params1: ds.dataSourceId,
          params2: ds.type,
          params3: ds.tableName
        })

      } else {
        newUrl = useParamsFormatter(url.dataSource.getTableData, {
          params1: ds.dataSourceId,
          params2: ds.type,
          params3: ds.tableName
        })
      }
    } else if (ds.type == 'esb' && ds.esbId) {
      newUrl = useParamsFormatter(url.dataSource.getEsbDataNoPage, {
        params1: ds.esbId
      })
    } else if (ds.type == 'form') { //YZ +
      newUrl = useParamsFormatter(url.dataSource.getFormNoPage, {
        params1: ds.formId,
        params2: ds.formTable
      })
    }
    const f = ff
    const c: string = btoa(JSON.stringify(arrys))
    const formdata = useFormData({
      f: f,
      c: c,
      o: ''
    })
    if (!newUrl)
      return
    const data = await usePostBody(newUrl, {}, formdata)
    // console.log(data,"?????")
    if (data && data.length > 0 && map) {
      let copyData = deepCopy(data).map((citem: any) => {
        let obj: any = {}
        Object.keys(map).forEach(key => {
          const bind = map[key]
          if (citem[key] != undefined && citem[key] != null) {
            obj[bind] = citem[key]
          }
        })
        return obj

      })

      data.forEach((item: any) => {
        const newRowCols: IYZColumn[] = []
        createRows(function (table: ITableData) {
          const cols = table.colums
          for (let i = 0; i < cols.length; i++) {
            const colItem = cols[i]
            const nfid = colItem.field as YZField
            if (map) {
              Object.keys(map).forEach(key => {
                const bind = map[key]
                if (bind === nfid.table) {
                  if (nfid.config.ctype == 'YZSelect') {
                    nfid.vModel.optionValue = item[key]
                  } else {
                    nfid.vModel.value = item[key]
                  }
                }
              })
            }
            newRowCols.push(colItem)
          }
          return newRowCols
        }, copyData)
      })
      setPage()

    } else {
      // 如果空白行存在一条不要再加新的空白数据了 
      if (vModel.value.length < 1 && config.getIsAddRow() && type != 'reload') {
        addRow()
      }

    }

  } else {
    // 如果空白行存在一条不要再加新的空白数据了 
    if (vModel.value.length < 1 && config.getIsAddRow() && type != 'reload') {
      addRow()
    }
  }
}
const deleteRow = (index: number): void => {
  let delIndex = enablePaging.value ? ((page.value - 1) * limit.value) + index : index
  vModel.value.splice(delIndex, 1);
  if (enablePaging.value) setPage()
};
const buildFilterStr = (str: string): string => {
  if (useIsChinese(str)) {
    return useStrToUnicode(str)
  }
  return str
}
eventBus.on('setFieldHidden', {
  cb: function (params) {
    if (params.uuid === props.field.uuid) {
      props.field.hidden = params.value
    }
  }
})
eventBus.on('setFieldDisable', {
  cb: function (params) {
    if (params.uuid === props.field.uuid) {
      props.field.disabled = params.value
    }
  }
})
eventBus.on('onDetailMap', {
  cb: function (params: any) {
    if (params && params.tableName == props.field.vModel.model) {
      afterMap(params)
      setPage()
      nextTick(() => {
        setPageScroll(0)
      })
    }
  }
})
const afterMap = (params: any) => {
  if (params) {
    const { list, map } = params
    const { $map } = map
    // 验证数据是否为空 空返回false 任何一个只要有直返回true
    function checkObject(arr: any) {
      for (let ai = 0; ai < arr.length; ai++) {
        const element = arr[ai];
        if (element.field.vModel.value && element.field.vModel.value !== '0' && element.field.vModel.value !== 0) {
          return true
        }
      }
      return false;
    }
    if ($map && list && list.length > 0) {
      // 字表每行数据循环
      for (let i = 0; i < vModel.value.length; i++) {
        const element = vModel.value[i];
        // 单行是否存在空数据
        if (!checkObject(element.colums)) {
          const cols = element.colums as IYZColumn[]
          cols.forEach(col => {
            Object.keys($map).forEach(key => {
              const name = key
              const value = $map[name] as string
              if (col.field.table === value) {
                // debugger 
                // 直接加第一条数据
                // if(col.field.config.ctype == 'YZStepper'){ //YZ -
                //   let setConfig = col.field.config
                //   col.field.vModel.value = currencyFormatValue(list[0][key],setConfig,false)
                //   col.field.vModel.optionValue = currencyTurnToNum(list[0][key],setConfig)
                // }else{
                //   col.field.vModel.value = list[0] && list[0][key] || ''
                // }
                eventBus.emit('onMap', true, { //YZ +
                  value: list[0] && list[0][key] || '',
                  model: col.field.vModel.model,
                  uuid: col.field.uuid,
                  index: col.field.pindex
                })
              }
            })
          })
          // 加完以后去掉第一条
          list.splice(0, 1)
        }
      }
      list.forEach((item: any) => {
        const newRowCols: IYZColumn[] = []
        createRows(function (tb: ITableData) {
          const cols = tb.colums
          cols.forEach(col => {
            Object.keys($map).forEach(key => {
              const name = key
              const value = $map[name] as string
              if (col.field.table === value) {
                if (col.field.config.ctype == 'YZSelect') { //YZ +
                  col.field.vModel.optionValue = item[key]
                } else {
                  col.field.vModel.value = item[key]
                }
              }
            })
            newRowCols.push(col)
          })
          return newRowCols
        })
      })
    }
  }
}
defineExpose({
  addRow,
  deleteRow
})
eventBus.on('setTableReload', {
  cb: function (parasm) {
    if (parasm.uuid === props.field.uuid) {

      const createValidate = props.field.getAllAddRow()
      if (createValidate) {
        vModel.value = []
        initAutoMap('reload')
      }
      // initAutoMap()
    }
  }
})

</script>
<style lang="scss" scoped>
.form-p {
  margin: 5px 5px;
  min-height: 1.625rem;
  background: var(--yz-div-background);
  margin: 0.625rem 0;
  padding: 0.125rem 0.125rem;
  font-family: PingFangSC-Medium;
  padding-left: 1em;
  border-left: 0.25rem solid var(--van-button-primary-border-color);
  color: #666666;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.btn-div {
  min-height: 35px;
  text-align: center;

  padding-bottom: 10px;
  padding-top: 10px;
  background-color: var(--yz-div-background);
}

.page-area {
  margin-top: 10px;
}

.card-table {
  .form-table-item>div {
    position: relative;
  }

  .form-table-item>div::after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: var(--van-padding-md);
    bottom: 0;
    left: var(--van-padding-md);
    border-bottom: 1px solid var(--van-cell-border-color);
    transform: scaleY(0.5);
  }
}

.sticky-btn :deep .van-sticky {
  z-index: 0 !important;
}

.form-loading :deep .wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.tools-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 5px;
}

.tools-wrap .form-table-item:not(:last-child) {
  margin-right: 5px;
}
</style>
