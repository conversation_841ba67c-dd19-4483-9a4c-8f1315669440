{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "allowJs": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "types": ["unplugin-vue-define-options/macros-global"],
    "baseUrl": "./", // 注意这个是必须的
    "paths": {
      // 导入配置
      "@/*": [
        // 标识 @ 符号需要指向的文件目录
        "src/*"
      ]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/init.ts", "src/init.ts","src/App.vue",
  "src/**/*.ts",
  "src/**/*.tsx",
  "src/**/*.vue",
  "tests/**/*.ts",
  "tests/**/*.tsx",
  "components.d.ts",
  "auto-imports.d.ts",
  "types/**/*.d.ts",
  "tests/*.ts"],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "exclude": ["node_modules"]
}
