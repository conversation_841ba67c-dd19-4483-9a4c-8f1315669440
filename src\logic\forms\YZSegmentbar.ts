import { YZ<PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON>ty<PERSON> } from "./formModel";
import { useLang } from "@/utils";
export class YZSegmentbarConfig extends YZConfig {
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZSegmentbar'
        if (this.required && !this.rules) {
            this.rules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        }
    }
}
export class YZSegmentbarStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZSegmentbar extends YZField {
    constructor(params: any) {
        super(params)
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZSegmentbarConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZSegmentbarStyle(style)
    }
}