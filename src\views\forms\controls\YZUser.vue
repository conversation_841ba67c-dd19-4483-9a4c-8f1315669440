<template>
    <div>
       
        <van-field v-if="!field.hidden" :name="field.vModel.model"  :readonly="true" :required="field.config.required" is-link
            v-model="vModel" :rules="field.config.rules" :placeholder="field.placeholder" @click="onUserClick"
           :label="field.config.label">
        </van-field>
        <YZUserSelect v-model:show="userShow" @onSave="onSave" :default-users="defaultUsers" />
    </div>
</template>

<script lang="ts" setup>
import { YZUser, YZUserConfig } from '@/logic/forms/YZUser';
import { computed, onMounted, PropType, ref } from 'vue';
import YZUserSelect, { ISelectUser } from '@/components/YZUserSelect.vue';
import { eventBus } from '@/utils/eventBus';
import { onUnmounted } from 'vue';
import { parseExpress } from '@/logic/forms/express';
import { Y<PERSON><PERSON>ield } from '@/logic/forms/formModel';
const props = defineProps({
    field: {
        type: Object as PropType<YZUser>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:optionValue', 'update:modelValue'])
const config = props.field.config as YZUserConfig
const isFirstLoad = ref<boolean>(true)
const defaultUsers = ref<Array<ISelectUser>>([])
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        if (typeof props.optionValue === 'object') {
            const newValue = props.optionValue as Array<any>
            // props.field.expressTest(props.field, props.index, isFirstLoad.value)
            // props.field.disableTest(props.field, props.index, isFirstLoad.value)
            // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
            // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
            return newValue[0].name
        } else {
            // props.field.expressTest(props.field, props.index, isFirstLoad.value)
            // props.field.disableTest(props.field, props.index, isFirstLoad.value)
            // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
            // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
            return props.optionValue
        }
    },
    set(val) {
        emit('update:optionValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const userShow = ref<boolean>(false)
const onSave = (user: ISelectUser[],userMap:any) => {
    if(userMap){
       userMapField(userMap)
    }
    defaultUsers.value  = []
    if (user && user.length > 0) {
        vModel.value = user[0].name
        emit('update:modelValue', user[0].account)
        setFun()
    } else {
        vModel.value = ''
        emit('update:modelValue', '')
        setFun()
    }
}
// 做map映射 目前只针对单选
const userMapField = (userMap:any) => {
    let mapValue:any = {}
    const mapFields: YZField[] = []
    // 获取map数据
    let mapObj = props.field.config.extends.$map
    // 得倒需要map的值
    if(mapObj){
        Object.keys(mapObj).forEach((name) => {
            const value = mapObj[name] as string
            if(name.indexOf('.') > -1){
                let keyArr = name.split('.')
                let keyName = keyArr[0]
                let keyValue = keyArr[1]
                if(keyName == 'ou'){
                    mapValue[name] = userMap[keyName][keyValue]
                }else if(keyName == 'pos'){
                    mapValue[name] = userMap['defaultPosition'][keyValue]
                }
            }else mapValue[name] = userMap['user'][name]
            
            let formId = props.field.targetFormId
            let field = parseExpress.getFieldByFid(formId,value)
            //parseExpress.getField(value)
            if (value.indexOf(".") > -1 || value.indexOf("字表") > -1 || value.split('.').length === 2){
                let fex = value.split('.')[0]
                let fieldModel = value.split('.')[1]
                field = parseExpress.getFieldByFid(formId,fex) //parseExpress.getField(fex)
                const fieldValue = field?.vModel.value
                if (fieldValue && fieldValue.length > 0) {
                    const newIndex = props.index
                    if (newIndex > -1) {
                        // 表示当前操作的控件是主表，但是要给字表赋值
                        fieldValue.forEach((f: any, index: number) => {
                            const items = fieldValue[index].colums as Array<any>
                            const xfield = items.find(x => x.field.vModel.model === fieldModel)
                            if (xfield) {
                                mapFields.push(xfield.field)
                            }
                        })
                    }else {
                        const items = fieldValue[newIndex].colums as Array<any>
                        const xfield = items.find(x => x.field.vModel.model === fieldModel)
                        if (xfield)
                            mapFields.push(xfield.field)
                    }
                }
            }
            if (field) {
                mapFields.push(field)
            }
        })
        if (mapFields.length > 0){
            mapFields.forEach(field => {
                if (field) {
                    for (const key in mapObj) {

                        if(mapObj[key] === field.vModel.model || mapObj[key] === field.table){
                            eventBus.emit('onMap', true, {
                                value:mapValue[key] ,
                                model: field.vModel.model,
                                uuid: field.uuid,
                                index: props.index
                            })
                        }
                    }
                }
            })
        }
    }
}
eventBus.on('setSigleUser', {
    cb: function (params) {
        if (props.field.uuid === params['uuid']) {
            props.field.vModel.value = params.user.account
            props.field.vModel.optionValue = params.user.text
            setFun()
        }
    }
})
onUnmounted(() => {
    eventBus.off('setSigleUser')
})
const onUserClick = () => {
    if (!props.field.disabled) {
        defaultUsers.value = []
        if (props.modelValue && props.optionValue) {
            defaultUsers.value.push({
                name: props.optionValue,
                account: props.modelValue
            })
        }
        userShow.value = true
    }
}

eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
             if(params.value ||  params ===1) {
                // 禁用验证
                props.field.config.required =false 
                props.field.config.rules = []
             } else {
                // 启用验证
                props.field.config.required = true 
                props.field.config.rules = config.getDefaultRule()     
             }
        }
    }
})
</script>
<style  scoped></style>