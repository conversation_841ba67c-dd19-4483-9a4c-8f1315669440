<template>
    <div>
        <van-field  v-if="!field.hidden" v-model="vModel"
        v-show="!field.hidden"
        :name="field.vModel.model"
         :label="field.config.label"
        :required="field.config.required"
        :readonly="field.readonly"
        :key="field.vModel.model"
         disabled
        :rules="field.config.rules"
        :placeholder="field.placeholder" />
    </div>
</template>

<script lang="ts" setup>
import { YZCreateTime } from '@/logic/forms/YZCreateTime';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, PropType, ref } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZCreateTime>,
        required: true
    },
    modelValue:{
        type:String,
        default:''
    },
    index:{
        type:Number,
        default:-1
    }
})
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue',val)  
    }
})

eventBus.on('setFieldDisable',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.disabled = params.value 
       }
    }
})
eventBus.on('setFieldHidden',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.hidden = params.value 
       }
    }
})
</script>
<style  scoped>

</style>