<template>
    <div class="header" :class="{ border: !noBorder }">
        <div v-if="isBack" class="back" @click="router.back()">
            <van-icon class="icon" name="arrow-left" />
        </div>
        <div class="cont">
            <slot name="title"></slot>
        </div>
    </div>
    <div class="nav"> </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
const router = useRouter()

const props = defineProps({
    isBack: {
        type: Boolean,
        default: false
    },
    noBorder: {
        type: Boolean,
        default: false
    }
})

</script>
<style scoped lang="scss">
/* 占位 */
.nav {
    padding-bottom: 48px;
}

.cont {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.border {
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
}

.header {
    height: 48px;
    width: 100%;
    background: var(--yz-div-background);
    text-align: center;
    line-height: 48px;
    font-family: PingFangSC-Medium;
    font-size: var(--yz-home-header);
    color: var(--yz-text-333);
    font-weight: 500;
    position: fixed;
    top: 0;
    z-index: 1999;
    display: flex;
    align-items: center;
    justify-content: center;

    .back {
        position: absolute;
        top: 0;
        left: 0;
        width: 40px;
        height: 100%;
        padding-right: 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .icon {
            font-size: 20px;
            color: var(--yz-text-666);
        }
    }

}
</style>