<template>
    <div v-if="!field.hidden" v-show="!field.hidden">
        <van-field v-model="vModel" :name="field.vModel.model" :label="field.config.label"
            :required="field.config.required" @blur="onBlur" @input="onInput" :key="field.vModel.model"
            :readonly="readOnly" :disabled="field.disabled" :rules="field.config.rules"
            :placeholder="field.placeholder">
            <template v-if="config.showTelBtn()" #button>
                <van-button size="small" type="primary" @click="onTel">{{ $t('Form.CallPhone') }}</van-button>
            </template>

        </van-field>
    </div>
</template>

<script lang="ts" setup>
import { YZTextBox, YZTextBoxConfig } from '@/logic/forms/YZTextbox';
import { onMounted, nextTick } from 'vue';
import { computed, PropType, ref } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { useCheckPhone } from '@/utils';
import { showNotify } from 'vant';
import { isFraction } from 'mathjs';
import { throttle, debounce } from 'lodash'
const props = defineProps({
    field: {
        type: Object as PropType<YZTextBox>,
        required: true,

    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const readOnly = computed(() => props.field.config.extends.readOnly ?? props.field.readonly)
const emit = defineEmits(['update:modelValue'])
const config = props.field.config as YZTextBoxConfig
const isFirstLoad = ref<boolean>(true)

onMounted(() => {
    // console.log("%c [ props.field.config.label ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", props.field.config.label)
    // console.log("%c [ props.field.config ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", props.field.config)

    // props.field.expressTest(props.field, props.index, isFirstLoad.value)
    // props.field.disableTest(props.field, props.index, isFirstLoad.value)
    // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    isFirstLoad.value = false
    nextTick(() => { //YZ +
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
        props.field.disableTest(props.field, props.index, isFirstLoad.value)
        props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    })


})
const setFun = () => { //YZ +
    nextTick(() => {
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
        props.field.disableTest(props.field, props.index, isFirstLoad.value)
        props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    })
}
const time = ref(null)
const onInput = debounce(() => {
    console.log('onInput', vModel.value)
    nextTick(() => { //YZ +
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
        props.field.disableTest(props.field, props.index, isFirstLoad.value)
        props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    })
}, 500)
const vModel = computed({
    get() {
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const onBlur = (e: any) => {
    if (!props.field.disabled) {
        const type = config.getVType()
        const validate = checkValidate[type]
        if (validate) {
            if (!validate(e.target.value)) {
                onValidateMessage(type)
                emit('update:modelValue', '')
            }
        }
    }


}
// mobile 手机号
// phone // 固定电话
// globalphone // 固定电话，必须带区号
// tel  //手机，固定电话
//email 邮箱
//postcode 邮政编码
//url 网站地址
// identificationCard 身份证
const onTel = () => {
    if (vModel.value) {
        if (onValidate(vModel.value)) {
            window.location.href = 'tel:' + vModel.value
        } else {
            const type = config.getVType()
            onValidateMessage(type)
        }
    } else {
        showNotify({ type: 'danger', message: '请输入合法的号码' })
    }

}
// function IsEmail(str) {
//              var reg = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
//             return  reg.test(str);
//          }

const checkValidate: Record<string, any> = {
    //手机号
    'mobile': function (val: string) {
        if (val.length !== 11)
            return false
        var phone = /^1[3-9][0-9]{9}$/ ///^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1}))+\d{8})$/;
        if (!phone.test(val))
            return false
        return true
    },
    //固定电话
    'phone': function (val: string) {
        var result = val.match(/^\d{0,9}$/);
        if (!result)
            return false
        return true
    },
    //固定电话，必须带区号
    'globalphone': function (val: string) {
        if (val.indexOf("-") === -1)
            return false
        var result = val.match(/\d{3}-\d{8}|\d{4}-\d{7}/);
        if (!result)
            return false
        return true
    },
    // 手机和固定电话
    'tel': function (val: string) {
        var tel = /^0\d{2,3}-?\d{7,8}$/;
        var phone = /^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1}))+\d{8})$/;
        if (val.length == 11) {//手机号码
            return true;
        } else if (val.length == 13 && val.indexOf("-") != -1) {//电话号码
            if (!tel.test(val)) {
                return false;
            }
        }
        return true
    },
    //邮箱
    'email': function (val: string) {
        var pattern = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
        if (!pattern.test(val))
            return false
        return true
    },
    //邮政编码
    'postcode': function (val: string) {
        var pattern = /^[0-9]{6}$/;
        if (!pattern.test(val))
            return false
        return true
    },
    // 地址
    'url': function (val: string) {
        const pattern = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/
        if (!pattern.test(val))
            return false
        return true
    },
    //身份证
    'identificationCard': function (val: string) {
        const pattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!pattern.test(val))
            return false
        return true
    }

}
const onValidate = (val: string): boolean => {
    if (val) {
        const type = config.getVType()
        const funs = checkValidate[type]
        if (funs) {
            const result = funs(val)
            return result
        }
    }
    return true
}
const onValidateMessage = (type: string): void => {
    if (type === 'mobile') {
        showNotify({ type: 'danger', message: '手机号不符合规则' })
    } else if (type === 'phone') {
        showNotify({ type: 'danger', message: '固定电话不符合规则' })
    } else if (type === 'globalphone') {
        showNotify({ type: 'danger', message: '固定电话不符合规则,必须带上区号' })
    } else if (type === 'tel') {
        showNotify({ type: 'danger', message: '手机和固定电话不符合规则' })
    } else if (type === 'email') {
        showNotify({ type: 'danger', message: '电子邮箱不符合规则' })
    } else if (type === 'postcode') {
        showNotify({ type: 'danger', message: '邮政编码不符合规则' })
    } else if (type === 'url') {
        showNotify({ type: 'danger', message: '网站地址不符合规则' })
    } else if (type === 'identificationCard') {
        showNotify({ type: 'danger', message: '身份证件号不符合规则' })
    }
}

// 监听数据MAP
eventBus.on('onMap', {
    cb: function (params) {
        console.log("%c [ params 9]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid && props.field.pindex == params.index) { //YZ + && props.field.pindex == params.index
            props.field.vModel.value = params.value
            setFun()
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) { //YZ + 
            props.field.vModel.value = String(params.value)
        }
    }
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) { //YZ + 
            // console.log('setFieldDisable', params.value)
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) { //YZ + 
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && props.field.pindex == params.index) { //YZ + 
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})



</script>
<style scoped></style>