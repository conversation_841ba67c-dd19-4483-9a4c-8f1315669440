<template>
    <div class="await-work-wrap">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="flex: 1;overflow-y: auto;">
            <van-list v-model:loading="loading" v-longpress="onListLongPress" :finished="finished"
                :finished-text="$t('Form.NoMore')" @load="onLoad">
                <div class="grap"></div>
                <YZCard v-for="item in AwaitList" :key="item.serialNum" :process="item" @click.stop="onClick(item)">
                    <template #checkbox v-if="showCheck">
                        <div style="padding-left: 10px;">
                            <van-checkbox-group v-model="selectCheck" @click="onCheckSelect(item)">
                                <van-checkbox :name="item.stepId"></van-checkbox>
                            </van-checkbox-group>
                        </div>
                    </template>
                    <template #default>
                        <div class="slot-span" v-show="!queryType">
                            {{ item.reciveUser }}:
                        </div>
                        <div class="slot-span1" :style="dynamicStyle">
                            <div style="text-align: center; margin-bottom:5px;">
                                <van-icon class="icon" name="more-o" color="#00a0e1" v-if="queryType"
                                    style="font-size: 20px;" />
                            </div>
                            <div>
                                {{ item.nodeName }}
                            </div>
                        </div>
                    </template>
                </YZCard>
                <YZEmpty v-if="AwaitList.length <= 0" />
            </van-list>
        </van-pull-refresh>
        <div class="select-check-btn" v-if="showCheck">
            <van-button size="small" type="primary" :disabled="approBtnDisabled" @click="onBatchApprove">{{
                $t('Form.batchApprove') }}</van-button>
            <van-button size="small" type="primary" :disabled="informBtnDisabled" @click="onBatchInform">{{
                $t('Form.Notify')
                }}</van-button>
            <van-button size="small" type="primary" :disabled="transferBtnDisabled" @click="onBatchTransfer">{{
                $t('Form.Entrust') }}</van-button>
            <van-button size="small" type="primary" :disabled="abortBtnDisabled" @click="onBatchabort">{{
                $t('Global.Cancel')
                }}</van-button>
            <!-- <van-button size="small" type="primary" :disabled="approBtnDisabled" @click="onBatchApprove">{{ $t('Form.batchApprove') }}</van-button> -->
            <van-button size="small" type="danger" @click="checkCancel">{{ $t('Global.Cancel') }}</van-button>
        </div>
    </div>
    <van-popup teleport="body" v-model:show="procesShow" :style="{ height: '100%' }" position="bottom">
        <renderForm :key="time" />
    </van-popup>
    <!-- 知会 -->
    <van-dialog v-model:show="showInform" :title="$t('Form.Notify')" @confirm="infoConfirm" :before-close="() => false"
        @cancel="infoCancle" show-cancel-button>
        <div class="dialog-div">
            <van-field v-model="notifyData.users" readonly :label="$t('Form.SelectUser')"
                :placeholder="$t('Form.Select_Username_tips')" @click="onUserClick" />
            <van-field v-model="notifyData.message" rows="1" autosize :label="$t('Form.NotifyOpinion')" type="textarea"
                :placeholder="$t('Form.NotifyOpinion_tips')" />
        </div>
    </van-dialog>
    <YZUserSelect v-model:show="userShow" :multiple="true" @on-save="onUserSave" />
    <!-- 委托 -->
    <van-dialog v-model:show="weiTuoShow" :title="$t('Form.Task_delegation')" @confirm="onwtConfirm"
        :before-close="() => false" @cancel="onwtCancle" show-cancel-button>
        <div class="dialog-div">
            <van-field v-model="wtData.users" readonly :label="$t('Form.SelectUser')"
                :placeholder="$t('Form.Select_Username_tips')" @click="wtUserClick" />
            <van-field v-model="wtData.message" rows="1" autosize :label="$t('Form.Entrusted_opinion_tips')"
                type="textarea" :placeholder="$t('Form.Entrusted_opinion_tips')" />
        </div>
    </van-dialog>
    <YZUserSelect v-model:show="wtUserShow" :multiple="false" @on-save="WtUserSave" />
    <!-- 撤销 -->
    <van-dialog v-model:show="backShow" :title="$t('Global.Cancel')" @confirm="onCancleConfirm"
        :before-close="() => false" @cancel="onCancleCancle" show-cancel-button>
        <div class="dialog-div">
            <van-field v-model="cancelMessage" rows="1" autosize :label="$t('Form.Reason_for_revocation')"
                type="textarea" :placeholder="$t('Form.Reason_for_revocation_tips')" />
        </div>
    </van-dialog>




</template>

<script lang="ts" setup>
import YZUserSelect from '@/components/YZUserSelect.vue';
import { MyWorkTask } from '@/logic/forms/processHelper';
import { ProcessService } from '@/logic/forms/processHelper';
import { SearchPanel } from '@/logic/work/workModel';
import { useHomeStore } from '@/store/home';
import { useProcessStore } from '@/store/process';
import { eventBus } from '@/utils/eventBus';
import { onMounted, onUnmounted, computed } from 'vue';
import { ref, reactive } from 'vue';
import YZCard from '@/components/YZCard.vue';
import YZEmpty from '@/components/YZEmpty.vue'
import { usePostBody } from '@/utils/request';
import { url } from '@/api/url';
import { useFormData, useLang, useParamsFormatter } from '@/utils';
import { Toast, showConfirmDialog, showNotify } from 'vant';
import { useRoute, useRouter } from 'vue-router'

const loading = ref<boolean>(false);
const finished = ref<boolean>(false);
const refreshing = ref<boolean>(false);
const pageIndex = ref<number>(1)
const pageSize = ref<number>(20)
const AwaitList = ref<Array<MyWorkTask>>([])
const procesShow = ref<boolean>(false)
const porcessStore = useProcessStore()
const homeStore = useHomeStore()
const time = ref<number>(0)
const service = new ProcessService()
const searchF = ref<SearchPanel>()
const isSearch = ref<boolean>(false)
const selectCheck = ref<any>([])
const showCheck = ref<boolean>(false)
const showInform = ref<boolean>(false)
const wtUserShow = ref<boolean>(false)

const router = useRouter()
const route = useRoute()

const queryType = ref(route?.query.type && route?.query.type !== 'ApplyWork')
console.log("%c [ AwaitWork --- route.query.type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route?.query.type)

const emits = defineEmits<{
    clearKwd: []
}>()
const onRefresh = () => {
    refreshing.value = true
    restart()
    isSearch.value = false
    emits('clearKwd')
    onLoad()
}
const restart = () => {
    pageIndex.value = 1
    loading.value = true
    finished.value = false
    AwaitList.value = []
}
const onLoad = async () => {
    let search: any = new SearchPanel({ limit: 20, start: 0, page: 1 })
    if (refreshing.value) {
        refreshing.value = false
        // await service.getWorkCount() // 获取待审批总数
    }
    if (isSearch.value && searchF.value) {
        search = searchF.value
    }
    if (queryType.value) {
        console.log("%c [ queryType---检查类型 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route?.query.type)
        toSearch2(search)
    } else {
        toSearch(search)
    }
    // console.log('待审批页面---222', 222) //这里走了待审批接口
    loading.value = false
}
const toSearch = async (search: SearchPanel) => {
    // console.log("%c [ 999search ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", search)
    const start = (pageIndex.value - 1) * pageSize.value
    const limit = pageSize.value
    const page = pageIndex.value
    search.limit = limit
    search.page = page
    search.start = start
    const data = await service.getMyWorkList(search)
    loading.value = false
    if (data.children.length <= 0 || data.total <= AwaitList.value.length) {
        finished.value = true
    } else {
        // 处理重复的问题
        data.children.forEach(item => {
            let index = AwaitList.value.findIndex(el => el.taskId == item.taskId)
            if (index <= 0) {
                AwaitList.value.push(item)
            }
        });
        pageIndex.value++
        finished.value = false
    }
}
const toSearch2 = async (search: SearchPanel) => {
    const start = (pageIndex.value - 1) * pageSize.value
    const limit = pageSize.value
    const page = pageIndex.value
    const queryType = route.query?.type
    let ProcessName = ''
    if (queryType === 'Preliminary') {
        ProcessName = 'SSI-Tool&Equipment Registration'
    } else if (queryType === 'Daily') {
        ProcessName = 'SSI-Daily Inspection'
    } else if (queryType === 'Monthly') {
        ProcessName = 'SSI-Monthly Inspection'
    } else if (queryType === 'Quarterly') {
        ProcessName = 'SSI-Quarterly Inspection'
    } else {
        console.log("%c [ AwaitWork -- 没有 ProcessName ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
    }
    const searchMain_: any = {
        ProcessName,
        searchType: 'AdvancedSearch',
        page,
        start,
        limit
    }
    const data = await service.getMyWorkList(searchMain_) //(searchMain) //
    console.log("%c [ --searchMain_ ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchMain_)
    loading.value = false
    if (data.children.length <= 0 || data.total <= AwaitList.value.length) {
        finished.value = true
    } else {
        data.children.forEach(item => {
            let index = AwaitList.value.findIndex(el => el.taskId == item.taskId)
            if (index <= 0) {
                AwaitList.value.push(item)
                // console.log("%c [ AwaitList.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", AwaitList.value)
            }
        });
        pageIndex.value++
        finished.value = false
    }
}
const approBtnDisabled = ref<boolean>(true)
const informBtnDisabled = ref<boolean>(true)
const transferBtnDisabled = ref<boolean>(true)
const abortBtnDisabled = ref<boolean>(true)
const onListLongPress = () => {
    if (!procesShow.value) {
        showCheck.value = true
    }
}
// 取消长按多选审批事件
const checkCancel = () => {
    showCheck.value = false //选择的checkbox隐藏
    selectCheck.value = [] //选中的数据清空
    approBtnDisabled.value = true //审批按钮状态为false
    informBtnDisabled.value = true
    transferBtnDisabled.value = true
    abortBtnDisabled.value = true
}
// 返回几个流程的权限
const selectPermiss = async (selectCheck: any, tid: string) => {
    if (selectCheck && selectCheck.length > 0) {
        const postData = {
            Method: 'GetProcessingPermision',
            perms: 'BatchApprove,Reject,Inform,Transfer,Abort',
            ids: selectCheck
        }
        const data = await usePostBody(url.process.getTaskPermission, {}, useFormData(postData));
        let arr = Object.keys(data.perms)
        let applyFlag = arr.every(item => {
            let el = data.perms[item]
            return el.BatchApprove
        })
        let informFlag = arr.every(item => {
            let el = data.perms[item]
            return el.Inform
        })
        let transferFlag = arr.every(item => {
            let el = data.perms[item]
            return el.Transfer
        })
        let abortlFlag = arr.every(item => {
            let el = data.perms[item]
            return el.Abort
        })
        approBtnDisabled.value = !applyFlag
        informBtnDisabled.value = !informFlag
        transferBtnDisabled.value = !transferFlag
        abortBtnDisabled.value = !abortlFlag
    } else {
        approBtnDisabled.value = true
        informBtnDisabled.value = true
        transferBtnDisabled.value = true
        abortBtnDisabled.value = true
    }
    let falg = selectCheck.includes(tid)
    if (selectCheck.length == 0 && falg == false) {
        approBtnDisabled.value = true
    }
}
const onCheckSelect = (task: any, type?: string) => { //列表里多选 --- 选中的
    // console.log("%c [ --AwaitList.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", AwaitList.value)
    let index = selectCheck.value.findIndex((item: any) => item == task.stepId)
    if (index >= 0) {
        selectCheck.value.splice(index, 1)
    } else {
        selectCheck.value.push(task.stepId)
    }
    selectPermiss(selectCheck.value, task.stepId)
}
const onBatchApprove = () => {
    showConfirmDialog({ title: useLang('Form.OperateConfirm'), message: useLang('Form.Select_Confirm_tips'), })
        .then(async () => {
            const postData = {
                items: selectCheck.value.map((item: any) => {
                    return {
                        ID: item,
                        StepID: item
                    }
                })
            }
            const data = await usePostBody(url.process.batchApprove, {
                Method: 'BatchApprove',
            }, postData);
            if (data.success) {
                let successStr = data.processedItems.map((el: any) => {
                    return `${el.SerialNum}：${el.Result}`
                }).join('\n')
                showNotify({ type: 'success', message: successStr });
                checkCancel()
                restart()
                onLoad()
            }
        })
        .catch(() => {
            // on cancel
        });
}
// 知会操作start
const notifyData = reactive({
    message: '',
    users: ''
})
const userShow = ref<boolean>(false)
const userIds = ref<string[]>([])
const infoConfirm = async () => {
    // 发起知会
    if (!notifyData.users) {
        showNotify({ message: useLang('Form.Select_Username_tips'), type: 'danger' })
        return
    }
    if (!notifyData.message) {
        showNotify({ message: useLang('Form.NotifyOpinion_tips'), type: 'danger' })
        return
    }
    const dataArr = selectCheck.value.map((item: any) => {
        let obj = AwaitList.value.find((el: any) => el.stepId == item)
        return {
            ID: obj?.stepId,
            TaskID: obj?.taskId
        }
    })
    const postJson: any = {
        accounts: userIds.value,
        comments: notifyData.message,
        items: dataArr
    }
    // console.log("%c [ 知会 params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", postJson)

    const data = await usePostBody(url.process.postNotify, {}, postJson, true)
    if (data.success === false) {
        showNotify({ type: 'danger', message: data.message ?? data.errorMessage })
    } else {
        showNotify({ type: 'success', message: useLang('Form.NotifySuccess') })
        showInform.value = false
        notifyData.message = ''
        notifyData.users = ''
        checkCancel()
        restart()
        onLoad()
    }
}
const onUserClick = () => {
    userShow.value = true
}
const onUserSave = (users: any) => {
    userShow.value = false
    userIds.value = users.map((x: any) => x.account)
    notifyData.users = users.map((x: any) => x.name).join(',')
}
const onBatchInform = () => {
    showInform.value = true
}
const infoCancle = () => {
    showInform.value = false
    notifyData.message = ''
    notifyData.users = ''
}
// 知会操作end

// 委托操作 start
const wtData = reactive({
    message: '',
    users: ''
})
const weiTuoShow = ref<boolean>(false)
const onBatchTransfer = () => {
    weiTuoShow.value = true
}
const wtUserClick = () => {
    wtUserShow.value = true
}
const WtUserSave = (users: any) => {
    wtUserShow.value = false
    userIds.value = users.map((x: any) => x.account)
    wtData.users = users.map((x: any) => x.name).join(',')
}
const onwtCancle = () => {
    weiTuoShow.value = false
    wtData.message = ''
    wtData.users = ''
}
const onwtConfirm = async () => {
    // 发起知会
    if (!wtData.users) {
        showNotify({ message: useLang('Form.select_EntrustedPersonnel_tips'), type: 'danger' })
        return
    }
    if (!wtData.message) {
        showNotify({ message: useLang('Form.Enter_Entrusted_opinion_tips'), type: 'danger' })
        return
    }
    const dataArr = selectCheck.value.map((item: any) => {
        return {
            ID: item,
            StepID: item,
        }
    })
    const postJson: any = {
        accounts: userIds.value,
        comments: wtData.message,
        items: dataArr
    }
    console.log("%c [ 委托 params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", postJson)

    let newUrl = useParamsFormatter(url.process.assigneUserBatch, {})
    const data = await usePostBody(newUrl, {}, postJson, true)
    if (!data.success) {
        showNotify({ type: 'danger', message: data.message || data.errorMessage })
    } else {
        showNotify({ type: 'success', message: useLang('Form.EntrustSuccess') })
        weiTuoShow.value = false
        wtData.message = ''
        wtData.users = ''
        checkCancel()
        restart()
        onLoad()
    }
}
// 委托操作 end

// 撤销操作start

const backShow = ref<boolean>(false)
const cancelMessage = ref<string>('')
const onBatchabort = () => {
    backShow.value = true
}

const onCancleConfirm = async () => {
    if (!cancelMessage.value) {
        showNotify({ message: useLang('Form.Reason_for_revocation_tips') })
    } else {
        const dataArr = selectCheck.value.map((item: any) => {
            let obj = AwaitList.value.find((el: any) => el.stepId == item)
            return {
                ID: obj?.stepId,
                TaskID: obj?.taskId
            }
        })
        const data = await usePostBody(url.process.cancleTask, {}, {
            comments: cancelMessage.value,
            items: dataArr
        }, true)
        if (data.success === false) {
            showNotify({ message: data.errorMessage, type: 'danger' })
        } else {
            showNotify({ message: useLang('Form.Cancellation_successful'), type: 'success' })
            backShow.value = false
            cancelMessage.value = ''
            checkCancel()
            restart()
            onLoad()
        }
    }
}
const onCancleCancle = () => {
    backShow.value = false
    cancelMessage.value = ''
}
const onClick = async (task: MyWorkTask) => {
    console.log("%c [ --task-- ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", task)
    if (showCheck.value) {
        onCheckSelect(task)
    } else {
        if (task.processShortName.includes('SSI')) {
            router.push({ path: '/ssi_apply', query: { next: 'await', taskId: task.taskId, processName: task.processName, stepId: task.stepId, serialNum: task.serialNum, stepName: task.activityName } }) // 去ssi_apply
        } else {
            porcessStore.setTaskId(task.taskId)
            porcessStore.setStepId(task.stepId)
            time.value = new Date().getTime()
            procesShow.value = true
        }
    }
}
eventBus.on('onBack', {
    cb: function () {
        const appActive = porcessStore.getTabName
        if (appActive === 'AwaitWork') {
            procesShow.value = false
            homeStore.setTab(true)
            AwaitList.value = []
            pageIndex.value = 1
            onLoad()
        }

    }
})
eventBus.on('AwaitWork', {
    cb: function (params) {
        console.log("%c [ AwaitWork --- params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        // if (loading.value) {
        //     return;
        // }
        pageIndex.value = 1
        pageSize.value = 10
        AwaitList.value = []
        if (params['resert']) {
            isSearch.value = false
            emits('clearKwd')
            onRefresh()
        } else {
            const search = new SearchPanel(params)
            console.log("%c [ AwaitWork search --- 构建搜索参数 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", search)
            selectCheck.value = []
            // loading.value = true
            console.log('待审批', 333)
            toSearch(search)
            console.log('待审批', 444)

            searchF.value = search
            isSearch.value = true
        }
    }
})
const dynamicStyle = computed(() => {
    return queryType.value ? { right: '10px', top: '-15px', fontSize: '11px' } : {} //{ left: '15px', right: 'auto', display: 'flow', fontSize: '14px' }
    // display: 'grid', 
})
onMounted(() => {
})
onUnmounted(() => {
    eventBus.off('AwaitWork')
    eventBus.off('onBack')

})
</script>
<style lang="scss" scoped>
.await-work-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.grap {
    width: 100%;
    height: 10px;
    background-color: #f9f9f9;
}

.slot-span {
    font-family: PingFangSC-Regular;
    font-size: var(--yz-work-slotspan);
    color: var(--yz-text-666);
    font-weight: 400;
    width: 185px !important;
    padding: 5px 0;
}

.slot-span1 {
    font-family: PingFangSC-Regular;
    font-size: var(--yz-work-slotspan);
    color: var(--yz-text-666);
    font-weight: 400;
    padding: 0 0 10px 0;
    position: absolute;
}

.van-list {
    background-color: var(--yz-background-vanlist);
}

.select-check-btn {
    display: flex;
    padding: 5px;
    align-items: center;

    button {
        padding: 0 15px;

        &:not(:last-child) {
            margin-right: 5px;
        }
    }
}

.van-list__finished-text {
    background: var(--yz-layout-mian) !important;
}
</style>
