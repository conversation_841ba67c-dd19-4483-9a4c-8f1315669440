import { UploaderFileListItem } from "vant";
import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON><PERSON>, Y<PERSON><PERSON>tyle } from "./formModel";
import { usePostBody } from "@/utils/request";
import { url } from "@/api/url";
import { eventBus } from "@/utils/eventBus";
import { useLang } from "@/utils";
export class Y<PERSON>OrgsConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZOrgs'
        this.defaultRules = [{ required: true, message: useLang('Form.SelectDepart') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZOrgsStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class <PERSON><PERSON><PERSON><PERSON><PERSON> extends Y<PERSON><PERSON>ield {
    constructor(params: any) {
        super(params)
        this.vModel.value = ''
        const modelValue = params['__vModel__'].modelValue
        this.vModel.value = modelValue
        if (modelValue) {
            usePostBody(url.process.getSimpleDept, {}, {
                ouids: modelValue.split(',')
            }).then(result => {
                if (result && result.length > 0) {
                    eventBus.emit('setMultipleOrg', false, result)
                }
            }).catch(error => {
                // console.log('获取单个部门异常',error)
            })
        }

    }
    initConfig(config: any): YZConfig {
        return new YZOrgsConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZOrgsStyle(style)
    }
}