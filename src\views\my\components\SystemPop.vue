<template>
    <div>
        <van-popup v-model:show="show" position="right" style="heigth: 100vh; width: 100%">
            <div class="service-div">
                <van-nav-bar :title="$t(item.name)" :left-text="$t('Global.Back')"
                    @click-left.stop="item.onBack(item.name)" left-arrow />
                <div>
                    <van-cell-group :title="$t(model.key)" v-for="model in menus" :key="model.key">
                        <van-cell v-for="child in model.children" @click="handlerClick(model.key, child.value)"
                            :key="child.name" :title="$t(child.name)">
                            <van-icon slot="right-icon" name="success" v-if="child.isSelect" color="#4caf50"
                                class="van-cell__right-icon"></van-icon>
                        </van-cell>
                    </van-cell-group>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { IMyMenu, ISystemMenu } from '@/logic/my/myModel';
import { useHomeStore } from '@/store/home';
import { mode } from 'mathjs';
import { reactive } from 'vue';
import { computed, PropType, ref } from 'vue';
export interface IMenus {
    key: string,
    children: Array<ISystemMenu>
}
const homeStore = useHomeStore()
const props = defineProps({
    isPop: {
        type: Boolean,
        default: false,
        required: false
    },
    item: {
        type: Object as PropType<IMyMenu>,
        required: true
    }
})
const show = computed(() => props.isPop)
const menus: Array<IMenus> = reactive([
    // my.model  隐藏
    {
        key: 'my.model',
        children: [
            { name: 'my.lint', value: "light", isSelect: (homeStore.getTheame === 'light') },
            { name: 'my.dark', value: "dark", isSelect: (homeStore.getTheame === 'dark') }
        ]
    },
    {
        key: 'my.Font',
        children: [
            { name: 'my.small', value: "小", isSelect: (homeStore.getFontSize === '小') },
            { name: 'my.medium', value: "中", isSelect: (homeStore.getFontSize === '中') },
            { name: 'my.large', value: '大', isSelect: (homeStore.getFontSize === '大') }
        ]
    }
])
const handlerClick = (key: string, value: string): void => {
    const model = menus.find(x => x.key === key)
    if (model) {
        model.children.forEach(item => {
            item.isSelect = (item.value === value ? true : false)
        })
    }
    if (key === 'my.model') {
        homeStore.setTheme(value)
        window.document.documentElement.setAttribute("data-theme", value);
    } else if (key === 'my.Font') {
        homeStore.setFontSize(value)
        window.document.documentElement.setAttribute("data-size", value);
    }

}

</script>
<style scoped>
.service-div {
    width: 100%;
    height: 100vh;

    ::v-deep(.van-cell__title) {
        font-size: var(--yz-com-14);
    }

    ::v-deep(.van-cell-group__title) {
        font-size: var(--yz-com-14);
    }
}
</style>