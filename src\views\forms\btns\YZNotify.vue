<template>
    <div>
        <van-button size="small" hairline type="default" :disabled="!btn.enable" icon="chat-o" @click="onClick"
            style="height: 32px;padding: 0px 10px;">
            {{ btn.text }}
        </van-button>
        <van-dialog teleport="body" v-model:show="dialogShow" :title="$t('Form.Notify')" @confirm="onConfirm"
            :before-close="notifyBefore" @cancel="onNotifyCancle" show-cancel-button>
            <div class="dialog-div">
                <van-field v-model="notifyData.users" readonly :label="$t('Form.SelectUser')"
                    :placeholder="$t('Form.Select_Username_tips')" @click="onUserClick" />
                <van-field v-model="notifyData.message" rows="1" autosize :label="$t('Form.NotifyOpinion')"
                    type="textarea" :placeholder="$t('Form.NotifyOpinion_tips')" />
            </div>
        </van-dialog>
        <YZUserSelect v-model:show="userShow" :multiple="true" @on-save="onUserSave" :default-users="defaultUsers" />
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { PropType, ref } from 'vue';
import YZUserSelect, { ISelectUser } from '@/components/YZUserSelect.vue';
import { reactive } from 'vue';
import { useHomeStore } from '@/store/home';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/store/users';
import { useCallBackClose, useLang, useParamsFormatter } from '@/utils';
import { url } from '@/api/url';
import { usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { useProcessStore } from '@/store/process';
import { eventBus } from '@/utils/eventBus';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const dialogShow = ref<boolean>(false)
const defaultUsers = ref<Array<ISelectUser>>([])
const notifyData = reactive({
    message: '',
    users: ''
})
const homeStore = useHomeStore()
const userShow = ref<boolean>(false)
const userIds = ref<string[]>([])
const route = useRoute()
const userStrore = useUserStore()
const processStore = useProcessStore()
const onClick = () => {
    dialogShow.value = true
}
const onUserClick = () => {

    defaultUsers.value = []
    if (userIds.value && userIds.value.length > 0 && notifyData.users && notifyData.users.length > 0) {
        const names = notifyData.users.split(',')
        const accounts = userIds.value
        for (let i = 0; i < names.length; i++) {
            defaultUsers.value.push({
                name: names[i],
                account: accounts[i]
            })
        }
    }
    userShow.value = true
    homeStore.setHead(false)
}
const onUserSave = (users: any) => {
    userShow.value = false
    homeStore.setHead(true)
    defaultUsers.value = []
    userIds.value = users.map((x: any) => x.account)
    notifyData.users = users.map((x: any) => x.name).join(',')
}
const onConfirm = async () => {
    // 发起知会
    if (!notifyData.users) {
        showNotify({ message: useLang('Form.Select_Username_tips'), type: 'danger' })
        return
    }
    if (!notifyData.message) {
        showNotify({ message: useLang('Form.NotifyOpinion_tips'), type: 'danger' })
        return
    }
    const instanceId = processStore.getProcLoad.taskId
    if (instanceId) {
        const postJson: any = {
            accounts: userIds.value,
            comments: notifyData.message,
            items: [{
                ID: instanceId,
                TaskID: instanceId
            }]
        }
        const data = await usePostBody(url.process.postNotify, {}, postJson)
        if (data.success === false) {
            showNotify({ type: 'danger', message: data.message ?? data.errorMessage })
            console.log('error-4')

        } else {
            showNotify({ type: 'success', message: useLang('Form.NotifySuccess') })
            dialogShow.value = false
            notifyData.message = ''
            notifyData.users = ''
            useCallBackClose(function () {
                eventBus.emit('onBack', true)
            }, processStore.getHideTabHeader)
        }
    }

}
const notifyBefore = () => {
    return false
}
const onNotifyCancle = () => {
    dialogShow.value = false
    notifyData.message = ''
    notifyData.users = ''
}
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    height: 26vh;
}
</style>