<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ $t('my.AccountSecurity') }}
            </template>
        </YZNav>
        <div class="logout">
            {{ $t('my.CancelAccount') }}
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';

</script>

<style lang="scss" scoped>
.page {
    background-color: var(--yz-layout-mian);
    min-height: 100vh;
    width: 100%;

    .logout {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        margin-top: 10px;
        background-color: #FFFFFF;
        font-size: var(--yz-com-14);
        color: #FF3333;
    }
}
</style>