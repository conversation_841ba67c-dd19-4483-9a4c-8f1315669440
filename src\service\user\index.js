import sendRequest from "@/service/requestService.js"
import loginRequest from "@/service/loginService.js"

// 转换formdata格式
function isFormData(data) {
	let formData = new URLSearchParams();
	for (let i in data) {
		formData.append(i, data[i]);
	}
	return formData;
}

/**
 * @desc ebook 列表
 * @param {data}
 */
export function GetEbookList(data) {
	return sendRequest({
		method: 'post',
		url: '/bpm/ebook/paging',

		// url: '/bpm/datasource/esb/567026479235141/paging',
		// url: `/app/instance/564805220016197/paging?_dc=${Date.now()}`,
		data: isFormData(data),
	})
}
// /app/instance/564805220016197/paging?_dc=1734934221514
/**
 * @desc ebook 详情
 * @param {data}
 */
export function GetEbookInfo(fileId) { //Not used
	return sendRequest({
		method: 'get',
		url: `/bpm/attachment/file/${fileId}`,
		responseType: 'blob',
	})
}

/**
 * @desc ebook 已阅读
 * @param {data}
 */
export function SetEbookRead(data) {
	return sendRequest({
		method: 'post',
		url: `/app/instance/566584481402949/rec/-1/appinfo/New/submit`,
		data,
	})
}

/** 
 * @desc 项目号/办公室地点 || 问题责任人 || 
 * @param {data}
 */
// export function ProjectSupplier(data) {
// 	return sendRequest({
// 		method: 'post',
// 		url: `/bpm/datasource/***************/table/VW_UAUC_ProjectSupplier/paging`,
// 		data,
// 	})
// }
/** 
 * @desc 告知人
 * @param {data}
 */
// export function InformPeople(data) {
// 	return sendRequest({
// 		method: 'post',
// 		url: `/bpm/datasource/***************/table/VW_ProjectUserRole/paging?_dc=${Date.now()}`,
// 		data,
// 	})
// }
/** 
 * @desc 培训类型 | Training QR Code 
 * @param {data}
 */
export function TrainingQRCodeApi(data) { //获取培训类型
	return sendRequest({
		method: 'post',
		url: `/bpm/datasource/***************/table/BDS_DefineTrainingCourse/paging?_dc=${Date.now()}`,
		data,
	})
}

// https://ppa80-dev.linde-le.cn/ppa/app/instance/604811365511237/rec/-1/appinfo/New/submit  //ppa 变成了mobile
export function submit_QR(data) { 		//提交二维码所需参数
	return sendRequest({
		method: 'post',
		url: `/app/instance/604811365511237/rec/-1/appinfo/New/submit`,
		data,
	})
}
// https://ppa80-dev.linde-le.cn/ppa/bpm/util/barcode/encode?text=DT2024110030&width=500&height=500&format=QR_CODE&_dc=1732847098190  //ppa 变成了mobile  //Not used
export function QR_code(params) { 		//生成二维码图
	sendRequest({
		url: `/bpm/util/barcode/encode`,
		method: 'get',
		params
	})
}
export function Sign_in_and_out(data) { //签到 签退扫码后的入口
	return sendRequest({
		method: 'post',
		url: `/bpm/datasource/***************/table/BDS_DefineTrainingInformation`,
		data,
	})
}

export function SignIn(data, id) { //点击签到
	return sendRequest({
		method: 'post',
		url: `/bpm/lindetraining/${id}/checkin`,
		data,
	})
}

export function SignOut(data, id) { //点击签退
	return sendRequest({
		method: 'post',
		url: `/bpm/lindetraining/${id}/checkout`,
		data,
	})
}
// export function Sign_in_out_record(Account) { 		//签到 签退记录
// 	return sendRequest({
// 		url: `/bpm/lindetraining/trainlog/${Account}`,
// 		method: 'get'
// 	})
// }

export function Sign_in_out_record(data) { 	//新 签到 签退记录
	return sendRequest({
		method: 'post',
		// url: `/bpm/datasource/***************/table/VW_DefineTrainingLogs/paging?_dc=${Date.now()}`,
		url: '/bpm/lindetraining/paging',
		data,
	})
}
export function SupplierPermissions() {
	return sendRequest({
		url: `bpm/lindetraining/checkpermission`,
		method: 'get'
	})
}
export function longitudeProject(params) { 	   //经纬度获取项目号
	return sendRequest({
		url: `uauc/defaultproject`,
		method: 'get',
		params
	})
}

export function OwnerPosition(data) { 		 // ownerPosition
	return sendRequest({
		method: 'post',
		url: `/bpm/workflow/process/name/UAUC/postinfo`,
		data,
	})
}
export function UAUC_Submit(data) { 		 //uauc提交
	return sendRequest({
		method: 'post',
		url: `/bpm/workflow/process/***************/version/1.0/start`,
		data,
	})
}

export function sso_accesstoken(data) { 	   // sso/accesstoken
	return sendRequest({
		url: `sso/accesstoken`,
		method: 'post',
		data
	})
}
export function sso_login(params) { 	   // sso/login
	return sendRequest({
		url: `sso/login`,
		method: 'get',
		params
	})
}

export function checkLogin(params) { 	   // sso/checklogin 
	return sendRequest({ //loginRequest
		url: `sso/checklogin`,
		method: 'get',
		params
	})
}

export function autoLogin(params) { 	   // sso/autoLogin
	return sendRequest({
		url: `sso/autologin`,
		method: 'get',
		params
	})
}

export function SSI_Submit(data) { 		 //SSI提交
	return sendRequest({
		method: 'post',
		url: `/lindessi/entryform`,
		data,
	})
}

export function CheckSubmission(data, type, stepId) { 		 //SSI Apply 提交
	return sendRequest({
		method: 'post',
		// url: `/preliminary/${stepId}`,
		url: `/lindessi/${type}/${stepId}`,
		data,
	})
}

export function BarCode(params) { 		//barCode
	return sendRequest({
		url: `/bpm/barcode/${params}`,
		method: 'get'
	})
}

export function BarcodeList(data) { 		 // barcodeList
	return sendRequest({
		method: 'post',
		url: `/bpm/barcode/list`,
		data,
	})
}