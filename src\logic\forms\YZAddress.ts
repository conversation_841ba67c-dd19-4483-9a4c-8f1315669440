import { UploaderFileListItem } from "vant";
import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
import { useLang } from "@/utils";
export class YZAddressConfig extends YZConfig {
    private level: number;
    private showDetail: boolean;
    private defaultRules: Array<any>
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZAddress'
        this.defaultRules = [{ required: true, message: useLang('Form.SelectAddress_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.level = parmas['regionLevel']
        this.showDetail = (parmas['hideDetailAddress'] === true ? false : true)
    }
    getLevel(): number {
        return this.level
    }
    getShowDetail(): boolean {
        return this.showDetail
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZAddressStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class Y<PERSON><PERSON>ddress extends YZField {
    constructor(params: any) {
        super(params)
        this.vModel.value = ''
        this.vModel.optionValue = ''
        const values = params['__vModel__'].modelValue
        if (values) {
            this.vModel.value = values.split(' ')[0]
            this.vModel.optionValue = values.split(' ')[1]
        }
    }
    initConfig(config: any): YZConfig {
        return new YZAddressConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZAddressStyle(style)
    }
}