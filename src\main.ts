import { createApp, defineAsyncComponent } from 'vue'
import 'vant/lib/index.css';
import App from './App.vue'
import Vant, { showNotify, useCurrentLang } from 'vant'
import router from './router';
// 引入字体图标
import '@/assets/font/iconfont.css'
// import '@/utils/uni.webview.1.5.5.js'
import i18n from './locale';
import store from './store';
import { regiest, useDymicVue } from './utils/regiest';
import { initHub } from '@/utils/chathub'
import * as Vue from 'vue'
import initDiretives from './Directives';
// import VConsole from 'vconsole';


// 引入全局css （放在所有css 后面）
import '@/assets/css/common.scss'
import axios from 'axios';
const app = createApp(App)
regiest(app)
// useDymicVue(Vue,Vant,app)
initDiretives(app)
app.use(Vant)
app.use(router)
app.use(i18n)
app.use(store)
app.config.globalProperties.$notify = showNotify

// const vConsole = new VConsole(); //& 生产隐藏

axios.defaults.withCredentials = true
// 自定义接口扫码事件
window.appScanCodeResultJSONFn = function (val) {
    console.log(val);
    window.appScanCodeResultJSON = val
    window.dispatchEvent(new CustomEvent('scanresult'))
}
app.mount('#app')
initHub()
