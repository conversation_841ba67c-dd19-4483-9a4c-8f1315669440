lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@dcloudio/uni-app':
        specifier: 2.0.2-4020420240722004
        version: 2.0.2-4020420240722004(@dcloudio/types@3.4.14)(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))
      '@microsoft/signalr':
        specifier: ^7.0.5
        version: 7.0.14
      '@rollup/rollup-darwin-arm64':
        specifier: ^4.12.0
        version: 4.14.1
      '@tato30/vue-pdf':
        specifier: ^1.11.1
        version: 1.11.1(vue@3.4.21(typescript@4.9.5))
      '@tinymce/tinymce-vue':
        specifier: ^5.1.1
        version: 5.1.1(vue@3.4.21(typescript@4.9.5))
      '@vitejs/plugin-legacy':
        specifier: ^5.1.3
        version: 5.3.2(terser@5.30.3)(vite@5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3))
      '@vue-office/docx':
        specifier: ^1.6.3
        version: 1.6.3(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue-demi@0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))
      '@vue-office/excel':
        specifier: ^1.7.14
        version: 1.7.14(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue-demi@0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))
      '@vue-office/pptx':
        specifier: ^1.0.1
        version: 1.0.1(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue-demi@0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))
      axios:
        specifier: ^1.3.1
        version: 1.6.8
      compressorjs:
        specifier: ^1.2.1
        version: 1.2.1
      crypto-js:
        specifier: ^4.1.1
        version: 4.2.0
      dayjs:
        specifier: ^1.11.7
        version: 1.11.10
      dingtalk-jsapi:
        specifier: ^3.0.25
        version: 3.0.32
      heic2any:
        specifier: ^0.0.4
        version: 0.0.4
      html5-qrcode:
        specifier: ^2.3.8
        version: 2.3.8
      js-cookie:
        specifier: ^3.0.5
        version: 3.0.5
      lint:
        specifier: ^0.8.19
        version: 0.8.19
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      mathjs:
        specifier: ^11.8.0
        version: 11.12.0
      mitt:
        specifier: ^3.0.0
        version: 3.0.1
      pdfh5:
        specifier: ^1.4.9
        version: 1.4.9
      pinia:
        specifier: ^2.0.30
        version: 2.1.7(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(typescript@4.9.5)(vue@3.4.21(typescript@4.9.5))
      quagga:
        specifier: ^0.12.1
        version: 0.12.1
      rollup-plugin-esbuild:
        specifier: ^5.0.0
        version: 5.0.0(esbuild@0.20.2)(rollup@4.14.1)
      terser:
        specifier: ^5.28.1
        version: 5.30.3
      tinymce:
        specifier: ^7.2.1
        version: 7.3.0
      upng-js:
        specifier: ^2.1.0
        version: 2.1.0
      vant:
        specifier: ^4.8.5
        version: 4.8.10(vue@3.4.21(typescript@4.9.5))
      vant-table:
        specifier: ^0.0.1
        version: 0.0.1(vant@4.8.10(vue@3.4.21(typescript@4.9.5)))
      vconsole:
        specifier: ^3.15.1
        version: 3.15.1
      vue:
        specifier: ^3.4.21
        version: 3.4.21(typescript@4.9.5)
      vue-doc-preview:
        specifier: ^0.3.2
        version: 0.3.2
      vue-i18n:
        specifier: ^9.2.2
        version: 9.11.1(vue@3.4.21(typescript@4.9.5))
      vue-router:
        specifier: ^4.1.6
        version: 4.3.0(vue@3.4.21(typescript@4.9.5))
      vuedraggable:
        specifier: ^4.1.0
        version: 4.1.0(vue@3.4.21(typescript@4.9.5))
      x2js:
        specifier: ^3.4.4
        version: 3.4.4
    devDependencies:
      '@types/crypto-js':
        specifier: ^4.1.1
        version: 4.2.2
      '@types/lodash':
        specifier: ^4.14.202
        version: 4.17.0
      '@types/node':
        specifier: ^18.11.18
        version: 18.19.31
      '@vitejs/plugin-vue':
        specifier: ^4.0.0
        version: 4.6.2(vite@5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3))(vue@3.4.21(typescript@4.9.5))
      amfe-flexible:
        specifier: ^2.2.1
        version: 2.2.1
      autoprefixer:
        specifier: ^10.4.14
        version: 10.4.19(postcss@8.4.38)
      postcss-pxtorem:
        specifier: ^6.1.0
        version: 6.1.0(postcss@8.4.38)
      sass:
        specifier: ^1.58.0
        version: 1.74.1
      typescript:
        specifier: ^4.9.3
        version: 4.9.5
      unplugin-vue-define-options:
        specifier: ^1.2.1
        version: 1.4.3(rollup@4.14.1)(vue@3.4.21(typescript@4.9.5))
      vite:
        specifier: ^5.1.3
        version: 5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3)
      vue-tsc:
        specifier: ^1.0.11
        version: 1.8.27(typescript@4.9.5)

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.24.2':
    resolution: {integrity: sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.24.4':
    resolution: {integrity: sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.24.4':
    resolution: {integrity: sha512-MBVlMXP+kkl5394RBLSxxk/iLTeVGuXTV3cIDXavPpMMqnSnt6apKgan/U8O3USWZCWZT/TbgfEpKa4uMgN4Dg==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.24.4':
    resolution: {integrity: sha512-Xd6+v6SnjWVx/nus+y0l1sxMOTOMBkyL4+BIdbALyatQnAe/SRVjANeDPSCYaX+i1iJmuGSKf3Z+E+V/va1Hvw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.22.5':
    resolution: {integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-builder-binary-assignment-operator-visitor@7.22.15':
    resolution: {integrity: sha512-QkBXwGgaoC2GtGZRoma6kv7Szfv06khvhFav67ZExau2RaXzy8MpHSMO2PNoP2XtmQphJQRHFfg77Bq731Yizw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.23.6':
    resolution: {integrity: sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.24.4':
    resolution: {integrity: sha512-lG75yeuUSVu0pIcbhiYMXBXANHrpUPaOfu7ryAzskCgKUHuAxRQI5ssrtmF0X9UXldPlvT0XM/A4F44OXRt6iQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.22.15':
    resolution: {integrity: sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.1':
    resolution: {integrity: sha512-o7SDgTJuvx5vLKD6SFvkydkSMBvahDKGiNJzG22IZYXhiqoe9efY7zocICBgzHV4IRg5wdgl2nEL/tulKIEIbA==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-environment-visitor@7.22.20':
    resolution: {integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-function-name@7.23.0':
    resolution: {integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-hoist-variables@7.22.5':
    resolution: {integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.23.0':
    resolution: {integrity: sha512-6gfrPwh7OuT6gZyJZvd6WbTfrqAo7vm4xCzAXOusKqq/vWdKXphTpj5klHKNmRUU6/QRGlBsyU9mAIPaWHlqJA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.24.3':
    resolution: {integrity: sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.23.3':
    resolution: {integrity: sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.22.5':
    resolution: {integrity: sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.24.0':
    resolution: {integrity: sha512-9cUznXMG0+FxRuJfvL82QlTqIzhVW9sL0KjMPHhAOOvpQGL8QtdxnBKILjBqxlHyliz0yCa1G903ZXI/FuHy2w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.22.20':
    resolution: {integrity: sha512-pBGyV4uBqOns+0UvhsTO8qgl8hO89PmiDYv+/COyp1aeMcmfrfruz+/nCMFiYyFF/Knn0yfrC85ZzNFjembFTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.24.1':
    resolution: {integrity: sha512-QCR1UqC9BzG5vZl8BMicmZ28RuUBnHhAMddD8yHFHDRH9lLTZ9uUPehX8ctVPT8l0TKblJidqcgUUKGVrePleQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-simple-access@7.22.5':
    resolution: {integrity: sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    resolution: {integrity: sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-split-export-declaration@7.22.6':
    resolution: {integrity: sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.24.1':
    resolution: {integrity: sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.22.20':
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.23.5':
    resolution: {integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.22.20':
    resolution: {integrity: sha512-pms/UwkOpnQe/PDAEdV/d7dVCoBbB+R4FvYoHGZz+4VPcg7RtYy2KP7S2lbuWM6FCSgob5wshfGESbC/hzNXZw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.24.4':
    resolution: {integrity: sha512-FewdlZbSiwaVGlgT1DPANDuCHaDMiOo+D/IDYRFYjHOuv66xMSJ7fQwwODwRNAPkADIO/z1EoF/l2BCWlWABDw==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.24.2':
    resolution: {integrity: sha512-Yac1ao4flkTxTteCDZLEvdxg2fZfz1v8M4QpaGypq/WPDqg3ijHYbDfs+LG5hvzSoqaSZ9/Z9lKSP3CjZjv+pA==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.24.4':
    resolution: {integrity: sha512-zTvEBcghmeBma9QIGunWevvBAp4/Qu9Bdq+2k0Ot4fVMD6v3dsC9WOcRSKk7tRRyBM/53yKMJko9xOatGQAwSg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.4':
    resolution: {integrity: sha512-qpl6vOOEEzTLLcsuqYYo8yDtrTocmu2xkGvgNebvPjT9DTtfFYGmgDqY+rBYXNlqL4s9qLDn6xkrJv4RxAPiTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.1':
    resolution: {integrity: sha512-y4HqEnkelJIOQGd+3g1bTeKsA5c6qM7eOn7VggGVbBc0y8MLSKHacwcIE2PplNlQSj0PqS9rrXL/nkPVK+kUNg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.1':
    resolution: {integrity: sha512-Hj791Ii4ci8HqnaKHAlLNs+zaLXb0EzSDhiAWp5VNlyvCNymYfacs64pxTxbH1znW/NcArSmwpmG9IKE/TUVVQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.24.1':
    resolution: {integrity: sha512-m9m/fXsXLiHfwdgydIFnpk+7jlVbnvlK5B2EKiPdLUb6WX654ZaaEWJUjk8TftRbZpK0XibovlLWX4KIZhV6jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-dynamic-import@7.8.3':
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-export-namespace-from@7.8.3':
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.24.1':
    resolution: {integrity: sha512-IuwnI5XnuF189t91XbxmXeCDz3qs6iDRO7GJ++wcfgeXNs/8FmIlKcpDSXNVyuLQxlwvskmI3Ct73wUODkJBlQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.24.1':
    resolution: {integrity: sha512-zhQTMH0X2nVLnb04tz+s7AMuasX8U0FnpE+nHTOhSOINjWMnopoZTxtIKsd45n4GQ/HIZLyfIpoul8e2m0DnRA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.24.1':
    resolution: {integrity: sha512-ngT/3NkRhsaep9ck9uj2Xhv9+xB1zShY3tM3g6om4xxCELwCDN4g4Aq5dRn48+0hasAql7s2hdBOysCfNpr4fw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.24.3':
    resolution: {integrity: sha512-Qe26CMYVjpQxJ8zxM1340JFNjZaF+ISWpr1Kt/jGo+ZTUzKkfw/pphEWbRCb+lmSM6k/TOgfYLvmbHkUQ0asIg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.24.1':
    resolution: {integrity: sha512-AawPptitRXp1y0n4ilKcGbRYWfbbzFWz2NqNu7dacYDtFtz0CMjG64b3LQsb3KIgnf4/obcUL78hfaOS7iCUfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.24.1':
    resolution: {integrity: sha512-TWWC18OShZutrv9C6mye1xwtam+uNi2bnTOCBUd5sZxyHOiWbU6ztSROofIMrK84uweEZC219POICK/sTYwfgg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.24.4':
    resolution: {integrity: sha512-nIFUZIpGKDf9O9ttyRXpHFpKC+X3Y5mtshZONuEUYBomAKoM4y029Jr+uB1bHGPhNmK8YXHevDtKDOLmtRrp6g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.24.1':
    resolution: {integrity: sha512-OMLCXi0NqvJfORTaPQBwqLXHhb93wkBKZ4aNwMl6WtehO7ar+cmp+89iPEQPqxAnxsOKTaMcs3POz3rKayJ72g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.24.4':
    resolution: {integrity: sha512-B8q7Pz870Hz/q9UgP8InNpY01CSLDSCyqX7zcRuv3FcPl87A2G17lASroHWaCtbdIcbYzOZ7kWmXFKbijMSmFg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.24.1':
    resolution: {integrity: sha512-ZTIe3W7UejJd3/3R4p7ScyyOoafetUShSf4kCqV0O7F/RiHxVj/wRaRnQlrGwflvcehNA8M42HkAiEDYZu2F1Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.24.1':
    resolution: {integrity: sha512-5pJGVIUfJpOS+pAqBQd+QMaTD2vCL/HcePooON6pDpHgRp4gNRmzyHTPIkXntwKsq3ayUFVfJaIKPw2pOkOcTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.24.1':
    resolution: {integrity: sha512-ow8jciWqNxR3RYbSNVuF4U2Jx130nwnBnhRw6N6h1bOejNkABmcI5X5oz29K4alWX7vf1C+o6gtKXikzRKkVdw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.24.1':
    resolution: {integrity: sha512-p7uUxgSoZwZ2lPNMzUkqCts3xlp8n+o05ikjy7gbtFJSt9gdU88jAmtfmOxHM14noQXBxfgzf2yRWECiNVhTCw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.24.1':
    resolution: {integrity: sha512-msyzuUnvsjsaSaocV6L7ErfNsa5nDWL1XKNnDePLgmz+WdU4w/J8+AxBMrWfi9m4IxfL5sZQKUPQKDQeeAT6lA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dynamic-import@7.24.1':
    resolution: {integrity: sha512-av2gdSTyXcJVdI+8aFZsCAtR29xJt0S5tas+Ef8NvBNmD1a+N/3ecMLeMBgfcK+xzsjdLDT6oHt+DFPyeqUbDA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.24.1':
    resolution: {integrity: sha512-U1yX13dVBSwS23DEAqU+Z/PkwE9/m7QQy8Y9/+Tdb8UWYaGNDYwTLi19wqIAiROr8sXVum9A/rtiH5H0boUcTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.24.1':
    resolution: {integrity: sha512-Ft38m/KFOyzKw2UaJFkWG9QnHPG/Q/2SkOrRk4pNBPg5IPZ+dOxcmkK5IyuBcxiNPyyYowPGUReyBvrvZs7IlQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.24.1':
    resolution: {integrity: sha512-OxBdcnF04bpdQdR3i4giHZNZQn7cm8RQKcSwA17wAAqEELo1ZOwp5FFgeptWUQXFyT9kwHo10aqqauYkRZPCAg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.24.1':
    resolution: {integrity: sha512-BXmDZpPlh7jwicKArQASrj8n22/w6iymRnvHYYd2zO30DbE277JO20/7yXJT3QxDPtiQiOxQBbZH4TpivNXIxA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.24.1':
    resolution: {integrity: sha512-U7RMFmRvoasscrIFy5xA4gIp8iWnWubnKkKuUGJjsuOH7GfbMkB+XZzeslx2kLdEGdOJDamEmCqOks6e8nv8DQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.24.1':
    resolution: {integrity: sha512-zn9pwz8U7nCqOYIiBaOxoQOtYmMODXTJnkxG4AtX8fPmnCRYWBOHD0qcpwS9e2VDSp1zNJYpdnFMIKb8jmwu6g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.24.1':
    resolution: {integrity: sha512-OhN6J4Bpz+hIBqItTeWJujDOfNP+unqv/NJgyhlpSqgBTPm37KkMmZV6SYcOj+pnDbdcl1qRGV/ZiIjX9Iy34w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.24.1':
    resolution: {integrity: sha512-4ojai0KysTWXzHseJKa1XPNXKRbuUrhkOPY4rEGeR+7ChlJVKxFa3H3Bz+7tWaGKgJAXUWKOGmltN+u9B3+CVg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.24.1':
    resolution: {integrity: sha512-lAxNHi4HVtjnHd5Rxg3D5t99Xm6H7b04hUS7EHIXcUl2EV4yl1gWdqZrNzXnSrHveL9qMdbODlLF55mvgjAfaQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.24.1':
    resolution: {integrity: sha512-szog8fFTUxBfw0b98gEWPaEqF42ZUD/T3bkynW/wtgx2p/XCP55WEsb+VosKceRSd6njipdZvNogqdtI4Q0chw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.24.1':
    resolution: {integrity: sha512-mqQ3Zh9vFO1Tpmlt8QPnbwGHzNz3lpNEMxQb1kAemn/erstyqw1r9KeOlOfo3y6xAnFEcOv2tSyrXfmMk+/YZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.24.1':
    resolution: {integrity: sha512-tuA3lpPj+5ITfcCluy6nWonSL7RvaG0AOTeAuvXqEKS34lnLzXpDb0dcP6K8jD0zWZFNDVly90AGFJPnm4fOYg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.22.5':
    resolution: {integrity: sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.24.1':
    resolution: {integrity: sha512-/rurytBM34hYy0HKZQyA0nHbQgQNFm4Q/BOc9Hflxi2X3twRof7NaE5W46j4kQitm7SvACVRXsa6N/tSZxvPug==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.24.1':
    resolution: {integrity: sha512-iQ+caew8wRrhCikO5DrUYx0mrmdhkaELgFa+7baMcVuhxIkN7oxt06CZ51D65ugIb1UWRQ8oQe+HXAVM6qHFjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.24.1':
    resolution: {integrity: sha512-7GAsGlK4cNL2OExJH1DzmDeKnRv/LXq0eLUSvudrehVA5Rgg4bIrqEUW29FbKMBRT0ztSqisv7kjP+XIC4ZMNw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.24.1':
    resolution: {integrity: sha512-XjD5f0YqOtebto4HGISLNfiNMTTs6tbkFf2TOqJlYKYmbo+mN9Dnpl4SRoofiziuOWMIyq3sZEUqLo3hLITFEA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.24.1':
    resolution: {integrity: sha512-oKJqR3TeI5hSLRxudMjFQ9re9fBVUU0GICqM3J1mi8MqlhVr6hC/ZN4ttAyMuQR6EZZIY6h/exe5swqGNNIkWQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.24.1':
    resolution: {integrity: sha512-oBTH7oURV4Y+3EUrf6cWn1OHio3qG/PVwO5J03iSJmBg6m2EhKjkAu/xuaXaYwWW9miYtvbWv4LNf0AmR43LUA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.24.1':
    resolution: {integrity: sha512-n03wmDt+987qXwAgcBlnUUivrZBPZ8z1plL0YvgQalLm+ZE5BMhGm94jhxXtA1wzv1Cu2aaOv1BM9vbVttrzSg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.24.1':
    resolution: {integrity: sha512-8Jl6V24g+Uw5OGPeWNKrKqXPDw2YDjLc53ojwfMcKwlEoETKU9rU0mHUtcg9JntWI/QYzGAXNWEcVHZ+fR+XXg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.24.1':
    resolution: {integrity: sha512-tGvisebwBO5em4PaYNqt4fkw56K2VALsAbAakY0FjTYqJp7gfdrgr7YX76Or8/cpik0W6+tj3rZ0uHU9Oil4tw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.24.1':
    resolution: {integrity: sha512-pTHxDVa0BpUbvAgX3Gat+7cSciXqUcY9j2VZKTbSB6+VQGpNgNO9ailxTGHSXlqOnX1Hcx1Enme2+yv7VqP9bg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.24.1':
    resolution: {integrity: sha512-LetvD7CrHmEx0G442gOomRr66d7q8HzzGGr4PMHGr+5YIm6++Yke+jxj246rpvsbyhJwCLxcTn6zW1P1BSenqA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.24.1':
    resolution: {integrity: sha512-sJwZBCzIBE4t+5Q4IGLaaun5ExVMRY0lYwos/jNecjMrVCygCdph3IKv0tkP5Fc87e/1+bebAmEAGBfnRD+cnw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-reserved-words@7.24.1':
    resolution: {integrity: sha512-JAclqStUfIwKN15HrsQADFgeZt+wexNQ0uLhuqvqAUFoqPMjEcFCYZBhq0LUdz6dZK/mD+rErhW71fbx8RYElg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.24.1':
    resolution: {integrity: sha512-LyjVB1nsJ6gTTUKRjRWx9C1s9hE7dLfP/knKdrfeH9UPtAGjYGgxIbFfx7xyLIEWs7Xe1Gnf8EWiUqfjLhInZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.24.1':
    resolution: {integrity: sha512-KjmcIM+fxgY+KxPVbjelJC6hrH1CgtPmTvdXAfn3/a9CnWGSTY7nH4zm5+cjmWJybdcPSsD0++QssDsjcpe47g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.24.1':
    resolution: {integrity: sha512-9v0f1bRXgPVcPrngOQvLXeGNNVLc8UjMVfebo9ka0WF3/7+aVUHmaJVT3sa0XCzEFioPfPHZiOcYG9qOsH63cw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.24.1':
    resolution: {integrity: sha512-WRkhROsNzriarqECASCNu/nojeXCDTE/F2HmRgOzi7NGvyfYGq1NEjKBK3ckLfRgGc6/lPAqP0vDOSw3YtG34g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.24.1':
    resolution: {integrity: sha512-CBfU4l/A+KruSUoW+vTQthwcAdwuqbpRNB8HQKlZABwHRhsdHZ9fezp4Sn18PeAlYxTNiLMlx4xUBV3AWfg1BA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.24.1':
    resolution: {integrity: sha512-RlkVIcWT4TLI96zM660S877E7beKlQw7Ig+wqkKBiWfj0zH5Q4h50q6er4wzZKRNSYpfo6ILJ+hrJAGSX2qcNw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.24.1':
    resolution: {integrity: sha512-Ss4VvlfYV5huWApFsF8/Sq0oXnGO+jB+rijFEFugTd3cwSObUSnUi88djgR5528Csl0uKlrI331kRqe56Ov2Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.24.1':
    resolution: {integrity: sha512-2A/94wgZgxfTsiLaQ2E36XAOdcZmGAaEEgVmxQWwZXWkGhvoHbaqXcKnU8zny4ycpu3vNqg0L/PcCiYtHtA13g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.24.1':
    resolution: {integrity: sha512-fqj4WuzzS+ukpgerpAoOnMfQXwUHFxXUZUE84oL2Kao2N8uSlvcpnAidKASgsNgzZHBsHWvcm8s9FPWUhAb8fA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.24.4':
    resolution: {integrity: sha512-7Kl6cSmYkak0FK/FXjSEnLJ1N9T/WA2RkMhu17gZ/dsxKJUuTYNIylahPTzqpLyJN4WhDif8X0XK1R8Wsguo/A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/regjsgen@0.8.0':
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==}

  '@babel/runtime@7.24.4':
    resolution: {integrity: sha512-dkxf7+hn8mFBwKjs9bvBlArzLVxVbS8usaPUDd5p2a9JCL9tB8OaOVN1isD4+Xyk4ns89/xeOmbQvgdK7IIVdA==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.24.0':
    resolution: {integrity: sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.24.1':
    resolution: {integrity: sha512-xuU6o9m68KeqZbQuDt2TcKSxUw/mrsvavlEqQ1leZ/B+C9tk6E4sRWy97WaXgvq5E+nU3cXMxv3WKOCanVMCmQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.24.0':
    resolution: {integrity: sha512-+j7a5c253RfKh8iABBhywc8NSfP5LURe7Uh4qpsh6jc+aLJguvmIUBdjSdEMQv2bENrCR5MfRdjGo7vzS/ob7w==}
    engines: {node: '>=6.9.0'}

  '@dcloudio/types@3.4.14':
    resolution: {integrity: sha512-VolQeZfTh8pQFsr2IlfIVX93blfvGTuBoJuZUc7iWOqtHV8gDrq6fXLhzsVlgZyuhhRZLOxlo33rkEqnY+ucAw==}

  '@dcloudio/uni-app@2.0.2-4020420240722004':
    resolution: {integrity: sha512-WzHNoEbOWkMWSW98AzWtexsLfaJfi/ujOHw7e8ffgYZVZYIyob2RGa3mQ61tb3j7/lZyRn48NsT2TXCc/ZaUxg==}
    peerDependencies:
      '@dcloudio/types': ^3.0.15
      '@vue/composition-api': ^1.7.0

  '@esbuild/aix-ppc64@0.20.2':
    resolution: {integrity: sha512-D+EBOJHXdNZcLJRBkhENNG8Wji2kgc9AZ9KiPr1JuZjsNtyHzrsfLRrY0tk2H2aoFu6RANO1y1iPPUCDYWkb5g==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.20.2':
    resolution: {integrity: sha512-mRzjLacRtl/tWU0SvD8lUEwb61yP9cqQo6noDZP/O8VkwafSYwZ4yWy24kan8jE/IMERpYncRt2dw438LP3Xmg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.20.2':
    resolution: {integrity: sha512-t98Ra6pw2VaDhqNWO2Oph2LXbz/EJcnLmKLGBJwEwXX/JAN83Fym1rU8l0JUWK6HkIbWONCSSatf4sf2NBRx/w==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.20.2':
    resolution: {integrity: sha512-btzExgV+/lMGDDa194CcUQm53ncxzeBrWJcncOBxuC6ndBkKxnHdFJn86mCIgTELsooUmwUm9FkhSp5HYu00Rg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.20.2':
    resolution: {integrity: sha512-4J6IRT+10J3aJH3l1yzEg9y3wkTDgDk7TSDFX+wKFiWjqWp/iCfLIYzGyasx9l0SAFPT1HwSCR+0w/h1ES/MjA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.20.2':
    resolution: {integrity: sha512-tBcXp9KNphnNH0dfhv8KYkZhjc+H3XBkF5DKtswJblV7KlT9EI2+jeA8DgBjp908WEuYll6pF+UStUCfEpdysA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.20.2':
    resolution: {integrity: sha512-d3qI41G4SuLiCGCFGUrKsSeTXyWG6yem1KcGZVS+3FYlYhtNoNgYrWcvkOoaqMhwXSMrZRl69ArHsGJ9mYdbbw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.20.2':
    resolution: {integrity: sha512-d+DipyvHRuqEeM5zDivKV1KuXn9WeRX6vqSqIDgwIfPQtwMP4jaDsQsDncjTDDsExT4lR/91OLjRo8bmC1e+Cw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.20.2':
    resolution: {integrity: sha512-9pb6rBjGvTFNira2FLIWqDk/uaf42sSyLE8j1rnUpuzsODBq7FvpwHYZxQ/It/8b+QOS1RYfqgGFNLRI+qlq2A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.20.2':
    resolution: {integrity: sha512-VhLPeR8HTMPccbuWWcEUD1Az68TqaTYyj6nfE4QByZIQEQVWBB8vup8PpR7y1QHL3CpcF6xd5WVBU/+SBEvGTg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.20.2':
    resolution: {integrity: sha512-o10utieEkNPFDZFQm9CoP7Tvb33UutoJqg3qKf1PWVeeJhJw0Q347PxMvBgVVFgouYLGIhFYG0UGdBumROyiig==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.20.2':
    resolution: {integrity: sha512-PR7sp6R/UC4CFVomVINKJ80pMFlfDfMQMYynX7t1tNTeivQ6XdX5r2XovMmha/VjR1YN/HgHWsVcTRIMkymrgQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.20.2':
    resolution: {integrity: sha512-4BlTqeutE/KnOiTG5Y6Sb/Hw6hsBOZapOVF6njAESHInhlQAghVVZL1ZpIctBOoTFbQyGW+LsVYZ8lSSB3wkjA==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.20.2':
    resolution: {integrity: sha512-rD3KsaDprDcfajSKdn25ooz5J5/fWBylaaXkuotBDGnMnDP1Uv5DLAN/45qfnf3JDYyJv/ytGHQaziHUdyzaAg==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.20.2':
    resolution: {integrity: sha512-snwmBKacKmwTMmhLlz/3aH1Q9T8v45bKYGE3j26TsaOVtjIag4wLfWSiZykXzXuE1kbCE+zJRmwp+ZbIHinnVg==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.20.2':
    resolution: {integrity: sha512-wcWISOobRWNm3cezm5HOZcYz1sKoHLd8VL1dl309DiixxVFoFe/o8HnwuIwn6sXre88Nwj+VwZUvJf4AFxkyrQ==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.20.2':
    resolution: {integrity: sha512-1MdwI6OOTsfQfek8sLwgyjOXAu+wKhLEoaOLTjbijk6E2WONYpH9ZU2mNtR+lZ2B4uwr+usqGuVfFT9tMtGvGw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.20.2':
    resolution: {integrity: sha512-K8/DhBxcVQkzYc43yJXDSyjlFeHQJBiowJ0uVL6Tor3jGQfSGHNNJcWxNbOI8v5k82prYqzPuwkzHt3J1T1iZQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.20.2':
    resolution: {integrity: sha512-eMpKlV0SThJmmJgiVyN9jTPJ2VBPquf6Kt/nAoo6DgHAoN57K15ZghiHaMvqjCye/uU4X5u3YSMgVBI1h3vKrQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.20.2':
    resolution: {integrity: sha512-2UyFtRC6cXLyejf/YEld4Hajo7UHILetzE1vsRcGL3earZEW77JxrFjH4Ez2qaTiEfMgAXxfAZCm1fvM/G/o8w==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.20.2':
    resolution: {integrity: sha512-GRibxoawM9ZCnDxnP3usoUDO9vUkpAxIIZ6GQI+IlVmr5kP3zUq+l17xELTHMWTWzjxa2guPNyrpq1GWmPvcGQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.20.2':
    resolution: {integrity: sha512-HfLOfn9YWmkSKRQqovpnITazdtquEW8/SoHW7pWpuEeguaZI4QnCRW6b+oZTztdBnZOS2hqJ6im/D5cPzBTTlQ==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.20.2':
    resolution: {integrity: sha512-N49X4lJX27+l9jbLKSqZ6bKNjzQvHaT8IIFUy+YIqmXQdjYCToGWwOItDrfby14c78aDd5NHQl29xingXfCdLQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@intlify/core-base@9.11.1':
    resolution: {integrity: sha512-qWXBBlEA+DC0CsHkfJiQK9ELm11c9I6lDpodY4FoOf99eMas1R6JR4woPhrfAcrtxFHp1UmXWdrQNKDegSW9IA==}
    engines: {node: '>= 16'}

  '@intlify/message-compiler@9.11.1':
    resolution: {integrity: sha512-y/aWx7DkaTKK2qWUw0hVbJpon8+urWXngeqh15DuIXZh6n/V/oPQiO/Ho1hUKbwap6MVMuz0OcnAJvqh3p9YPg==}
    engines: {node: '>= 16'}

  '@intlify/shared@9.11.1':
    resolution: {integrity: sha512-yuDG82vjgId2oasNRgZ0PKJrF65zlL33MNyITP5itbLcP4AYOR/NcIuD+/DiI+GHXdxASMKJU0ZiITLc6RC+qw==}
    engines: {node: '>= 16'}

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@kwsites/file-exists@1.1.1':
    resolution: {integrity: sha512-m9/5YGR18lIwxSFDwfE3oA7bWuq9kdau6ugN4H2rJeyhFQZcG9AgSHkQtSD15a8WvTgfz9aikZMrKPHvbpqFiw==}

  '@kwsites/promise-deferred@1.1.1':
    resolution: {integrity: sha512-GaHYm+c0O9MjZRu0ongGBRbinu8gVAMd2UZjji6jVmqKtZluZnptXGWhz1E8j8D2HJ3f/yMxKAUC0b+57wncIw==}

  '@mapbox/node-pre-gyp@1.0.11':
    resolution: {integrity: sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==}
    hasBin: true

  '@microsoft/signalr@7.0.14':
    resolution: {integrity: sha512-dnS7gSJF5LxByZwJaj82+F1K755ya7ttPT+JnSeCBef3sL8p8FBkHePXphK8NSuOquIb7vsphXWa28A+L2SPpw==}

  '@rollup/pluginutils@5.1.0':
    resolution: {integrity: sha512-XTIWOPPcpvyKI6L1NHo0lFlCyznUEyPmPY1mc3KpPVDYulHSTvyeLNVW00QTLIAFNhR3kYnJTQHeGqU4M3n09g==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.14.1':
    resolution: {integrity: sha512-fH8/o8nSUek8ceQnT7K4EQbSiV7jgkHq81m9lWZFIXjJ7lJzpWXbQFpT/Zh6OZYnpFykvzC3fbEvEAFZu03dPA==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.14.1':
    resolution: {integrity: sha512-Y/9OHLjzkunF+KGEoJr3heiD5X9OLa8sbT1lm0NYeKyaM3oMhhQFvPB0bNZYJwlq93j8Z6wSxh9+cyKQaxS7PQ==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.14.1':
    resolution: {integrity: sha512-+kecg3FY84WadgcuSVm6llrABOdQAEbNdnpi5X3UwWiFVhZIZvKgGrF7kmLguvxHNQy+UuRV66cLVl3S+Rkt+Q==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.14.1':
    resolution: {integrity: sha512-2pYRzEjVqq2TB/UNv47BV/8vQiXkFGVmPFwJb+1E0IFFZbIX8/jo1olxqqMbo6xCXf8kabANhp5bzCij2tFLUA==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-linux-arm-gnueabihf@4.14.1':
    resolution: {integrity: sha512-mS6wQ6Do6/wmrF9aTFVpIJ3/IDXhg1EZcQFYHZLHqw6AzMBjTHWnCG35HxSqUNphh0EHqSM6wRTT8HsL1C0x5g==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.14.1':
    resolution: {integrity: sha512-p9rGKYkHdFMzhckOTFubfxgyIO1vw//7IIjBBRVzyZebWlzRLeNhqxuSaZ7kCEKVkm/kuC9fVRW9HkC/zNRG2w==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.14.1':
    resolution: {integrity: sha512-nDY6Yz5xS/Y4M2i9JLQd3Rofh5OR8Bn8qe3Mv/qCVpHFlwtZSBYSPaU4mrGazWkXrdQ98GB//H0BirGR/SKFSw==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-powerpc64le-gnu@4.14.1':
    resolution: {integrity: sha512-im7HE4VBL+aDswvcmfx88Mp1soqL9OBsdDBU8NqDEYtkri0qV0THhQsvZtZeNNlLeCUQ16PZyv7cqutjDF35qw==}
    cpu: [ppc64le]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.14.1':
    resolution: {integrity: sha512-RWdiHuAxWmzPJgaHJdpvUUlDz8sdQz4P2uv367T2JocdDa98iRw2UjIJ4QxSyt077mXZT2X6pKfT2iYtVEvOFw==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.14.1':
    resolution: {integrity: sha512-VMgaGQ5zRX6ZqV/fas65/sUGc9cPmsntq2FiGmayW9KMNfWVG/j0BAqImvU4KTeOOgYSf1F+k6at1UfNONuNjA==}
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.14.1':
    resolution: {integrity: sha512-9Q7DGjZN+hTdJomaQ3Iub4m6VPu1r94bmK2z3UeWP3dGUecRC54tmVu9vKHTm1bOt3ASoYtEz6JSRLFzrysKlA==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.14.1':
    resolution: {integrity: sha512-JNEG/Ti55413SsreTguSx0LOVKX902OfXIKVg+TCXO6Gjans/k9O6ww9q3oLGjNDaTLxM+IHFMeXy/0RXL5R/g==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-win32-arm64-msvc@4.14.1':
    resolution: {integrity: sha512-ryS22I9y0mumlLNwDFYZRDFLwWh3aKaC72CWjFcFvxK0U6v/mOkM5Up1bTbCRAhv3kEIwW2ajROegCIQViUCeA==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.14.1':
    resolution: {integrity: sha512-TdloItiGk+T0mTxKx7Hp279xy30LspMso+GzQvV2maYePMAWdmrzqSNZhUpPj3CGw12aGj57I026PgLCTu8CGg==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.14.1':
    resolution: {integrity: sha512-wQGI+LY/Py20zdUPq+XCem7JcPOyzIJBm3dli+56DJsQOHbnXZFEwgmnC6el1TPAfC8lBT3m+z69RmLykNUbew==}
    cpu: [x64]
    os: [win32]

  '@tato30/vue-pdf@1.11.1':
    resolution: {integrity: sha512-r32h9kLIZsuQGPibpM6jmZK6UisJ1J20BqW1RKtptuiMnukArsINU6Hheo7IWDAljkFk8faLfx2vz+LYhwZV6w==}
    peerDependencies:
      vue: ^3.2.33

  '@tinymce/tinymce-vue@5.1.1':
    resolution: {integrity: sha512-iO57HOWesFOhsaqjA5Ea6sDvQBmJJH3/dq00Uvg7metlct2kLF+ctRgoDsetLt6gmeZ7COPftr814/XzqnJ/dg==}
    peerDependencies:
      vue: ^3.0.0

  '@types/crypto-js@4.2.2':
    resolution: {integrity: sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==}

  '@types/estree@1.0.5':
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}

  '@types/lodash@4.17.0':
    resolution: {integrity: sha512-t7dhREVv6dbNj0q17X12j7yDG4bD/DHYX7o5/DbDxobP0HnGPgpRz2Ej77aL7TZT3DSw13fqUTj8J4mMnqa7WA==}

  '@types/node@18.19.31':
    resolution: {integrity: sha512-ArgCD39YpyyrtFKIqMDvjz79jto5fcI/SVUs2HwB+f0dAzq68yqOdyaSivLiLugSziTpNXLQrVb7RZFmdZzbhA==}

  '@vant/popperjs@1.3.0':
    resolution: {integrity: sha512-hB+czUG+aHtjhaEmCJDuXOep0YTZjdlRR+4MSmIFnkCQIxJaXLQdSsR90XWvAI2yvKUI7TCGqR8pQg2RtvkMHw==}

  '@vant/use@1.6.0':
    resolution: {integrity: sha512-PHHxeAASgiOpSmMjceweIrv2AxDZIkWXyaczksMoWvKV2YAYEhoizRuk/xFnKF+emUIi46TsQ+rvlm/t2BBCfA==}
    peerDependencies:
      vue: ^3.0.0

  '@vitejs/plugin-legacy@5.3.2':
    resolution: {integrity: sha512-8moCOrIMaZ/Rjln0Q6GsH6s8fAt1JOI3k8nmfX4tXUxE5KAExVctSyOBk+A25GClsdSWqIk2yaUthH3KJ2X4tg==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      terser: ^5.4.0
      vite: ^5.0.0

  '@vitejs/plugin-vue@4.6.2':
    resolution: {integrity: sha512-kqf7SGFoG+80aZG6Pf+gsZIVvGSCKE98JbiWqcCV9cThtg91Jav0yvYFC9Zb+jKetNGF6ZKeoaxgZfND21fWKw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.0.0 || ^5.0.0
      vue: ^3.2.25

  '@volar/language-core@1.11.1':
    resolution: {integrity: sha512-dOcNn3i9GgZAcJt43wuaEykSluAuOkQgzni1cuxLxTV0nJKanQztp7FxyswdRILaKH+P2XZMPRp2S4MV/pElCw==}

  '@volar/source-map@1.11.1':
    resolution: {integrity: sha512-hJnOnwZ4+WT5iupLRnuzbULZ42L7BWWPMmruzwtLhJfpDVoZLjNBxHDi2sY2bgZXCKlpU5XcsMFoYrsQmPhfZg==}

  '@volar/typescript@1.11.1':
    resolution: {integrity: sha512-iU+t2mas/4lYierSnoFOeRFQUhAEMgsFuQxoxvwn5EdQopw43j+J27a4lt9LMInx1gLJBC6qL14WYGlgymaSMQ==}

  '@vue-macros/common@1.10.2':
    resolution: {integrity: sha512-WC66NPVh2mJWqm4L0l/u/cOqm4pNOIwVdMGnDYAH2rHcOWy5x68GkhpkYTBu1+xwCSeHWOQn1TCGGbD+98fFpA==}
    engines: {node: '>=16.14.0'}
    peerDependencies:
      vue: ^2.7.0 || ^3.2.25
    peerDependenciesMeta:
      vue:
        optional: true

  '@vue-office/docx@1.6.3':
    resolution: {integrity: sha512-Cs+3CAaRBOWOiW4XAhTwwxJ0dy8cPIf6DqfNvYcD3YACiLwO4kuawLF2IAXxyijhbuOeoFsfvoVbOc16A/4bZA==}
    peerDependencies:
      '@vue/composition-api': ^1.7.1
      vue: ^2.0.0 || >=3.0.0
      vue-demi: ^0.14.6
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  '@vue-office/excel@1.7.14':
    resolution: {integrity: sha512-pVUgt+emDQUnW7q22CfnQ+jl43mM/7IFwYzOg7lwOwPEbiVB4K4qEQf+y/bc4xGXz75w1/e3Kz3G6wAafmFBFg==}
    peerDependencies:
      '@vue/composition-api': ^1.7.1
      vue: ^2.0.0 || >=3.0.0
      vue-demi: ^0.14.6
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  '@vue-office/pptx@1.0.1':
    resolution: {integrity: sha512-+V7Kctzl6f6+Yk4NaD/wQGRIkqLWcowe0jEhPexWQb8Oilbzt1OyhWRWcMsxNDTdrgm6aMLP+0/tmw27cxddMg==}
    peerDependencies:
      '@vue/composition-api': ^1.7.1
      vue: ^2.0.0 || >=3.0.0
      vue-demi: ^0.14.6
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  '@vue/compiler-core@3.4.21':
    resolution: {integrity: sha512-MjXawxZf2SbZszLPYxaFCjxfibYrzr3eYbKxwpLR9EQN+oaziSu3qKVbwBERj1IFIB8OLUewxB5m/BFzi613og==}

  '@vue/compiler-dom@3.4.21':
    resolution: {integrity: sha512-IZC6FKowtT1sl0CR5DpXSiEB5ayw75oT2bma1BEhV7RRR1+cfwLrxc2Z8Zq/RGFzJ8w5r9QtCOvTjQgdn0IKmA==}

  '@vue/compiler-sfc@3.4.21':
    resolution: {integrity: sha512-me7epoTxYlY+2CUM7hy9PCDdpMPfIwrOvAXud2Upk10g4YLv9UBW7kL798TvMeDhPthkZ0CONNrK2GoeI1ODiQ==}

  '@vue/compiler-ssr@3.4.21':
    resolution: {integrity: sha512-M5+9nI2lPpAsgXOGQobnIueVqc9sisBFexh5yMIMRAPYLa7+5wEJs8iqOZc1WAa9WQbx9GR2twgznU8LTIiZ4Q==}

  '@vue/composition-api@1.7.2':
    resolution: {integrity: sha512-M8jm9J/laYrYT02665HkZ5l2fWTK4dcVg3BsDHm/pfz+MjDYwX+9FUaZyGwEyXEDonQYRCo0H7aLgdklcIELjw==}
    peerDependencies:
      vue: '>= 2.5 < 2.7'

  '@vue/devtools-api@6.6.1':
    resolution: {integrity: sha512-LgPscpE3Vs0x96PzSSB4IGVSZXZBZHpfxs+ZA1d+VEPwHdOXowy/Y2CsvCAIFrf+ssVU1pD1jidj505EpUnfbA==}

  '@vue/language-core@1.8.27':
    resolution: {integrity: sha512-L8Kc27VdQserNaCUNiSFdDl9LWT24ly8Hpwf1ECy3aFb9m6bDhBGQYOujDm21N7EW3moKIOKEanQwe1q5BK+mA==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@vue/reactivity@3.4.21':
    resolution: {integrity: sha512-UhenImdc0L0/4ahGCyEzc/pZNwVgcglGy9HVzJ1Bq2Mm9qXOpP8RyNTjookw/gOCUlXSEtuZ2fUg5nrHcoqJcw==}

  '@vue/runtime-core@3.4.21':
    resolution: {integrity: sha512-pQthsuYzE1XcGZznTKn73G0s14eCJcjaLvp3/DKeYWoFacD9glJoqlNBxt3W2c5S40t6CCcpPf+jG01N3ULyrA==}

  '@vue/runtime-dom@3.4.21':
    resolution: {integrity: sha512-gvf+C9cFpevsQxbkRBS1NpU8CqxKw0ebqMvLwcGQrNpx6gqRDodqKqA+A2VZZpQ9RpK2f9yfg8VbW/EpdFUOJw==}

  '@vue/server-renderer@3.4.21':
    resolution: {integrity: sha512-aV1gXyKSN6Rz+6kZ6kr5+Ll14YzmIbeuWe7ryJl5muJ4uwSwY/aStXTixx76TwkZFJLm1aAlA/HSWEJ4EyiMkg==}
    peerDependencies:
      vue: 3.4.21

  '@vue/shared@3.4.21':
    resolution: {integrity: sha512-PuJe7vDIi6VYSinuEbUIQgMIRZGgM8e4R+G+/dQTk0X1NEdvgvvgv7m+rfmDH1gZzyA1OjjoWskvHlfRNfQf3g==}

  '@xmldom/xmldom@0.8.10':
    resolution: {integrity: sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==}
    engines: {node: '>=10.0.0'}

  abbrev@1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  acorn@8.11.3:
    resolution: {integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  amfe-flexible@2.2.1:
    resolution: {integrity: sha512-L2VfvDzoETBjhRptg5u/IUuzHSuxm22JpSRb404p/TBGeRfwWmmNEbB+TFPIP/sS/+pbM18bCFH9QnMojLuPNw==}

  ansi-escapes@3.2.0:
    resolution: {integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==}
    engines: {node: '>=4'}

  ansi-regex@3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}

  ansi-regex@4.1.1:
    resolution: {integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==}
    engines: {node: '>=6'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  aproba@2.0.0:
    resolution: {integrity: sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==}

  are-we-there-yet@2.0.0:
    resolution: {integrity: sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==}
    engines: {node: '>=10'}
    deprecated: This package is no longer supported.

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  asn1@0.2.6:
    resolution: {integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==}

  assert-plus@1.0.0:
    resolution: {integrity: sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==}
    engines: {node: '>=0.8'}

  ast-kit@0.12.1:
    resolution: {integrity: sha512-O+33g7x6irsESUcd47KdfWUrS2F6aGp9KeVJFGj0YjIznfXpBxVGjA0w+y/1OKqX4mFOfmZ9Xpf1ixPT4n9xxw==}
    engines: {node: '>=16.14.0'}

  ast-walker-scope@0.6.1:
    resolution: {integrity: sha512-0ZdQEsSfH3mX4BFbRCc3xOBjx5bDbm73+aAdQOHerPQNf8K0XFMAv79ucd2BpnSc4UMyvBDixiroT8yjm2Y6bw==}
    engines: {node: '>=16.14.0'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  autoprefixer@10.4.19:
    resolution: {integrity: sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  aws-sign2@0.7.0:
    resolution: {integrity: sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==}

  aws4@1.12.0:
    resolution: {integrity: sha512-NmWvPnx0F1SfrQbYwOi7OeaNGokp9XhzNioJ/CSBs8Qa4vxug81mhJEAVZwxXuBmYB5KDRfMq/F3RR0BIU7sWg==}

  axios@0.18.1:
    resolution: {integrity: sha512-0BfJq4NSfQXd+SkFdrvFbG7addhYSBA2mQwISr46pD6E5iqkWg02RAs8vyTT/j0RTnoYmeXauBuSv1qKwR179g==}
    deprecated: Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410

  axios@1.6.8:
    resolution: {integrity: sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ==}

  babel-plugin-polyfill-corejs2@0.4.10:
    resolution: {integrity: sha512-rpIuu//y5OX6jVU+a5BCn1R5RSZYWAl2Nar76iwaOdycqb6JPxediskWFMMl7stfwNJR4b7eiQvh5fB5TEQJTQ==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.10.4:
    resolution: {integrity: sha512-25J6I8NGfa5YkCDogHRID3fVCadIR8/pGl1/spvCkzb6lVn6SR3ojpx9nOn9iEBcUsjY24AmdKm5khcfKdylcg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.1:
    resolution: {integrity: sha512-JfTApdE++cgcTWjsiCQlLyFBMbTUft9ja17saCc93lgV33h4tuCVj7tlvu//qpLwaG+3yEz7/KhahGrUMkVq9g==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  blueimp-canvas-to-blob@3.29.0:
    resolution: {integrity: sha512-0pcSSGxC0QxT+yVkivxIqW0Y4VlO2XSDPofBAqoJ1qJxgH9eiUDLv50Rixij2cDuEfx4M6DpD9UGZpRhT5Q8qg==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}

  browserslist-to-esbuild@2.1.1:
    resolution: {integrity: sha512-KN+mty6C3e9AN8Z5dI1xeN15ExcRNeISoC3g7V0Kax/MMF9MSoYA2G7lkTTcVUFntiEjkpI0HNgqJC1NjdyNUw==}
    engines: {node: '>=18'}
    hasBin: true
    peerDependencies:
      browserslist: '*'

  browserslist@4.23.0:
    resolution: {integrity: sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001608:
    resolution: {integrity: sha512-cjUJTQkk9fQlJR2s4HMuPMvTiRggl0rAVMtthQuyOlDWuqHXqN8azLq+pi8B2TjwKJ32diHjUqRIKeFX4z1FoA==}

  canvas@2.11.2:
    resolution: {integrity: sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==}
    engines: {node: '>=6'}

  caseless@0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chardet@0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}

  cli-cursor@2.1.0:
    resolution: {integrity: sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==}
    engines: {node: '>=4'}

  cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}

  cli-table@0.3.11:
    resolution: {integrity: sha512-IqLQi4lO0nIB4tcdTpN4LCB9FI3uqrJZK7RC515EnhZ6qBaglkIgICb1wjeAqpdoOabm1+SuQtkXIPdYC93jhQ==}
    engines: {node: '>= 0.2.0'}

  cli-width@2.2.1:
    resolution: {integrity: sha512-GRMWDxpOB6Dgk2E5Uo+3eEBvtOOlimMmpbFiKuLFnQzYDavtLFY3K5ona41jgN/WdRZtG7utuVSVTL4HbZHGkw==}

  cliui@5.0.0:
    resolution: {integrity: sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==}

  clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==}
    engines: {node: '>=0.8'}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-support@1.1.3:
    resolution: {integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==}
    hasBin: true

  colors@1.0.3:
    resolution: {integrity: sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=, tarball: https://registry.npmmirror.com/colors/download/colors-1.0.3.tgz}
    engines: {node: '>=0.1.90'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  complex.js@2.1.1:
    resolution: {integrity: sha512-8njCHOTtFFLtegk6zQo0kkVX1rngygb/KQI6z1qZxlFI3scluC+LVTCFbrkWjBv4vvLlbQ9t88IPMC6k95VTTg==}

  compressorjs@1.2.1:
    resolution: {integrity: sha512-+geIjeRnPhQ+LLvvA7wxBQE5ddeLU7pJ3FsKFWirDw6veY3s9iLxAQEw7lXGHnhCJvBujEQWuNnGzZcvCvdkLQ==}

  computeds@0.0.1:
    resolution: {integrity: sha512-7CEBgcMjVmitjYo5q8JTJVra6X5mQ20uTThdK+0kR7UEaDrAWEQcRiBtWJzga4eRpP6afNwwLsX2SET2JhVB1Q==}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz}

  console-control-strings@1.1.0:
    resolution: {integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  copy-text-to-clipboard@3.2.0:
    resolution: {integrity: sha512-RnJFp1XR/LOBDckxTib5Qjr/PMfkatD0MUCQgdpqS8MdKiNUzBjAQBEN6oUy+jW7LI93BBG3DtMB2KOOKpGs2Q==}
    engines: {node: '>=12'}

  core-js-compat@3.36.1:
    resolution: {integrity: sha512-Dk997v9ZCt3X/npqzyGdTlq6t7lDBhZwGvV94PKzDArjp7BTRm7WlDAXYd/OWdeFHO8OChQYRJNJvUCqCbrtKA==}

  core-js@3.36.1:
    resolution: {integrity: sha512-BTvUrwxVBezj5SZ3f10ImnX2oRByMxql3EimVqMysepbC9EeMUOpLwdy6Eoili2x6E4kf+ZUB5k/+Jv55alPfA==}

  core-util-is@1.0.2:
    resolution: {integrity: sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==}

  crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  cwise-compiler@1.1.3:
    resolution: {integrity: sha512-WXlK/m+Di8DMMcCjcWr4i+XzcQra9eCdXIJrgh4TUgh0pIS/yJduLxS9JgefsHJ/YVLdgPtXm9r62W92MvanEQ==}

  dashdash@1.14.1:
    resolution: {integrity: sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==}
    engines: {node: '>=0.10'}

  data-uri-to-buffer@0.0.3:
    resolution: {integrity: sha512-Cp+jOa8QJef5nXS5hU7M1DWzXPEIoVR3kbV0dQuVGwROZg8bGf1DcCnkmajBTnvghTtSNMUdRrPjgaT6ZQucbw==}

  dayjs@1.11.10:
    resolution: {integrity: sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==}

  de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  debug@3.1.0:
    resolution: {integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  decimal.js@10.4.3:
    resolution: {integrity: sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==}

  decompress-response@4.2.1:
    resolution: {integrity: sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==}
    engines: {node: '>=8'}

  defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  delegates@1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}

  detect-libc@2.0.3:
    resolution: {integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==}
    engines: {node: '>=8'}

  dingtalk-jsapi@3.0.32:
    resolution: {integrity: sha512-TvB6xBGYjtIQahAK69VKlh0vrVU1uhedkMjPjQdOMabw5hrnCXpOZkGe2LqAkjZO/gh1LhmYE3WBxbwFJetDzg==}

  ecc-jsbn@0.1.2:
    resolution: {integrity: sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==}

  electron-to-chromium@1.4.730:
    resolution: {integrity: sha512-oJRPo82XEqtQAobHpJIR3zW5YO3sSRRkPz2an4yxi1UvqhsGm54vR/wzTFV74a3soDOJ8CKW7ajOOX5ESzddwg==}

  emoji-regex@7.0.3:
    resolution: {integrity: sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  es-module-lexer@1.5.0:
    resolution: {integrity: sha512-pqrTKmwEIgafsYZAGw9kszYzmagcE/n4dbgwGWLEXg7J4QFJVQRBld8j3Q3GNez79jzxZshq0bcT962QHOghjw==}

  esbuild@0.20.2:
    resolution: {integrity: sha512-WdOOppmUNU+IbZ0PaDiTst80zjnrOkyJNHoKupIcVyU8Lvla3Ugx94VzkQ32Ijqd7UhHJy75gNWDMUekcrSJ6g==}
    engines: {node: '>=12'}
    hasBin: true

  escalade@3.1.2:
    resolution: {integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==}
    engines: {node: '>=6'}

  escape-latex@1.2.0:
    resolution: {integrity: sha512-nV5aVWW1K0wEiUIEdZ4erkGGH8mDxGyxSeqPzRNtWP7ataw+/olFObw7hujFWlVjNsaDFw5VZ5NzVSIqRgfTiw==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  eventsource@2.0.2:
    resolution: {integrity: sha512-IzUmBGPR3+oUG9dUeXynyNmf91/3zUSJg1lCktzKw47OXuhco54U3r9B7O4XX+Rb1Itm9OZ2b0RkTs10bICOxA==}
    engines: {node: '>=12.0.0'}

  extend-shallow@2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  external-editor@3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}

  extsprintf@1.3.0:
    resolution: {integrity: sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==}
    engines: {'0': node >=0.6.0}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fetch-cookie@2.2.0:
    resolution: {integrity: sha512-h9AgfjURuCgA2+2ISl8GbavpUdR+WGAM2McW/ovn4tVccegp8ZqCKWSBR8uRdM8dDNlx5WdKRWxBYUwteLDCNQ==}

  figures@2.0.0:
    resolution: {integrity: sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==}
    engines: {node: '>=4'}

  fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}

  find-up@3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}

  follow-redirects@1.15.6:
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  follow-redirects@1.5.10:
    resolution: {integrity: sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==}
    engines: {node: '>=4.0'}

  forever-agent@0.6.1:
    resolution: {integrity: sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==}

  form-data@2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  fraction.js@4.3.4:
    resolution: {integrity: sha512-pwiTgt0Q7t+GHZA4yaLjObx4vXmmdcS0iSJ19o8d/goUGgItX9UZWKWNnLHehxviD8wU2IWRsnR8cD5+yOJP2Q==}

  fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}

  fs-exists-sync@0.1.0:
    resolution: {integrity: sha512-cR/vflFyPZtrN6b38ZyWxpWdhlXrzZEBawlpBQMq7033xVY7/kg0GDMBK5jg8lDYQckdJ5x/YC88lM3C7VMsLg==}
    engines: {node: '>=0.10.0'}

  fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  gauge@3.0.2:
    resolution: {integrity: sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==}
    engines: {node: '>=10'}
    deprecated: This package is no longer supported.

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-pixels@3.3.3:
    resolution: {integrity: sha512-5kyGBn90i9tSMUVHTqkgCHsoWoR+/lGbl4yC83Gefyr0HLIhgSWEx/2F/3YgsZ7UpYNuM6pDhDK7zebrUJ5nXg==}

  getpass@0.1.7:
    resolution: {integrity: sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==}

  gl-mat2@1.0.1:
    resolution: {integrity: sha512-oHgZ3DalAo9qAhMZM9QigXosqotcUCsgxarwrinipaqfSHvacI79Dzs72gY+oT4Td1kDQKEsG0RyX6mb02VVHA==}

  gl-vec2@1.3.0:
    resolution: {integrity: sha512-YiqaAuNsheWmUV0Sa8k94kBB0D6RWjwZztyO+trEYS8KzJ6OQB/4686gdrf59wld4hHFIvaxynO3nRxpk1Ij/A==}

  gl-vec3@1.1.3:
    resolution: {integrity: sha512-jduKUqT0SGH02l8Yl+mV1yVsDfYgQAJyXGxkJQGyxPLHRiW25DwVIRPt6uvhrEMHftJfqhqKthRcyZqNEl9Xdw==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  har-schema@2.0.0:
    resolution: {integrity: sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==}
    engines: {node: '>=4'}

  har-validator@5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-unicode@2.0.1:
    resolution: {integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  heic2any@0.0.4:
    resolution: {integrity: sha512-3lLnZiDELfabVH87htnRolZ2iehX9zwpRyGNz22GKXIu0fznlblf0/ftppXKNqS26dqFSeqfIBhAmAj/uSp0cA==}

  highlight.js@9.18.5:
    resolution: {integrity: sha512-a5bFyofd/BHCX52/8i8uJkjr9DYwXIPnM/plwI6W7ezItLGqzt7X2G2nXuYSfsIJdkwwj/g9DG1LkcGJI/dDoA==}
    deprecated: Support has ended for 9.x series. Upgrade to @latest

  html5-qrcode@2.3.8:
    resolution: {integrity: sha512-jsr4vafJhwoLVEDW3n1KvPnCCXWaQfRng0/EEYk1vNcQGcG/htAdhJX0be8YyqMoSz7+hZvOZSTAepsabiuhiQ==}

  http-signature@1.2.0:
    resolution: {integrity: sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==}
    engines: {node: '>=0.8', npm: '>=1.3.7'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  immutable@4.3.5:
    resolution: {integrity: sha512-8eabxkth9gZatlwl5TBuJnCsoTADlL6ftEr7A4qgdaTsPyreilDSnUk57SO+jfKcNtxPa22U5KK6DSeAYhpBJw==}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  inquirer@6.5.2:
    resolution: {integrity: sha512-cntlB5ghuB0iuO65Ovoi8ogLHiWGs/5yNrtUcKjFhSSiVeAIVpD7koaSU9RM8mpXw5YDi9RdYXGQMaOURB7ycQ==}
    engines: {node: '>=6.0.0'}

  iota-array@1.0.0:
    resolution: {integrity: sha512-pZ2xT+LOHckCatGQ3DcG/a+QuEqvoxqkiL7tvE8nn3uuu+f6i1TtpB5/FtWFbxUuVr5PZCx8KskuGatbJDXOWA==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-blob@2.1.0:
    resolution: {integrity: sha512-SZ/fTft5eUhQM6oF/ZaASFDEdbFVe89Imltn9uZr03wdKMcWNVYSMjQPFtg05QuNkt5l5c135ElvXEQG0rk4tw==}
    engines: {node: '>=6'}

  is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}

  is-buffer@2.0.5:
    resolution: {integrity: sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==}
    engines: {node: '>=4'}

  is-core-module@2.13.1:
    resolution: {integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==}

  is-extendable@0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@2.0.0:
    resolution: {integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==}
    engines: {node: '>=4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  isstream@0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}

  javascript-natural-sort@0.7.1:
    resolution: {integrity: sha512-nO6jcEfZWQXDhOiBtG2KvKyEptz7RVbpGP4vTD2hLBdmNQSsCiicO2Ioinv6UI4y9ukqnBpy+XZ9H6uLNgJTlw==}

  joycon@3.1.1:
    resolution: {integrity: sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==}
    engines: {node: '>=10'}

  jpeg-js@0.4.4:
    resolution: {integrity: sha512-WZzeDOEtTOBK4Mdsar0IqEU5sMr3vSV2RqkAIzUEV2BHnUfKGyswWFPFwK5EeDo93K3FohSHbLAjj0s1Wzd+dg==}

  js-cookie@3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsbn@0.1.1:
    resolution: {integrity: sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==}

  jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema@0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonc-parser@3.2.1:
    resolution: {integrity: sha512-AilxAyFOAcK5wA1+LeaySVBrHsGQvUFCDWXKpZjzaL0PqW+xfBOttn8GNtWKFWqneyMZj41MWF9Kl6iPWLwgOA==}

  jsprim@1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}

  lint@0.8.19:
    resolution: {integrity: sha512-i9iqBX/OO2+zSE7hEDXJ0rdLMxvBluK2T/xbCKAhEgyHE1q6kjp1HJGOVagkVB0f0UZ+FnW/wM3smsihQN0tFw==}
    hasBin: true

  loadash@1.0.0:
    resolution: {integrity: sha512-xlX5HBsXB3KG0FJbJJG/3kYWCfsCyCSus3T+uHVu6QL6YxAdggmm3QeyLgn54N2yi5/UE6xxL5ZWJAAiHzHYEg==}
    deprecated: Package is unsupport. Please use the lodash package instead.

  local-pkg@0.5.0:
    resolution: {integrity: sha512-ok6z3qlYyCDS4ZEU27HaU6x/xZa9Whf8jD4ptH5UZTQYZVYeb9bnZ3ojVhiJNLiXK1Hfc0GNbLXcmZ5plLDDBg==}
    engines: {node: '>=14'}

  locate-path@3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  log-symbols@2.2.0:
    resolution: {integrity: sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==}
    engines: {node: '>=4'}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  magic-string-ast@0.3.0:
    resolution: {integrity: sha512-0shqecEPgdFpnI3AP90epXyxZy9g6CRZ+SZ7BcqFwYmtFEnZ1jpevcV5HoyVnlDS9gCnc1UIg3Rsvp3Ci7r8OA==}
    engines: {node: '>=16.14.0'}

  magic-string@0.30.9:
    resolution: {integrity: sha512-S1+hd+dIrC8EZqKyT9DstTH/0Z+f76kmmvZnkfQVmOpDEF9iVgdYif3Q/pIWHmCoo59bQVGW0kVL3e2nl+9+Sw==}
    engines: {node: '>=12'}

  make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}

  marked@0.4.0:
    resolution: {integrity: sha512-tMsdNBgOsrUophCAFQl0XPe6Zqk/uy9gnue+jIIKhykO51hxyu6uNx7zBPy0+y/WKYVZZMspV9YeXLNdKk+iYw==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  mathjs@11.12.0:
    resolution: {integrity: sha512-UGhVw8rS1AyedyI55DGz9q1qZ0p98kyKPyc9vherBkoueLntPfKtPBh14x+V4cdUWK0NZV2TBwqRFlvadscSuw==}
    engines: {node: '>= 14'}
    hasBin: true

  meow@13.2.0:
    resolution: {integrity: sha512-pxQJQzB6djGPXh08dacEloMFopsOqGVRKFPYvPOt9XDZ1HasbgDZA74CJGreSU4G3Ak7EFJGoiH2auq+yXISgA==}
    engines: {node: '>=18'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mimic-fn@1.2.0:
    resolution: {integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==}
    engines: {node: '>=4'}

  mimic-response@2.1.0:
    resolution: {integrity: sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==}
    engines: {node: '>=8'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.4:
    resolution: {integrity: sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}

  minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}

  minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}

  mitt@3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mlly@1.6.1:
    resolution: {integrity: sha512-vLgaHvaeunuOXHSmEbZ9izxPx3USsk8KCQ8iC+aTlp5sKRSoZvwhHh5L9VbKSaVC6sJDqbyohIS76E2VmHIPAA==}

  moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  muggle-string@0.3.1:
    resolution: {integrity: sha512-ckmWDJjphvd/FvZawgygcUeQCxzvohjFO5RxTjj4eq8kw359gFF3E1brjfI+viLMxss5JrHTDRHZvu2/tuy0Qg==}

  mutation-observer@1.0.3:
    resolution: {integrity: sha512-M/O/4rF2h776hV7qGMZUH3utZLO/jK7p8rnNgGkjKUw8zCGjRQPxB8z6+5l8+VjRUQ3dNYu4vjqXYLr+U8ZVNA==}

  mute-stream@0.0.7:
    resolution: {integrity: sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=, tarball: https://registry.npmmirror.com/mute-stream/download/mute-stream-0.0.7.tgz}

  nan@2.21.0:
    resolution: {integrity: sha512-MCpOGmdWvAOMi4RWnpxS5G24l7dVMtdSHtV87I3ltjaLdFOTO74HVJ+DfYiAXjxGKsYR/UCmm1rBwhMN7KqS1A==}

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  ndarray-linear-interpolate@1.0.0:
    resolution: {integrity: sha512-UN0f4+6XWsQzJ2pP5gVp+kKn5tJed6mA3K/L50uO619+7LKrjcSNdcerhpqxYaSkbxNJuEN76N05yBBJySnZDw==}

  ndarray-pack@1.2.1:
    resolution: {integrity: sha512-51cECUJMT0rUZNQa09EoKsnFeDL4x2dHRT0VR5U2H5ZgEcm95ZDWcMA5JShroXjHOejmAD/fg8+H+OvUnVXz2g==}

  ndarray@1.0.19:
    resolution: {integrity: sha512-B4JHA4vdyZU30ELBw3g7/p9bZupyew5a7tX1Y/gGeF2hafrPaQZhgrGQfsvgfYbgdFZjYwuEcnaobeM/WMW+HQ==}

  node-bitmap@0.0.1:
    resolution: {integrity: sha512-Jx5lPaaLdIaOsj2mVLWMWulXF6GQVdyLvNSxmiYCvZ8Ma2hfKX0POoR2kgKOqz+oFsRreq0yYZjQ2wjE9VNzCA==}
    engines: {node: '>=v0.6.5'}

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-releases@2.0.14:
    resolution: {integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==}

  nopt@5.0.0:
    resolution: {integrity: sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==}
    engines: {node: '>=6'}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  npmlog@5.0.1:
    resolution: {integrity: sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==}
    deprecated: This package is no longer supported.

  oauth-sign@0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  omggif@1.0.10:
    resolution: {integrity: sha512-LMJTtvgc/nugXj0Vcrrs68Mn2D1r0zf630VNtqtpI1FEO7e+O9FP4gqs9AcnBaSEeoHIPm28u6qgPR0oyEpGSw==}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@2.0.1:
    resolution: {integrity: sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==}
    engines: {node: '>=4'}

  ora@3.4.0:
    resolution: {integrity: sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg==}
    engines: {node: '>=6'}

  os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==}
    engines: {node: '>=0.10.0'}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-locate@3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  parse-data-uri@0.2.0:
    resolution: {integrity: sha512-uOtts8NqDcaCt1rIsO3VFDRsAfgE4c6osG4d9z3l4dCBlxYFzni6Di/oNU270SDrjkfZuUvLZx1rxMyqh46Y9w==}

  path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==}

  path-exists@3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==}
    engines: {node: '>=4'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path2d@0.2.1:
    resolution: {integrity: sha512-Fl2z/BHvkTNvkuBzYTpTuirHZg6wW9z8+4SND/3mDTEcYbbNKWAy21dz9D3ePNNwrrK8pqZO5vLPZ1hLF6T7XA==}
    engines: {node: '>=6'}

  pathe@1.1.2:
    resolution: {integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==}

  pdfh5@1.4.9:
    resolution: {integrity: sha512-BCKDjvfMY7+aLjv5/imP+Z6WCX26HH3EcBOKtfvPEpvzZaYepsypFoZEI7a0y+GngCzc4rPaupFsPCSVMonS5w==}

  pdfjs-dist@4.5.136:
    resolution: {integrity: sha512-V1BALcAN/FmxBEShLxoP73PlQZAZtzlaNfRbRhJrKvXzjLC5VaIlBAQUJuWP8iaYUmIdmdLHmt3E2TBglxOm3w==}
    engines: {node: '>=18'}

  performance-now@2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}

  picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pinia@2.1.7:
    resolution: {integrity: sha512-+C2AHFtcFqjPih0zpYuvof37SFxMQ7OEG2zV9jRI12i9BOy3YQVAHwdKtyyc8pDcDyIc33WCIsZaCFWU7WWxGQ==}
    peerDependencies:
      '@vue/composition-api': ^1.4.0
      typescript: '>=4.4.4'
      vue: ^2.6.14 || ^3.3.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      typescript:
        optional: true

  pkg-types@1.0.3:
    resolution: {integrity: sha512-nN7pYi0AQqJnoLPC9eHFQ8AcyaixBUOwvqc5TDnIKCMEE6I0y8P7OKA7fPexsXGCGxQDl/cmrLAp26LhcwxZ4A==}

  pngjs@3.4.0:
    resolution: {integrity: sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w==}
    engines: {node: '>=4.0.0'}

  postcss-pxtorem@6.1.0:
    resolution: {integrity: sha512-ROODSNci9ADal3zUcPHOF/K83TiCgNSPXQFSbwyPHNV8ioHIE4SaC+FPOufd8jsr5jV2uIz29v1Uqy1c4ov42g==}
    peerDependencies:
      postcss: ^8.0.0

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.38:
    resolution: {integrity: sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==}
    engines: {node: ^10 || ^12 || >=14}

  prettier@1.19.1:
    resolution: {integrity: sha512-s7PoyDv/II1ObgQunCbB9PdLmUcBZcnWOcxDh7O0N/UwDEsHyqkW+Qh28jW+mVuCdx7gLB0BotYI1Y6uI9iyew==}
    engines: {node: '>=4'}
    hasBin: true

  promise-polyfill@7.1.2:
    resolution: {integrity: sha512-FuEc12/eKqqoRYIGBrUptCBRhobL19PS2U31vMNTfyck1FxPyMfgsXyW4Mav85y/ZN1hop3hOwRlUDok23oYfQ==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qs@6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}

  quagga@0.12.1:
    resolution: {integrity: sha512-bb2N6eT7ss6Bg27sxQgv/CT96KQBkXa+4YeS1W8bhsaXxoWp8zOQbrOwFWEPxPDTNaWEl7hTs3ZB7OC4k3EY3Q==}
    engines: {node: '>= 4.0'}

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  regenerate-unicode-properties@10.1.1:
    resolution: {integrity: sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q==}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==}

  regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==}
    engines: {node: '>=4'}

  regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true

  replace-in-file@3.4.4:
    resolution: {integrity: sha512-ehq0dFsxSpfPiPLBU5kli38Ud8bZL0CQKG8WQVbvhmyilXaMJ8y4LtDZs/K3MD8C0+rHbsfW8c9r2bUEy0B/6Q==}
    hasBin: true

  request@2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-main-filename@2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true

  restore-cursor@2.0.0:
    resolution: {integrity: sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==}
    engines: {node: '>=4'}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rollup-plugin-esbuild@5.0.0:
    resolution: {integrity: sha512-1cRIOHAPh8WQgdQQyyvFdeOdxuiyk+zB5zJ5+YOwrZP4cJ0MT3Fs48pQxrZeyZHcn+klFherytILVfE4aYrneg==}
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    peerDependencies:
      esbuild: '>=0.10.1'
      rollup: ^1.20.0 || ^2.0.0 || ^3.0.0

  rollup@4.14.1:
    resolution: {integrity: sha512-4LnHSdd3QK2pa1J6dFbfm1HN0D7vSK/ZuZTsdyUAlA6Rr1yTouUTL13HaDOGJVgby461AhrNGBS7sCGXXtT+SA==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  run-async@2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}

  rxjs@6.6.7:
    resolution: {integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==}
    engines: {npm: '>=2.0.0'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sass@1.74.1:
    resolution: {integrity: sha512-w0Z9p/rWZWelb88ISOLyvqTWGmtmu2QJICqDBGyNnfG4OUnPX9BBjjYIXUpXCMOOg5MQWNpqzt876la1fsTvUA==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  seedrandom@3.0.5:
    resolution: {integrity: sha512-8OwmbklUNzwezjGInmZ+2clQmExQPvomqjL7LFqOYqtmuxRgQYqOD3mHaU+MvZn5FLUeVxVfQjwLZW/n/JFuqg==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.6.0:
    resolution: {integrity: sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==}
    engines: {node: '>=10'}
    hasBin: true

  set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  set-cookie-parser@2.6.0:
    resolution: {integrity: sha512-RVnVQxTXuerk653XfuliOxBP81Sf0+qfQE73LIYKcyMYHG94AuH0kgrQpRDuTZnSmjpysHmzxJXKNfa6PjFhyQ==}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  simple-concat@1.0.1:
    resolution: {integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==}

  simple-get@3.1.1:
    resolution: {integrity: sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==}

  simple-git@3.24.0:
    resolution: {integrity: sha512-QqAKee9Twv+3k8IFOFfPB2hnk6as6Y6ACUpwCtQvRYBAes23Wv3SZlHVobAzqcE8gfsisCvPw3HGW3HYM+VYYw==}

  sortablejs@1.14.0:
    resolution: {integrity: sha512-pBXvQCs5/33fdN1/39pPL0NZF20LeRbLQ5jtnheIPN9JQAaufGjKdWduZn4U7wCtVuzKhmRkI0DFYHYRbB2H1w==}

  source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  sshpk@1.18.0:
    resolution: {integrity: sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  string-width@2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}

  string-width@3.1.0:
    resolution: {integrity: sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==}
    engines: {node: '>=6'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@4.0.0:
    resolution: {integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==}
    engines: {node: '>=4'}

  strip-ansi@5.2.0:
    resolution: {integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==}
    engines: {node: '>=6'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  systemjs@6.14.3:
    resolution: {integrity: sha512-hQv45irdhXudAOr8r6SVSpJSGtogdGZUbJBRKCE5nsIS7tsxxvnIHqT4IOPWj+P+HcSzeWzHlGCGpmhPDIKe+w==}

  tar@6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}

  terser@5.30.3:
    resolution: {integrity: sha512-STdUgOUx8rLbMGO9IOwHLpCqolkDITFFQSMYYwKE1N2lY6MVSaeoi10z/EhWxRc6ybqoVmKSkhKYH/XUpl7vSA==}
    engines: {node: '>=10'}
    hasBin: true

  through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  tiny-emitter@2.1.0:
    resolution: {integrity: sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==}

  tinymce@6.8.4:
    resolution: {integrity: sha512-okoJyxuPv1gzASxQDNgQbnUXOdAIyoOSXcXcZZu7tiW0PSKEdf3SdASxPBupRj+64/E3elHwVRnzSdo82Emqbg==}

  tinymce@7.3.0:
    resolution: {integrity: sha512-Ls4PgYlpk73XAxBSBqbVmSl8Mb3DuNfgF01GZ0lY6/MOEVRl3IL+VxC1Oe6165e8WqbqVsxO3Qj/PmoYNvQKGQ==}

  tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  tough-cookie@2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}

  tough-cookie@4.1.3:
    resolution: {integrity: sha512-aX/y5pVRkfRnfmuX+OdbSdXvPe6ieKX/G2s7e98f4poJHnqH3281gDPm/metm6E/WRamfx7WC4HUqkWHfQHprw==}
    engines: {node: '>=6'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}

  tunnel-agent@0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==}

  tweetnacl@0.14.5:
    resolution: {integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==}

  typed-function@4.1.1:
    resolution: {integrity: sha512-Pq1DVubcvibmm8bYcMowjVnnMwPVMeh0DIdA8ad8NZY2sJgapANJmiigSUwlt+EgXxpfIv8MWrQXTIzkfYZLYQ==}
    engines: {node: '>= 14'}

  typescript@4.9.5:
    resolution: {integrity: sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==}
    engines: {node: '>=4.2.0'}
    hasBin: true

  ufo@1.5.3:
    resolution: {integrity: sha512-Y7HYmWaFwPUmkoQCUIAYpKqkOf+SbVj/2fJJZ4RJMCfZp0rTGwRbzQD+HghfnhKOjL9E01okqz+ncJskGYfBNw==}

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  unicode-canonical-property-names-ecmascript@2.0.0:
    resolution: {integrity: sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.1.0:
    resolution: {integrity: sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  uniq@1.0.1:
    resolution: {integrity: sha512-Gw+zz50YNKPDKXs+9d+aKAjVwpjNwqzvNpLigIruT4HA9lMZNdMqs9x07kKHB/L9WRzqp4+DlTU5s4wG2esdoA==}

  universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}

  unplugin-vue-define-options@1.4.3:
    resolution: {integrity: sha512-CN5xF8l8bySD6okw6PCt1zWFHf5vX+q4Cg2ssn9usANHtxmeyCgMccC7ywZyzI32dQS+pq6hvuSz/GewtYnbEQ==}
    engines: {node: '>=16.14.0'}

  unplugin@1.10.1:
    resolution: {integrity: sha512-d6Mhq8RJeGA8UfKCu54Um4lFA0eSaRa3XxdAJg8tIdxbu1ubW0hBCZUL7yI2uGyYCRndvbK8FLHzqy2XKfeMsg==}
    engines: {node: '>=14.0.0'}

  update-browserslist-db@1.0.13:
    resolution: {integrity: sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  upng-js@2.1.0:
    resolution: {integrity: sha512-d3xzZzpMP64YkjP5pr8gNyvBt7dLk/uGI67EctzDuVp4lCZyVMo0aJO6l/VDlgbInJYDY6cnClLoBp29eKWI6g==}

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  uuid@3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  vant-table@0.0.1:
    resolution: {integrity: sha512-bqhywsaf3WNDxrOAZm14v6eEwxNMtN5LDRxOXWBOUei568PH6QjgVGJLtzjYX6qJz+XzNSB5toPsrJkQV/jUoA==}
    engines: {node: '>=14.18.0'}
    peerDependencies:
      vant: ^4.5.0

  vant@4.8.10:
    resolution: {integrity: sha512-JKfywSAwxVrY6q2KbESdW9IyGMeOFKa8ub3DLluTaYV7ff4ECXFilkjMeRYVT4hdsNcNii6KCQF6mUYyI56SBA==}
    peerDependencies:
      vue: ^3.0.0

  vconsole@3.15.1:
    resolution: {integrity: sha512-KH8XLdrq9T5YHJO/ixrjivHfmF2PC2CdVoK6RWZB4yftMykYIaXY1mxZYAic70vADM54kpMQF+dYmvl5NRNy1g==}

  verror@1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=, tarball: https://registry.npmmirror.com/verror/download/verror-1.10.0.tgz}
    engines: {'0': node >=0.6.0}

  vite@5.2.8:
    resolution: {integrity: sha512-OyZR+c1CE8yeHw5V5t59aXsUPPVTHMDjEZz8MgguLL/Q7NblxhZUlTu9xSPqlsUO/y+X7dlU05jdhvyycD55DA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vue-demi@0.14.10:
    resolution: {integrity: sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-doc-preview@0.3.2:
    resolution: {integrity: sha512-1zRW/LOLpgtt+bdb4jRzsKfOngkJYzFJsKT0uH4/KqdUMVo7W8jhYUuVcllnw5mHVL/l4v3JESsqX+i8v3uhRw==}

  vue-i18n@9.11.1:
    resolution: {integrity: sha512-S7Xi8DkLQG4xnnbxkxzipJK6CdfLdZkmApn95st89HFGp8LTmTH0Tv+Zw6puhOCZJCFrH73PHo3Ylwd2+Bmdxg==}
    engines: {node: '>= 16'}
    peerDependencies:
      vue: ^3.0.0

  vue-router@4.3.0:
    resolution: {integrity: sha512-dqUcs8tUeG+ssgWhcPbjHvazML16Oga5w34uCUmsk7i0BcnskoLGwjpa15fqMr2Fa5JgVBrdL2MEgqz6XZ/6IQ==}
    peerDependencies:
      vue: ^3.2.0

  vue-template-compiler@2.7.16:
    resolution: {integrity: sha512-AYbUWAJHLGGQM7+cNTELw+KsOG9nl2CnSv467WobS5Cv9uk3wFcnr1Etsz2sEIHEZvw1U+o9mRlEO6QbZvUPGQ==}

  vue-tsc@1.8.27:
    resolution: {integrity: sha512-WesKCAZCRAbmmhuGl3+VrdWItEvfoFIPXOvUJkjULi+x+6G/Dy69yO3TBRJDr9eUlmsNAwVmxsNZxvHKzbkKdg==}
    hasBin: true
    peerDependencies:
      typescript: '*'

  vue@3.4.21:
    resolution: {integrity: sha512-5hjyV/jLEIKD/jYl4cavMcnzKwjMKohureP8ejn3hhEjwhWIhWeuzL2kJAjzl/WyVsgPY56Sy4Z40C3lVshxXA==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  vuedraggable@4.1.0:
    resolution: {integrity: sha512-FU5HCWBmsf20GpP3eudURW3WdWTKIbEIQxh9/8GE806hydR9qZqRRxRE3RjqX7PkuLuMQG/A7n3cfj9rCEchww==}
    peerDependencies:
      vue: ^3.0.1

  wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}

  webpack-virtual-modules@0.6.1:
    resolution: {integrity: sha512-poXpCylU7ExuvZK8z+On3kX+S8o/2dQ/SVYueKA0D4WEMXROXgY8Ez50/bQEUmvoSMMrWcrJqCHuhAbsiwg7Dg==}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which-module@2.0.1:
    resolution: {integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==}

  wide-align@1.1.5:
    resolution: {integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==}

  wrap-ansi@5.1.0:
    resolution: {integrity: sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==}
    engines: {node: '>=6'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  write-yaml@1.0.0:
    resolution: {integrity: sha512-QFB0QwNlUTSsICNb1HV+822MvFpTC1gtKcOfm0B9oqz4qOQXbRuMSxWPWryTEFBEZDWbI5zXabXArvShXTdLiA==}
    engines: {node: '>=0.10.0'}

  write@0.3.3:
    resolution: {integrity: sha512-e63bsTAFxFUU8OGClhjhhf2R72Njpq6DDTOFFBxDkfZFwoRRKZUx9rll6g/TvY0UcCdKE2OroYZje0v9ROzmfA==}
    engines: {node: '>=0.10.0'}

  ws@7.5.9:
    resolution: {integrity: sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  x2js@3.4.4:
    resolution: {integrity: sha512-yG/ThaBCgnsa3aoMPAe7QwDpcyU4D70hjXC4Y1lZSfD/Tgd0MpE19FnZZRAjekryw0c8cffpOt9zsPEiqktO6Q==}

  y18n@4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yargs-parser@13.1.2:
    resolution: {integrity: sha512-3lbsNRf/j+A4QuSZfDRA7HRSfWrzO0YjqTJd5kjAq37Zep1CEgaYmrH9Q3GwPiB9cHyd1Y1UwggGhJGoxipbzg==}

  yargs@13.3.2:
    resolution: {integrity: sha512-AX3Zw5iPruN5ie6xGRIDgqkT+ZhnRlZMLMHAs8tg7nRruy2Nb+i5o9bwghAogtM08q1dpr2LVoS8KSTMYpWXUw==}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@babel/code-frame@7.24.2':
    dependencies:
      '@babel/highlight': 7.24.2
      picocolors: 1.0.0

  '@babel/compat-data@7.24.4': {}

  '@babel/core@7.24.4':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.4
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.4)
      '@babel/helpers': 7.24.4
      '@babel/parser': 7.24.4
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.1
      '@babel/types': 7.24.0
      convert-source-map: 2.0.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.24.4':
    dependencies:
      '@babel/types': 7.24.0
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-builder-binary-assignment-operator-visitor@7.22.15':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-compilation-targets@7.23.6':
    dependencies:
      '@babel/compat-data': 7.24.4
      '@babel/helper-validator-option': 7.23.5
      browserslist: 4.23.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-replace-supers': 7.24.1(@babel/core@7.24.4)
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      semver: 6.3.1

  '@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-annotate-as-pure': 7.22.5
      regexpu-core: 5.3.2
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-plugin-utils': 7.24.0
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.22.20': {}

  '@babel/helper-function-name@7.23.0':
    dependencies:
      '@babel/template': 7.24.0
      '@babel/types': 7.24.0

  '@babel/helper-hoist-variables@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-member-expression-to-functions@7.23.0':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-module-imports@7.24.3':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-module-transforms@7.23.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.24.3
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/helper-optimise-call-expression@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-plugin-utils@7.24.0': {}

  '@babel/helper-remap-async-to-generator@7.22.20(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-wrap-function': 7.22.20

  '@babel/helper-replace-supers@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5

  '@babel/helper-simple-access@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-split-export-declaration@7.22.6':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-string-parser@7.24.1': {}

  '@babel/helper-validator-identifier@7.22.20': {}

  '@babel/helper-validator-option@7.23.5': {}

  '@babel/helper-wrap-function@7.22.20':
    dependencies:
      '@babel/helper-function-name': 7.23.0
      '@babel/template': 7.24.0
      '@babel/types': 7.24.0

  '@babel/helpers@7.24.4':
    dependencies:
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.1
      '@babel/types': 7.24.0
    transitivePeerDependencies:
      - supports-color

  '@babel/highlight@7.24.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.0

  '@babel/parser@7.24.4':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-transform-optional-chaining': 7.24.1(@babel/core@7.24.4)

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-import-assertions@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-import-attributes@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-arrow-functions@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-async-generator-functions@7.24.3(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-remap-async-to-generator': 7.22.20(@babel/core@7.24.4)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.24.4)

  '@babel/plugin-transform-async-to-generator@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-module-imports': 7.24.3
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-remap-async-to-generator': 7.22.20(@babel/core@7.24.4)

  '@babel/plugin-transform-block-scoped-functions@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-block-scoping@7.24.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-class-properties@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-class-features-plugin': 7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-class-static-block@7.24.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-class-features-plugin': 7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.24.4)

  '@babel/plugin-transform-classes@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-replace-supers': 7.24.1(@babel/core@7.24.4)
      '@babel/helper-split-export-declaration': 7.22.6
      globals: 11.12.0

  '@babel/plugin-transform-computed-properties@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/template': 7.24.0

  '@babel/plugin-transform-destructuring@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-dotall-regex@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-duplicate-keys@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-dynamic-import@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.24.4)

  '@babel/plugin-transform-exponentiation-operator@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.22.15
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-export-namespace-from@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.24.4)

  '@babel/plugin-transform-for-of@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5

  '@babel/plugin-transform-function-name@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-json-strings@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.24.4)

  '@babel/plugin-transform-literals@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-logical-assignment-operators@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.24.4)

  '@babel/plugin-transform-member-expression-literals@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-modules-amd@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-modules-commonjs@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-simple-access': 7.22.5

  '@babel/plugin-transform-modules-systemjs@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/plugin-transform-modules-umd@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-named-capturing-groups-regex@7.22.5(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-new-target@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-nullish-coalescing-operator@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.24.4)

  '@babel/plugin-transform-numeric-separator@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.24.4)

  '@babel/plugin-transform-object-rest-spread@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-transform-parameters': 7.24.1(@babel/core@7.24.4)

  '@babel/plugin-transform-object-super@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-replace-supers': 7.24.1(@babel/core@7.24.4)

  '@babel/plugin-transform-optional-catch-binding@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.24.4)

  '@babel/plugin-transform-optional-chaining@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.24.4)

  '@babel/plugin-transform-parameters@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-private-methods@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-class-features-plugin': 7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-private-property-in-object@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-create-class-features-plugin': 7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.24.4)

  '@babel/plugin-transform-property-literals@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-regenerator@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-reserved-words@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-shorthand-properties@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-spread@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5

  '@babel/plugin-transform-sticky-regex@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-template-literals@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-typeof-symbol@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-unicode-escapes@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-unicode-property-regex@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-unicode-regex@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-unicode-sets-regex@7.24.1(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/preset-env@7.24.4(@babel/core@7.24.4)':
    dependencies:
      '@babel/compat-data': 7.24.4
      '@babel/core': 7.24.4
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-validator-option': 7.23.5
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.24.4(@babel/core@7.24.4)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.24.4)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.24.4)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.24.4)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-import-assertions': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-syntax-import-attributes': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.24.4)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.24.4)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.24.4)
      '@babel/plugin-transform-arrow-functions': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-async-generator-functions': 7.24.3(@babel/core@7.24.4)
      '@babel/plugin-transform-async-to-generator': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-block-scoped-functions': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-block-scoping': 7.24.4(@babel/core@7.24.4)
      '@babel/plugin-transform-class-properties': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-class-static-block': 7.24.4(@babel/core@7.24.4)
      '@babel/plugin-transform-classes': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-computed-properties': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-destructuring': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-dotall-regex': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-duplicate-keys': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-dynamic-import': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-exponentiation-operator': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-export-namespace-from': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-for-of': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-function-name': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-json-strings': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-literals': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-logical-assignment-operators': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-member-expression-literals': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-amd': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-commonjs': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-systemjs': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-umd': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.22.5(@babel/core@7.24.4)
      '@babel/plugin-transform-new-target': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-numeric-separator': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-object-rest-spread': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-object-super': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-optional-catch-binding': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-optional-chaining': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-parameters': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-private-methods': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-private-property-in-object': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-property-literals': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-regenerator': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-reserved-words': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-shorthand-properties': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-spread': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-sticky-regex': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-template-literals': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-typeof-symbol': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-escapes': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-property-regex': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-regex': 7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-sets-regex': 7.24.1(@babel/core@7.24.4)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.24.4)
      babel-plugin-polyfill-corejs2: 0.4.10(@babel/core@7.24.4)
      babel-plugin-polyfill-corejs3: 0.10.4(@babel/core@7.24.4)
      babel-plugin-polyfill-regenerator: 0.6.1(@babel/core@7.24.4)
      core-js-compat: 3.36.1
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.24.4)':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/types': 7.24.0
      esutils: 2.0.3

  '@babel/regjsgen@0.8.0': {}

  '@babel/runtime@7.24.4':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.24.0':
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/parser': 7.24.4
      '@babel/types': 7.24.0

  '@babel/traverse@7.24.1':
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.4
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.24.4
      '@babel/types': 7.24.0
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.24.0':
    dependencies:
      '@babel/helper-string-parser': 7.24.1
      '@babel/helper-validator-identifier': 7.22.20
      to-fast-properties: 2.0.0

  '@dcloudio/types@3.4.14': {}

  '@dcloudio/uni-app@2.0.2-4020420240722004(@dcloudio/types@3.4.14)(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))':
    dependencies:
      '@dcloudio/types': 3.4.14
      '@vue/composition-api': 1.7.2(vue@3.4.21(typescript@4.9.5))

  '@esbuild/aix-ppc64@0.20.2':
    optional: true

  '@esbuild/android-arm64@0.20.2':
    optional: true

  '@esbuild/android-arm@0.20.2':
    optional: true

  '@esbuild/android-x64@0.20.2':
    optional: true

  '@esbuild/darwin-arm64@0.20.2':
    optional: true

  '@esbuild/darwin-x64@0.20.2':
    optional: true

  '@esbuild/freebsd-arm64@0.20.2':
    optional: true

  '@esbuild/freebsd-x64@0.20.2':
    optional: true

  '@esbuild/linux-arm64@0.20.2':
    optional: true

  '@esbuild/linux-arm@0.20.2':
    optional: true

  '@esbuild/linux-ia32@0.20.2':
    optional: true

  '@esbuild/linux-loong64@0.20.2':
    optional: true

  '@esbuild/linux-mips64el@0.20.2':
    optional: true

  '@esbuild/linux-ppc64@0.20.2':
    optional: true

  '@esbuild/linux-riscv64@0.20.2':
    optional: true

  '@esbuild/linux-s390x@0.20.2':
    optional: true

  '@esbuild/linux-x64@0.20.2':
    optional: true

  '@esbuild/netbsd-x64@0.20.2':
    optional: true

  '@esbuild/openbsd-x64@0.20.2':
    optional: true

  '@esbuild/sunos-x64@0.20.2':
    optional: true

  '@esbuild/win32-arm64@0.20.2':
    optional: true

  '@esbuild/win32-ia32@0.20.2':
    optional: true

  '@esbuild/win32-x64@0.20.2':
    optional: true

  '@intlify/core-base@9.11.1':
    dependencies:
      '@intlify/message-compiler': 9.11.1
      '@intlify/shared': 9.11.1

  '@intlify/message-compiler@9.11.1':
    dependencies:
      '@intlify/shared': 9.11.1
      source-map-js: 1.2.0

  '@intlify/shared@9.11.1': {}

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.4.15

  '@kwsites/file-exists@1.1.1':
    dependencies:
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  '@kwsites/promise-deferred@1.1.1': {}

  '@mapbox/node-pre-gyp@1.0.11':
    dependencies:
      detect-libc: 2.0.3
      https-proxy-agent: 5.0.1
      make-dir: 3.1.0
      node-fetch: 2.7.0
      nopt: 5.0.0
      npmlog: 5.0.1
      rimraf: 3.0.2
      semver: 7.6.0
      tar: 6.2.1
    transitivePeerDependencies:
      - encoding
      - supports-color
    optional: true

  '@microsoft/signalr@7.0.14':
    dependencies:
      abort-controller: 3.0.0
      eventsource: 2.0.2
      fetch-cookie: 2.2.0
      node-fetch: 2.7.0
      ws: 7.5.9
    transitivePeerDependencies:
      - bufferutil
      - encoding
      - utf-8-validate

  '@rollup/pluginutils@5.1.0(rollup@4.14.1)':
    dependencies:
      '@types/estree': 1.0.5
      estree-walker: 2.0.2
      picomatch: 2.3.1
    optionalDependencies:
      rollup: 4.14.1

  '@rollup/rollup-android-arm-eabi@4.14.1':
    optional: true

  '@rollup/rollup-android-arm64@4.14.1':
    optional: true

  '@rollup/rollup-darwin-arm64@4.14.1': {}

  '@rollup/rollup-darwin-x64@4.14.1':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.14.1':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.14.1':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.14.1':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.14.1':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.14.1':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.14.1':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.14.1':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.14.1':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.14.1':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.14.1':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.14.1':
    optional: true

  '@tato30/vue-pdf@1.11.1(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      pdfjs-dist: 4.5.136
      vue: 3.4.21(typescript@4.9.5)
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@tinymce/tinymce-vue@5.1.1(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      tinymce: 6.8.4
      vue: 3.4.21(typescript@4.9.5)

  '@types/crypto-js@4.2.2': {}

  '@types/estree@1.0.5': {}

  '@types/lodash@4.17.0': {}

  '@types/node@18.19.31':
    dependencies:
      undici-types: 5.26.5

  '@vant/popperjs@1.3.0': {}

  '@vant/use@1.6.0(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      vue: 3.4.21(typescript@4.9.5)

  '@vitejs/plugin-legacy@5.3.2(terser@5.30.3)(vite@5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3))':
    dependencies:
      '@babel/core': 7.24.4
      '@babel/preset-env': 7.24.4(@babel/core@7.24.4)
      browserslist: 4.23.0
      browserslist-to-esbuild: 2.1.1(browserslist@4.23.0)
      core-js: 3.36.1
      magic-string: 0.30.9
      regenerator-runtime: 0.14.1
      systemjs: 6.14.3
      terser: 5.30.3
      vite: 5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue@4.6.2(vite@5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3))(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      vite: 5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3)
      vue: 3.4.21(typescript@4.9.5)

  '@volar/language-core@1.11.1':
    dependencies:
      '@volar/source-map': 1.11.1

  '@volar/source-map@1.11.1':
    dependencies:
      muggle-string: 0.3.1

  '@volar/typescript@1.11.1':
    dependencies:
      '@volar/language-core': 1.11.1
      path-browserify: 1.0.1

  '@vue-macros/common@1.10.2(rollup@4.14.1)(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      '@babel/types': 7.24.0
      '@rollup/pluginutils': 5.1.0(rollup@4.14.1)
      '@vue/compiler-sfc': 3.4.21
      ast-kit: 0.12.1
      local-pkg: 0.5.0
      magic-string-ast: 0.3.0
    optionalDependencies:
      vue: 3.4.21(typescript@4.9.5)
    transitivePeerDependencies:
      - rollup

  '@vue-office/docx@1.6.3(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue-demi@0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      vue: 3.4.21(typescript@4.9.5)
      vue-demi: 0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))
    optionalDependencies:
      '@vue/composition-api': 1.7.2(vue@3.4.21(typescript@4.9.5))

  '@vue-office/excel@1.7.14(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue-demi@0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      vue: 3.4.21(typescript@4.9.5)
      vue-demi: 0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))
    optionalDependencies:
      '@vue/composition-api': 1.7.2(vue@3.4.21(typescript@4.9.5))

  '@vue-office/pptx@1.0.1(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue-demi@0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      vue: 3.4.21(typescript@4.9.5)
      vue-demi: 0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))
    optionalDependencies:
      '@vue/composition-api': 1.7.2(vue@3.4.21(typescript@4.9.5))

  '@vue/compiler-core@3.4.21':
    dependencies:
      '@babel/parser': 7.24.4
      '@vue/shared': 3.4.21
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.0

  '@vue/compiler-dom@3.4.21':
    dependencies:
      '@vue/compiler-core': 3.4.21
      '@vue/shared': 3.4.21

  '@vue/compiler-sfc@3.4.21':
    dependencies:
      '@babel/parser': 7.24.4
      '@vue/compiler-core': 3.4.21
      '@vue/compiler-dom': 3.4.21
      '@vue/compiler-ssr': 3.4.21
      '@vue/shared': 3.4.21
      estree-walker: 2.0.2
      magic-string: 0.30.9
      postcss: 8.4.38
      source-map-js: 1.2.0

  '@vue/compiler-ssr@3.4.21':
    dependencies:
      '@vue/compiler-dom': 3.4.21
      '@vue/shared': 3.4.21

  '@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      vue: 3.4.21(typescript@4.9.5)

  '@vue/devtools-api@6.6.1': {}

  '@vue/language-core@1.8.27(typescript@4.9.5)':
    dependencies:
      '@volar/language-core': 1.11.1
      '@volar/source-map': 1.11.1
      '@vue/compiler-dom': 3.4.21
      '@vue/shared': 3.4.21
      computeds: 0.0.1
      minimatch: 9.0.4
      muggle-string: 0.3.1
      path-browserify: 1.0.1
      vue-template-compiler: 2.7.16
    optionalDependencies:
      typescript: 4.9.5

  '@vue/reactivity@3.4.21':
    dependencies:
      '@vue/shared': 3.4.21

  '@vue/runtime-core@3.4.21':
    dependencies:
      '@vue/reactivity': 3.4.21
      '@vue/shared': 3.4.21

  '@vue/runtime-dom@3.4.21':
    dependencies:
      '@vue/runtime-core': 3.4.21
      '@vue/shared': 3.4.21
      csstype: 3.1.3

  '@vue/server-renderer@3.4.21(vue@3.4.21(typescript@4.9.5))':
    dependencies:
      '@vue/compiler-ssr': 3.4.21
      '@vue/shared': 3.4.21
      vue: 3.4.21(typescript@4.9.5)

  '@vue/shared@3.4.21': {}

  '@xmldom/xmldom@0.8.10': {}

  abbrev@1.1.1:
    optional: true

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  acorn@8.11.3: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color
    optional: true

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  amfe-flexible@2.2.1: {}

  ansi-escapes@3.2.0: {}

  ansi-regex@3.0.1: {}

  ansi-regex@4.1.1: {}

  ansi-regex@5.0.1:
    optional: true

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  aproba@2.0.0:
    optional: true

  are-we-there-yet@2.0.0:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2
    optional: true

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  assert-plus@1.0.0: {}

  ast-kit@0.12.1:
    dependencies:
      '@babel/parser': 7.24.4
      pathe: 1.1.2

  ast-walker-scope@0.6.1:
    dependencies:
      '@babel/parser': 7.24.4
      ast-kit: 0.12.1

  asynckit@0.4.0: {}

  autoprefixer@10.4.19(postcss@8.4.38):
    dependencies:
      browserslist: 4.23.0
      caniuse-lite: 1.0.30001608
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  aws-sign2@0.7.0: {}

  aws4@1.12.0: {}

  axios@0.18.1:
    dependencies:
      follow-redirects: 1.5.10
      is-buffer: 2.0.5
    transitivePeerDependencies:
      - supports-color

  axios@1.6.8:
    dependencies:
      follow-redirects: 1.15.6
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-plugin-polyfill-corejs2@0.4.10(@babel/core@7.24.4):
    dependencies:
      '@babel/compat-data': 7.24.4
      '@babel/core': 7.24.4
      '@babel/helper-define-polyfill-provider': 0.6.1(@babel/core@7.24.4)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.10.4(@babel/core@7.24.4):
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-define-polyfill-provider': 0.6.1(@babel/core@7.24.4)
      core-js-compat: 3.36.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.1(@babel/core@7.24.4):
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-define-polyfill-provider': 0.6.1(@babel/core@7.24.4)
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  binary-extensions@2.3.0: {}

  blueimp-canvas-to-blob@3.29.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browserslist-to-esbuild@2.1.1(browserslist@4.23.0):
    dependencies:
      browserslist: 4.23.0
      meow: 13.2.0

  browserslist@4.23.0:
    dependencies:
      caniuse-lite: 1.0.30001608
      electron-to-chromium: 1.4.730
      node-releases: 2.0.14
      update-browserslist-db: 1.0.13(browserslist@4.23.0)

  buffer-from@1.1.2: {}

  camelcase@5.3.1: {}

  caniuse-lite@1.0.30001608: {}

  canvas@2.11.2:
    dependencies:
      '@mapbox/node-pre-gyp': 1.0.11
      nan: 2.21.0
      simple-get: 3.1.1
    transitivePeerDependencies:
      - encoding
      - supports-color
    optional: true

  caseless@0.12.0: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chardet@0.7.0: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chownr@2.0.0:
    optional: true

  cli-cursor@2.1.0:
    dependencies:
      restore-cursor: 2.0.0

  cli-spinners@2.9.2: {}

  cli-table@0.3.11:
    dependencies:
      colors: 1.0.3

  cli-width@2.2.1: {}

  cliui@5.0.0:
    dependencies:
      string-width: 3.1.0
      strip-ansi: 5.2.0
      wrap-ansi: 5.1.0

  clone@1.0.4: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-name@1.1.3: {}

  color-support@1.1.3:
    optional: true

  colors@1.0.3: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  complex.js@2.1.1: {}

  compressorjs@1.2.1:
    dependencies:
      blueimp-canvas-to-blob: 3.29.0
      is-blob: 2.1.0

  computeds@0.0.1: {}

  concat-map@0.0.1: {}

  console-control-strings@1.1.0:
    optional: true

  convert-source-map@2.0.0: {}

  copy-text-to-clipboard@3.2.0: {}

  core-js-compat@3.36.1:
    dependencies:
      browserslist: 4.23.0

  core-js@3.36.1: {}

  core-util-is@1.0.2: {}

  crypto-js@4.2.0: {}

  csstype@3.1.3: {}

  cwise-compiler@1.1.3:
    dependencies:
      uniq: 1.0.1

  dashdash@1.14.1:
    dependencies:
      assert-plus: 1.0.0

  data-uri-to-buffer@0.0.3: {}

  dayjs@1.11.10: {}

  de-indent@1.0.2: {}

  debug@3.1.0:
    dependencies:
      ms: 2.0.0

  debug@4.3.4:
    dependencies:
      ms: 2.1.2

  decamelize@1.2.0: {}

  decimal.js@10.4.3: {}

  decompress-response@4.2.1:
    dependencies:
      mimic-response: 2.1.0
    optional: true

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  delayed-stream@1.0.0: {}

  delegates@1.0.0:
    optional: true

  detect-libc@2.0.3:
    optional: true

  dingtalk-jsapi@3.0.32:
    dependencies:
      promise-polyfill: 7.1.2

  ecc-jsbn@0.1.2:
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  electron-to-chromium@1.4.730: {}

  emoji-regex@7.0.3: {}

  emoji-regex@8.0.0:
    optional: true

  entities@4.5.0: {}

  es-module-lexer@1.5.0: {}

  esbuild@0.20.2:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.20.2
      '@esbuild/android-arm': 0.20.2
      '@esbuild/android-arm64': 0.20.2
      '@esbuild/android-x64': 0.20.2
      '@esbuild/darwin-arm64': 0.20.2
      '@esbuild/darwin-x64': 0.20.2
      '@esbuild/freebsd-arm64': 0.20.2
      '@esbuild/freebsd-x64': 0.20.2
      '@esbuild/linux-arm': 0.20.2
      '@esbuild/linux-arm64': 0.20.2
      '@esbuild/linux-ia32': 0.20.2
      '@esbuild/linux-loong64': 0.20.2
      '@esbuild/linux-mips64el': 0.20.2
      '@esbuild/linux-ppc64': 0.20.2
      '@esbuild/linux-riscv64': 0.20.2
      '@esbuild/linux-s390x': 0.20.2
      '@esbuild/linux-x64': 0.20.2
      '@esbuild/netbsd-x64': 0.20.2
      '@esbuild/openbsd-x64': 0.20.2
      '@esbuild/sunos-x64': 0.20.2
      '@esbuild/win32-arm64': 0.20.2
      '@esbuild/win32-ia32': 0.20.2
      '@esbuild/win32-x64': 0.20.2

  escalade@3.1.2: {}

  escape-latex@1.2.0: {}

  escape-string-regexp@1.0.5: {}

  esprima@4.0.1: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  event-target-shim@5.0.1: {}

  eventsource@2.0.2: {}

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  extend@3.0.2: {}

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  extsprintf@1.3.0: {}

  fast-deep-equal@3.1.3: {}

  fast-json-stable-stringify@2.1.0: {}

  fetch-cookie@2.2.0:
    dependencies:
      set-cookie-parser: 2.6.0
      tough-cookie: 4.1.3

  figures@2.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@3.0.0:
    dependencies:
      locate-path: 3.0.0

  follow-redirects@1.15.6: {}

  follow-redirects@1.5.10:
    dependencies:
      debug: 3.1.0
    transitivePeerDependencies:
      - supports-color

  forever-agent@0.6.1: {}

  form-data@2.3.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  fraction.js@4.3.4: {}

  fraction.js@4.3.7: {}

  fs-exists-sync@0.1.0: {}

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6
    optional: true

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gauge@3.0.2:
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5
    optional: true

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-pixels@3.3.3:
    dependencies:
      data-uri-to-buffer: 0.0.3
      jpeg-js: 0.4.4
      mime-types: 2.1.35
      ndarray: 1.0.19
      ndarray-pack: 1.2.1
      node-bitmap: 0.0.1
      omggif: 1.0.10
      parse-data-uri: 0.2.0
      pngjs: 3.4.0
      request: 2.88.2
      through: 2.3.8

  getpass@0.1.7:
    dependencies:
      assert-plus: 1.0.0

  gl-mat2@1.0.1: {}

  gl-vec2@1.3.0: {}

  gl-vec3@1.1.3: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  har-schema@2.0.0: {}

  har-validator@5.1.5:
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  has-flag@3.0.0: {}

  has-unicode@2.0.1:
    optional: true

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  heic2any@0.0.4: {}

  highlight.js@9.18.5: {}

  html5-qrcode@2.3.8: {}

  http-signature@1.2.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color
    optional: true

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  immutable@4.3.5: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  inquirer@6.5.2:
    dependencies:
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-width: 2.2.1
      external-editor: 3.1.0
      figures: 2.0.0
      lodash: 4.17.21
      mute-stream: 0.0.7
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 2.1.1
      strip-ansi: 5.2.0
      through: 2.3.8

  iota-array@1.0.0: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-blob@2.1.0: {}

  is-buffer@1.1.6: {}

  is-buffer@2.0.5: {}

  is-core-module@2.13.1:
    dependencies:
      hasown: 2.0.2

  is-extendable@0.1.1: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@2.0.0: {}

  is-fullwidth-code-point@3.0.0:
    optional: true

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  is-typedarray@1.0.0: {}

  isstream@0.1.2: {}

  javascript-natural-sort@0.7.1: {}

  joycon@3.1.1: {}

  jpeg-js@0.4.4: {}

  js-cookie@3.0.5: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsbn@0.1.1: {}

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  json-schema-traverse@0.4.1: {}

  json-schema@0.4.0: {}

  json-stringify-safe@5.0.1: {}

  json5@2.2.3: {}

  jsonc-parser@3.2.1: {}

  jsprim@1.4.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  lint@0.8.19:
    dependencies:
      chalk: 2.4.2
      cli-table: 0.3.11
      commander: 2.20.3
      inquirer: 6.5.2
      js-yaml: 4.1.0
      loadash: 1.0.0
      moment: 2.30.1
      ora: 3.4.0
      prettier: 1.19.1
      replace-in-file: 3.4.4
      request: 2.88.2
      simple-git: 3.24.0
      write-yaml: 1.0.0
    transitivePeerDependencies:
      - supports-color

  loadash@1.0.0: {}

  local-pkg@0.5.0:
    dependencies:
      mlly: 1.6.1
      pkg-types: 1.0.3

  locate-path@3.0.0:
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  lodash.debounce@4.0.8: {}

  lodash@4.17.21: {}

  log-symbols@2.2.0:
    dependencies:
      chalk: 2.4.2

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  magic-string-ast@0.3.0:
    dependencies:
      magic-string: 0.30.9

  magic-string@0.30.9:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.4.15

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1
    optional: true

  marked@0.4.0: {}

  mathjs@11.12.0:
    dependencies:
      '@babel/runtime': 7.24.4
      complex.js: 2.1.1
      decimal.js: 10.4.3
      escape-latex: 1.2.0
      fraction.js: 4.3.4
      javascript-natural-sort: 0.7.1
      seedrandom: 3.0.5
      tiny-emitter: 2.1.0
      typed-function: 4.1.1

  meow@13.2.0: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@1.2.0: {}

  mimic-response@2.1.0:
    optional: true

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.4:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0
    optional: true

  minipass@5.0.0:
    optional: true

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    optional: true

  mitt@3.0.1: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4:
    optional: true

  mlly@1.6.1:
    dependencies:
      acorn: 8.11.3
      pathe: 1.1.2
      pkg-types: 1.0.3
      ufo: 1.5.3

  moment@2.30.1: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  muggle-string@0.3.1: {}

  mutation-observer@1.0.3: {}

  mute-stream@0.0.7: {}

  nan@2.21.0:
    optional: true

  nanoid@3.3.7: {}

  ndarray-linear-interpolate@1.0.0: {}

  ndarray-pack@1.2.1:
    dependencies:
      cwise-compiler: 1.1.3
      ndarray: 1.0.19

  ndarray@1.0.19:
    dependencies:
      iota-array: 1.0.0
      is-buffer: 1.1.6

  node-bitmap@0.0.1: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-releases@2.0.14: {}

  nopt@5.0.0:
    dependencies:
      abbrev: 1.1.1
    optional: true

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  npmlog@5.0.1:
    dependencies:
      are-we-there-yet: 2.0.0
      console-control-strings: 1.1.0
      gauge: 3.0.2
      set-blocking: 2.0.0
    optional: true

  oauth-sign@0.9.0: {}

  object-assign@4.1.1:
    optional: true

  omggif@1.0.10: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@2.0.1:
    dependencies:
      mimic-fn: 1.2.0

  ora@3.4.0:
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-spinners: 2.9.2
      log-symbols: 2.2.0
      strip-ansi: 5.2.0
      wcwidth: 1.0.1

  os-tmpdir@1.0.2: {}

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-locate@3.0.0:
    dependencies:
      p-limit: 2.3.0

  p-try@2.2.0: {}

  pako@1.0.11: {}

  parse-data-uri@0.2.0:
    dependencies:
      data-uri-to-buffer: 0.0.3

  path-browserify@1.0.1: {}

  path-exists@3.0.0: {}

  path-is-absolute@1.0.1: {}

  path-parse@1.0.7: {}

  path2d@0.2.1:
    optional: true

  pathe@1.1.2: {}

  pdfh5@1.4.9: {}

  pdfjs-dist@4.5.136:
    optionalDependencies:
      canvas: 2.11.2
      path2d: 0.2.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  performance-now@2.1.0: {}

  picocolors@1.0.0: {}

  picomatch@2.3.1: {}

  pinia@2.1.7(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(typescript@4.9.5)(vue@3.4.21(typescript@4.9.5)):
    dependencies:
      '@vue/devtools-api': 6.6.1
      vue: 3.4.21(typescript@4.9.5)
      vue-demi: 0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5))
    optionalDependencies:
      '@vue/composition-api': 1.7.2(vue@3.4.21(typescript@4.9.5))
      typescript: 4.9.5

  pkg-types@1.0.3:
    dependencies:
      jsonc-parser: 3.2.1
      mlly: 1.6.1
      pathe: 1.1.2

  pngjs@3.4.0: {}

  postcss-pxtorem@6.1.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-value-parser@4.2.0: {}

  postcss@8.4.38:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.2.0

  prettier@1.19.1: {}

  promise-polyfill@7.1.2: {}

  proxy-from-env@1.1.0: {}

  psl@1.9.0: {}

  punycode@2.3.1: {}

  qs@6.5.3: {}

  quagga@0.12.1:
    dependencies:
      get-pixels: 3.3.3
      gl-mat2: 1.0.1
      gl-vec2: 1.3.0
      gl-vec3: 1.1.3
      lodash: 4.17.21
      ndarray: 1.0.19
      ndarray-linear-interpolate: 1.0.0

  querystringify@2.2.0: {}

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    optional: true

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  regenerate-unicode-properties@10.1.1:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.24.4

  regexpu-core@5.3.2:
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.1
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.1.0

  regjsparser@0.9.1:
    dependencies:
      jsesc: 0.5.0

  replace-in-file@3.4.4:
    dependencies:
      chalk: 2.4.2
      glob: 7.2.3
      yargs: 13.3.2

  request@2.88.2:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.12.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  require-directory@2.1.1: {}

  require-main-filename@2.0.0: {}

  requires-port@1.0.0: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@2.0.0:
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3
    optional: true

  rollup-plugin-esbuild@5.0.0(esbuild@0.20.2)(rollup@4.14.1):
    dependencies:
      '@rollup/pluginutils': 5.1.0(rollup@4.14.1)
      debug: 4.3.4
      es-module-lexer: 1.5.0
      esbuild: 0.20.2
      joycon: 3.1.1
      jsonc-parser: 3.2.1
      rollup: 4.14.1
    transitivePeerDependencies:
      - supports-color

  rollup@4.14.1:
    dependencies:
      '@types/estree': 1.0.5
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.14.1
      '@rollup/rollup-android-arm64': 4.14.1
      '@rollup/rollup-darwin-arm64': 4.14.1
      '@rollup/rollup-darwin-x64': 4.14.1
      '@rollup/rollup-linux-arm-gnueabihf': 4.14.1
      '@rollup/rollup-linux-arm64-gnu': 4.14.1
      '@rollup/rollup-linux-arm64-musl': 4.14.1
      '@rollup/rollup-linux-powerpc64le-gnu': 4.14.1
      '@rollup/rollup-linux-riscv64-gnu': 4.14.1
      '@rollup/rollup-linux-s390x-gnu': 4.14.1
      '@rollup/rollup-linux-x64-gnu': 4.14.1
      '@rollup/rollup-linux-x64-musl': 4.14.1
      '@rollup/rollup-win32-arm64-msvc': 4.14.1
      '@rollup/rollup-win32-ia32-msvc': 4.14.1
      '@rollup/rollup-win32-x64-msvc': 4.14.1
      fsevents: 2.3.3

  run-async@2.4.1: {}

  rxjs@6.6.7:
    dependencies:
      tslib: 1.14.1

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  sass@1.74.1:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.5
      source-map-js: 1.2.0

  seedrandom@3.0.5: {}

  semver@6.3.1: {}

  semver@7.6.0:
    dependencies:
      lru-cache: 6.0.0

  set-blocking@2.0.0: {}

  set-cookie-parser@2.6.0: {}

  signal-exit@3.0.7: {}

  simple-concat@1.0.1:
    optional: true

  simple-get@3.1.1:
    dependencies:
      decompress-response: 4.2.1
      once: 1.4.0
      simple-concat: 1.0.1
    optional: true

  simple-git@3.24.0:
    dependencies:
      '@kwsites/file-exists': 1.1.1
      '@kwsites/promise-deferred': 1.1.1
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  sortablejs@1.14.0: {}

  source-map-js@1.2.0: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  sprintf-js@1.0.3: {}

  sshpk@1.18.0:
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  string-width@2.1.1:
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0

  string-width@3.1.0:
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    optional: true

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1
    optional: true

  strip-ansi@4.0.0:
    dependencies:
      ansi-regex: 3.0.1

  strip-ansi@5.2.0:
    dependencies:
      ansi-regex: 4.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1
    optional: true

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  systemjs@6.14.3: {}

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    optional: true

  terser@5.30.3:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.11.3
      commander: 2.20.3
      source-map-support: 0.5.21

  through@2.3.8: {}

  tiny-emitter@2.1.0: {}

  tinymce@6.8.4: {}

  tinymce@7.3.0: {}

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tough-cookie@2.5.0:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1

  tough-cookie@4.1.3:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@0.0.3: {}

  tslib@1.14.1: {}

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tweetnacl@0.14.5: {}

  typed-function@4.1.1: {}

  typescript@4.9.5: {}

  ufo@1.5.3: {}

  undici-types@5.26.5: {}

  unicode-canonical-property-names-ecmascript@2.0.0: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.1.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  uniq@1.0.1: {}

  universalify@0.2.0: {}

  unplugin-vue-define-options@1.4.3(rollup@4.14.1)(vue@3.4.21(typescript@4.9.5)):
    dependencies:
      '@vue-macros/common': 1.10.2(rollup@4.14.1)(vue@3.4.21(typescript@4.9.5))
      ast-walker-scope: 0.6.1
      unplugin: 1.10.1
    transitivePeerDependencies:
      - rollup
      - vue

  unplugin@1.10.1:
    dependencies:
      acorn: 8.11.3
      chokidar: 3.6.0
      webpack-sources: 3.2.3
      webpack-virtual-modules: 0.6.1

  update-browserslist-db@1.0.13(browserslist@4.23.0):
    dependencies:
      browserslist: 4.23.0
      escalade: 3.1.2
      picocolors: 1.0.0

  upng-js@2.1.0:
    dependencies:
      pako: 1.0.11

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  util-deprecate@1.0.2:
    optional: true

  uuid@3.4.0: {}

  vant-table@0.0.1(vant@4.8.10(vue@3.4.21(typescript@4.9.5))):
    dependencies:
      vant: 4.8.10(vue@3.4.21(typescript@4.9.5))

  vant@4.8.10(vue@3.4.21(typescript@4.9.5)):
    dependencies:
      '@vant/popperjs': 1.3.0
      '@vant/use': 1.6.0(vue@3.4.21(typescript@4.9.5))
      '@vue/shared': 3.4.21
      vue: 3.4.21(typescript@4.9.5)

  vconsole@3.15.1:
    dependencies:
      '@babel/runtime': 7.24.4
      copy-text-to-clipboard: 3.2.0
      core-js: 3.36.1
      mutation-observer: 1.0.3

  verror@1.10.0:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  vite@5.2.8(@types/node@18.19.31)(sass@1.74.1)(terser@5.30.3):
    dependencies:
      esbuild: 0.20.2
      postcss: 8.4.38
      rollup: 4.14.1
    optionalDependencies:
      '@types/node': 18.19.31
      fsevents: 2.3.3
      sass: 1.74.1
      terser: 5.30.3

  vue-demi@0.14.10(@vue/composition-api@1.7.2(vue@3.4.21(typescript@4.9.5)))(vue@3.4.21(typescript@4.9.5)):
    dependencies:
      vue: 3.4.21(typescript@4.9.5)
    optionalDependencies:
      '@vue/composition-api': 1.7.2(vue@3.4.21(typescript@4.9.5))

  vue-doc-preview@0.3.2:
    dependencies:
      axios: 0.18.1
      highlight.js: 9.18.5
      marked: 0.4.0
    transitivePeerDependencies:
      - supports-color

  vue-i18n@9.11.1(vue@3.4.21(typescript@4.9.5)):
    dependencies:
      '@intlify/core-base': 9.11.1
      '@intlify/shared': 9.11.1
      '@vue/devtools-api': 6.6.1
      vue: 3.4.21(typescript@4.9.5)

  vue-router@4.3.0(vue@3.4.21(typescript@4.9.5)):
    dependencies:
      '@vue/devtools-api': 6.6.1
      vue: 3.4.21(typescript@4.9.5)

  vue-template-compiler@2.7.16:
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  vue-tsc@1.8.27(typescript@4.9.5):
    dependencies:
      '@volar/typescript': 1.11.1
      '@vue/language-core': 1.8.27(typescript@4.9.5)
      semver: 7.6.0
      typescript: 4.9.5

  vue@3.4.21(typescript@4.9.5):
    dependencies:
      '@vue/compiler-dom': 3.4.21
      '@vue/compiler-sfc': 3.4.21
      '@vue/runtime-dom': 3.4.21
      '@vue/server-renderer': 3.4.21(vue@3.4.21(typescript@4.9.5))
      '@vue/shared': 3.4.21
    optionalDependencies:
      typescript: 4.9.5

  vuedraggable@4.1.0(vue@3.4.21(typescript@4.9.5)):
    dependencies:
      sortablejs: 1.14.0
      vue: 3.4.21(typescript@4.9.5)

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  webidl-conversions@3.0.1: {}

  webpack-sources@3.2.3: {}

  webpack-virtual-modules@0.6.1: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-module@2.0.1: {}

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3
    optional: true

  wrap-ansi@5.1.0:
    dependencies:
      ansi-styles: 3.2.1
      string-width: 3.1.0
      strip-ansi: 5.2.0

  wrappy@1.0.2: {}

  write-yaml@1.0.0:
    dependencies:
      extend-shallow: 2.0.1
      js-yaml: 3.14.1
      write: 0.3.3

  write@0.3.3:
    dependencies:
      fs-exists-sync: 0.1.0
      mkdirp: 0.5.6

  ws@7.5.9: {}

  x2js@3.4.4:
    dependencies:
      '@xmldom/xmldom': 0.8.10

  y18n@4.0.3: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yargs-parser@13.1.2:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs@13.3.2:
    dependencies:
      cliui: 5.0.0
      find-up: 3.0.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 3.1.0
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 13.1.2
