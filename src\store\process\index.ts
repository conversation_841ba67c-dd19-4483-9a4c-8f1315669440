import { useParams<PERSON><PERSON>atter, useQueryParams, useSessionStorage } from '@/utils'
import { ConstConfig } from '@/utils/localConst'
import { defineStore } from 'pinia'
import { IProcessState } from '../storeModel'
import { useGetQuery, usePostBody } from '@/utils/request'
import { url } from '@/api/url'
import { showNotify } from 'vant'
import { eventBus } from '@/utils/eventBus'
export interface IProcd {
    processId: string;
    actionType: number;
    instanceId: string;
    taskId: string;
    stepId: string
}
const storeId = 'process'
const useProcessStore = defineStore(storeId, {
    getters: {
        getPostInfoData: (state: IProcessState) => state.postInfo,
        getTabName: (state: IProcessState) => state.tabActive,
        getConsign: (state: IProcessState) => state.consignInfo,
        getProcLoad(state: IProcessState) {
            const processId = useSessionStorage.get('processId')
            const actionType = useSessionStorage.get('actionType')
            const instanceId = useSessionStorage.get('instanceId')
            const taskId = useSessionStorage.get('taskId')
            const stepId = useSessionStorage.get('stepId')
            const loadType = useSessionStorage.get('loadType')
            if (processId)
                state.procLoad.processId = processId
            if (actionType)
                state.procLoad.actionType = Number(actionType)
            if (instanceId)
                state.procLoad.instanceId = instanceId
            if (taskId)
                state.procLoad.taskId = taskId
            if (stepId)
                state.procLoad.stepId = stepId
            if (loadType)
                state.procLoad.loadType = loadType
            return state.procLoad
        },
        getAppUrlName(state: IProcessState) {
            const name = useSessionStorage.get("appName")
            if (name) {
                state.appUrlName = name
                return state.appUrlName
            }
            return state.appUrlName
        },
        getAppUrlId(state: IProcessState) {
            const id = useSessionStorage.get("appId")
            if (id) {
                state.appUrlId = id
                return state.appUrlId
            }
            return state.appUrlId
        },
        getHideTabHeader(state: IProcessState) {
            const value = useSessionStorage.get('hide')
            const nValue = Number(value)
            state.hideTabHeader = nValue === 1 ? true : false
            return state.hideTabHeader
        },
        getOuMemberId: (state: IProcessState) => state.oumemberId
    },
    state: (): IProcessState => {
        return {
            postInfo: null,
            tabActive: 'AwatWork',
            procLoad: {
                processId: '',
                actionType: 0,
                instanceId: '',
                taskId: '',
                stepId: '',
                loadType: 'apply'
            },
            consignInfo: null,
            returnUrl: '',
            appUrlName: '',
            appUrlId: '',
            hideTabHeader: false,
            oumemberId: ''
        }
    },
    actions: {
        setPostInfo(postInfo: any) {
            this.$state.postInfo = postInfo
        },
        async getAttachmentInfo(field: string): Promise<any> {
            field = field == null ? '' : field //YZ +
            const files = field.split(',')
            const formdata = new FormData()
            files.forEach(file => {
                formdata.append('fileIds', file)
            })
            const data = await usePostBody(url.form.getAttachmentInfo, {}, formdata, false)
            return data
        },
        setTabName(name: string) {
            this.$state.tabActive = name
        },
        setProcessId(processId: string) {
            useSessionStorage.set("processId", processId)
            this.$state.procLoad.processId = processId ?? ''
        },
        setActionType(type: number) {
            useSessionStorage.set("actionType", String(type))
            this.$state.procLoad.actionType = type ?? 0
        },
        setLoadType(type: string = 'read') {
            useSessionStorage.set("loadType", String(type))
            this.$state.procLoad.loadType = type
        },
        setInstanceId(instanceId: string) {
            useSessionStorage.set('instanceId', instanceId)
            this.$state.procLoad.instanceId = instanceId ?? ''
        },
        setTaskId(taskId: string) {
            useSessionStorage.set("taskId", taskId)
            this.$state.procLoad.taskId = taskId ?? ''

        },
        setStepId(stepId: string) {
            useSessionStorage.set('stepId', stepId)
            this.$state.procLoad.stepId = stepId ?? ''

        },
        setConsign(info: any) {
            this.$state.consignInfo = info
        },
        setReturnUrl(url: string) {
            if (url) {
                this.$state.returnUrl = url
                // '/mobile/portal/get-user-info'
                console.log("%c [ this.$state.returnUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", this.$state.returnUrl)
            }
        },
        setAppTypeName(name: string, id: string) {
            this.$state.appUrlName = name
            this.$state.appUrlId = id
            useSessionStorage.set("appName", name)
            useSessionStorage.set("appId", id)
        },
        setHideTabHeader(hide: boolean) {
            this.$state.hideTabHeader = hide
            const value = hide ? 1 : 0
            useSessionStorage.set('hide', value.toString())
        },
        /**
         * 当表单关闭时，销毁表单所有相关的东西
         */
        clearFormStorage() {
            this.$state.procLoad = {
                processId: '',
                taskId: '',
                stepId: '',
                instanceId: '',
                actionType: 0,
                loadType: 'apply'
            }
            useSessionStorage.remove('stepId')
            useSessionStorage.remove('processId')
            useSessionStorage.remove('taskId')
            useSessionStorage.remove('actionType')
            useSessionStorage.remove('instanceId')
            useSessionStorage.remove('loadType')
        },
        setMemberOuId(ouid: string) {
            this.$state.oumemberId = ouid
        },
        async LoadOWerPropty(): Promise<any> {
            const newUrl = useParamsFormatter(url.process.getMemberDetail,
                {
                    params1: this.$state.oumemberId
                })
            const data = await useGetQuery(newUrl)
            return data
        }
    }
})
export { useProcessStore }