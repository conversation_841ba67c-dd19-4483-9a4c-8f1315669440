import { UploaderFileListItem } from "vant";
import { Y<PERSON><PERSON>onfig, Y<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
import { useGetBody, useGetQuery, usePostBody, useRequest } from "@/utils/request";
import { useLang } from "@/utils";
export class YZUploaderConfig extends YZConfig {
    // 文件大小
    private fileSize: number;
    private sizeUnit: string;
    private defaultRules: Array<any>;
    private fileTypes: string
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZUploader'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.upload')}[${this.label}]${useLang('Form.file')}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.fileSize = parmas['fileSize']
        this.sizeUnit = parmas['sizeUnit']
        this.fileTypes = parmas['fileTypes']
    }
    getSize(): number {
        return this.fileSize
    }
    getUnit(): string {
        return this.sizeUnit
    }
    getFileTypes(): string {
        return this.fileTypes
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZUploaderStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZUploader extends YZField {
    private autoUpload: boolean;
    private listType: string;
    private multipe: boolean;
    private action: string;
    private accpt: string;
    private isLoading: boolean
    constructor(params: any) {
        super(params)
        this.vModel.value = []
        this.vModel.optionValue = params['__vModel__'].modelValue
        this.autoUpload = params['auto-upload']
        this.listType = params['list-type']
        this.multipe = params['multiple']
        this.action = params['action']
        this.accpt = params['accept']
        this.isLoading = false
    }
    initConfig(config: any): YZConfig {
        return new YZUploaderConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZUploaderStyle(style)
    }
    getMultipe(): boolean {
        return this.multipe
    }
    getTypes(): string {
        return this.listType
    }
    getAction(): string {
        return this.action
    }
    getAccept(): string {
        return this.accpt
    }
    setIsUpload(value: boolean) {
        this.isLoading = value
    }
    getIsUpload() {
        return this.isLoading
    }
}