import { showToast } from "vant"
const wechatLogin = async () => {
    const loading = showToast({
        type: 'loading',
        message: '正在进行身份认证...',
        duration: 0
    })
    const config = window.webConfig
    const rediruri = encodeURIComponent(window.location.href)
    const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${config.weChat.appid}&redirect_uri=${rediruri}&response_type=code&scope=snsapi_base&state=1&agentid=${config.weChat.agentid}#wechat_redirect`
    loading.close()
    window.location.replace(url)
}
export {
    wechatLogin
}