import mitt from 'mitt'
const mitter = mitt()
const  emitQunee:Array<any> =[]
let interId:number = 0
const runEvents =()=> {
     interId = setInterval(()=>{
        if(emitQunee.length>0) {
         const pop = emitQunee.pop()
         mitter.emit(pop.eventName,pop.params)
        } else {
            clearInterval(interId)
            interId = 0
        }
     },50)
}
// runEvents();
export interface ICallBack {
    cb(params?: any): void
}
export interface IMitter {
    /**
     * 发布事件
     * 
     * @param eventName 事件名称
     * @param immedia 是否立即执行
     * @param params 事件参数
     */
    emit<T>(eventName: string,immedia:boolean, params?: T): void;
    /**
     * 监听事件
     * @param eventName 事件名称
     * @param cb 回调
     */
    on(eventName: string, cb: ICallBack): void;
    /**
     * 关闭指定事件订阅，不销毁实际挂载的事件
     * @param isDelete 是否销毁挂载的事件
     * @param eventName  事件名称
     */
    off(eventName:string,isDelete?:boolean):void;
    /**
     * 关闭所有已挂载的事件
     * 并且销毁所有事件
     */
    clear():void;
}
const eventBus: IMitter = {
    emit(eventName, immedia,params) {
        if (immedia) {
           mitter.emit(eventName,params)
        } else {
            emitQunee.push({
                eventName,
                params
            })
        }
        if(interId === 0 && emitQunee.length>0)
          runEvents()
    },
    on(eventName:string, cb: ICallBack) {
        mitter.on(eventName, (e:any) => {
            cb.cb(e)
        })
    },
    off:(eventName,isDelete)=>{
        // console.log('事件准备销毁:'+eventName)
        mitter.off(eventName)
        if (isDelete)
           mitter.all.delete(eventName)
        // console.log('事件已销毁:'+eventName)
    },
    clear:()=>{
        mitter.all.clear()
    }
}

export {
    eventBus
}
