import { createRouter, create<PERSON><PERSON><PERSON>ash<PERSON><PERSON><PERSON>, RouteRecordRaw } from 'vue-router';
import Home from '@/views/home/<USER>';
import Login from '@/views/login/index.vue';
import My from '@/views/my/index.vue';
import WeChat from '@/views/login/wechat.vue';
import ThirdAuth from '@/views/login/thirdAuth.vue';
import DingTalk from '@/views/login/dingtalk.vue';
import FeiShu from '@/views/login/feiShu.vue';
import store from '@/store';
import { useLoingStore } from '@/store/login';
import LayoutVue from '@/views/Layout.vue';
import { useHomeStore } from '@/store/home';
import RenderForm from '@/views/forms/renderForm.vue';
import Work from '@/views/work/index.vue';
import { useEnviroMent } from '@/utils';
import { useGetQuery } from '@/utils/request';
import { url } from '@/api/url';
import { useLogin } from '@/logic/login/index'
import { sso_accesstoken, sso_login, checkLogin, autoLogin } from '@/service/user'

const routes: Array<RouteRecordRaw> = [
  //cf modify 新增首页
  {
    path: '/',
    name: 'index', //cf modify
    component: () => import('@/views/index/index.vue'), //cf modify
  },
  {
    name: 'layout', //cf modify
    path: '/layout', //cf modify
    redirect: '/login',
    meta: { keepalive: true }, //cf modify
    component: LayoutVue,
    children: [
      { name: 'home', path: '/layout/home', component: Home, meta: { keepalive: true }, }, //cf modify meta: { keepalive: true }
      { name: 'my', path: '/layout/my', component: My, meta: { keepalive: true }, },
      { name: 'reform', path: '/layout/reform', component: RenderForm, meta: { keepalive: true }, },
      { name: 'work', path: '/layout/work', component: Work, meta: { keepalive: true }, },
    ],
  },
  {
    name: 'login',
    path: '/login',
    component: Login,
  },
  // {
  //   name: 'wechat',
  //   path: '/wechat',
  //   component: WeChat,
  // },
  // {
  //   name: 'thirdauth',
  //   path: '/thirdauth',
  //   component: ThirdAuth,
  // },
  // {
  //   name: 'dingtalk',
  //   path: '/dingtalk',
  //   component: DingTalk,
  // },
  // {
  //   name: 'feishu',
  //   path: '/feishu',
  //   component: FeiShu,
  // },

  /******************** EBOOK ********************/ // cf +
  {
    name: 'ebook',
    path: '/ebook',
    // meta: { keepalive: true },
    component: () => import('@/views/ebook/index.vue'),
  },
  {
    name: 'ebookRead',
    path: '/ebookRead',
    component: () => import('@/views/ebook/ebookRead.vue'),
  },
  {
    name: 'qrcode',
    path: '/qrcode',
    component: () => import('@/views/my/qrcode.vue'),
  },

  /******************** MY ********************/ // cf +
  {
    name: 'account',
    path: '/account',
    component: () => import('@/views/my/account.vue'),
  },
  {
    name: 'about',
    path: '/about',
    component: () => import('@/views/my/about.vue'),
  },

  /******************** CSTC ********************/ // cf +
  {
    name: 'cstc',
    path: '/cstc',
    component: () => import('@/views/cstc/index.vue'),
  },
  {
    name: 'trainingQRCode',
    path: '/trainingQRCode',
    component: () => import('@/views/cstc/trainingQRCode.vue'),
  },
  {
    name: 'trainingtHistoryDetail',
    path: '/trainingtHistoryDetail',
    component: () => import('@/views/cstc/trainingtHistoryDetail.vue'),
  },
  {
    name: 'trainingtHistory',
    path: '/trainingtHistory',
    component: () => import('@/views/cstc/trainingtHistory.vue'),
  },
  {
    name: 'generateQRCode',
    path: '/generateQRCode',
    component: () => import('@/views/cstc/generateQRCode.vue'),
  },
  {
    name: 'projectSelectCstc',
    path: '/projectSelectCstc',
    component: () => import('@/views/cstc/projectSelectCstc.vue'),
  },
  {
    name: 'projectDetailCstc',
    path: '/projectDetailCstc',
    component: () => import('@/views/cstc/projectDetailCstc.vue'),
  },
  {
    name: 'qrCodeCstc',
    path: '/qrCodeCstc',
    component: () => import('@/views/cstc/qrCodeCstc.vue'),
  },
  {
    name: 'scan',
    path: '/scan',
    component: () => import('@/views/cstc/scan.vue'),
  },
  {
    name: 'search',
    path: '/search',
    component: () => import('@/views/cstc/components/search.vue'),
  },

  /******************** UAUC ********************/ // cf +
  {
    name: 'uauc',
    path: '/uauc',
    component: () => import('@/views/uauc/index.vue'),
  },
  {
    name: 'projectSelectUauc',
    path: '/projectSelectUauc',
    component: () => import('@/views/uauc/projectSelectUauc.vue'),
  },
  {
    name: 'projectDetailUauc',
    path: '/projectDetailUauc',
    component: () => import('@/views/uauc/projectDetailUauc.vue'),
  },
  {
    name: 'supplierSelect',
    path: '/supplierSelect',
    component: () => import('@/views/uauc/supplierSelect.vue'),
  },
  {
    name: 'supplierDetail',
    path: '/supplierDetail',
    component: () => import('@/views/uauc/supplierDetail.vue'),
  },
  {
    name: 'personSelect',
    path: '/personSelect',
    component: () => import('@/views/uauc/personSelect.vue'),
  },
  {
    name: 'personDetail',
    path: '/personDetail',
    component: () => import('@/views/uauc/personDetail.vue'),
  },
  {
    name: 'processTimeSelect',
    path: '/processTimeSelect',
    component: () => import('@/views/uauc/processTimeSelect.vue'),
  },
  {
    name: 'processTimeDetail',
    path: '/processTimeDetail',
    component: () => import('@/views/uauc/processTimeDetail.vue'),
  },
  {
    name: 'HazardClassification',
    path: '/HazardClassification',
    component: () => import('@/views/uauc/HazardClassification.vue'),
  },
  {
    name: 'Hazard_classificationDetail',
    path: '/Hazard_classificationDetail',
    component: () => import('@/views/uauc/Hazard_classificationDetail.vue'),
  },
  {
    name: 'InformantSelect',
    path: '/InformantSelect',
    component: () => import('@/views/uauc/InformantSelect.vue'),
  },
  {
    name: 'InformantDetail',
    path: '/InformantDetail',
    component: () => import('@/views/uauc/InformantDetail.vue'),
  },
  /******************** SSI ********************/
  {
    name: 'ssi',
    path: '/ssi',
    component: () => import('@/views/ssi/index.vue'),
  },
  {
    name: 'Tool/Equipment Registration Form',
    path: '/ssiRegister',
    component: () => import('@/views/ssi/ssiRegister.vue'),
  },
  {
    name: 'projectSSI',
    path: '/projectSSI',
    component: () => import('@/views/ssi/projectSSI.vue'),
  },
  {
    name: 'projectDetailSSI',
    path: '/projectDetailSSI',
    component: () => import('@/views/ssi/projectDetailSSI.vue'),
  },
  {
    name: 'operatorSelect',
    path: '/operatorSelect',
    component: () => import('@/views/ssi/operatorSelect.vue'),
  },
  {
    name: 'operatorDetail',
    path: '/operatorDetail',
    component: () => import('@/views/ssi/operatorDetail.vue'),
  },
  {
    name: 'ssi_apply',
    path: '/ssi_apply',
    component: () => import('@/views/ssi/ssi_apply.vue'),
  },
  {
    name: 'fileRead',
    path: '/fileRead',
    component: () => import('@/views/ssi/fileRead.vue'),
  },
  {
    name: 'scanBarcode',
    path: '/scanBarcode',
    // meta: { keepalive: true },
    component: () => import('@/views/ssi/scanBarcode.vue'),
  },
  {
    name: 'barcodeDetail',
    path: '/barcodeDetail',
    component: () => import('@/views/ssi/barcodeDetail.vue'),
  },




];
const { onSubmit } = useLogin()
const defaultLogin = '/login';
const defaultHome = '/';                  //cf modify
// const defaultHome = '/layout/home';    //cf modify
const loginStore = useLoingStore(store);
const homeStore = useHomeStore(store);
const router = createRouter({
  history: createWebHashHistory(),
  routes: routes,
});
const env = useEnviroMent();
const href_ = window.location.origin //https://ppa-test.linde-le.cn


console.log('agetn', navigator.userAgent);
console.log('env', env);


router.beforeEach(async (to, from, next) => {
  // console.log("%c [ from ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", from)
  // console.log("%c [ to ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", to)
  const cookie = document.cookie;
  let token = location.hostname === 'localhost' ? 'dfsjffldsjlfjlje' : loginStore.getToken;

  if (!token) { //cf modify
    const ssoLogin = async () => {
      if (localStorage.getItem('Account')) {
        const params = {
          appKeyName: 'dev',
          SecurityKey: "fuuCVLQZZVHFn3jwmNVtG5y0eM0wSRGD"
        }
        const sso_res = await sso_accesstoken(params) // sso/accesstoken --- post
        if (sso_res?.data?.success) {
          localStorage.setItem('SSOStatus', 'pending')
          const newUrl = href_ + `/EMIP/sso/login?appKeyName=Dev&AccessToken=${sso_res?.data?.Data?.AccessToken}&Account=${localStorage.getItem('Account')}&LoginType=${localStorage.getItem('SubjectType')}` //${SubjectType}`
          // alert('ssoLogin ')
          window.location.href = newUrl
        }
      } else {
        localStorage.setItem('SSOStatus', 'pending')
        // alert(location.href)
        // window.location.href = href_

        // this.webUrl	= "https://ppa.linde-le.cn/emip/?ver=201&appid=com.yzsoft.lindeapp"  //PPA系统 应用宝 有两个入口
        const url_appid = new URLSearchParams(window.location?.search)?.get('appid') //cn.linde-le.bpa PPA 对外
        if (url_appid === 'cn.linde-le.bpa') { // b2c打包的入口地址做判断
          // alert('B2C')
          window.location.href = href_ + '/identity' //重定向到此地址👉 https://ppa.linde-le.cn/identity
        } else {
          // alert('B2C || AAD')
          localStorage.setItem('url_appid', url_appid) //记录对内对外的 appid || com.yzsoft.lindeapp
          window.location.href = href_ // 未获取用户信息 两个入口登录
        }

      }
    }

    if (localStorage.getItem('SSOStatus') === 'pending') { //1.首次登录 checklogin 2.自动登录 checklogin
      console.log(12223)
      next()
      console.log(12224)
      // if (location.host === ('ppa-test.linde-le.cn')) {
      //   console.log('cn 切换 com')
      //   location.href = location.href.replace('-test', '').replace('.cn', '.com');
      // }

      const res = await useGetQuery(url.login.checklogin);
      if (res?.success) {
        token = res.Data.Token;
        loginStore.setToken(res.Data.Token);
        const { success, user } = await useGetQuery(url.login.getUserInfo)
        if (success) {
          localStorage.setItem('Account', user?.Account)
          localStorage.setItem('Email', user?.EMail)
          localStorage.setItem('DisplayName', user?.DisplayName)
          const SubjectType = (user?.Account?.includes('@linde-le.com') || user?.Account?.includes('@linde.com')) ? 'AAD' : 'B2C'
          localStorage.setItem('SubjectType', SubjectType)
        } else {
          console.log('router error getUserInfo')
        }
      } else {
        // alert('checkLogin false')
        ssoLogin()
      }
    } else {
      // alert('SSOStatus 无')
      ssoLogin()
    }

    homeStore.setActivePath(to.path);
    if (to.path === defaultHome) {
      homeStore.setTab(true);
    }
    // console.log("%c [ token ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", token)
    // if (to.query.point === 'true' || env.isH5) {
    //   console.log('local login') //本地登录
    //   next({ path: defaultLogin, replace: true });
    // }
    if (location.hostname === 'localhost') {
      next({ path: defaultLogin, replace: true })
    }
  } else {
    if (to.path === defaultLogin) { // 已登录状态下访问登录页,重定向到首页 /
      console.log(1111)
      next({ path: defaultHome, replace: true });
    } else {
      console.log('1 login')  // 已登录状态下访问其他页面,直接放行
      next();
      // if (location.host === ('ppa-test.linde-le.cn')) {
      //   console.log('cn 切换 com 2')
      //   location.href = location.href.replace('-test', '').replace('.cn', '.com');
      //   console.log("%c [ location.href ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", location.href)
      // }
    }
  }
});
export default router;
