<template>
  <div v-show="!field.hidden">
    <van-field :name="field.vModel.model" v-model="vModel" :required="field.config.required"
      :rules="!field.hidden ? field.config.rules : []" :readonly="readOnly" :disabled="field.disabled"
      :label="field.config.label" type="text" format-trigger="onBlur" :formatter="(value) => onformatter(value, true)"
      @focus="onFource" @input="onInput">
      <!-- @focus="onFource" @input="onInput" YZ + -->
    </van-field>
  </div>
</template>

<script lang="ts" setup>
import { YZStepper, YZStepperConfig } from "@/logic/forms/YZStepper";
import { useCore } from "@/store/core";
import { useUUID } from "@/utils";
import { eventBus } from "@/utils/eventBus";
import { e, index, molarVolumeDependencies, number } from "mathjs";
import { showNotify } from "vant";
import { ref } from "vue";
import { onMounted, nextTick, watch } from "vue";
import { computed, PropType } from "vue";
import { currencyTurnToNum, currencyFormatValue } from '@/logic/forms/processHelper'
import { debounce } from "lodash";
const core = useCore()
const props = defineProps({
  field: {
    type: Object as PropType<YZStepper>,
    required: true,
  },
  modelValue: {
    type: [String, Number],
    default: "",
  },
  optionValue: {
    type: String,
    default: ''
  },
  index: {
    type: Number,
    default: -1
  }
});
const stepConfig = props.field.config as YZStepperConfig;
const config = props.field.config as YZStepperConfig
const readOnly = computed(() => props.field.config.extends.readOnly ?? props.field.readonly)
const onformatter = (value: string, isNotice: boolean = true): string => {
  let flag = isFirstLoad.value ? false : isNotice
  voptionModel.value = value
  return currencyFormatValue(value, stepConfig, flag)
  // if (!value) {
  //   return value
  // }
  // // 判断是否符合数字要求
  // const isValidate = parseFloat(value)
  // if (isNaN(isValidate)) {
  //   return ''
  // }
  // // 判断是否开启小数位
  // if (stepConfig.digt == -1 || stepConfig.digt == 0) {
  //   let vaildValue = typeof(value) === 'number'? String(value)  : value 
  //   if (vaildValue.indexOf('.') > -1) {
  //     showNotify({ message: '不允许输入小数,请开启小数配置', type: 'danger' })
  //     return ''
  //   }
  // }
  // // 如果开启了小数位
  // if (stepConfig.digt !== -1) {
  //   const num: number = parseInt(value)
  //   if (num !== isValidate) {
  //     const length = String(value).split('.')[1].length
  //     const jd = stepConfig.digt //小数精度
  //     if (length > jd) {
  //       showNotify({ message: '仅仅允许保留' + jd + '位小数', type: 'danger' })
  //       return ''
  //     }
  //     let fex = String(value).split('.')[1]
  //     for (let i = length; i < stepConfig.digt; i++) {
  //       fex += '0'
  //     }
  //     value = num + '.' + fex
  //   } else {
  //     // 不包含小数,根据位数补充
  //     if (stepConfig.digt > 0) {
  //       let fex = ''
  //       for (let i = 0; i < stepConfig.digt; i++) {
  //         fex += '0'
  //       }
  //       value = num + '.' + fex
  //     }

  //   }
  // }
  // // 判断是否开启了千分位
  // if (stepConfig.thousands) {
  //   //如果开启了千分位
  //   const num: any = parseInt(value).toString()
  //   const resove = [...num].reverse().join('')
  //   const numarrys: Array<string> = []
  //   const pushArrys: Array<string> = []
  //   let j = 1
  //   for (let i = 0; i < resove.length; i++) {
  //     const charValue = resove[i]
  //     pushArrys.push(charValue)
  //     if (pushArrys.length === num.length) {
  //       numarrys.push(charValue)
  //     } else {
  //       numarrys.push(charValue)
  //       if (pushArrys.length > 0 && pushArrys.length % 3 === 0) {
  //         numarrys.push(',')
  //       }

  //     }
  //     // if (j === 3) {
  //     //   numarrys.push(charValue)
  //     //   if (resove.length > 3 && i !=resove.length - 1)
  //     //     numarrys.push(',')
  //     //   j = 1
  //     // } else {
  //     //   numarrys.push(charValue)
  //     // }
  //     // j++
  //   }

  //   let endValue = numarrys.reverse().join('')
  //   if (endValue.startsWith(','))
  //     endValue = endValue.substring(1)

  //   if (stepConfig.digt !== -1) {
  //     if (value.indexOf('.') > -1) {
  //       value = endValue + '.' + value.split('.')[1]
  //     } else {
  //       value = endValue
  //     }

  //   } else {
  //     value = endValue
  //   }
  // }
  // // 判断是否开启了币种
  // if (stepConfig.currency) {
  //   value = stepConfig.currency + value
  // }
  // return value
}
const emit = defineEmits(["update:modelValue", "change", 'update:optionValue']);
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
  // onInput()
  isFirstLoad.value = false
})
const handlerOnChange = () => {
  // nextTick(() => {
  props.field.expressTest(props.field, props.index, isFirstLoad.value)
  props.field.disableTest(props.field, props.index, isFirstLoad.value)
  props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
  props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
  // })
}
const onInput = debounce(() => {
  let value: string = vModel.value.toString()
  vModel.value = value.replace(/[^0-9.]/g, '');
  props.field.disableTest(props.field, props.index, isFirstLoad.value)
  props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
  props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
  props.field.expressTest(props.field, props.index, isFirstLoad.value)
}, 300)
const vModel = computed({
  get() {
    // props.field.disableTest(props.field, props.index, isFirstLoad.value)
    // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    // props.field.expressTest(props.field, props.index, isFirstLoad.value)
    // if(isFirstLoad)
    // handlerOnChange()
    return props.modelValue;
  },
  set(val) {
    emit("update:modelValue", val);
    emit("change", props.field);
    // 当vmodel双向绑定的时候 需要将voptionModel同步双向更新
    voptionModel.value = currencyTurnToNum(val, stepConfig) as string
  },
});
const voptionModel = computed({
  get() {
    return props.optionValue;
  },
  set(val) {
    emit("update:optionValue", val);
  },
});
const onFource = () => {
  let value: string = vModel.value as string
  // 是否开启了小数位
  // if (!value) {
  //   vModel.value = ''
  // } else {
  //   //是否开启了币种
  //   if (stepConfig.currency)
  //     value = value.replace(stepConfig.currency, '')
  //   //是否开启了千分位
  //   if (stepConfig.thousands)
  //     value = value.replaceAll(',', '')
  //   if (stepConfig.digt !== -1)
  //     value = parseFloat(value).toString()
  //   nextTick(() => {
  //     vModel.value = value
  //   })
  // }
  nextTick(() => {
    vModel.value = currencyTurnToNum(value, stepConfig) as string
  })
  var up = core.$update as any
  if (up) {
    up.update()
  }
};
eventBus.on('onMap', {
  cb: function (params) {
    console.log("%c [ params 6]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
    if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
      // props.field.vModel.value =  params.value 
      props.field.vModel.value = onformatter(params.value)
      //onFource()
      onInput()
    }
  }
})
eventBus.on('setFieldValue', {
  cb: function (params) {
    if (params.uuid === props.field.uuid && params.index == props.field.pindex) {//+ && params.index == props.field.pindex 
      props.field.vModel.value = onformatter(params.value, false)
      props.field.vModel.optionValue = params.value
      nextTick(() => {
        onInput()
      })
    }
  }
})
eventBus.on('setFieldDisable', {
  cb: function (params) {
    if (params.uuid === props.field.uuid && params.index == props.field.pindex) {//+
      props.field.disabled = params.value
    }
  }
})
eventBus.on('setFieldHidden', {
  cb: function (params) {
    if (params.uuid === props.field.uuid && params.index == props.field.pindex) {//+
      props.field.hidden = params.value
    }
  }
})

eventBus.on('setFieldRequeired', {
  cb: function (params) {
    if (params.uuid === props.field.uuid && params.index == props.field.pindex) {//+
      if (params.value || params === 1) {
        // 禁用验证
        props.field.config.required = false
        props.field.config.rules = []
      } else {
        // 启用验证
        props.field.config.required = true
        props.field.config.rules = config.getDefaultRule()
      }
    }
  }
})
</script>
<style scoped></style>
