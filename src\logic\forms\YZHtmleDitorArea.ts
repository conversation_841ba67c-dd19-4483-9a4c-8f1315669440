

import { useLang } from "@/utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON><PERSON><PERSON> } from "./formModel";
export class YZHtmleDitorAreaConfig extends YZConfig {
    private defaultRules: Array<any>;
    private maxLength: number
    constructor(parmas: any) {
        super(parmas)
        // console.log(parmas,'9999')
        this.ctype = 'YZHtmleDitorArea'
        this.maxLength = parmas['extends'].maxLength ?? 2000
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.defaultRules = [...this.defaultRules, { validator: (val: any) => { return val.length < this.maxLength }, message: `${useLang('Form.FieldLength_tips')}${this.maxLength}` }]
        } else {
            this.defaultRules = [{ validator: (val: any) => { return val.length < this.maxLength }, message: `${useLang('Form.FieldLength_tips')}${this.maxLength}` }]
        }
        this.rules = this.defaultRules
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
    getMaxLength(): number {
        return this.maxLength
    }
}
export class YZHtmleDitorAreaStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZHtmleDitorArea extends YZField {
    /**
     * 私有控件属性或自定义重写控件属性
     */
    private row: number;
    private showLimit: boolean;
    private minRows: number
    private maxRows: number
    constructor(parms: any) {
        super(parms)
        // console.log(parms,4566)
        this.minRows = parms['autosize'].minRows || 1
        this.maxRows = parms['autosize'].maxRows || 1
        // this.readonly = parms['__config__'].extends.readOnly ?? parms.readonly
        this.row = parms.autosize.minRows
        this.showLimit = parms['show-word-limit']
    }
    initConfig(config: any): YZConfig {
        return new YZHtmleDitorAreaConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZHtmleDitorAreaStyle(style)
    }
    getRow() {
        return this.row
    }
    getShowLimit() {
        return this.showLimit
    }
    getMaxCount() {
        return this.row * 30
    }
    getMaxHeight() {
        return {
            minHeight: 123 + 20 * (this.minRows - 1),
            maxHeight: 123 + 20 * (this.maxRows - 1)
        }
    }
}

