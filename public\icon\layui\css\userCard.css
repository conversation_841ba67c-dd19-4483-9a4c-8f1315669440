.user-card {
    background-color: #fff;
    border-radius: 12px;
}

    .user-card .user-top {
        padding: 20px;
        background: url('../../style/imgs/user-bg.jpg') no-repeat;
        height: 185px;
        color: #fff;
        background-size: cover;
        text-align: center;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

        .user-card .user-top .user-img {
            width: 118px;
            height: 118px;
            object-fit: cover;
            border-radius: 50%;
        }

        .user-card .user-top .user-name {
            font-size: 24px;
            margin-top: 10px;
        }

        .user-card .user-top .tips {
            font-size: 16px;
            margin-top: 5px;
        }

    .user-card .user-list {
        padding: 20px 50px;
    }

        .user-card .user-list .user-item {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #ddd;
            justify-content: space-between;
            font-size: 16px;
            align-items: center;
            color: #666;
        }

            .user-card .user-list .user-item .i-right {
                text-align: right;
                color: #999;
            }

            .user-card .user-list .user-item .iconfont, .user-card .user-list .user-item .layui-icon {
                font-size: 20px;
                margin-right: 10px;
                position: relative;
                top: 2px;
            }

            .user-card .user-list .user-item:last-child {
                border-bottom: 0;
            }
