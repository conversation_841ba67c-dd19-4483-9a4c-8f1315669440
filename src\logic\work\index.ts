
import { computed, onMounted, reactive, ref } from "vue";
import { IPanelData, IWorkBar } from "./workModel";
import { useProcessStore } from "@/store/process";
import { useGetQuery } from "@/utils/request";
import { url } from "@/api/url";
import { useHomeStore } from "@/store/home";
import { useShortTimeArrys, useDayjs, useQuto, useLang, useQutoRange } from "@/utils";
import { eventBus } from "@/utils/eventBus";
import { ProcessService } from "../forms/processHelper";
import { useRoute } from 'vue-router'
import { useCore } from '@/store/core/index'



export function useWork() {
    const homeStore = useHomeStore()
    const processStore = useProcessStore()
    const readCount = computed(() => homeStore.getReadCount)
    const workCount = computed(() => homeStore.getWorkCount)
    const activeFilter = ref<string>('Year')
    const searchKwd = ref<string>('')
    const perType = ref<string>('Year')
    const searchMain: Record<string, any> = reactive({})
    const route = useRoute()
    const showUserCom = ref<boolean>(false) //YZ +
    const core = useCore()
    const lang = core.$lang
    const topBar = ref<Array<IWorkBar>>([
        // { name: 'AwaitWork', text: useLang('Work.AwaitWork') }, //这里不要使用useLang，否则翻译会有问题，用 t('')
        { name: 'AwaitWork', text: 'Work.AwaitWork' },
        { name: 'NotifyWork', text: 'Work.NotifyWork' },
        { name: 'CompleteWork', text: 'Work.CompleteWork' },
        { name: 'ApplyWork', text: 'Work.ApplyWork' },
    ])
    const active = computed({
        get() {
            return processStore.getTabName
        },
        set(val: string) {
            processStore.setTabName(val)
        }
    })
    const onTabChange = async (val: string) => {
        onResetClick()
        clearKwd()
        const service = new ProcessService()
        await service.getWorkCount()
    }
    const searchShow = ref<boolean>(false)
    const searchShow_ = ref<boolean>(false)
    const onSearch = () => {
        searchShow.value = true
    }
    const onSearch_ = () => {
        searchShow_.value = true
    }
    const onCloseSearch = () => {
        searchShow.value = false
    }
    const onCloseSearch_ = () => {
        searchShow_.value = false
    }

    const onMSearch = () => {
        clearSearchMain()
        const queryType = route.query?.type && route.query?.type !== 'ApplyWork'
        // console.log("%c [ route.query?.type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route.query?.type)
        // console.log("%c [ queryType ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", queryType)
        searchMain['keySearch'] = true
        searchMain['higthSearch'] = false
        searchMain['searchType'] = 'QuickSearch'
        searchMain['kwd'] = searchKwd.value //queryType ? route.query?.type : searchKwd.value
        if (active.value === 'ApplyWork' || active.value === 'CompleteWork') {
            searchMain["Year"] = new Date().getFullYear()
        }
        // console.log("%c [ searchMain ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchMain)

        // 执行查询
        eventBus.emit(active.value, true, searchMain)
        // eventBus.emit(queryType ? route.query?.type : active.value, true, searchMain)
    }
    const time = new Date()
    // 查询面板数据
    const panelData = reactive<IPanelData>({
        year: time.getFullYear(),
        month: time.getMonth() + 1,
        quto: useQuto(time),
        day: '',
        processname: '',
        taskid: '',
        sn: '',
        kwd: '',
        postUser: '', //YZ +
        // pn: '', //新增 1
        // contractor: '', //新增 2
        // equipment: '', //新增 3
    })
    const searchPost = reactive({
        HistoryTaskType: '',
        ProcessName: '',
        PostDateType: '',
        Year: 0,
        byYear: 1,
        TaskID: '',
        searchType: 'AdvancedSearch',
        kwd: '',
        SerialNum: '',
        PostDate1: '',
        PostDate2: '',
    })
    const currentDate = useShortTimeArrys(new Date())
    const onSelectChange = (val: string) => {
        console.log("%c [onSelectChange val ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", val)
        perType.value = val
    }
    const timeShow = ref<boolean>(false)
    const timeClick = () => {
        timeShow.value = true
    }
    const onTimeConfirm = (val: any) => {
        if (val) {
            panelData.day = val.selectedValues.join('-')
        }
        timeShow.value = false
    }
    const buildSearch = () => {
        searchMain['SerialNum'] = panelData.sn
        searchMain['TaskID'] = panelData.taskid // d
        searchMain['kwd'] = panelData.kwd
        searchMain['processName'] = panelData.processname
        searchMain['ProcessName'] = panelData.processname
        searchMain['PostUserAccount'] = panelData.postUser //YZ +
        searchMain['searchType'] = 'AdvancedSearch'
        searchMain['TaskStatus'] = 'all'
        // searchMain['pn'] = panelData.pn //新增
        // searchMain['contractor'] = panelData.contractor //新增
        // searchMain['equipment'] = panelData.equipment //新增

        switch (perType.value) {
            case 'Year':
                searchMain['Year'] = panelData.year
                searchMain['PostDateType'] = 'period'
                searchMain['PostDate1'] = `${panelData.year}-01-01T00:00:00`
                searchMain['PostDate2'] = `${panelData.year + 1}-01-01T00:00:00`
                break;
            case 'Month':
                searchMain['Year'] = panelData.year
                searchMain['PostDateType'] = 'period'
                const smonth = panelData.month >= 10 ? panelData.month : '0' + panelData.month
                const emonth = (panelData.month + 1) >= 10 ? (panelData.month + 1) : '0' + (panelData.month + 1)
                searchMain['PostDate1'] = `${panelData.year}-${smonth}-01T00:00:00`
                searchMain['PostDate2'] = `${panelData.year}-${emonth}-01T00:00:00`
                break;
            case 'Quarter':
                const { PostDate1, PostDate2 } = useQutoRange(panelData.quto, panelData.year)
                searchMain['PostDate1'] = PostDate1
                searchMain['PostDate2'] = PostDate2
                searchMain['Year'] = panelData.year
                searchMain['PostDateType'] = 'period'
                break;
            case 'Date':
                const dayjs = useDayjs(panelData.day)
                const enddate = dayjs.add(1, 'day').format('YYYY-MM-DD')
                searchMain['PostDate1'] = `${panelData.day}T00:00:00`
                searchMain['PostDate2'] = `${enddate}T00:00:00`
                searchMain['PostDateType'] = 'day'
                searchMain['Year'] = dayjs.year()
                break;
        }
    }
    const onSearchClick = () => {
        console.log('查询 11111')

        buildSearch()

        searchMain['resert'] = false
        // 执行查询
        eventBus.emit(active.value, true, searchMain)
        console.log("%c [ --searchMain ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchMain)
        console.log("%c [ --active.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", active.value)
        searchShow.value = false
    }
    const onSearchClick_ = () => {
        buildSearch()
        searchMain['resert'] = false
        // 执行查询_
        eventBus.emit(active.value, true, searchMain)
        searchShow_.value = false
    }
    const clearSearchMain = () => {
        Object.keys(searchMain).forEach((key: string) => {
            searchMain[key] = ''
        })
        panelData.year = time.getFullYear()
        panelData.month = time.getMonth() + 1
        panelData.quto = useQuto(time)
        panelData.day = ''
        panelData.processname = ''
        panelData.taskid = ''
        panelData.sn = ''
        panelData.kwd = ''
        panelData.postUser = '' //YZ +
        // panelData.pn = '' //新增
        // panelData.contractor = '' //新增
        // panelData.equipment = '' //新增


    }
    const onResetClick = () => {
        clearSearchMain()
        buildSearch()
        searchMain['resert'] = true
        eventBus.emit(active.value, true, searchMain)
        searchShow.value = false
    }
    const onResetClick_ = () => {
        clearSearchMain()
        buildSearch()
        searchMain['resert'] = true
        eventBus.emit(active.value, true, searchMain)
        searchShow_.value = false
    }
    const clearKwd = () => {
        searchKwd.value = ''
    }
    const SelectUser = () => {
        showUserCom.value = true
    }
    const selectPN = () => {
        showUserCom.value = true
    }
    const selectEquipment = () => {
        showUserCom.value = true
    }
    const userSelectSave = (users: any) => { // YZ +
        panelData.postUser = users.map((el: any) => el.account).join(',')
    }

    onMounted(async () => {
        if (route.query?.type) { // === ApplyWork, Preliminary, Daily, Monthly, Quarterly
            // console.log("%c [ --- route.query?.type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route.query?.type)
            processStore.setTabName(route.query.type as any)
        } else {
            processStore.setTabName(topBar.value[0].name)
        }
        // const { total } = await useGetQuery(url.work.getWorkListCount)
        // console.log("%c [ 获取待办数量 3 total ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", total)

        // const { total: ReadCount } = await useGetQuery(url.work.getWorkReadCount)
        // console.log("%c [ 待阅 3 ReadCount ]", "font-size:13px; background:linear-gradient(to right ,rgb(83, 66, 112), #462188); color:yellow;", ReadCount)
        // homeStore.setAllCount(total + ReadCount)
        // homeStore.setReadCount(ReadCount)
        // homeStore.setWorkCount(total)
    })
    return {
        topBar,
        active,
        readCount,
        workCount,
        searchShow,
        searchShow_,
        onSearch,
        onSearch_,
        searchPost,
        onSelectChange,
        timeShow,
        timeClick,
        onTimeConfirm,
        currentDate,
        perType,
        panelData,
        onTabChange,
        onMSearch,
        searchKwd,
        onCloseSearch,
        onCloseSearch_,
        activeFilter,
        onSearchClick,
        onSearchClick_,
        searchMain,
        clearKwd,
        onResetClick,
        onResetClick_,
        SelectUser,
        selectPN,
        selectEquipment,
        showUserCom,
        userSelectSave
    }
    // SelectUser,   //YZ +
    // showUserCom,  //YZ +
    // userSelectSave//YZ +
}

