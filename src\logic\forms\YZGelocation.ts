import { useLang } from "@/utils";
import { Y<PERSON><PERSON>onfig, Y<PERSON><PERSON><PERSON>, YZ<PERSON>ty<PERSON> } from "./formModel";
export class YZGelocationConfig extends YZConfig {
    private limitMap: boolean;
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZGelocation'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.limitMap = false
        if (parmas['extends'].vicinity)
            this.limitMap = true
        // console.log('地图',parmas)
    }
    getLimit(): boolean {
        return this.limitMap
    }
    getMap(): any {
        return this.extends['$map']
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZGelocationStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZGelocation extends YZField {
    constructor(params: any) {
        super(params)
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZGelocationConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZGelocationStyle(style)
    }
}