window.clientHelper= {
    
    /**
     * 
     * @param {表单内全部字段} fields 
     * @param {字段代码} model 
     * @returns 返回当前字段绑定的值,如果字段不存在，将返回null
     */
    getFieldModelValue(fields,model) {
      const item = fields.find(x=>x.vModel.model === model)
      if (item) {
         return item.vModel.value 
      }
      return null
    },
    /**
     * 
     * @param {表单内全部字段} fields 
     * @param {字段代码} model 
     * @returns 返回当前字段绑定值的optionValue，如果不存在，将返回null
     */
    getFieldOptionValue(fields,model) {
        const item = fields.find(x=>x.vModel.model === model)
        if (item) {
           return item.vModel.optionValue 
        }
        return null
    },
    /**
     * 
     * @param {表单内全部字段} fields 
     * @param {字段代码} model 
     * @returns 返回指定字段的绑定信息,如果不存在，将返回null
     * 
     */
    getFieldModel(fields,model) {
        const item = fields.find(x=>x.vModel.model === model)
        if (item) {
           return item.vModel
        }
        return null
    },
        /**
     * 
     * @param {表单内全部字段} fields 
     * @param {字段代码} model 
     * @returns 返回指定字段的实例
     * 
     */
    getFieldInstance(fields,model) {
        const item = fields.find(x=>x.vModel.model === model)
        if (item) {
           return item
        }
        return null
    },
    /**
     * 
     * @param {表单内全部字段} fields 
     * @param {字段代码} model 
     * @param {需要设置的值} value 
     */
    setFieldModelValue(fields,model,value) {
        const item = fields.find(x=>x.vModel.model === model)
        if (item) {
            item.vModel.value = value 
        }
    },
       /**
     * 
     * @param {表单内全部字段} fields 
     * @param {字段代码} model 
     * @param {需要设置的值} value 
     */
    setFieldOptionValue(fields,model,value) {
        const item = fields.find(x=>x.vModel.model === model)
        if (item) {
            if(item.config.ctype === 'YZStepper'){
                item.vModel.value  = value 
            }
            item.vModel.optionValue = value 
        }
    }
}