<template>
    <div>
    </div>
</template>

<script lang="ts" setup>
import * as dd from 'dingtalk-jsapi'; // 此方式为整体加载，也可按需进行加载
import { url } from '@/api/url';
import { IUserInfo } from '@/logic/login/loginModel';
import { useLoingStore } from '@/store/login';
import { useUserStore } from '@/store/users';
import { useParamsFormatter, useQueryHashParams, useQueryParams } from '@/utils';
import { useGetQuery, usePostBody } from '@/utils/request';
import { showLoadingToast, showNotify } from 'vant';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useProcessStore } from '@/store/process';
import { pi } from 'mathjs';
const processStore = useProcessStore()
const loginStore = useLoingStore()
const userStore = useUserStore()
const router = useRouter()
let loading: any = {}
onMounted(async () => {
    loading = showLoadingToast({
        message: '正在进行身份认证...',
        forbidClick: true,
        duration: 0
    });
    if (loginStore.getToken) {
        loading.close()
        console.log('存在token,代码31')
        buildParamsToForm()
    } else {
        dd.ready(function () {
            dd.runtime.permission.requestAuthCode({
                corpId: window.webConfig.dingtalk.corpId
            }).then(result => {
                console.log('进入39行', loading)
                if (result.code) {
                    const newUrl = useParamsFormatter(url.login.getDingTalkUser, {
                        params1: result.code
                    })
                    useGetQuery(newUrl)
                        .then(result => {
                            if (result.success) {
                                buidlSignature()
                                    .then(auth => {
                                        if (auth) {
                                            // 注册sdk
                                            buildDingTalkSdk(auth, function (val: boolean) {
                                                if (val) {
                                                    loginStore.ssoLogin(result.auth, 'dd')
                                                }
                                            })
                                            loading.close()
                                        }
                                    }).catch(error => {
                                        alert('服务端验证签名失败:' + JSON.stringify(error))
                                        loading.close()
                                    })
                                loading.close()
                            } else {
                                showNotify({ type: 'danger', message: '平台认证失败: ' + result.errorMessage })
                                loading.close()
                            }
                        })
                } else {
                    loading.close()
                }
            }).catch(errror => {
                alert("授权失败" + JSON.stringify(errror))
                loading.close()
            })
        });
    }

})
const buildJwtToken = async ({ AppkeyName, AccessToken, Account }: any) => {
    const { success, token, errorMessage } = await usePostBody(url.login.thirdAuth, {
    }, {
        appKeyName: AppkeyName,
        AccessToken: AccessToken,
        Account: Account
    }, false)
    console.log("%c [ buildJwtToken token ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", token)
    if (!success) {
        showNotify({
            type: 'danger',
            message: errorMessage
        })
    } else {
        // 设置实际token 
        loginStore.setToken(token)
        //获取用户信息
        const { success, user } = await useGetQuery(url.login.getUserInfo, false)
        if (success) {
            userStore.setUserInfo(user as IUserInfo)
            loading.close()
            router.push({ path: '/layout/home', replace: true })
        }
    }
}
const buidlSignature = async () => {
    const urls = window.location.href.split('#')[0]
    const { success, timestamp, signature, nonceStr, errorMessage } = await useGetQuery(url.login.getDingTalkSdk, {
        url: urls
    }, false)

    if (!success) {
        showNotify({ type: 'danger', message: errorMessage });
        return null
    }
    return {
        timestamp,
        signature,
        nonceStr
    }
}
const buildDingTalkSdk = ({ timestamp, signature, nonceStr }: any, fn: Function): void => {
    dd.config({
        agentId: window.webConfig.dingtalk.agentid, // 必填，微应用ID
        corpId: window.webConfig.dingtalk.corpId,//必填，企业ID
        timeStamp: timestamp, // 必填，生成签名的时间戳
        nonceStr: nonceStr, // 必填，自定义固定字符串。
        signature: signature, // 必填，签名
        type: 0,   //选填。0表示微应用的jsapi,1表示服务窗的jsapi；不填默认为0。该参数从dingtalk.js的0.8.3版本开始支持
        jsApiList: [
            'biz.util.chooseImage',
            'biz.util.uploadFile',
            'biz.util.compressImage'
        ] // 必填，需要使用的jsapi列表，注意：不要带dd。
    })
    dd.ready(function () {
        fn(true)
    })
    dd.error(function (error) {
        fn(false)
    })
}
const buildParamsToForm = async () => {

    const data = await userStore.setUserInfoAsync();
    if (data) {

        let app = useQueryHashParams("app")
        if (!app) {
            app = useQueryParams("app")
        }

        if (app) {
            processStore.setHideTabHeader(true)
            if (app === 'process') {
                // 待办

                let pid = useQueryHashParams("pid")
                if (!pid) {
                    pid = String(useQueryParams("pid"))
                    processStore.setStepId(pid)
                    router.push({ path: '/layout/reform', replace: true })
                }


            } else if (app === 'read' || app === 'openTask') {
                const appType = app == 'read' ? app : 'read'
                // 已办
                let tid = useQueryHashParams("tid")
                if (!tid)
                    tid = String(useQueryParams("tid"))
                processStore.setTaskId(tid)
                processStore.setLoadType(appType) //原本app
                router.push({ path: '/layout/reform', replace: true })
            }
        } else {
            router.push({ path: '/layout/home', replace: true })
        }
    } else {
        showNotify({
            type: 'danger',
            message: '无法获取用户信息'
        })
    }

}
</script>
<style scoped></style>