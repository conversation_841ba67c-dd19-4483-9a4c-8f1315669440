<template>
    <div>
        <van-field v-if="!field.hidden" v-model="vModel" is-link :name="field.vModel.model"
            :required="field.config.required" :placeholder="field.placeholder" readonly :disabled="field.disabled"
            :rules="field.config.rules" :label="field.config.label" @click="onDateClick" />
        <van-popup v-model:show="dateShow" position="bottom" v-if="config.getType() != 'Hi'">
            <van-picker-group title="日期选择" :tabs="config.getTabs()" @confirm="onDateConfirm" @cancel="onDateCancle"
                :next-step-text="config.getNextText()">
                <van-date-picker v-model="currentDate" :min-date="config.minDateValue()" :readonly="field.readonly"
                    :max-date="config.maxDateValue()" :columns-type="config.getFormatter()" />
                <van-time-picker v-model="currentTime" />
            </van-picker-group>
        </van-popup>

        <!-- 时间选择 -->
        <van-popup v-model:show="dateShow" position="bottom" v-if="config.getType() === 'Hi'">
            <van-time-picker v-model="currentTime" @confirm="onDateConfirm" @cancel="onDateCancle" />
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { YZDatePickerConfig, YZDatePicker } from '@/logic/forms/YZDatePicker';
import { useDayjs, useShortTime, useShortTimeArrys } from '@/utils';
import { eventBus } from '@/utils/eventBus';
import dayjs from 'dayjs';
import { PickerOption } from 'vant';
import { onMounted } from 'vue';
import { computed, PropType, ref } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZDatePicker>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue', 'update:optionValue'])
const config = props.field.config as YZDatePickerConfig
const dateShow = ref<boolean>(config.getShow())
const time = useShortTimeArrys(new Date()) as Array<string>
const currentDate = ref(time)
const currentTime = ref([''])
onMounted(() => {
    const type = config.getType()
    switch (type) {
        case 'Ym':
            currentDate.value = [time[0], time[2]]
            break;
    }
})
const onDateClick = () => {
    if (!props.field.disabled) {
        config.setShow(true)
        dateShow.value = true
    }

}
const onDateConfirm = (val: any) => {
    if (val && val.length > 0) {
        const timeValues: string[] = []
        val.forEach(({ selectedValues }: any) => {
            let value = selectedValues.join('/')
            if (selectedValues.length === 2 && val.length > 1) {
                value = selectedValues.join(':')
            }
            timeValues.push(value)
        })
        vModel.value = timeValues.join(' ')
        //emit('update:modelValue', timeValues.join(' '))
        emit('update:optionValue', timeValues.join(' '))
    } else {
        const { selectedValues } = val
        let value = selectedValues.join(':')
        vModel.value = value
        //  emit('update:modelValue',value)
        emit('update:optionValue', value)
    }
    setFun() //YZ +
    onDateCancle()
}
const Onformatter = () => {

}
const onDateCancle = () => {
    config.setShow(false)
    dateShow.value = false
}
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (typeof params.value === 'object') {
                const dayjs = useDayjs(params.value)
                const value = dayjs.format(config.getFormatterType())
                props.field.vModel.value = String(value)
                props.field.vModel.optionValue = String(value)
            } else {
                props.field.vModel.value = String(params.value)
                props.field.vModel.optionValue = String(params.value)
            }
            setFun()
        }
    }
})

eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
eventBus.on('onMap', {
    cb: function (params) {
        console.log("%c [ params 3]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
            const dayjs = useDayjs(params.value)
            const value = dayjs.format(config.getFormatterType())
            props.field.vModel.value = String(value)
            props.field.vModel.optionValue = String(value)
            setFun()

        }
    }
})
</script>
<style scoped></style>