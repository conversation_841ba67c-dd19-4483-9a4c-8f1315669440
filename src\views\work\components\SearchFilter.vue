<template>
    <div>
        <div class="fiter-day">
            <div :class="[selectKey === item.value ? 'day-div-active' : '', 'day-div']" v-for="item in options"
                :key="item.value" @click="$emit('onSelectChange', item.value)">
                {{ $t(item.name) }}
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';

defineEmits<{
    onSelectChange: [value: string]
}>()
defineProps({
    selectKey: {
        type: String,
        default: 'Year',
        required: true
    }
})
const options: Array<any> = reactive([
    { name: 'Work.Year', value: 'Year' },
    { name: 'Work.Month', value: 'Month' },
    { name: 'Work.Quarter', value: 'Quarter' },
    { name: 'Work.Date', value: 'Date' }
])
</script>
<style lang="scss" scoped>
.fiter-day {
    width: 100%;
    height: 45px;
    background: var(--yz-div-background);
    overflow-y: hidden;
    overflow-x: auto;
    display: flex;
    padding-bottom: 5px;

    .day-div {
        background: #F2F3F4;
        border-radius: 17px;
        text-align: center;
        // width: 22%; //-
        flex: 1;
        flex-wrap: wrap;
        height: 24px;
        float: left;
        margin: 10px;
        margin-left: 8px;
        line-height: 24px;
        font-family: PingFangSC-Regular;
        font-size: var(--yz-work-daydiv);
        color: #65717C;
        font-weight: 400;
        flex-shrink: 0;
    }

    .day-div-active {
        background: #E8EFFF;
        border-radius: 17px;
    }
}
</style>