<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
        <span class="sub-title">彩色字体</span>
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3722765" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">通讯录2</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">加号</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">加号-copy</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">个人中心</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">个人中心-copy</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">个人中心-copy-copy</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">代填</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">待办</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">更多_面性</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">签名</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">减号</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">拒绝</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">收起</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">手机</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">首页-面性-1</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">释放</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">下属头像</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">编号</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">提醒</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">用户名</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">退回</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">向上</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">同意</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">通讯录</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">委托</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">向下1</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">英文</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">中文</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">生日</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">展开</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">收藏1</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">邮箱</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">任务流程</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">制度</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">性别</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">账号</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">追踪</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">领导头像</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">收藏2</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">首页-面性-1-copy</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">首页-面性-1-copy</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">向下1-copy</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">向下1-copy</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">减号-copy</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">向上-copy</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">向上-copy</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">筛选-copy</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">更多_面性-copy</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">更多_面性-copy</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: 
       url('iconfont.woff2?t=1669598580376') format('woff2'),
       url('iconfont.woff?t=1669598580376') format('woff'),
       url('iconfont.ttf?t=1669598580376') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo1"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icon-gengduo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongxunlu2"></span>
            <div class="name">
              通讯录2
            </div>
            <div class="code-name">.icon-tongxunlu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiahao"></span>
            <div class="name">
              加号
            </div>
            <div class="code-name">.icon-jiahao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiahao-copy"></span>
            <div class="name">
              加号-copy
            </div>
            <div class="code-name">.icon-jiahao-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenzhongxin"></span>
            <div class="name">
              个人中心
            </div>
            <div class="code-name">.icon-gerenzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenzhongxin-copy"></span>
            <div class="name">
              个人中心-copy
            </div>
            <div class="code-name">.icon-gerenzhongxin-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenzhongxin-copy-copy"></span>
            <div class="name">
              个人中心-copy-copy
            </div>
            <div class="code-name">.icon-gerenzhongxin-copy-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daitian"></span>
            <div class="name">
              代填
            </div>
            <div class="code-name">.icon-daitian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daiban"></span>
            <div class="name">
              待办
            </div>
            <div class="code-name">.icon-daiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo_mianxing"></span>
            <div class="name">
              更多_面性
            </div>
            <div class="code-name">.icon-gengduo_mianxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qianming"></span>
            <div class="name">
              签名
            </div>
            <div class="code-name">.icon-qianming
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianhao"></span>
            <div class="name">
              减号
            </div>
            <div class="code-name">.icon-jianhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icon-gengduo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jujue"></span>
            <div class="name">
              拒绝
            </div>
            <div class="code-name">.icon-jujue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouqi"></span>
            <div class="name">
              收起
            </div>
            <div class="code-name">.icon-shouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mima"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.icon-mima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouji"></span>
            <div class="name">
              手机
            </div>
            <div class="code-name">.icon-shouji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye-mianxing-1"></span>
            <div class="name">
              首页-面性-1
            </div>
            <div class="code-name">.icon-shouye-mianxing-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shifang"></span>
            <div class="name">
              释放
            </div>
            <div class="code-name">.icon-shifang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiashutouxiang"></span>
            <div class="name">
              下属头像
            </div>
            <div class="code-name">.icon-xiashutouxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianhao"></span>
            <div class="name">
              编号
            </div>
            <div class="code-name">.icon-bianhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tixing"></span>
            <div class="name">
              提醒
            </div>
            <div class="code-name">.icon-tixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yonghuming"></span>
            <div class="name">
              用户名
            </div>
            <div class="code-name">.icon-yonghuming
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuihui"></span>
            <div class="name">
              退回
            </div>
            <div class="code-name">.icon-tuihui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangshang"></span>
            <div class="name">
              向上
            </div>
            <div class="code-name">.icon-xiangshang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongyi"></span>
            <div class="name">
              同意
            </div>
            <div class="code-name">.icon-tongyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongxunlu"></span>
            <div class="name">
              通讯录
            </div>
            <div class="code-name">.icon-tongxunlu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weituo"></span>
            <div class="name">
              委托
            </div>
            <div class="code-name">.icon-weituo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangxia1"></span>
            <div class="name">
              向下1
            </div>
            <div class="code-name">.icon-xiangxia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingwen"></span>
            <div class="name">
              英文
            </div>
            <div class="code-name">.icon-yingwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongwen"></span>
            <div class="name">
              中文
            </div>
            <div class="code-name">.icon-zhongwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shengri"></span>
            <div class="name">
              生日
            </div>
            <div class="code-name">.icon-shengri
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhankai"></span>
            <div class="name">
              展开
            </div>
            <div class="code-name">.icon-zhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang1"></span>
            <div class="name">
              收藏1
            </div>
            <div class="code-name">.icon-shoucang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youxiang"></span>
            <div class="name">
              邮箱
            </div>
            <div class="code-name">.icon-youxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.icon-xiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-renwuliucheng"></span>
            <div class="name">
              任务流程
            </div>
            <div class="code-name">.icon-renwuliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhidu"></span>
            <div class="name">
              制度
            </div>
            <div class="code-name">.icon-zhidu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingbie"></span>
            <div class="name">
              性别
            </div>
            <div class="code-name">.icon-xingbie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhanghao"></span>
            <div class="name">
              账号
            </div>
            <div class="code-name">.icon-zhanghao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuizong"></span>
            <div class="name">
              追踪
            </div>
            <div class="code-name">.icon-zhuizong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lingdaotouxiang"></span>
            <div class="name">
              领导头像
            </div>
            <div class="code-name">.icon-lingdaotouxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang2"></span>
            <div class="name">
              收藏2
            </div>
            <div class="code-name">.icon-shoucang2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye-mianxing-1-copy"></span>
            <div class="name">
              首页-面性-1-copy
            </div>
            <div class="code-name">.icon-shouye-mianxing-1-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye-mianxing-1-copy1"></span>
            <div class="name">
              首页-面性-1-copy
            </div>
            <div class="code-name">.icon-shouye-mianxing-1-copy1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangxia1-copy"></span>
            <div class="name">
              向下1-copy
            </div>
            <div class="code-name">.icon-xiangxia1-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangxia1-copy1"></span>
            <div class="name">
              向下1-copy
            </div>
            <div class="code-name">.icon-xiangxia1-copy1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianhao-copy"></span>
            <div class="name">
              减号-copy
            </div>
            <div class="code-name">.icon-jianhao-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangshang-copy"></span>
            <div class="name">
              向上-copy
            </div>
            <div class="code-name">.icon-xiangshang-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangshang-copy1"></span>
            <div class="name">
              向上-copy
            </div>
            <div class="code-name">.icon-xiangshang-copy1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shaixuan-copy"></span>
            <div class="name">
              筛选-copy
            </div>
            <div class="code-name">.icon-shaixuan-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo_mianxing-copy"></span>
            <div class="name">
              更多_面性-copy
            </div>
            <div class="code-name">.icon-gengduo_mianxing-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo_mianxing-copy1"></span>
            <div class="name">
              更多_面性-copy
            </div>
            <div class="code-name">.icon-gengduo_mianxing-copy1
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo1"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon-gengduo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongxunlu2"></use>
                </svg>
                <div class="name">通讯录2</div>
                <div class="code-name">#icon-tongxunlu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiahao"></use>
                </svg>
                <div class="name">加号</div>
                <div class="code-name">#icon-jiahao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiahao-copy"></use>
                </svg>
                <div class="name">加号-copy</div>
                <div class="code-name">#icon-jiahao-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenzhongxin"></use>
                </svg>
                <div class="name">个人中心</div>
                <div class="code-name">#icon-gerenzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenzhongxin-copy"></use>
                </svg>
                <div class="name">个人中心-copy</div>
                <div class="code-name">#icon-gerenzhongxin-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenzhongxin-copy-copy"></use>
                </svg>
                <div class="name">个人中心-copy-copy</div>
                <div class="code-name">#icon-gerenzhongxin-copy-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daitian"></use>
                </svg>
                <div class="name">代填</div>
                <div class="code-name">#icon-daitian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daiban"></use>
                </svg>
                <div class="name">待办</div>
                <div class="code-name">#icon-daiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo_mianxing"></use>
                </svg>
                <div class="name">更多_面性</div>
                <div class="code-name">#icon-gengduo_mianxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qianming"></use>
                </svg>
                <div class="name">签名</div>
                <div class="code-name">#icon-qianming</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianhao"></use>
                </svg>
                <div class="name">减号</div>
                <div class="code-name">#icon-jianhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon-gengduo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jujue"></use>
                </svg>
                <div class="name">拒绝</div>
                <div class="code-name">#icon-jujue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouqi"></use>
                </svg>
                <div class="name">收起</div>
                <div class="code-name">#icon-shouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mima"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#icon-mima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouji"></use>
                </svg>
                <div class="name">手机</div>
                <div class="code-name">#icon-shouji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye-mianxing-1"></use>
                </svg>
                <div class="name">首页-面性-1</div>
                <div class="code-name">#icon-shouye-mianxing-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shifang"></use>
                </svg>
                <div class="name">释放</div>
                <div class="code-name">#icon-shifang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiashutouxiang"></use>
                </svg>
                <div class="name">下属头像</div>
                <div class="code-name">#icon-xiashutouxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianhao"></use>
                </svg>
                <div class="name">编号</div>
                <div class="code-name">#icon-bianhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tixing"></use>
                </svg>
                <div class="name">提醒</div>
                <div class="code-name">#icon-tixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yonghuming"></use>
                </svg>
                <div class="name">用户名</div>
                <div class="code-name">#icon-yonghuming</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuihui"></use>
                </svg>
                <div class="name">退回</div>
                <div class="code-name">#icon-tuihui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshang"></use>
                </svg>
                <div class="name">向上</div>
                <div class="code-name">#icon-xiangshang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongyi"></use>
                </svg>
                <div class="name">同意</div>
                <div class="code-name">#icon-tongyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongxunlu"></use>
                </svg>
                <div class="name">通讯录</div>
                <div class="code-name">#icon-tongxunlu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weituo"></use>
                </svg>
                <div class="name">委托</div>
                <div class="code-name">#icon-weituo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangxia1"></use>
                </svg>
                <div class="name">向下1</div>
                <div class="code-name">#icon-xiangxia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingwen"></use>
                </svg>
                <div class="name">英文</div>
                <div class="code-name">#icon-yingwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongwen"></use>
                </svg>
                <div class="name">中文</div>
                <div class="code-name">#icon-zhongwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shengri"></use>
                </svg>
                <div class="name">生日</div>
                <div class="code-name">#icon-shengri</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhankai"></use>
                </svg>
                <div class="name">展开</div>
                <div class="code-name">#icon-zhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang1"></use>
                </svg>
                <div class="name">收藏1</div>
                <div class="code-name">#icon-shoucang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youxiang"></use>
                </svg>
                <div class="name">邮箱</div>
                <div class="code-name">#icon-youxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#icon-xiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-renwuliucheng"></use>
                </svg>
                <div class="name">任务流程</div>
                <div class="code-name">#icon-renwuliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhidu"></use>
                </svg>
                <div class="name">制度</div>
                <div class="code-name">#icon-zhidu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingbie"></use>
                </svg>
                <div class="name">性别</div>
                <div class="code-name">#icon-xingbie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhanghao"></use>
                </svg>
                <div class="name">账号</div>
                <div class="code-name">#icon-zhanghao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuizong"></use>
                </svg>
                <div class="name">追踪</div>
                <div class="code-name">#icon-zhuizong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lingdaotouxiang"></use>
                </svg>
                <div class="name">领导头像</div>
                <div class="code-name">#icon-lingdaotouxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang2"></use>
                </svg>
                <div class="name">收藏2</div>
                <div class="code-name">#icon-shoucang2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye-mianxing-1-copy"></use>
                </svg>
                <div class="name">首页-面性-1-copy</div>
                <div class="code-name">#icon-shouye-mianxing-1-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye-mianxing-1-copy1"></use>
                </svg>
                <div class="name">首页-面性-1-copy</div>
                <div class="code-name">#icon-shouye-mianxing-1-copy1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangxia1-copy"></use>
                </svg>
                <div class="name">向下1-copy</div>
                <div class="code-name">#icon-xiangxia1-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangxia1-copy1"></use>
                </svg>
                <div class="name">向下1-copy</div>
                <div class="code-name">#icon-xiangxia1-copy1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianhao-copy"></use>
                </svg>
                <div class="name">减号-copy</div>
                <div class="code-name">#icon-jianhao-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshang-copy"></use>
                </svg>
                <div class="name">向上-copy</div>
                <div class="code-name">#icon-xiangshang-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshang-copy1"></use>
                </svg>
                <div class="name">向上-copy</div>
                <div class="code-name">#icon-xiangshang-copy1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shaixuan-copy"></use>
                </svg>
                <div class="name">筛选-copy</div>
                <div class="code-name">#icon-shaixuan-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo_mianxing-copy"></use>
                </svg>
                <div class="name">更多_面性-copy</div>
                <div class="code-name">#icon-gengduo_mianxing-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo_mianxing-copy1"></use>
                </svg>
                <div class="name">更多_面性-copy</div>
                <div class="code-name">#icon-gengduo_mianxing-copy1</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
