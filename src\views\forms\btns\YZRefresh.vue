
<template>
    <div>
        <van-button  size="small"  hairline type="default" 
        :disabled="!btn.enable"
        style="height: 32px;padding: 0px 20px;">
            {{ btn.text }}
        </van-button>
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { eventBus } from '@/utils/eventBus';
import { PropType, ref } from 'vue';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const onClick=()=>{
    eventBus.emit('onSubmit',true,props.btn.text)
}
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var( --yz-btn-14);
    border-radius: 5px;
}
</style>