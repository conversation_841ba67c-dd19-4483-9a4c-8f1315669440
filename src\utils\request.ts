import axios, { AxiosRequestConfig } from 'axios';
import router from '@/router';
import { useLoingStore } from '@/store/login';
import store from '@/store';
import { showLoadingToast, showNotify } from 'vant';
import { useCore } from '@/store/core';
const loginStore = useLoingStore(store);
const coreStore = useCore(store);
let loading: any = null;
// 扩展原有的 AxiosRequestConfig 类型以支持新的属性
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  showError?: boolean; // 假设 showError 是布尔类型
}
axios.defaults.withCredentials = true // cf + 允许携带cookie
axios.interceptors.request.use(config => {
  if (config.url === 'customer/area.json') {
    config.baseURL = '';
  } else {
    config.baseURL =
      process.env.NODE_ENV === 'development'
        ? window.webConfig.devhost
        : window.webConfig.host;
  }
  config.headers['Authorization'] = 'Bearer ' + loginStore.getToken;
  config.headers['Accept-Language'] =
    coreStore.$lang == 'en' ? 'en-US' : coreStore.$lang;

  return config;
});
axios.interceptors.response.use(
  (result) => {
    let config = result.config as ExtendedAxiosRequestConfig
    switch (result.status) {
      case 200:
        if (result.data.errorMessage === '未授权') { //YZ +
          const agent = window.navigator.userAgent.toLocaleLowerCase();
          if (agent.indexOf('uni-app') > -1) {
            window.logOutApp();
          } else {
            loginStore.restart();
            sessionStorage.clear();
            router.push({ path: '/login', replace: true });
          }
          return false;
        }

        if (result.data && result.data.errorCode === 101) {
          localStorage.clear();
          loginStore.restart();
          setTimeout(() => {
            router.push({ path: '/login', replace: true });
          }, 1000);
        }
        if (result.data.success === false && config.showError) {
          // showNotify({ message: result.data.errorMessage });
          console.log('error-1', result.data.errorMessage)
          return result;
        } else {
          return result;
        }
      case 401:
        setTimeout(() => {
          router.push({ path: '/login', replace: true });
        }, 1000);
        break;
      case 501:
        break;
    }
    return result;
  },
  error => {
    // console.log("%c [ error 111]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
    const result = error.response;
    if (result?.status === 401) {
      showNotify({ message: '身份认证过期,请重新登录,系统将会在2s后跳转登录' });
      setTimeout(() => {
        loginStore.restart();
        sessionStorage.clear();
        window.location.reload();
      }, 2000);
    } else if (result?.status === 500) {
      return result;
    } else if (result?.status === 404) {
      const data = {
        code: 1,
        errorMessage: '接口未找到',
        success: undefined,
      };
      result.data = data;
      return result;
    } else if (result?.status === 400) {
      const data = {
        code: 1,
        errorMessage: '接口入参格式校验失败',
        success: undefined,
      };
      result.data = data;
      return result;
    } else {
      return result;
    }
  }
);
export const useRequest = (
  config: AxiosRequestConfig,
  showloading = false,
  showError = true
): Promise<any> => {
  if (showloading) {
    loading = showLoadingToast({
      message: '',
      forbidClick: true,
      duration: 0,
      className: 'axios-request',
    });
  }
  return new Promise((resolve, reject) => {
    axios
      .request({
        url: config.url,
        method: config.method,
        params: config.params,
        data: config.data,
        headers: config.headers,
        showError: showError,
        withCredentials: true //跨域
      } as ExtendedAxiosRequestConfig)
      .then(result => {
        if (loading) loading.close();
        resolve(result.data);
      })
      .catch(error => {
        if (loading) loading.close();
        reject(error);
      });
  });
};
export const useAxios = () => {
  return axios;
};
export const useGetQuery = (
  url: string,
  params?: any,
  showLoading: boolean = false,
  showError: boolean = true
): Promise<any> =>
  useRequest(
    {
      url: url,
      method: 'GET',
      params: params,
      withCredentials: true
    },
    showLoading,
    showError
  );
export const useGetBody = (
  url: string,
  params?: any,
  data?: any,
  showLoading: boolean = false,
  showError: boolean = true
): Promise<any> =>
  useRequest(
    {
      url: url,
      method: 'GET',
      params: params,
      data: data,
      withCredentials: true
    },
    showLoading,
    showError
  );
export const usePostQuery = (
  url: string,
  params?: any,
  showLoading: boolean = false,
  showError: boolean = true
): Promise<any> =>
  useRequest(
    {
      url: url,
      method: 'POST',
      params: params,
      withCredentials: true
    },
    showLoading,
    showError
  );
export const usePostBody = (
  url: string,
  params?: any,
  data?: any,
  showLoading: boolean = false,
  showError: boolean = true
): Promise<any> =>
  useRequest(
    {
      url: url,
      method: 'POST',
      params: params,
      data: data,
      withCredentials: true
    },
    showLoading,
    showError
  );

//van-loading__spinner
