<template>
    <div>
        <van-field v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required"
            :rules="field.config.rules" :readonly="field.disabled" :label="field.config.label">
            <template #input>
                <div>
                    <van-image @click="onPreview(findex)" width="80" height="80" :src="img" alt=""
                        v-for="(img, findex) in imageSrc" :key="img"
                        style="border:1px dashed #d9d2d2;float: left;position: relative;">
                        <div v-if="!field.readonly" class="remove-dot" @click.stop="onRemoveClick(img, findex)">
                            <van-icon name="cross" />
                        </div>
                        <template v-slot:error>加载失败</template>
                    </van-image>
                    <div class="image-update-div" v-for="(img, index) in fileData"
                        @click="onCustomerPerivew(img.previewUrl)" :key="img.fileId">
                        <img :src="img.content" alt="">
                        <div @click.stop="onCusometerRemove(img)" class="remove-dot">
                            <van-icon name="cross" />
                        </div>
                    </div>
                    <van-button v-if="!field.readonly" size="small" type="primary" @click="onData" icon="plus"
                        :disabled="field.disabled">{{
                            $t('UAUC.SelectPicture') }}</van-button>
                    <van-uploader ref="imageref" style="display: none;" v-if="!field.readonly" :after-read="afterRead"
                        v-model="vModel" :max-size="isOverSize" @delete="deleteFile" :before-read="beforeRead"
                        :accept="field.getAccept()" :multiple="field.getMultipe()" />
                </div>
            </template>
        </van-field>
        <van-action-sheet v-model:show="actionShow" :actions="actionData" cancel-text="取消" close-on-click-action
            @cancel="onActionCancel" @select="onActionSelect" />

        <!-- 标准版本预览 -->
        <!-- //YZ - -->
        <!-- <van-image-preview v-model:show="previewShow" :images="imageSrc" :doubleScale="true">
            <template #image="{ src }">
                <img :src="'/api' + src" style="width: 100%;" />
            </template>
        </van-image-preview> -->
    </div>
</template>

<script lang="ts" setup>
import { url } from '@/api/url';
import { YZImageUpload, YZImageUploadConfig } from '@/logic/forms/YZImageUpload';
import { useTaskProcess } from '@/logic/forms/formsutil';
// import { dataURLtoBlob,dataURLtoArrys  } from '@/utils/index'
import { useLoingStore } from '@/store/login';
import { eventBus } from '@/utils/eventBus';
import { usePostBody, useRequest } from '@/utils/request';
import { composition, number } from 'mathjs';
import { showImagePreview, showLoadingToast, showNotify, UploaderFileListItem } from 'vant';
import { onMounted } from 'vue';
import { watch } from 'vue';
import { computed, PropType, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useProcessStore } from '@/store/process/index'
import { dataURLtoBlob, dataURLtoArrys, dataToFile, useEnviroMent } from '@/utils/index'
import { JsonHubProtocol } from '@microsoft/signalr';
import * as dd from 'dingtalk-jsapi';
export interface IFileData {
    name: string;
    size: number;
    fileId: string;
    content: string;
    previewUrl: string;
}
const props = defineProps({
    field: {
        type: Object as PropType<YZImageUpload>,
        required: true
    },
    modelValue: {
        type: Array<UploaderFileListItem>,
        default: []
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue', 'update:optionValue'])
const yzconfig = props.field.config as YZImageUploadConfig
const size = yzconfig.getUnit()
const sizeMax = yzconfig.getSize()
const imageSrc = ref<Array<string>>([])
const imageSrcObj = ref<Array<any>>([])
const loginStore = useLoingStore()
const previewShow = ref<boolean>(false)
const route = useRoute()
const imageref = ref<any>()
const fileContent = ref<Array<string>>([])
const actionShow = ref<boolean>(false)
const actionData = ref<any>([
    // { name: '拍照' },
    { name: '上传照片' }
])
const actionType = computed(() => {
    return Number(route.query['actionType'])
})
const onData = () => {
    actionShow.value = true
}
const UploadBase64Image = (data: string) => {
    const newBase = data.indexOf("base64") > -1 ? data : 'data:image/jpeg;base64,' + data;
    const ext = '.jpeg';
    const fileName = new Date().getTime().toString() + ext
    console.log("%c [ fileName ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileName)
    usePostBody(url.form.updateBase64, {},
        {
            fileName: fileName,
            fileExt: ext,
            fileContent: newBase
        }).then(result => {
            if (result && result.length > 0) {
                result.forEach((item: any) => {
                    const id = item.fileId
                    builderOption(id)
                    builderFileData(item.fileName, item.size, id, newBase)
                })
            } else {
                console.log('error-7')
                showNotify({ type: 'danger', message: result.errorMessage })
            }
        }).catch(error => {
            alert('拍照失败: -2 ' + JSON.stringify(error))
        })
}
const onActionSelect = ({ name }: any) => {
    console.log("%c [ name 上传图片 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", name)
    const env = useEnviroMent()
    if (name === "拍照") {
        if (env.isComWeChat) {
            window.wx.chooseImage({
                count: 9, // 默认9
                sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['camera'], // 可以指定来源是相册还是相机，默认二者都有
                defaultCameraMode: "normal", //表示进入拍照界面的默认模式，目前有normal与batch两种选择，normal表示普通单拍模式，batch表示连拍模式，不传该参数则为normal模式。从3.0.26版本开始支持front和batch_front两种值，其中front表示默认为前置摄像头单拍模式，batch_front表示默认为前置摄像头连拍模式。（注：用户进入拍照界面仍然可自由切换两种模式）
                isSaveToAlbum: 1, //整型值，0表示拍照时不保存到系统相册，1表示自动保存，默认值是1
                success: function (res: any) {
                    var localIds = res.localIds as Array<string>
                    // 如出现上传丢失，请改用递归
                    if (localIds && localIds.length > 0) {
                        localIds.forEach(img => {
                            window.wx.getLocalImgData({
                                localId: img, // 图片的localID
                                success: function (res: any) {
                                    const imgData = res.localData
                                    UploadBase64Image(imgData)
                                }
                            });

                        })
                    }
                }
            });
        } else if (env.isDingTalk) {
            // dd.biz.util.chooseImage({
            //     count: 1,
            //     sourceType: ['camera']
            // })
            //     .then(result => {
            //         if (result && result.files.length > 0) {
            //           //  dingtalkUpload(result.filePaths)
            //         }
            //     }).catch(error => {
            //         alert('拍照失败:' + JSON.stringify(error))
            //     })
            const refs = imageref.value
            refs.chooseFile()
        } else if (env.isUniApp) {
            const andorid = parent as any
            console.log("%c [ andorid 上传图片 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", andorid)
            var cmr = andorid.plus.camera.getCamera();
            var res = cmr.supportedImageResolutions[0];
            var fmt = cmr.supportedImageFormats[0];
            cmr.captureImage(function (path: any) {
                getAppFileInfo(path);
            }, function () {
            }, { resolution: res, filename: 'file:///storage/emulated/0/Pictures', format: fmt });
        } else if (env.isFeiShu) {
            window.tt.chooseImage({
                sourceType: ['camera'],
                count: 1,
                sizeType: ['compressed'],
                cameraDevice: 'back',
                isSaveToAlbum: '0',
                success(res: any) {
                    getFileInfo(res.tempFilePaths[0])
                },
                fail(res: any) {
                    console.log(`chooseImage 调用失败`);
                }
            });
        } else {
            showNotify({ type: 'danger', message: '未知平台，请使用上传文件功能' })
        }
    } else {
        const refs = imageref.value
        refs.chooseFile()
    }
}
const getFileInfo = (filePath: string) => {
    // 读取文件内容
    const fileSystemManager = window.tt.getFileSystemManager();
    fileSystemManager.readFile({
        encoding: 'base64',
        filePath: filePath,
        success(res: any) {
            UploadBase64Image(res.data)
        },
        fail(res: any) {
            console.log(`readFile fail: ${JSON.stringify(res)}`);
        }
    });
}
const getAppFileInfo = (uri: string) => {
    const uniapp = parent as any
    uniapp.plus.io.getFileInfo({
        filePath: uri,
        digestAlgorithm: 'md5',
        fail: function (error: any) {
            console.log('getFileInfo-faild', error)
            alert(error.message);
            return false;
        },
        success: function (f: any) {
            const fileName = 'abc.jpg'
            var uploadurl = window.location.origin + url.form.updateAttach
            const loading = showLoadingToast({
                message: '',
                forbidClick: true,
                duration: 0,
                className: 'axios-request'
            });
            var task = uniapp.plus.uploader.createUpload(uploadurl,
                { method: "POST", priority: 100 },
                function (t: any, status: any) {
                    // 上传完成
                    if (status == 200) {
                        const res = eval("(" + t.responseText + ")");
                        if (res && res.length > 0) {
                            const fileData = res[0]
                            const id = fileData.fileId
                            const file = {
                                file: {
                                    name: fileData.fileName,
                                    size: fileData.size,

                                },
                                content: fileData.base64
                            }
                            loading?.close()
                            builderOption(id)
                            builderFileData(file.file.name, file.file.size, id, file.content)

                        } console.log('文件上传结果', res)
                    } else {
                        loading?.close()
                        console.log('文件上传失败ers', t)
                    }
                }
            );
            task.addFile(uri, { key: fileName });
            task.setRequestHeader('Cookie', document.cookie);
            task.start();

            // afterRead({ file: f })
        }
    });
}
const onActionCancel = () => {
    actionShow.value = false
}
const vModel = computed({
    get() {
        if (typeof props.modelValue === 'string')
            return []
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const optionData = computed({
    get() {
        return props.optionValue
    },
    set(val) {
        emit('update:optionValue', val)
    }
})
const isOverSize = (file: File): boolean => {
    let showValue = ''
    if (size === 'MB') {
        const value = file.size / 1024 / 1024
        const mbValue = Math.round((value) * 100) / 100
        if (sizeMax >= mbValue) {
            return false
        }
        showValue = mbValue + size
    } else if (size === 'GB') {
        const value = file.size / 1024 / 1024 / 1024
        const mbValue = Math.round((value) * 100) / 100
        if (sizeMax >= mbValue) {
            return false
        }
        showValue = mbValue + size
    } else if (size === "KB") {
        const value = file.size / 1024
        const mbValue = Math.round((value) * 100) / 100
        if (sizeMax >= mbValue) {
            return false
        }
        showValue = mbValue + size
    }
    showNotify({
        type: 'danger',
        message: `上传的文件大小超出限制，限制【${sizeMax}${size}】，实际文件【${showValue}】`
    })
    return true

}
const fileData = ref<Array<IFileData>>([])
const afterRead = (file: any): any => {
    const formData = new FormData()
    file.status = 'uploading';
    file.message = '上传中...';
    formData.append('file', file.file)
    props.field.setIsUpload(true)
    usePostBody(url.form.updateAttach, {}, formData, true)
        .then(result => {
            if (result && result.length > 0) {
                result.forEach((item: any) => {
                    file.status = 'done'
                    file.message = '上传完成'
                    const id = item.fileId
                    builderOption(id)
                    builderFileData(file.file.name, file.file.size, id, file.content)
                })
            } else {
                file.status = 'failed';
                file.message = '上传失败';
            }
            props.field.setIsUpload(false)
        }).catch(error => {
            file.status = 'failed';
            file.message = '上传失败';
            console.log('error -1 ', error)
            props.field.setIsUpload(false)
        })
}
const onCustomerPerivew = (url: string) => {
    showImagePreview({
        images: [url],
        onClose() {

        }
    })
}
const beforeRead = (file: any): any => {
    const item = fileData.value.find(x => x.name === file.name && x.size === file.size)
    if (item) {
        showNotify({
            type: 'danger',
            message: '请勿重复上传相同的文件'
        })
        return false
    }
    return true
}
const deleteFile = (file: any): any => {
    removeOption(file.file.name, file.file.size)
}
const builderOption = (fileId: string) => {
    const option = optionData.value
    if (option) {
        const value = option.split(',')
        value.push(fileId)
        const newFileId = value.join(',')
        emit('update:modelValue', value)
        emit('update:optionValue', newFileId)

    } else {
        emit('update:modelValue', [fileId])
        emit('update:optionValue', fileId)
    }
    setFun()
    if (!props.field.disabled)
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
}

const builderFileData = (name: string, size: number, fileId: string, content: string): void => {
    const json = {
        name: name,
        size: size,
        fileId: fileId,
        content: content,
        previewUrl: ''
    }
    const urls = dataURLtoBlob(json.content)
    json.previewUrl = URL.createObjectURL(urls)
    fileData.value.push(json)
}
const removeOption = (name: string, size: number): void => {
    const item = fileData.value.find(x => x.name === name && size === x.size)
    const option = optionData.value
    if (item) {
        if (option) {
            const optionArrys: Array<string> = option.split(',')
            const newOption = optionArrys.filter(x => x !== item.fileId)
            emit('update:optionValue', newOption.join(','))
            if (!props.field.disabled)
                props.field.expressTest(props.field, props.index, isFirstLoad.value)
        }
    }

}
const token = loginStore.getToken
const processStore = useProcessStore()
const isFirstLoad = ref<boolean>(true)
onMounted(async () => {
    // 暂时忽略mobileImageSource 跟魏老师在4.30确认
    // const imageSource = yzconfig.extends['mobileImageSource']
    //  if (imageSource && imageSource.length>0) {
    //     actionData.value = []
    //     imageSource.forEach((item:string)=>{
    //         if (item === 'photograph') {
    //             actionData.value.push({
    //                 name:'拍照'
    //             })
    //         }
    //         if (item === 'album') {
    //             actionData.value.push({
    //                 name:'上传照片'
    //             })
    //         }
    //     })
    //  }
    const params = await processStore.getAttachmentInfo(props.field.vModel.optionValue)
    if (params && params.length > 0) {
        for (let i = 0; i < params.length; i++) {
            const id = params[i].fileId
            const srcUrl = `bpm/attachment/file/${id}?access_token=${token}`
            imageSrc.value.push(srcUrl)
            imageSrcObj.value.push(params[i])
        }
    }
    emit('update:modelValue', imageSrc.value)
    setFun()
    isFirstLoad.value = false
})
const onPreview = (index) => {
    showImagePreview({
        images: imageSrc.value,
        startPosition: index
    })
}
const onRemoveClick = (img: string, index: number) => {
    //提取文件名称
    const endIndex = img.indexOf('?')
    const subStr = img.substring(0, endIndex)
    const lastIndex = subStr.lastIndexOf('/') + 1
    const fileName = subStr.substring(lastIndex)
    //移除文件
    imageSrc.value.splice(index, 1)
    //提取剩余文件
    const exitFiles = imageSrcObj.value.filter(x => x.fileId !== fileName)
    imageSrcObj.value = exitFiles
    optionData.value = exitFiles.map(x => x.fileId).join(',')
    if (!props.field.disabled)
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
    emit('update:modelValue', exitFiles)
    setFun()
}
const onCusometerRemove = (img: any) => {
    // 移除实际option的值
    const item = fileData.value.find(x => x.name === img.name && x.size === img.size)
    const index = fileData.value.findIndex(x => x.name === img.name && x.size === img.size)
    const option = optionData.value
    if (item) {
        if (option) {
            const optionArrys: Array<string> = option.split(',')
            const newOption = optionArrys.filter(x => x !== item.fileId)
            emit('update:optionValue', newOption.join(','))
            emit('update:modelValue', newOption)
            setFun()
            // 移除上传的指定文件
            if (index > -1) {
                fileData.value.splice(index, 1)
                if (!props.field.disabled)
                    props.field.expressTest(props.field, props.index, isFirstLoad.value)
            }

        }
    }
}
// const  dingtalkUpload= async (imgs:Array<string>)=> {
//     dd.biz.util.compressImage({ filePaths:imgs, compressLevel:4 })
//     .then(result=>{ 
//         const newUrl = window.webConfig.host === '/' ?
//          window.origin+'/'+url.process.dingTalkUpload
//          :window.webConfig.host
//          alert('newurl: '+newUrl)
//         dd.biz.util.uploadFile({
//             url:newUrl,
//             fileName:'image',
//             filePath:result.filePaths[0],
//             header:'',
//             formData:{}
//         }).then(result=>{
//             alert('上传文件:' +JSON.stringify(result))
//         }).catch(error=>{
//             alert('文件上传失败： '+JSON.stringify(error))
//         })

//     }).catch(error=>{
//         alert('文件压缩失败: '+JSON.stringify(error))
//     })
//     // const data = await dd.biz.util.compressImage({ filePaths:imgs, compressLevel:4 })
//     // if( data && data.filePaths && data.filePaths.length>0) {
//     //      // 执行文件上传
//     //     dd.biz.util.uploadFile({
//     //         url:url.process.dingTalkUpload,
//     //         fileName:'image',
//     //         filePath:data.filePaths[0],
//     //         header:'',
//     //         formData:{}
//     //     }).then(result=>{
//     //         alert('上传文件:' +JSON.stringify(result))
//     //     })
//     // }
// }
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = yzconfig.getDefaultRule()
            }
        }
    }
})
// 监听数据MAP
eventBus.on('onMap', { //YZ +
    cb: async function (params) {
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
            // vModel.value = params.value

            const img = await processStore.getAttachmentInfo(params.value)
            if (img && img.length > 0) {
                imageSrc.value = []
                imageSrcObj.value = []
                for (let i = 0; i < img.length; i++) {
                    const id = img[i].fileId
                    const srcUrl = `bpm/attachment/file/${id}?access_token=${token}`
                    imageSrc.value.push(srcUrl)
                    imageSrcObj.value.push(img[i])
                }
            }
            emit('update:modelValue', imageSrc.value)
            optionData.value = params.value
            setFun()
        }
    }
})
</script>
<style lang="scss">
.form-item-div {
    .van-uploader__input-wrapper {
        width: 100% !important;
    }
}

.image-div {
    width: 120px;
    height: 120px;
    border: 1px solid red;
    float: left;
}

.remove-dot {
    width: 20px;
    height: 20px;
    background: #7a7878e3;
    position: absolute;
    right: 0;
    top: 0;
    text-align: center;
    line-height: 20px;
    color: #fff;
}

.image-update-div {
    width: 60px;
    height: 60px;
    margin: 3px;
    float: left;
    position: relative;

    img {
        width: 100%;
        height: 60px;
    }
}
</style>