export interface IUrl {
  login: ILoginUrl; // 登录使用相关接口
  home: IHomeUrl; // 首页使用相关接口
  my: IMyUrl;
  form: IFormUrl;
  org: IOrgUrl;
  work: IWorkUrl;
  dataSource: IDataSource;
  process: IProcess; // 流程使用相关接口
}
export interface ILoginUrl {
  userLogin: string;
  getUserInfo: string;
  thirdAuth: string;
  getPublickey: string;
  getWeChatUser: string;
  getWeChatJssdk: string;
  getDingTalkUser: string;
  getDingTalkSdk: string;
  SsoLogin: string;
  loginOut: string;
  getFeiShuUser: string;
  getFeiShuSdk: string;
  getThirdUser: string;
  submitauth: string;
  checklogin: string;
}
export interface IHomeUrl {
  getMyCollection: string;
  getlibiaryProcess: string;
  getProcessForLib: string;
  addCollection: string;
  cancleCollection: string;
  getTopList: string;
  getTreeFloder: string;
  getFolderProcess: string;
  getLindeProcess: string;
}
export interface IMyUrl {
  getOutSetting: string;
  saveOutSetting: string;
  saveLange: string;
}
export interface IFormUrl {
  getFormUf: string;
  getFatureProcess: string;
  updateAttach: string;
  updateBase64: string;
  getAttachmentInfo: string;
  getChildFormData: string;
  childFormSubmit: string;
}
export interface IOrgUrl {
  getOrgUsers: string;
  searchUser: string;
  getOrgs: string;
  getOrgChildren: string;
  getUserMapData: string;
  getOrgMapData: string;
}
export interface IWorkUrl {
  getMyRequest: string;
  getMyWorks: string;
  getMyProcessed: string;
  getMyNotifys: string;
  getMyRequestForm: string;
  getProcessPostInfo: string;
  getWorkListCount: string;
  getWorkReadCount: string;
}
export interface IDataSource {
  getTableData: string;
  getTableParams: string;
  getDataSource: string;
  getEsbDataSource: string;
  getEsbDataNoPage: string;
  getDataESB: string;
  getEsbDataPaging:string
  getEsbParams:string
  getFormNoPage:string
  getFormPage:string
  getFormParams:string
}
export interface IProcess {
  getSubmitProcessInfoInfo: string;
  getforecastProcess: string;
  getRequestForecast: string;
  getMyRequestInfo: string;
  postNotify: string;
  remandTask: string;
  remandToUser: string;
  getTaskTacke: string;
  backToUser: string;
  cancleTask: string;
  getMyWorkInfo: string;
  getReturnSteps: string;
  returnToStep: string;
  returnToUser: string;
  assigneUser: string;
  assigneUserBatch:string
  processAudit: string;
  rejectTask: string;
  getWorkForecast: string;
  getCompletePostInfo: string;
  notifyRead: string;
  submitProcess: string;
  getInitUser: string;
  getMemberDetail: string;
  getOwnerUser: string;
  getInitDepart: string;
  getSimpleUser: string;
  getSimpleDept: string;
  getTaskPermission: string;
  backToNode: string;
  dingTalkUpload: string;
  createQrCode: string;
  historyFormInfo: string;
  batchApprove:string
}
