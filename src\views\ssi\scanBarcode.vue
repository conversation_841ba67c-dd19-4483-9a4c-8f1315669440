<template>
    <div class="box">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="btn" v-if="!symbol_">
                <span @click="scanner">
                    {{ $t(qs_title) }}
                </span>
            </div>
            <div class="search" v-if="symbol_">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div v-if="symbol_">
                <van-icon name="filter-o" @click="rightShow" class="rightShow" />
                <van-popup v-model:show="isShow" position="top" :style="{ width: '100%', height: '49%' }">
                    <div>
                        <div style="position:relative;padding:15px;">
                            <div style="position:absolute;"><van-icon name="arrow-left" @click="isShow = false"
                                    style="font-size:16px;" /></div>
                            <div style="display:flex;justify-content: center;font-size:16px">
                                <div>{{ $t('Form.Filter') }}</div>
                            </div>
                        </div>
                        <div class="li_">
                            <div class="li-name">{{ $t('SSI.InspectionDate') }}</div>
                            <div style="display: flex; align-items: center;" class="datePicker">
                                <van-field v-model="minTime" is-link readonly name="datePicker"
                                    @click="showPicker = true" />
                                <van-popup v-model:show="showPicker" destroy-on-close position="bottom">
                                    <van-date-picker :model-value="minPickerValue" @confirm="minConfirm"
                                        :default-date="currentDate" :min-date="minDate" :max-date="maxDate"
                                        @cancel="showPicker = false" disabled />
                                </van-popup>
                                <span style="margin:0 0 4px 12px; color: #dcdfe6;">—</span>
                                <van-field v-model="maxTime" is-link readonly name="datePicker"
                                    @click="showPicker_ = true" style="margin-left: 15px;" />
                                <van-popup v-model:show="showPicker_" destroy-on-close position="bottom">
                                    <van-date-picker :model-value="maxPickerValue" @confirm="maxConfirm"
                                        :default-date="currentDate_" :min-date="minDate_" :max-date="maxDate_"
                                        @cancel="showPicker_ = false" disabled />
                                </van-popup>
                            </div>
                        </div>
                        <div class="li_" style="padding: 0;">
                            <van-field v-model="InspectionSearchText" is-link readonly :label="$t('SSI.InspectionType')"
                                placeholder="" @click="showPickerType = true" />
                            <van-popup v-model:show="showPickerType" destroy-on-close round position="bottom">
                                <van-picker :model-value="pickerValue" :columns="columns"
                                    @cancel="showPickerType = false" @confirm="onConfirm" disabled />
                            </van-popup>
                        </div>
                        <div class="li_" style="padding: 0;">
                            <van-field v-model="InspectionResultSearch" is-link readonly
                                :label="$t('SSI.EvaluationResult')" placeholder="" @click="showPickerRes = true" />
                            <van-popup v-model:show="showPickerRes" destroy-on-close round position="bottom">
                                <van-picker :model-value="pickerValueRes" :columns="columnsRes"
                                    @cancel="showPickerRes = false" @confirm="onConfirmRes" disabled />
                            </van-popup>
                        </div>
                        <div class="li_" style="padding: 0;">
                            <van-field v-model="search.keyword" clearable :label="$t('Form.Keywords')" />
                        </div>
                    </div>
                    <div style="display: flex; padding: 10px; justify-content: center; margin-top:20px">
                        <van-button type="default" style="width: 25%;height:2rem" @click="rest" size="mini">{{
                            $t('Global.Reset')
                        }}</van-button>
                        <van-button type="primary" style="width: 25%;height:2rem; margin-left: 17px;" @click="confirm"
                            size="mini">{{
                                $t('Global.Confirm')
                            }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="mb" v-if="!symbol_">
            <!-- <div id="reader" width="600px;"></div> -->
            <div id="interactive" class="viewport">
                <canvas class="drawingBuffer"></canvas>
                <video autoplay="true" preload="auto" src="" muted="true" playsinline="true"></video>
                <div class="codeMsg">
                    <!-- @click="confirmBarcode(codeMsg)" -->
                    {{ codeMsg }}
                </div>
            </div>
        </div>
        <div class="mb_" v-if="symbol_">
            <div class="list">
                <div class="li">
                    <div class="head">
                        <img src="./img/Barcode3X.png" alt="">
                    </div>
                    <div class="info">
                        <div class="key_">
                            <div>{{ $t('SSI.Equipment') }}：</div>
                            <div>{{ equipmentType }}</div>
                        </div>
                        <div class="key_">
                            <div>{{ $t('SSI.SerialNo') }}：</div>
                            <div>{{ serialNo }}</div>
                        </div>
                        <div class="key_">
                            <div>{{ $t('SSI.BarcodeNo') }}：</div>
                            <div>{{ barcodeNo_ }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :loosing-text="$t('Form.Release_to_refresh')"
                :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loading" :finished="finished" :finished-text="$t('Form.NoMore')"
                    :loading-text="$t('Form.Loading')" @load="onLoad()">
                    <div class="T_">
                        <div class="t_" style="width:100px;">{{ $t('SSI.InspectionType') }}</div>
                        <div class="t_" style="width:100px;">{{ $t('SSI.InspectionDate') }}</div>
                        <div class="t_" style="width:130px;">{{ $t('SSI.EvaluationResult') }}</div>
                    </div>
                    <div class="T_2" v-for="(item, idx) in List" :key="idx" @click="historyDetail(item)">
                        <div class="t_" style="width:100px;">{{ item?.inspectionType ? $t('SSI.' + item?.inspectionType)
                            : item?.inspectionType }}</div>
                        <div class="t_" style="width:100px;">
                            {{ item?.inspectionDate && new Intl.DateTimeFormat(navigator?.language,
                                options_).format(Date.parse(item?.inspectionDate + 'Z')) }}
                            <!-- new Intl.DateTimeFormat(navigator?.language, options).format(new Date(item?.CheckInValidationDuration + 'Z')) -->
                        </div>
                        <div class="t_" style="width:100px;">{{ item?.result ? $t('SSI.' + item?.result) : item?.result
                        }}</div>
                        <van-icon name="arrow" />
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed, ref, reactive, onUnmounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Html5QrcodeScanner, Html5Qrcode } from "html5-qrcode";
import Quagga from 'quagga';
import { BarCode, BarcodeList } from '@/service/user'
import { useFormData, useLang, TodaysDateFormat } from '@/utils'
import { showNotify } from 'vant';
import { usePostBody, useGetQuery } from '@/utils/request';
import { url } from '@/api/url';

const router = useRouter()
const route = useRoute()
const qs_title = ref('')
// const symbol_ = ref(location.hostname === 'localhost' ? 'in' : '') // in 展示扫码后的页面
const symbol_ = ref('')
const Sign = ref('')
const loading = ref(false)
const Equipment = ref({})
const options = ref({
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    timeZoneName: "short"
})
const options_ = ref({
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
})
const finished = ref(false);
const refreshing = ref(false); // 下拉刷新
const pageParams = ref({
    page: 1, // 当前页数, 从 1 开始
    size: 20 // 每页条数
});
const equipmentId = ref('')
const equipmentType = ref('')
const serialNo = ref('')
const barcodeNo_ = ref('')

// const equipmentId = ref(location.hostname === 'localhost' ? '676364776353861' : '')
// const equipmentType = ref(location.hostname === 'localhost' ? 'Mobile & Crawler Crane' : '')
// const serialNo = ref(location.hostname === 'localhost' ? 'tast' : '')
// const barcodeNo_ = ref(location.hostname === 'localhost' ? '1098922' : '')

const List = ref([
    // {
    //     inspectionType: '1',
    //     inspectionDate: '3333',
    //     result: '5555'
    // }
])
const searchValue = ref('')
const isShow = ref(false)
const search = reactive({
    type: '',
    result: '',
    keyword: ''
})
const currentDate = new Date()
const minDate = ref(new Date(1980, 0, 1))
const maxDate = ref(new Date())
const searchType = ref(route.query?.type)
const showPicker = ref(false)
const showPicker_ = ref(false)
const minPickerValue = ref([
    currentDate.getFullYear(),
    String(currentDate.getMonth() + 1).padStart(2, '0'),
    String(currentDate.getDate()).padStart(2, '0')
])

const currentDate_ = new Date()
const minDate_ = ref(new Date(1980, 0, 1))
const maxDate_ = ref(new Date(2040, 11, 31))
const maxPickerValue = ref([
    currentDate_.getFullYear(),
    String(currentDate_.getMonth() + 1).padStart(2, '0'),
    String(currentDate_.getDate()).padStart(2, '0')
])
const minTime = ref('')
const maxTime = ref('')
const isScanning = ref(true);
const codeMsg = ref('')

const state = reactive({
    html5QrCode: null
});
const getCamerasInfo = () => {
    Html5Qrcode.getCameras()
        .then((devices) => {
            if (devices && devices.length) {
                state.html5QrCode = new Html5Qrcode("reader");
                startScan();
            }
        })
        .catch((err) => {
            console.log("%c [ Scan BarCode 请设置摄像头访问权限 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
            showNotify({ message: useLang('SSI.Camera_permission_tips'), type: 'danger', duration: 2000 })
        });
};

const startScan = () => {
    state.html5QrCode
        .start(
            { facingMode: "environment" },  //environment 后置摄像头 || user前置摄像头
            {
                fps: 1,
                qrbox: { width: 250, height: 150 },
            },
            async (decodedText, decodedResult) => {
                console.log("%c [ -decodedText ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", decodedText)
                if (decodedText) {
                    try {
                        const scan_res = await BarCode(decodedText)
                        console.log("%c [ scan_res.data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", scan_res.data)
                        if (scan_res.status === 200) {
                            const { equipment_id, equipment_type, serial_no, barcodeNo } = scan_res.data
                            equipmentId.value = equipment_id
                            console.log("%c [ equipmentId.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", equipmentId.value)
                            equipmentType.value = equipment_type
                            console.log("%c [ equipmentType.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", equipmentType.value)
                            serialNo.value = serial_no
                            console.log("%c [ serialNo.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", serialNo.value)
                            barcodeNo_.value = barcodeNo
                            console.log("%c [ barcodeNo_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", barcodeNo_.value)
                            stopScan()
                            symbol_.value = 'in' //展示签到后的页面
                        }
                    } catch (error) {
                        console.log("%c [ BarCode_error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
                    }
                } else {
                    console.log('such as ==>> DT2024110017, CaseNo is no data')
                }
            }
        )
        .catch((err) => {
            console.log("扫码失败===>>>>>", err);
        });
};
const stopScan = () => {
    state.html5QrCode
        .stop()
        .then((a) => {
            console.log("已停止扫码", a);
        })
        .catch((err) => {
            console.log("%c [ 停止失败 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
        });
};

//不断检测条形码位置，为图像 / 视频每帧不断进行调用
Quagga.onProcessed(result => {
    // 绘制实际检测的捕获框框
    const drawingCtx = Quagga.canvas.ctx.overlay;
    const drawingCanvas = Quagga.canvas.dom.overlay;
    // console.log("%c [ drawingCanvas ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", drawingCanvas)
    // 下面会实时绘制绿色框、蓝色框、红色线三种图形，你可以对照下方的GIF图进行对比
    if (result) {
        // 绿色框
        if (result.boxes) {
            drawingCtx.clearRect(0, 0, parseInt(drawingCanvas.getAttribute("width")), parseInt(drawingCanvas.getAttribute("height")));
            result.boxes.filter(box => {
                return box !== result.box;
            }).forEach(box => {
                Quagga.ImageDebug.drawPath(box, { x: 0, y: 1 }, drawingCtx, { color: "green", lineWidth: 2 });
            });
        }
        // 蓝色框
        if (result.box) {
            Quagga.ImageDebug.drawPath(result.box, { x: 0, y: 1 }, drawingCtx, { color: "blue", lineWidth: 2 });
        }
        // 红色线
        if (result.codeResult && result.codeResult.code) {
            Quagga.ImageDebug.drawPath(result.line, { x: 'x', y: 'y' }, drawingCtx, { color: 'red', lineWidth: 3 });
        }
    }
});


// 正确定位并解码到条形码时触发
Quagga.onDetected(async (result) => {
    if (!isScanning.value) {
        return;
    }
    const code = result.codeResult.code;
    codeMsg.value = code;
    isScanning.value = false;
    console.log("%c [ -- Barcode ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", code)
    try {
        const scan_res = await BarCode(code)
        console.log("%c [ scan_res.data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", scan_res.data)
        if (scan_res.status === 200) {
            const { equipment_id, equipment_type, serial_no, barcodeNo } = scan_res.data
            equipmentId.value = equipment_id
            console.log("%c [ equipmentId.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", equipmentId.value)
            equipmentType.value = equipment_type
            console.log("%c [ equipmentType.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", equipmentType.value)
            serialNo.value = serial_no
            console.log("%c [ serialNo.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", serialNo.value)
            barcodeNo_.value = barcodeNo
            console.log("%c [ barcodeNo_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", barcodeNo_.value)
            setTimeout(() => {
                Quagga.pause(); // 暂停扫描
                symbol_.value = 'in' //展示签到后的页面
            }, 2500)
        }
    } catch (error) {
        isScanning.value = true;
        console.log("%c [ BarCode_error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
    }
});
const scanner = () => {
    Quagga.init(
        {
            // 输入流，定义图形/视频来源
            inputStream: {
                // LiveStream(默认)/ImageStream/VideoStream
                type: "LiveStream",
                target: document.querySelector(".viewport"),
                constraints: {
                    // width: 640,
                    // height: 250,
                    facingMode: "environment",
                    focusMode: "continuous",    // 连续自动对焦
                    aspectRatio: { ideal: 16 / 9 },
                    // frame: { ideal: 10 }
                },
                // exposureMode: "continuous", // 持续曝光模式
                // exposureCompensation: 0.5, // 曝光补偿，根据需要调整
                // // 关键参数：降低图像亮度敏感度，减少反光干扰
                // brightness: -5,
                // contrast: 50,
                // saturation: 20,
                // // 降噪处理
                // noiseReduction: "high"
            },
            // 解码器
            decoder: {
                readers: ["code_128_reader", "code_39_vin_reader"], //解码器类型:
                // "code_128_reader",    // Code 128 (常见于物流)
                // "ean_reader",         // EAN-13 (商品条码)
                // "ean_8_reader",       // EAN-8 (短商品条码)
                // "code_39_reader",     // Code 39
                // "code_39_vin_reader", // Code 39 车辆识别码
                // "codabar_reader",     // Codabar
                // "upc_reader",         // UPC-A
                // "upc_e_reader",       // UPC-E
                // "qr_code_reader"      // QR码（需额外配置）
            },
            // frequency: 15,            // 每秒处理10帧（默认值为15）
            // numOfWorkers: 2,           // 根据设备性能调整工作线程数
            // locator: {
            //     patchSize: "medium",    // 可选项：small/medium/large
            //     halfSample: true,       // 半采样提高性能和准确率
            //     debug: {
            //         showCanvas: true,     // 显示定位画布（调试用）
            //         showPatches: true,    // 显示定位斑块
            //         showFoundPatches: true
            //     }
            // },
            // locate: true, // 定位条码位置
        },
        (err) => {
            if (err) {
                console.log("%c [ 请设置摄像头访问权限 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
                showNotify({ message: useLang('SSI.Camera_permission_tips'), type: 'danger', duration: 2000 })
                symbol_.value = '';
                return;
            };
            Quagga.start();
        }
    );
}
// const confirmBarcode = (codeMsg) => {
//     if (codeMsg) {
//         Quagga.stop(); // 暂停扫描
//         symbol_.value = 'in' //展示签到后的页面
//     }
// }

const onRefresh = () => { //下拉刷新
    loading.value = true;
    pageParams.value.page = 1;
    finished.value = false;
    searchValue.value = ''
    List.value = [];
    onLoad();
};
const onLoad = async () => {
    if (refreshing.value) {
        refreshing.value = false
        List.value = []
    }
    try {
        const params = useFormData({
            equipment_id: equipmentId.value,
            page: pageParams.value.page,
            size: pageParams.value.size,
            planDate: minTime.value, // || '1990-01-01',
            endDate: maxTime.value, // || new Date((new Date() / 1000 + 86400) * 1000).toISOString().split('T')[0],
            type: search.type,
            kwd: search.keyword,
            result: search.result
        })
        const res = await BarcodeList(params)
        console.log("%c [ BarcodeList --- res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res.data.list)
        if (res?.data?.code === '200') {
            List.value = res?.data?.list || []
            pageParams.value.page++
            loading.value = false
            if (res?.data?.list.length < pageParams.value.size) {
                finished.value = true
                console.log("停止刷新 3")
            }
        }
    }
    catch (error) {
        finished.value = true
        loading.value = false
        console.log('Barcode List error', error)
    }
}


const minConfirm = ({ selectedValues }) => {
    minTime.value = selectedValues.join('-');
    minPickerValue.value = selectedValues;
    const selectedDate = new Date(selectedValues[0], selectedValues[1] - 1, selectedValues[2])
    minDate_.value = selectedDate
    if (maxTime.value) {
        const maxDate = new Date(maxPickerValue.value[0], maxPickerValue.value[1] - 1, maxPickerValue.value[2])
        if (maxDate < selectedDate) {
            maxTime.value = ''
            maxPickerValue.value = []
        }
    }
    showPicker.value = false;
}
const maxConfirm = ({ selectedValues }) => {
    maxTime.value = selectedValues.join('-');
    maxPickerValue.value = selectedValues;
    showPicker_.value = false;
}
const columns = [
    { text: useLang('SSI.Preliminary'), value: 'Preliminary' },
    { text: useLang('SSI.Daily'), value: 'Daily' },
    { text: useLang('SSI.Monthly'), value: 'Monthly' },
    { text: useLang('SSI.Quarterly'), value: 'Quarterly' },
];
const showPickerType = ref(false);
const pickerValue = ref([]);
const InspectionSearchText = ref('');
const onConfirm = ({ selectedValues, selectedOptions }) => {
    console.log("%c [ selectedValues ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedValues)
    console.log("%c [ selectedOptions ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedOptions)
    showPickerType.value = false
    pickerValue.value = selectedValues
    InspectionSearchText.value = selectedOptions[0].text
    search.type = selectedOptions[0].value
};

const columnsRes = [
    { text: useLang('SSI.Pass'), value: 'Pass' },
    { text: useLang('SSI.Pending'), value: 'Pending' },
    { text: useLang('SSI.Rejected'), value: 'Rejected' },
    // { text: useLang('SSI.NotApplicable'), value: 'Not Applicable' }
];
const showPickerRes = ref(false);
const pickerValueRes = ref([]);
const InspectionResultSearch = ref('');
const onConfirmRes = ({ selectedValues, selectedOptions }) => {
    console.log("%c [ selectedOptions ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedOptions)
    console.log("%c [ selectedValues ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedValues)
    showPickerRes.value = false;
    pickerValueRes.value = selectedValues;
    InspectionResultSearch.value = selectedOptions[0].text;
    search.result = selectedOptions[0].value;
};

const rest = () => {
    search.type = ''
    InspectionSearchText.value = ''
    search.result = ''
    InspectionResultSearch.value = ''
    search.keyword = ''
    minPickerValue.value = [
        currentDate.getFullYear(),
        String(currentDate.getMonth() + 1).padStart(2, '0'),
        String(currentDate.getDate()).padStart(2, '0')
    ]
    maxPickerValue.value = [
        currentDate_.getFullYear(),
        String(currentDate_.getMonth() + 1).padStart(2, '0'),
        String(currentDate_.getDate()).padStart(2, '0')
    ]
    minTime.value = ''
    maxTime.value = ''
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const confirm = () => {
    console.log("%c [  minTime.value  ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", minTime.value)
    console.log("%c [  maxTime.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", maxTime.value)
    const { type, result, keyword } = search
    console.log("%c [ keyword ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", keyword)
    console.log("%c [ result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)
    console.log("%c [ type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", type)
    loading.value = true;
    pageParams.value.page = 1;
    finished.value = false;
    List.value = [];
    onLoad();
    isShow.value = false
}
const historyDetail = (i) => {
    router.push({
        path: '/barcodeDetail',
        query: {
            i: JSON.stringify(i),
            equipmentId: equipmentId.value,
            equipmentType: equipmentType.value,
            serialNo: serialNo.value,
            barcodeNo_: barcodeNo_.value,
        }
    })
}
const comeBack = () => {
    router.push({
        path: '/ssi?fid=646064554467397',
        replace: true
    })
    if (document.querySelector('#interactive')) {
        Quagga.stop()
    }
    // if (state.html5QrCode?.isScanning) {
    //     stopScan()
    // }
}
onMounted(() => {
    console.log("%c [ scanBarcode ---- route?.query ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route?.query)
    qs_title.value = route?.query?.qs_title
    Sign.value = route?.query?.type
    if (route?.query?.symbol_) {
        symbol_.value = route?.query?.symbol_
    }
    console.log("%c [ symbol_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", symbol_.value)

    if (!symbol_.value) {
        scanner()
        // getCamerasInfo();
    }
    if (route?.query?.equipmentId) {
        equipmentId.value = route?.query?.equipmentId
        equipmentType.value = route?.query?.equipmentType
        serialNo.value = route?.query?.serialNo
        barcodeNo_.value = route?.query?.barcodeNo_
    }
})
onUnmounted(() => {
    // if (state.html5QrCode?.isScanning) {
    //     stopScan();
    // }
    if (document.querySelector('#interactive')) {
        Quagga.stop()
    }
});

</script>

<style scoped lang="scss">
::v-deep(.van-field__label) {
    width: 150px !important;
}

.box {
    background-color: #f9f9f9; //var(--van-background-2);
    position: relative;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: sticky;
        top: 0;
        z-index: 99;
        box-shadow: 0 1px 4px rgba(0, 0, 0, .1);

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 5px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            position: absolute;
            top: 50%;
            left: 37%;
            transform: translateY(-50%);
        }

        .search {
            width: 100%;

            .van-search {
                background: transparent;
            }
        }

        .rightShow {
            font-size: 16px;
            color: var(--yz-text-666);
            margin-right: 10px;
        }

        .li_ {
            padding: 10px 0;
            font-size: var(--yz-com-14);
            border-bottom: 1px solid #eaecf0;
            width: 94%;
            margin: 0 auto;

            .li-name {
                width: 145px;
                margin-bottom: 10px;
            }

            .datePicker {
                .van-cell {
                    border-radius: 3px;
                    padding: 3px;
                    border: 1px solid #dcdfe6;
                }
            }

            .van-cell {
                padding: 0.625rem 0;
            }
        }
    }

    .mb {
        #interactive.viewport {
            position: relative;
            width: 100%;
            height: 250px;

            video {
                max-width: 100%;
                width: 100%;
                height: 250px;
                object-fit: cover;
            }

            .drawingBuffer {
                width: 100%;
                height: 250px;
                position: absolute;
                left: 0;
                top: 0;
            }

            .codeMsg {
                position: absolute;
                right: 5px;
                top: 5px;
            }
        }
    }

    .mb_ {

        .list {
            border-bottom: 1px solid aliceblue;
            width: 100%;

            .li {
                background-color: var(--yz-div-background);
                display: flex;
                padding: 15px;

                .head {
                    width: 40px;
                    margin-right: 8px;
                    height: 40px;
                    border: 1px solid #ddd;
                    overflow: hidden;
                    border-radius: 50%;
                    background-color: #ddd;
                    display: flex;
                    justify-content: center;
                    align-content: center;
                }

                .info {
                    flex: 1;
                    font-size: var(--yz-com-14);
                    color: #666666;

                    .key_ {
                        display: flex;
                        align-items: center;
                        padding: 1px 0;
                    }
                }
            }
        }

        .T_ {
            margin-top: 6px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: #ffffff;
            margin-bottom: 3px;

            .t_ {
                padding: 10px 0px;
                text-align: left;
            }
        }

        .T_2 {
            margin-top: 6px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: #ffffff;
            margin-bottom: 3px;

            .t_ {
                padding: 10px 5px;
                text-align: left;
            }
        }
    }
}
</style>