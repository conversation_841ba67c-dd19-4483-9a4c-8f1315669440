import { useProcessStore } from "@/store/process";
import { Y<PERSON><PERSON>onfig, Y<PERSON>Field, YZStyle } from "./formModel";
import { useGetQuery } from "@/utils/request";
import { url } from "@/api/url";
import { useLang, useParamsFormatter } from "@/utils";
import { eventBus } from "@/utils/eventBus";
import { useCore } from "@/store/core";
const store = useProcessStore()
export class YZDeptConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {

        super(parmas)
        this.ctype = 'YZDept'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZDeptStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZDept extends YZField {
    constructor(params: any) {
        super(params)
        this.vModel.value = params['__vModel__'].modelValue

        // YZ -
        //    const info = store.getPostInfoData
        //    if (info) {
        //      const paramsValue = `${info.providerName}.${info.ouid}`
        //      const newUrl = useParamsFormatter(url.process.getInitDepart,{
        //          params1: info.providerName ?  paramsValue : info.processInstance.ouid
        //     })
        //       useGetQuery(newUrl)
        //       .then(result=>{
        //          this.vModel.value = result.OUName
        //          this.vModel.optionValue = result.providerName+"."+result.ouid
        //          const core = useCore()
        //          const intance = core.$update as any
        //          if (intance)
        //             intance.update()
        //       }).catch(error=>{
        //         console.error('初始化用户所属部门',error)
        //       })
        //    }
        //
        //这里先打开，关于userType的
        const info = store.getPostInfoData
        if (info) {
            const paramsValue = `${info.providerName}.${info.ouid}`
            const newUrl = useParamsFormatter(url.process.getInitDepart, {
                params1: info.providerName ? paramsValue : info.processInstance.ouid
            })
            console.log("%c [ newUrl bpm/ou/qualified/bpmou.922 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)

            //bpm/ou/qualified/bpmou.922

            useGetQuery(newUrl)
                .then(result => {
                    this.vModel.value = result.OUName
                    this.vModel.optionValue = result.providerName + "." + result.ouid
                    const core = useCore()
                    const intance = core.$update as any
                    if (intance)
                        intance.update()
                }).catch(error => {
                    console.error('初始化用户所属部门', error)
                })
        }

    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZDeptConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZDeptStyle(style)
    }
}