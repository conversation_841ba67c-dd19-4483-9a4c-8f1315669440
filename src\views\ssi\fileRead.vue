<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack(route.query.next)">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="iTitle">
                {{ route.query?.formattedName }}
            </div>
        </div>
        <div class="pad">
            <div v-if='route.query.type === "pdf"' class="pad">
                <div id="demo"></div>
            </div>
            <div v-else-if='route.query.type?.includes("doc")'>
                <vue-office-docx :src="docContent" style="height: 100vh;" @rendered="renderedHandler"
                    @error="errorHandler" />
            </div>
            <div v-else-if='route.query.type?.includes("ppt")'>
                <vue-office-pptx :src="pptSrc" style="height: 100vh;" @rendered="renderedHandler"
                    @error="errorHandler" />
            </div>
            <div v-else-if='route.query.type === "txt"'>
                {{ textSrc }}
            </div>
            <div v-else-if='route.query.type?.includes("xl")'>
                <vue-office-excel :src="excelSrc" :options="options" style="height: 100vh;" @rendered="renderedHandler"
                    @error="errorHandler" />
            </div>
            <!-- <VueDocPreview :value="docContent" :type="docxType" /> -->
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue'
import { showNotify } from 'vant'
import { onMounted, onUnmounted, ref, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLoingStore } from '@/store/login'
import store from '@/store'
import axios from 'axios'
import Pdfh5 from 'pdfh5'
import 'pdfh5/css/pdfh5.css'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficePptx from '@vue-office/pptx'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/docx/lib/index.css'
import '@vue-office/excel/lib/index.css'
// import VueDocPreview from 'vue-doc-preview';
import { useLang, TodaysDateFormat, formatDate, shortTimezoneName } from "@/utils"
import { useCore } from '@/store/core'

const coreStore = useCore(store)
const loginStore = useLoingStore(store)
const route = useRoute()
const router = useRouter()
const excelSrc = ref('')
const docContent = ref('')
const docxType = ref('docx')
const pptSrc = ref('')
const textSrc = ref('')
const options = ref({
    xls: route.query.type === 'xlsx' ? false : true,  //预览xlsx文件设为false；预览xls文件设为true
    minColLength: 0,  // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
    minRowLength: 0,  // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
    widthOffset: 10,  //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
    heightOffset: 10, //在默认渲染的列表高度上再加 Npx高
    beforeTransformData: (workbookData) => { return workbookData }, //底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。
    transformData: (workbookData) => { return workbookData }, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容
})


const getDetail = async (FileId, type) => {
    console.log("%c [ File --- type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", type)
    await axios({
        method: 'GET',
        headers: {
            'Authorization': "Bearer " + loginStore.getToken,
            'Accept-Language': coreStore.$lang == 'en' ? 'en-US' : coreStore.$lang
        },
        url: `/bpm/attachment/file/${FileId}`,
        responseType: type === 'txt' ? 'text' : 'blob' // 'arraybuffer' //
    }).then(res => {
        console.log("%c [ File --- res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res)
        if (res.status === 200) {
            if (type.includes('pdf')) {
                new Pdfh5('#demo', {
                    pdfurl: res?.request?.responseURL
                });
            } else if (type.includes('xls')) {
                excelSrc.value = res?.request?.responseURL
            } else if (type.includes('doc')) {
                docContent.value = res?.request?.responseURL
            } else if (type.includes('ppt')) {
                pptSrc.value = res?.request?.responseURL
            } else {
                textSrc.value = res.data
            }
        } else {
            showNotify({ message: 'File does not exist.' })
            return
        }
    }).catch(err => {
        console.log(err)
    })
}

const renderedHandler = (val) => {
    console.log("%c [ 渲染完成 val ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", val)
}
const errorHandler = (val) => {
    console.log("%c [ 渲染失败 val ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", val)
}
const comeBack = (next) => {
    if (next === 'ssiRegister') {
        router.push({
            path: '/ssiRegister',
            query: JSON.parse(route.query.query),
            replace: true
        })
    } else {
        router.push({
            path: '/ssi_apply',
            query: JSON.parse(route.query.query),
            replace: true
        })
    }
}
onMounted(() => {
    if (route.query.fileId) {
        getDetail(route.query.fileId, route.query.type)
    }
    console.log("%c [ fileRead --- route.query.query ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route.query.query)
})

onUnmounted(() => {
})

</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;
        position: relative;
        box-shadow: 0 .0625rem .25rem rgba(0, 0, 0, .1);

        .back {
            position: absolute;
            left: 10px;
            top: 50%;
            width: 40px;
            box-sizing: border-box;
            transform: translate(0, -50%);

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .iTitle {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: var(--yz-text-333);
            font-weight: 500;
            text-align: center;
        }
    }
}

.pad {
    width: 100%;
    height: calc(100vh - 48px);
    overflow-y: auto;
}

#demo {
    .pinch-zoom-container {
        height: 100% !important;
    }
}
</style>