<template>
    <div>
        <van-field v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required" is-link
            v-model="vModel" :rules="field.config.rules" :placeholder="field.placeholder" @click="onOrgClickShow"
            :readonly="true" :label="field.config.label">
        </van-field>
        <YZOrgSelect v-model:show="orgShow" @onSave="onSave" :default-orgs="defaultOrgs" />
    </div>
</template>

<script lang="ts" setup>
import { YZUser } from '@/logic/forms/YZUser';
import { computed, onMounted, PropType, ref } from 'vue';
import YZOrgSelect, { ISelectOrg } from '@/components/YZOrgSelect.vue';
import { eventBus } from '@/utils/eventBus';
import { reactive } from 'vue';
import { parseExpress } from '@/logic/forms/express';
import { YZField } from '@/logic/forms/formModel';
import { YZOrgConfig } from '@/logic/forms/YZOrg';
const props = defineProps({
    field: {
        type: Object as PropType<YZUser>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:optionValue', 'update:modelValue'])
const isFirstLoad = ref<boolean>(true)
const defaultOrgs = ref<ISelectOrg[]>([])
const config = props.field.config as YZOrgConfig
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.optionValue
    },
    set(val) {
        emit('update:optionValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const orgShow = ref<boolean>(false)
const onSave = (user: ISelectOrg[],orgMapData:any) => {
    if(orgMapData){
       orgMapField(orgMapData)
    }
    defaultOrgs.value  =[]
    if (user && user.length > 0) {
        vModel.value = user[0].name
        const value = 'bpmou.' + user[0].ouid
        emit('update:modelValue', value)
    } else {
        vModel.value = ''
        emit('update:modelValue', '')
    }
    setFun()
}
const orgMapField = (mapData:any) => {
    // let mapValue:any = {}
    const mapFields: YZField[] = []
    // 获取map数据
    let mapObj = props.field.config.extends.$map
    // 得倒需要map的值
    if(mapObj){
        Object.keys(mapObj).forEach((name) => {
            const value = mapObj[name] as string

            let formId = props.field.targetFormId
            let field = parseExpress.getFieldByFid(formId,value)
            // let field = parseExpress.getField(value)
            if (value.indexOf(".") > -1 || value.indexOf("字表") > -1 || value.split('.').length === 2){
                let fex = value.split('.')[0]
                let fieldModel = value.split('.')[1]
                field = parseExpress.getFieldByFid(formId,fex) //parseExpress.getField(fex)
                const fieldValue = field?.vModel.value
                if (fieldValue && fieldValue.length > 0) {
                    const newIndex = props.index
                    if (newIndex > -1) {
                        // 表示当前操作的控件是主表，但是要给字表赋值
                        fieldValue.forEach((f: any, index: number) => {
                            const items = fieldValue[index].colums as Array<any>
                            const xfield = items.find(x => x.field.vModel.model === fieldModel)
                            if (xfield) {
                                mapFields.push(xfield.field)
                            }
                        })
                    }else {
                        const items = fieldValue[newIndex].colums as Array<any>
                        const xfield = items.find(x => x.field.vModel.model === fieldModel)
                        if (xfield)
                            mapFields.push(xfield.field)
                    }
                }
            }
            if (field) {
                mapFields.push(field)
            }
        })
        if (mapFields.length > 0){
            mapFields.forEach(field => {
                if (field) {
                    for (const key in mapObj) {
                        if(mapObj[key] === field.vModel.model || mapObj[key] === field.table){
                            eventBus.emit('onMap', true, {
                                value:mapData['ou'][key] ,
                                model: field.vModel.model,
                                uuid: field.uuid,
                                index: props.index
                            })
                        }
                    }
                }
            })
        }
    }
}
eventBus.on('setSignleOrg', {
    cb: function (params) {
        vModel.value = params.text
        setFun()
    }
})
const onOrgClickShow = () => {
    if (!props.field.disabled) {
        defaultOrgs.value = []
        if (props.modelValue && props.optionValue) {
            const value = props.modelValue.split('.')[1]
            defaultOrgs.value.push({
                name: props.optionValue,
                ouid: value,
                code: ''
            })
        }
        orgShow.value = true
    }
   
}

eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
             if(params.value ||  params ===1) {
                // 禁用验证
                props.field.config.required =false 
                props.field.config.rules = []
             } else {
                // 启用验证
                props.field.config.required = true 
                props.field.config.rules = config.getDefaultRule()     
             }
        }
    }
})
</script>
<style  scoped></style>