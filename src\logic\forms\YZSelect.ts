import { useGet<PERSON><PERSON>y, use<PERSON>ost<PERSON><PERSON>, usePostQ<PERSON>y } from "@/utils/request";
import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZStyle } from "./formModel";
import { decodeBase64, useFormData, useIs<PERSON><PERSON><PERSON>, useLang, useParamsFormatter, useStrToUnicode } from "@/utils";
import { url } from "@/api/url";
import { useUserStore } from "@/store/users";
import { eventBus } from "@/utils/eventBus";
import { formBuilder } from "./formBuilder";
export class YZSelectConfig extends YZConfig {
  public selectShow: boolean;
  private mapData: any;
  private defaultRules: Array<any>;
  constructor(parmas: any) {
    super(parmas);
    this.ctype = "YZSelect";
    this.selectShow = false;
    this.defaultRules = [{ required: true, message: this.label? `${useLang('Form.PleaseSelect')}${this.label}`: useLang('Form.Select_Required_fields_tips') }];
    if (this.required && this.writable) {
      this.rules = this.defaultRules
    }
    this.mapData = parmas["map"];
  }
  getShow() {
    return this.selectShow;
  }
  setShow(show: boolean) {
    this.selectShow = show;
  }
  getMap(): any {
    return this.mapData;
  }
  getDefaultRule(): Array<any> {
    return this.defaultRules
  }
}
export class YZSelectStyle extends YZStyle<object> {
  constructor(params: object) {
    super(params);
  }
  initStyle(style: any): object {
    return {
      width: "100%",
      background: "red",
      ...style,
      tst: "111",
    };
  }
}
export interface ISelectOption {
  text: string;
  value: any;
  checked?:boolean;
}
export class YZSelect extends YZField {
  // public selectOption: YZSelectOption;
  public dataType:string
  public dataOptions: Array<any>
  constructor(params: any) {
    super(params);
    const optionValue = params["__vModel__"].modelValue;
    this.vModel.optionValue = optionValue;
    this.dataType = params['__config__'].dataType
    this.dataOptions = []
    // this.vModel.modelValue = optionValue;
    // this.selectOption = new YZSelectOption(params, this);
    // if (this.selectOption.getType() === 'static') {
    //   const item = this.selectOption.options.find(x => x.value === optionValue)
    //   if (item) {
    //     this.vModel.value = item.text
    //   } else {
    //     // 设置默认值
    //     const defaultItem = this.selectOption.options.find(x=>x.checked)
    //     if (defaultItem) {
    //       this.vModel.value = defaultItem.text
    //       this.vModel.optionValue = defaultItem.value
    //     }
    //   }
    // }
  }
  initConfig(config: any): YZConfig {
    return new YZSelectConfig(config);
  }
  initStyle(style: any): YZStyle<object> {
    return new YZSelectStyle(style);
  }
  getOptions(): ISelectOption[] {
    return this.dataOptions;
  }
  setOption(options:ISelectOption[]){
    this.dataOptions = options
  }
  getDateType(){
    return this.dataType
  }
}
// export class YZSelectOption extends YZOption<Array<ISelectOption>> {
//   private dataType: string;
//   private paramsField: YZField;
//   private targetFormId:string
//   constructor(params: any, yzfield: YZField) {
//     super(params);
//     this.dataType = params['__config__'].dataType
//     this.paramsField = yzfield
//     this.targetFormId = params.targetFormId
//   }
//   initOption(config: any): ISelectOption[] {
//     const user = useUserStore();
//     const options: ISelectOption[] = [];
//     let type = config.dataType;
//     this.dataType = type;
//     if (config["__config__"]["extends"]) {
//       type =
//         config["__config__"]["extends"].use === "options" ? "static" : "dymic";
//     }
//     if (type === "static") {
//       // 静态数据源
//       options.push(...(config["__config__"]["extends"].options as Array<any>));
//     } else {
//       if (config["__config__"]["extends"].ds) {
//         const { displayField, valueField } = config["__config__"]["extends"];
//         const { dataSourceId, filter, orderBy, tableName, type, esbId } = config[
//           "__config__"
//         ]["extends"].ds;
//         let newUrl = ''
//         if (type === 'esb') {
//           newUrl = useParamsFormatter(url.dataSource.getEsbDataNoPage, {
//             params1: esbId
//           });
//           usePostQuery(newUrl)
//             .then(result => {
//               if (result && result.length > 0) {
//                 for (let i = 0; i < result.length; i++) {
//                   const item = result[i];
//                   const json: ISelectOption = {
//                     text: "",
//                     value: "",
//                     ...item
//                   };
//                   if (item[displayField]) {
//                     json.text = item[displayField];
//                   }
//                   if (item[valueField]) {
//                     json.value = item[valueField];
//                   }
//                   options.push(json);
//                 }
//               }
//               const value = config["__vModel__"].modelValue;
//               if (value) {
//                 const item = options.find((x) => x.value === value);
//                 if (item) {
//                   eventBus.emit("onSetModel", false, {
//                     field: this.paramsField,
//                     item
//                   });
//                 }
//               }
//             })
//         }else {
//           newUrl = useParamsFormatter(url.dataSource.getTableData, {
//             params1: dataSourceId,
//             params2: type,
//             params3: tableName,
//           });

//           const postFilter = {
//             dataSourceId: dataSourceId,
//             tableName: tableName,
//             filters: [],
//             orderBy: "",
//             columns: [],
//             uid: user.getAccount,
//           };
//           const formdata = useFormData( {
//             f: this.buildFilter(filter),
//             c: '',
//             // o: "",
//             o: decodeBase64(orderBy),
//           })
//           usePostBody(
//             newUrl,
//             {},
//             formdata
//           )
//             .then((result) => {
//               if (result && result.length > 0) {
//                 for (let i = 0; i < result.length; i++) {
//                   const item = result[i];
//                   const json: ISelectOption = {
//                     text: "",
//                     value: "",
//                     ...item
//                   };
//                   if (item[displayField]) {
//                     json.text = item[displayField];
//                   }
//                   if (item[valueField]) {
//                     json.value = item[valueField];
//                   }
//                   options.push(json);
//                 }
//               }
//               const value = config["__vModel__"].modelValue;
//               if (value) {
//                 const item = options.find((x) => x.value === value);
//                 if (item) {
//                   eventBus.emit("onSetModel", false, {
//                     field: this.paramsField,
//                     item
//                   });
//                 }
//               }
//             })
//             .catch((error) => {
//               // console.log("selectDataSourceError", error);
//             });
//         }
//       }
//     }
//     return options;
//   }
//   buildFilterStr(str: string): string {
//     if (useIsChinese(str)) {
//         return useStrToUnicode(str)
//     }
//     return str
//   }
//   getType(): string {
//     return this.dataType
//   }
//   buildFilter(filter:any){
//     const fields = formBuilder.formConfig?.getFieldsRecord(this.targetFormId)
//     // console.log('222',fields)
//     let ff = 'e30='
//     if (filter) {
//         let strs='{'
//         Object.keys(filter).forEach(key => {
//             const item = filter[key]
//             if (fields && fields.length > 0) {
//                 let valueData = item.value
//                 if(item.field) {
//                     if (item.field.indexOf('.') > -1) {
//                     // 读取明细表
//                     const fieldItem = fields.find(x => x.tableName == item.field)
//                     if (!fieldItem) {
//                       const nkey:string =this.buildFilterStr(key)
//                       strs+=`"${nkey}":{"op":"${item.op}","value":"${this.buildFilterStr(valueData)}"},`
//                     }
//                 } else {
//                   const fieldItem = fields.find(x => x.vModel.model == item.field)
//                   if (!fieldItem) {
//                     const nkey:string =this.buildFilterStr(key)
//                     strs+=`"${nkey}":{"op":"${item.op}","value":"${this.buildFilterStr(valueData)}"},`
//                   }

//                 }
//               }
                
                
//             }else {
//               const nkey:string =this.buildFilterStr(key)
//               strs+=`"${nkey}":{"op":"${item.op}","value":"${this.buildFilterStr(item.val)}"},`
//             }
//         })
//         if (strs.lastIndexOf(',')>-1)
//             strs = strs.substring(0, strs.lastIndexOf(','))
//         strs+="}"
//         ff = btoa(strs)
//     }
//     return ff
//   }
// }
