import { useParamsFormatter, useSessionStorage } from "@/utils";
import { ConstConfig } from "@/utils/localConst";
import { defineStore } from "pinia";
import { IUserState } from "../storeModel";
import { url } from "@/api/url";
import { useGetQuery } from "@/utils/request";
import { IUserInfo } from "@/logic/login/loginModel";
const userId = "userId";
const useUserStore = defineStore(userId, {
  getters: {
    getUserInfo(state: IUserState) {
      if (state.userInfo) {
        return state.userInfo;
      } else {
        const user = useSessionStorage.getSecretObject<IUserInfo>(
          ConstConfig.user.getUserInfo
        );
        state.userInfo = user
        return user
      }
    },
    getAccount(state: IUserState) {
      if (state.userInfo) {
        return state.userInfo.Account
      } else {
        const user = useSessionStorage.getSecretObject<IUserInfo>(
          ConstConfig.user.getUserInfo
        )
        state.userInfo = user
        return user!.Account
      }
    },
    getUserName(state: IUserState): string {
      if (state.userInfo) {
        return state.userInfo.DisplayName
      } else {
        const user = useSessionStorage.getSecretObject<IUserInfo>(
          ConstConfig.user.getUserInfo
        )
        state.userInfo = user
        return user!.DisplayName
      }
    },
    getFriendly(state: IUserState): string {
      if (state.userInfo) {
        return state.userInfo.FriendlyName
      } else {
        const user = useSessionStorage.getSecretObject<IUserInfo>(
          ConstConfig.user.getUserInfo
        )
        state.userInfo = user
        return user!.FriendlyName
      }
    },
    getUserImage(state: IUserState): string {
      if (state.userInfo) {
        return state.userInfo.PortraitUrl
      } else {
        const user = useSessionStorage.getSecretObject<IUserInfo>(
          ConstConfig.user.getUserInfo
        )
        state.userInfo = user
        return user!.PortraitUrl //头像地址
      }
    }
  },
  state: (): IUserState => {
    return {
      userInfo: null,
    };
  },
  actions: {
    setUserInfo(user: IUserInfo) {
      this.$state.userInfo = user;
      useSessionStorage.setSecretObject<IUserInfo>(
        ConstConfig.user.setUserInfo,
        user
      );
    },
    async getUserInfoForAccount(account: string): Promise<IUserInfo> {
      const userUrl = useParamsFormatter(url.login.getUserInfo, {
        params1: account
      })
      const userInfo = await useGetQuery(userUrl)
      return userInfo.user as IUserInfo
    },
    async setUserInfoAsync(): Promise<boolean> {
      const { success, user } = await useGetQuery(url.login.getUserInfo, false)
      if (success) {
        this.setUserInfo(user)
        return success
      } else {
        return success
      }
    }
  },
});
export { useUserStore };
