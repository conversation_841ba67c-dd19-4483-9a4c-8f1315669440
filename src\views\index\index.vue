<template>
    <div class="page">
        <div class="search" @click="router.push('/layout/home?type=all')">
            <van-search v-model="keyword" shape="round" background="#00A0E1 " :placeholder="$t('home.AllProcesses')" />
            <!-- #00A0E1  #1D5A91-->
        </div>
        <div class="slide">
            <div class="slide-li" v-for="item in slide" :key="item.name" @click="router.push(item.path)">
                <div class="tip">
                    <i class="tip-icon" v-if="item.news"></i>
                    <i class="iconfont" :class="[item.icon]"></i>
                </div>
                <p>{{ $t(item.title) }}</p>
            </div>
        </div>
        <!-- 常用流程 -->
        <view class="fave">
            <view class="fave-box">
                <view class="fave-title">
                    <view class="fave-title-l">{{ $t('home.AllProcesses') }}</view>
                </view>
                <van-grid square :border="false">
                    <YZFloderGrid v-for="(item, index) in treeProcess" v-longpress="onLongPress" :attid="item.fid"
                        :atttrue="item.IsCollection" :isprocess="item.isProcess" :proname="item.text"
                        :prover="item.ProcessVersion" :key="index" :process="item" @onGridClick="onFloderClick" />
                </van-grid>
            </view>
        </view>

        <!-- 首页 我的收藏 隐藏  start -->
        <!-- <view class="fave">
            <view class="fave-box">
                <view class="fave-title">
                    <view class="fave-title-l">{{ $t('home.MyPopularProcess') }}</view>
                    <view class="fave-title-r" @click="shoeDel = !shoeDel">{{ !shoeDel ? '编辑' : '取消' }}</view>
                </view>
                <van-empty :image="emptyimg" image-size="100" v-if="process?.length <= 0">
                    <template #description>
                        <span class="desc-title">{{ $t('New.NoProcess') }}
                            <span class="desc-add" @click="router.push('/layout/home?type=all')">{{ $t('New.Add')
                                }}</span>
                        </span>
                    </template>
</van-empty>
<van-grid square :border="false" v-else>
    <YZGrid v-for="item in process" :key="item.ProcessName" @onClick="onOpenForm" v-longpress="onLongPress"
        :attid="item.Id" :atttrue="item.IsCollection" :proname="item.ProcessName" :prover="item.ProcessVersion"
        :process="item" />
</van-grid>
</view>
</view> -->
        <!-- 首页 我的收藏 隐藏  end -->

        <!-- 展开面板 -->
        <van-action-sheet v-model:show="actionShow" :actions="actions" :cancel-text="$t('Global.Cancel')"
            close-on-click-action @cancel="onCancel" @select="onSelect" :close-on-click-overlay="false" />
        <!-- 流程弹出框 -->
        <van-popup v-model:show="procesShow" style="height: 100vh;" position="bottom">
            <renderForm :key="time" />
        </van-popup>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import YZGrid from '@/components/YZGrid.vue'
import emptyimg from '@/assets/imgs/empty.jpg'
import { useHome } from '@/logic/home/<USER>';
import YZFloderGrid from '@/components/YZFloderGrid.vue'
import { showDialog } from 'vant';
import { url } from '@/api/url';
import { useGetQuery } from '@/utils/request'
import i18n from '@/locale/index'

const keyword = ref('')
const { process,
    LibiaryData, chActiveTab, collActives, addColl, collActive,
    onLongPress, actionShow, actions, onCancel, onSelect, searchKwd,
    onOpenForm, onSearch, procesShow, time, historyData,
    treeProcess,
    breadClick,
    defaultPath,
    onFloderClick
} = useHome()

const currentUser = ref('')
const getParam = (name, defaultValue) => {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
    const r = window.location.search?.substr(1).match(reg);
    if (r != null) return decodeURI(r[2]);
    if (defaultValue !== undefined) return defaultValue;
    return null;
}
const getDeviceOS = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    // 检测是否为移动设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    if (!isMobile) return "unknown";
    // Windows Phone
    if (/windows phone/i.test(userAgent)) {
        return "Windows Phone";
    }
    // Android
    if (/android/i.test(userAgent)) {
        return "Android";
    }
    // iOS 检测
    if (['iPad Simulator', 'iPhone Simulator', 'iPod Simulator', 'iPad', 'iPhone', 'iPod'].includes(navigator.platform) ||
        (navigator.userAgent.includes("Mac") && "ontouchend" in document)) {
        return "iOS";
    }
    return "unknown";
}
const checkCountry = () => {
    return new Promise((resolve, reject) => {
        fetch("https://api.visitorapi.com/api/?pid=2MZwLZ2vBOhdsTWvnBd2")
            .then(response => response.json())
            .then(data => {
                if (data.status === 200) {
                    resolve(data.data.countryCode);
                } else {
                    reject(new Error(data.result.errorMsg));
                }
            })
            .catch(error => {
                reject(error);
            });
    });
}
// 检查版本并显示下载对话框
const check = (appUpgradeInfo) => {
    const userAppVer = getParam("ver", "");
    console.log("%c [ userAppVer ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", userAppVer)
    const userAppId = getParam("appid", ""); //未使用
    let downloadUrl = '';
    const os = getDeviceOS();
    console.log("%c [ os ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", os)
    if (os === "iOS") {
        if (appUpgradeInfo.UserType === "Linde") {
            if (userAppVer.length === 0) {
                downloadUrl = !appUpgradeInfo.IOS_PPM_Upgraded ?
                    'https://portal.manage.microsoft.com/WebCP/Apps/55db45ea-21fe-4d97-b75e-5b189e407651' : '';
                console.log("%c [ 456  downloadUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", downloadUrl)
                if (downloadUrl) {
                    showUPVersion(downloadUrl)
                }
            } else {
                downloadUrl = userAppVer < appUpgradeInfo.IOS_PPM_Version ?
                    'https://portal.manage.microsoft.com/WebCP/Apps/55db45ea-21fe-4d97-b75e-5b189e407651' : '';
                console.log("%c [ 123  downloadUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;",)
                if (downloadUrl) {
                    showUPVersion(downloadUrl)
                }
            }
        } else {
            if (userAppVer.length === 0) {
                downloadUrl = !appUpgradeInfo.IOS_PPA_Upgraded ?
                    'https://apps.apple.com/us/app/linde-ppa/id6444094203' : '';
                if (downloadUrl) {
                    showUPVersion(downloadUrl)
                }
            } else {
                downloadUrl = userAppVer < appUpgradeInfo.IOS_PPA_Version ?
                    'https://apps.apple.com/us/app/linde-ppa/id6444094203' : '';
                if (downloadUrl) {
                    showUPVersion(downloadUrl)
                }
            }
        }
        // if (downloadUrl) {
        //     showDownload(downloadUrl);
        // }
    } else if (os === "Android") {
        checkCountry().then(countryCode => {
            if (countryCode === "CN") {
                if (userAppVer.length === 0) {
                    downloadUrl = !appUpgradeInfo.Android_OpenQQ_Upgraded ?
                        'https://a.app.qq.com/o/simple.jsp?pkgname=com.yzsoft.lindeapp' : '';
                    if (downloadUrl) {
                        showUPVersion(downloadUrl)
                    }
                } else {
                    downloadUrl = userAppVer < appUpgradeInfo.Android_OpenQQ_Version ?
                        'https://a.app.qq.com/o/simple.jsp?pkgname=com.yzsoft.lindeapp' : '';
                    if (downloadUrl) {
                        showUPVersion(downloadUrl)
                    }
                }
            } else {
                if (userAppVer.length === 0) {
                    downloadUrl = !appUpgradeInfo.Android_Google_Upgraded ?
                        'https://play.google.com/store/apps/details?id=ppa.lindele.cn' : '';
                    if (downloadUrl) {
                        showUPVersion(downloadUrl)
                    }
                } else {
                    downloadUrl = userAppVer < appUpgradeInfo.Android_Google_Version ?
                        'https://play.google.com/store/apps/details?id=ppa.lindele.cn' : '';
                    if (downloadUrl) {
                        showUPVersion(downloadUrl)
                    }
                }
            }
            // if (downloadUrl) {
            //     showDownload(downloadUrl);
            // }
        }).catch(error => {
            console.log("%c [ Failed to obtain country code: ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error.message)
        });
    }
};
const beforeClose = (action) =>
    new Promise((resolve) => {
        setTimeout(() => {
            resolve(action === 'confirm');
        }, 1000);
    });
const showUPVersion = (downloadUrl) => {
    showDialog({
        message: i18n.global.t('home.ForceUpgrade_tips'),
        theme: 'round-button',
        // beforeClose
    }).then(() => {
        showDownload(downloadUrl);
    }).catch(() => {
        console.log('用户选择稍后升级');
    });
}

const showDownload = (downloadUrl) => {
    console.log("%c [ 下载地址 ===>>> downloadUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", downloadUrl)
    window.location.href = downloadUrl;
}


const router = useRouter()
// tab栏
const slide = ref([
    { name: 'process', title: 'home.Process', icon: 'icon-icon-liuchengku', path: '/layout/home' },
    { name: 'approve', title: 'New.Approve', icon: 'icon-icon-shenpi', path: '/layout/work', },
    { name: 'apply', title: 'New.Apply', icon: 'icon-icon-shenqing', path: '/layout/work?type=ApplyWork', },
    { name: 'my', title: 'LayOut.My', icon: 'icon-icon-wode', path: '/layout/my' }
])

// 收藏列表
const shoeDel = ref(false)

// 删除我的收藏
const delFavo = async (item) => {
    console.log(11);
}
onMounted(async () => {
    const params = {
        appUpgrade: '1'
    }
    const { user, appUpgradeInfo } = await useGetQuery(url.login.getUserInfo, params)
    currentUser.value = user?.DisplayName
    if (location.hostname !== 'localhost') {
        check(appUpgradeInfo)
    }
})
</script>

<style lang="scss" scoped>
.page {
    background-color: var(--yz-layout-mian);
    min-height: 100vh;
}

.search {
    box-sizing: border-box;
    overflow: hidden;
}

.slide {
    background-color: #00A0E1;
    // background-color: #1D5A91; //#00A0E1;
    display: flex;
    padding: 30px 10px;

    .slide-li {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #ffffff;

        .iconfont {
            font-size: 30px;

        }

        p {
            margin-top: 8px;
            font-size: var(--yz-com-14);
            font-family: Microsoft YaHei UI-Regular, Microsoft YaHei UI;
            color: #FFFFFF;
        }
    }
}

.desc-title {
    font-family: PingFang-SC-ExtraLight;
    font-size: var(--yz-home-desctitle);
    color: #999999;
    text-align: center;
    font-weight: 400;
    margin-top: -25px;
    display: block;

    .desc-add {
        font-family: PingFang-SC-ExtraLight;
        font-size: var(--yz-home-desctitle);
        color: #1989FB;
        text-align: center;
        line-height: 16px;
        font-weight: 400;
    }
}

// 列表
.fave {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    padding: 10px;

    .fave-box {
        flex: 1;
        padding: 12px 10px;
        background-color: var(--van-background-2);
        box-shadow: 0px 0px 4px 0px rgba(222, 222, 222, 0.25);
        border-radius: 5px;
        overflow: hidden;
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }

        .fave-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            .fave-title-l {
                font-size: var(--yz-com-16);
                font-weight: 400;
                color: #999999;
            }

            .fave-title-r {
                font-size: 12px;
                font-weight: 400;
                color: #999999;
            }
        }
    }

    .fave-list {
        display: flex;
        flex-wrap: wrap;

        .fave-li {
            width: calc(100% / 4);
            box-sizing: border-box;
            padding: 16px 5px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .fave-li-box {
                text-align: center;
                position: relative;
                width: 100%;

                .del-icon {
                    position: absolute;
                    top: -10px;
                    right: -10px;
                    z-index: 1;
                    color: #FA5A5A;
                    font-size: 20px;
                }
            }

            img {
                width: 28px;
                height: 28px;
            }

            .iconfont {
                font-size: 28px;
            }

            p {
                width: 100%;
                margin-top: 8px;
                font-size: 12px;
                font-weight: 400;
                color: #636363;
                text-align: center;
                overflow: hidden;
                -webkit-line-clamp: 2;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
            }
        }
    }
}
</style>