const validator = {
  /**
   *
   * @param {当前绑定自定义高级验证的值对对象} vModel 包含（value,optionValue）
   * @param {当前绑定自定义高级验证的控件} field
   * @param {整个表单字段} $fields
   */
  cfd: function(vModel, field, $fields) {
    const value = clientHelper.getFieldModelValue($fields, "单行文本2");
    const numb = Number(value);
    if (numb <= 0) {
      return "天数不能小雨0";
    } else {
      return true;
    }
  },
  cfd: function(vModel, field, $fields) {
    const value = clientHelper.getFieldModelValue($fields, "field");
    if (value == "1") return true;
    return "自定义验证错误信息提示";
  },
};
