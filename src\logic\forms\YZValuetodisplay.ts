
//formtitle
import { Y<PERSON><PERSON><PERSON><PERSON>g, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON>ty<PERSON> } from "./formModel";
import { useLang } from "@/utils";
export class YZValuetodisplayConfig extends YZConfig {
    private valueConvert: any;
    private valueExpress: string;
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZValuetodisplay'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.valueConvert = parmas['extends'].valueConvert
        this.valueExpress = parmas['extends'].$express
    }
    getValueConvert() {
        return this.valueConvert
    }
    getExpressValue(): string {
        return this.valueExpress
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZValuetodisplayStyle extends Y<PERSON><PERSON><PERSON><PERSON><object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZValuetodisplay extends YZField {
    private title: string;
    constructor(params: any) {
        super(params)
        this.title = params['__config__'].extends['title']
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZValuetodisplayConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZValuetodisplayStyle(style)
    }
    getTitle() {
        return this.title
    }
}