import { OutType } from "@/types/bpm";
import { useDayjs } from "@/utils";

export interface IMyMenu {
    name: string;
    iconName: string;
    handler(index: number): void;
    onBack(name: string): void;
    color: string;
    component: string;
    isPop: boolean

}
export interface INewSetting {
    name: string;
    model: boolean
}
export class OutOffice {
    from: string;
    state: OutType;
    to: string;
    constructor(params: any) {
        this.from = ''
        this.to = ''
        this.state = 'InOffice'
        if (params) {
            const { Data } = params
            this.state = Data && Data['OutOfOfficeState'] as OutType
            if (this.state === 'Period') {
                this.from = useDayjs(Data['OutOfOfficeFrom']).format('YYYY-MM-DD HH:mm')
                this.to = useDayjs(Data['OutOfOfficeTo']).format('YYYY-MM-DD HH:mm')
            }
        }


    }
}
export interface IOutMenu {
    name: string;
    value: OutType;
}
export interface ILangMenu {
    name: string;
    value: string;
}
export interface ISystemMenu extends ILangMenu {
    isSelect: boolean;
}