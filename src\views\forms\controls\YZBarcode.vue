
<template>
    <div>
        <p>{{ vModel }}</p>
        <van-field v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required"
            :readonly="field.readonly" :disabled="field.disabled" :rules="field.config.rules" :label="field.config.label">
            <template  #label> 
               <div v-if="field.config.showLabel"> {{  field.config.label }}</div>
            </template>
            <template #input>
                <div class="code" :style="{width: config.getWidth()+'px', height: config.getHeight()+'px' }">
                    <img :src="field.imgSrc" alt="">
                </div>
            </template>
        </van-field>
    </div>
</template>

<script lang="ts" setup>
import { YZBarcode ,YZBarcodeConfig } from '@/logic/forms/YZBarcode';
import { eventBus } from '@/utils/eventBus';
import { useAxios, usePostBody, useRequest } from '@/utils/request';
import {url} from '@/api/url'
import { computed, onMounted, PropType, ref, watch } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZBarcode>,
        required: true
    },
    modelValue: {
        type: [String, Number],
        default: 0
    },
    index: {
        type: Number,
        defualt: -1
    }
})
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
const config =props.field.config as YZBarcodeConfig
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {

            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            buildQrImage(params.value)
        }
    }
})
const buildQrImage =(value:string)=>{
     const axios = useAxios()
     axios.request({
        url: url.process.createQrCode,
        method:'get',
        params:{
            text: value,
        width:config.getWidth(),
        height: config.getHeight(),
        format:config.getFormatter(),
        _dc: new Date().getTime()
        },
        responseType:'blob'
     }).then(result=>{
        const blob = new Blob([result.data], { type: 'image/png' })
         const url = window.URL.createObjectURL(blob)
        props.field.imgSrc = url 
    }).catch(error=>{
       console.log('创建动态码失败',error)
    })
}
</script>
<style lang="scss"  scoped>
.code {
    border: 1px dashed #ddd7d7;
    max-width: 300px;
    max-height: 300px;
    img {
        width: 100%;
    }
}
</style>