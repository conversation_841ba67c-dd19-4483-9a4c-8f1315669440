import { defineStore } from 'pinia'

export const HSE = defineStore('hse', {
    state: () => {
        return {
            manyParams: {
                // CourseID 
                // :
                //     "***************",
                // CreateBy
                //     :
                //     "<EMAIL>",
                // Issuer
                //     :
                //     "<PERSON>",
                // IssuerAccount
                //     :
                //     "<EMAIL>",
                // IssuerDate
                //     :
                //     "2024-03-14 00:00:00",
                // TrainingCategory
                //     :
                //     "Site HSE Induction Training -  Full Course",
                // UpdateBy
                //     :
                //     "<EMAIL>",
                // UserType
                //     :
                //     "Linde"
            },
            projectData: {
                projectName: '',
                projectCode: '',
            },
            SupplierParams: {
                SupplierName: '',
                SupplierCode: '',
            },
            ProblemOwner: [],
            FollowupPersonList: [],
            Informant: [],
            processTime: '',
            processTime_name: '',
            HazardClassification: '',
            WorkArea: '',
            checkedPersons:[],
        }
    },
    actions: {
        setWorkArea(value) {
            this.WorkArea = value
        }
    }
})