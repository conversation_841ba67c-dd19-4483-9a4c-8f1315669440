<template>
    <div>
        <van-popup teleport="body" v-model:show="isShowModel" position="bottom" :style="{ height: '100%' }">
            <div class="data-brower-content">
                <van-nav-bar :title="config.getDlgConfig()?.title || $t('Form.WindowSelection')"
                    :left-text="$t('Global.Back')" left-arrow @click-left="onLeftClick"
                    :right-text="$t('Global.Confirm')" @click-right="onRightClick" />
                <div>
                    <div v-if="config.getDataBoweswer().ds.type == 'table' || config.getDataBoweswer().ds.type == 'form'"
                        style="padding: var(--van-search-padding);">
                        <div v-for="(sl, si) in tableSearchArr" :key="si" class="search-item">
                            <van-search style="padding: 0;flex: 1;" v-model="sl.searchValue"
                                :placeholder="$t('Form.keywordsSearch')" @search="onSearch">
                                <template #left>
                                    <div class="search-box">
                                        <van-popover v-model:show="sl.showFieldParams" trigger="click"
                                            placement="bottom-start" :actions="tableParamsArr"
                                            @select="(val) => onFieldParams(val, si)">
                                            <template #reference>
                                                <div class="search-text">
                                                    <span>
                                                        {{ sl.searchFiled == 'all' ? i18n.global.t('home.All') :
                                                            sl.searchFiled }}
                                                    </span>
                                                    <van-icon :name="sl.showFieldParams ? 'arrow-up' : 'arrow-down'" />
                                                </div>
                                            </template>
                                        </van-popover>
                                    </div>
                                    <div class="search-box">
                                        <van-popover v-model:show="sl.showOpParams" trigger="click" placement="bottom"
                                            :actions="sl.opValueList" @select="(val) => onOpsParams(val, si)">
                                            <template #reference>
                                                <div class="search-text search-ops">
                                                    <span>
                                                        {{ sl.opValue === 'like' ? '包含' : sl.opValue }}
                                                    </span>
                                                    <van-icon :name="sl.showOpParams ? 'arrow-up' : 'arrow-down'" />
                                                </div>
                                            </template>
                                        </van-popover>
                                    </div>
                                </template>
                            </van-search>
                            <div style="display: flex;flex-direction: column;padding: 0 5px;font-size: 19px;">
                                <van-icon v-if="tableSearchArr.length - 1 === si" name="add"
                                    style="color: var(--van-button-primary-background);" @click="onAddTableParam" />
                                <van-icon v-if="tableSearchArr.length > 1" name="clear"
                                    style="color: var(--van-button-danger-background);"
                                    @click="onRemoveTableParam(si)" />
                            </div>
                        </div>
                        <!-- <van-search  v-model="searchKey" :placeholder="$t('Form.keywordsSearch')" @search="onSearch" /> -->
                    </div>
                    <van-dropdown-menu class="esb-params" v-if="config.getDataBoweswer().ds.type == 'esb'"
                        swipe-threshold="4">
                        <van-dropdown-item v-for="(item, index) in esbParams" :key="index + item.name"
                            :title="item.value || item.name" ref="esbParamsRef">
                            <div style="padding: 10px;">
                                <van-cell center title="是否启用">
                                    <template #right-icon>
                                        <van-switch v-model="item.isEnable" />
                                    </template>
                                </van-cell>
                                <van-field input-align="right" v-model="item.modelValue" :label="item.name"
                                    :placeholder="$t('Form.Enter_tips')" clearable />
                                <van-button style="margin-top: 10px;" type="primary" block round
                                    @click="onEsbSearch(index, item.modelValue)">确认</van-button>
                            </div>
                        </van-dropdown-item>
                    </van-dropdown-menu>
                </div>
                <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                    :style="{ 'overflow-y': 'scroll', flex: 1, }">
                    <van-list v-model:loading="loadData.loading" :finished="loadData.finished" finished-text="没有更多了"
                        @load="onLoad">
                        <div class="div-work" v-for="(item, key) in viewData" :key="key" @click="onSelect(item, key)">
                            <div v-for="col in viewColsData" :key="col">
                                <span v-if="!col.isHide">{{ col.name }}: {{ item[col.name] }}</span>
                            </div>
                            <span class="icon-span" v-show="selectKey.includes(key)"><van-icon name="success" /></span>
                        </div>
                    </van-list>
                </van-pull-refresh>
            </div>

        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import { formBuilder } from "@/logic/forms/formBuilder";
import { YZDataBowerConfig } from '@/logic/forms/YZDataBower';
import { YZField } from '@/logic/forms/formModel';
import { useBase64, useFormData, useIsChinese, useParamsFormatter, useStrToUnicode, useUUID, useUninCode, decodeBase64 } from '@/utils';
import { onMounted, computed, PropType } from 'vue';
import { url } from '@/api/url'
import { ref } from 'vue';
import { useGetBody, usePostBody } from '@/utils/request';
import { useUserStore } from '@/store/users';
import { showNotify } from 'vant';
import { reactive, watch } from 'vue';
import i18n from '@/locale/index'
import CryptoJS from 'crypto-js'
const props = defineProps({
    field: {
        type: Object as PropType<YZField>,
        default: {}
    },
    isShow: {
        type: Boolean,
        default: false
    }
})
const isShowModel = computed(() => props.isShow)
const sSHow = ref<boolean>(false)
const config = props.field.config as YZDataBowerConfig
// console.log("%c [ pageSize 分页 props.field.config ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", config)

const userstore = useUserStore()
const viewData = ref<Array<any>>([])
const viewColsData = ref<Array<any>>([])
const selectKey = ref<Array<any>>([])
const selectItem = ref<Array<any>>([])
const searchKey = ref<string>('')
const searchValue = ref<string>('')

const tableSearchArr = ref<Array<any>>([])
const showFieldParams = ref<boolean>(false)
const showOpParams = ref<boolean>(false)
const tableParamsArr = ref<any>([])
const dataTypes = ref<any>({
    Decimal: { op: 'number', valueField: 'number' },
    Double: { op: 'number', valueField: 'number' },
    Single: { op: 'number', valueField: 'number' },
    Int16: { op: 'number', valueField: 'number' },
    Int32: { op: 'number', valueField: 'number' },
    Int64: { op: 'number', valueField: 'number' },
    UInt16: { op: 'number', valueField: 'number' },
    UInt32: { op: 'number', valueField: 'number' },
    UInt64: { op: 'number', valueField: 'number' },
    SByte: { op: 'number', valueField: 'number' },
    Byte: { op: 'number', valueField: 'number' },
    Char: { op: 'char', valueField: 'string' },
    Boolean: { op: 'bool', valueField: 'bool' },
    DateTime: { op: 'date', valueField: 'datetime' },
    String: { op: 'string', valueField: 'string' },
    'Byte[]': { op: 'number', valueField: 'number' },
})
const ops = ref<any>({
    number: ['>', '>=', '=', '<', '<=', '!='],
    string: ['like', '>', '>=', '=', '<', '<=', '!='],
    date: ['>', '>=', '=', '<', '<=', '!='],
    bool: ['='],
    char: ['>', '>=', '=', '<', '<=', '!=']
})
// const showSearch = ref<boolean>(false)
const esbParams = ref<any>([])
const esbParamsRef = ref<any>(null)
const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    pageSize: config.getDataBoweswer()?.pageSize || 10
})
watch(() => props.isShow, (ov) => {
    if (ov) {
        onRefresh()
    }
})
// onMounted(async () => {
//    await setTableParams()
// })
watch(() => isShowModel.value, async (value) => {
    if (value) await setTableParams()
}, { immediate: true })
const setTableParams = async () => {
    let dataBrower = config.getDataBoweswer()
    if (!dataBrower) return
    const { $map, ds, viewConfig } = config.getDataBoweswer()
    const start = (loadData.pageIndex - 1) * loadData.pageSize
    const quId = ds.type === 'esb' ? ds.esbId : ds.dataSourceId
    if (ds.type === 'esb') return
    // let tableParamsUrl = useParamsFormatter(url.dataSource.getTableParams,{
    //     params1:quId,
    //     params2:ds.type,
    //     params3: ds.tableName
    // })
    let tableParamsUrl = ''
    if (ds.type == 'table') {
        tableParamsUrl = useParamsFormatter(url.dataSource.getTableParams, {
            params1: quId,
            params2: ds.type,
            params3: ds.tableName
        })
    } else if (ds.type == 'form') {
        tableParamsUrl = useParamsFormatter(url.dataSource.getFormParams, {
            params1: ds.formId,
            params2: ds.formTable,
        })
    }
    const data = await useGetBody(tableParamsUrl)
    tableParamsArr.value = []
    tableParamsArr.value.push({
        name: i18n.global.t('home.All'),
        dataType: "String",
        op: [{ value: "like", text: i18n.global.t('home.Contain') }],
        text: i18n.global.t('home.All'),
        value: 'all'
    })
    let filterKeyArr = ds.filter ? Object.keys(ds.filter) : []
    viewConfig.columns.forEach((vc: any) => {
        data.forEach((el: any) => {
            if (vc.columnName == el.name && !filterKeyArr.includes(el.name)) {
                // console.log(el.dataType)
                let obj = {
                    ...el,
                    op: setOpData(dataTypes.value[el.dataType]?.op || dataTypes.value['String'].op),
                    text: vc.displayName || vc.columnName,
                    columnName: vc.columnName,
                    value: vc.columnName,
                }
                tableParamsArr.value.push(obj)
            }
        })
    })
    tableSearchArr.value = [{
        searchFiled: "all", // 查询字段
        opValue: "like",
        opValueList: tableParamsArr.value[0].op,
        searchValue: "", // 查询值
        showFieldParams: false,
        showOpParams: false,
        dataType: 'String'
    }]
}
const setOpData = (dataType: string) => {
    let arr: any = []
    if (ops.value[dataType]) {
        ops.value[dataType].forEach((item: any) => {
            arr.push({
                text: item == 'like' ? '包含' : item,
                value: item
            })
        })
    } else {
        arr.push(ops.value['string'])
    }
    return arr
}
const onFieldParams = (action: any, index: number) => {
    tableSearchArr.value[index].searchFiled = action.value
    tableSearchArr.value[index].opValueList = action.op
    tableSearchArr.value[index].dataType = action.dataType
    let obj = tableSearchArr.value[index].opValueList.find((item: any) => item.value == tableSearchArr.value[index].opValue)
    if (!obj) {
        tableSearchArr.value[index].opValue = ''
    }
}
const onOpsParams = (action: any, index: number) => {
    tableSearchArr.value[index].opValue = action.value
}
const onAddTableParam = () => {
    let obj = {
        searchFiled: "", // 查询字段
        opValue: "",
        opValueList: [],
        searchValue: "", // 查询值
        showFieldParams: false,
        showOpParams: false,
        dataType: ''
    }
    tableSearchArr.value.push(obj)
}
const onRemoveTableParam = (index: number) => {
    tableSearchArr.value.splice(index, 1)
}
const onRefresh = () => {
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    viewColsData.value = []
    viewData.value = []
    selectItem.value = []
    selectKey.value = []
    onLoad()
}
const onSearch = async () => {
    //   let searchArr = tableSearchArr.value.filter(el => el.searchFiled && el.opValue && el.searchValue )
    let searchArr = tableSearchArr.value.filter(el => el.searchFiled && el.opValue && el.searchValue).map(item => {
        let findObj = tableParamsArr.value.find((pi: any) => pi.text == item.searchFiled)
        item.searchColField = findObj.columnName || findObj.text
        return item
    })
    console.log("%c [ 搜 2 searchArr ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchArr)

    let strs = '['
    searchArr.forEach((item, index) => {
        let value = setFiledsValue(item.dataType, item.searchValue)
        value = typeof value == 'string' ? `"${value}"` : value
        // strs += `""`
        strs += `{"name":"${buildFilterStr(item.searchFiled)}","op":"${item.opValue}","value":${buildFilterStr(value)},"isAll":${item.searchFiled === 'all'}}${index < searchArr.length - 1 ? ',' : ''}`
    })
    strs += ']'
    searchValue.value = encodeURIComponent(btoa(strs))
    loadData.pageIndex = 1
    loadData.pageSize = config.getDataBoweswer()?.pageSize || 10
    viewData.value = []
    viewColsData.value = []
    await onLoad()

    // const code = useUninCode(val)
    // searchValue.value = btoa(`[{"name":"all","op":"like","value":"${code}","isAll":true}]`)
    // loadData.pageIndex = 1
    // loadData.pageSize = 10
    // viewData.value = []
    // viewColsData.value = []
    // await onLoad()

    //{"name":"all","op":"like","value":"\u53f0\u5f0f","isAll":true}
}
const setFiledsValue = (dataType: string, value: string) => {
    let obj: any = {
        'number': Number(value),
        'string': String(value),
        'bool': value == 'true' ? true : false,
        'datetime': String(value),
    }
    return obj[dataTypes.value[dataType]?.valueField || dataTypes.value['String'].valueField]
}
const onEsbSearch = async (index: number, value: string | number) => {
    esbParamsRef.value[index].toggle(false)
    esbParams.value[index].value = value


    let strs = '['
    esbParams.value.filter((item: any) => item.isEnable).forEach((item: any) => {
        const name: string = buildFilterStr(item.name)
        strs += `{"name":"${name}","value":"${buildFilterStr(item.value)}"},`
    })
    if (strs.lastIndexOf(',') > -1)
        strs = strs.substring(0, strs.lastIndexOf(','))
    strs += "]"
    // console.log(strs)
    searchValue.value = btoa(strs)
    await onRefresh()
}
const emit = defineEmits(['update:isShow', 'onDataSave'])
// const fields = formBuilder.formConfig?.getFormAllFieldsRecord(props.field.targetFormId)

const onLoad = async () => {
    if (loadData.refreshing) {
        loadData.refreshing = false
        viewData.value = []
    }
    if (config && config.getDataBoweswer()) {
        const { $map, ds, viewConfig } = config.getDataBoweswer()
        const start = (loadData.pageIndex - 1) * loadData.pageSize
        const quId = ds.type === 'esb' ? ds.esbId : ds.dataSourceId
        let newUrl: string = ''
        if (ds.type === 'esb') {
            newUrl = useParamsFormatter(url.dataSource.getDataESB, {
                params1: ds.type,
                params2: quId,
            })
            // esb 固定搜索参数
            let esbParamsUrl = useParamsFormatter(url.dataSource.getEsbParams, {
                params1: quId,
            })
            const data = await useGetBody(esbParamsUrl)
            esbParams.value = Object.keys(esbParams.value).length == 0 ? data.map((item: any) => {
                return {
                    name: item.name,
                    value: '',
                    isEnable: true
                }
            }) : esbParams.value
        } else if (ds.type === 'table') {
            newUrl = useParamsFormatter(url.dataSource.getDataSource, {
                params1: quId,
                params2: ds.type,
                params3: ds.tableName
            })
            console.log("%c [ newUrl 2]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)

        } else if (ds.type === 'form') {
            newUrl = useParamsFormatter(url.dataSource.getFormPage, {
                params1: ds.formId,
                params2: ds.formTable,
            })
        }
        const codeCol = viewConfig.columns as Array<any>
        let lc = 'W10='
        //小米报销需要注释，临时方案
        if (codeCol && codeCol.length > 0) {
            let mapArr = $map ? Object.keys($map) : []
            let newCodeColArr = codeCol.map(x => x.columnName)
            mapArr.forEach(mv => {
                let findObj = newCodeColArr.find(col => col === mv)
                if (!findObj) {
                    newCodeColArr.push(mv)
                }
            })
            const colArrys = newCodeColArr.map(x => "\"" + buildFilterStr(x) + "\"").join(',')
            // const colArrys = codeCol.map(x => "\"" + buildFilterStr(x.columnName) + "\"").join(',')
            lc = btoa(`[${colArrys}]`)
        }
        let cf = ''
        const newColds = [...codeCol]
        if (newColds && newColds.length > 0) {
            let mapArr = $map ? Object.keys($map) : []
            let newCodesArr = newColds.map(x => x.columnName)
            mapArr.forEach(mv => {
                let findObj = newCodesArr.find(col => col === mv)
                if (!findObj) {
                    newCodesArr.push(mv)
                }
            })
            const colArrys = newCodesArr.map(x => "\"" + buildFilterStr(x) + "\"").join(',')
            // const colArrys = newColds.map(x => "\"" + buildFilterStr(x.columnName) + "\"").join(',')
            cf = btoa(`[${colArrys}]`)
        }
        let ff = 'e30='
        // showSearch.value = !(Object.keys(ds.filter).length > 0 && ds.filter)
        const fields = formBuilder.formConfig?.getFormAllFieldsRecord(props.field.targetFormId)
        if (ds.filter) {
            let strs = '{'
            Object.keys(ds.filter).forEach(key => {
                const item = ds.filter[key]
                if (fields && fields.length > 0) {
                    let valueData = item.value
                    if (item.field) {
                        if (item.field.indexOf('.') > -1) {
                            // 读取明细表
                            const fieldItem = fields.find(x => x.tableName == item.field)
                            if (fieldItem) {
                                valueData = fieldItem.vModel.value
                            }
                        } else {
                            const fieldItem = fields.find(x => !x.tableName && x.vModel.model == item.field)
                            if (fieldItem) {
                                if (fieldItem.config.ctype === 'YZUploader' || fieldItem.config.ctype === 'YZSelect') {
                                    valueData = fieldItem.vModel.optionValue
                                } else {
                                    valueData = fieldItem.vModel.value
                                }

                            }
                        }
                    }
                    const nkey: string = buildFilterStr(key)
                    strs += `"${nkey}":{"op":"${item.op}","value":"${buildFilterStr(valueData)}"},`
                }
            })
            if (strs.lastIndexOf(',') > -1)
                strs = strs.substring(0, strs.lastIndexOf(','))
            strs += "}"
            ff = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(strs))
        }
        const objs: any = {
            o: '', //decodeBase64(ds.orderBy),
            start: start,
            page: loadData.pageIndex,
            limit: loadData.pageSize,
            f: ff,
            c: cf, // 全部列
            lc: lc, // 查询列      
        }
        if (searchValue.value)
            objs.s = searchValue.value
        const formdata = useFormData(objs)
        let formURLData = ''
        Object.keys(objs).forEach(key => {
            formURLData += `${key}=${objs[key]}&`
        })
        if (formURLData.lastIndexOf('&') > -1)
            formURLData = formURLData.substring(0, formURLData.lastIndexOf('&'))
        const data = await usePostBody(newUrl, {}, formURLData)
        if (data.code === 1 && data.message) {
            showNotify({ type: 'danger', message: '无法加载数据' })
        } else {
            if (data && data.children) {
                let viewCols: Array<any> = []
                if (viewConfig) {
                    viewCols = viewConfig.columns as Array<any>
                    // const findex = viewCols.findIndex(x => x.columnName === valueField)
                    // if (findex < 0) {
                    //     viewCols.push({
                    //         columnCode: valueField,
                    //         columnName: valueField,
                    //         isHide: true
                    //     })
                    // }
                    if (loadData.pageIndex === 1) {
                        // 构建动态列
                        viewCols.forEach(col => {
                            if (col.displayName) {
                                viewColsData.value.push(
                                    {
                                        name: col.displayName,
                                        isHide: col.isHide
                                    })
                            } else {
                                viewColsData.value.push({
                                    name: col.columnName,
                                    isHide: col.isHide

                                })
                            }
                        })
                    }
                } else {
                    //const viewCols:Array<any>
                    if (loadData.pageIndex === 1) {
                        Object.keys($map).forEach(item => {
                            viewColsData.value.push({
                                name: item,
                                isHide: false
                            })
                            viewCols.push({
                                columnName: item,
                                displayName: item
                            })
                        })
                    }
                }
                const rows = data.children
                for (let i = 0; i < rows.length; i++) {
                    const fields = rows[i]
                    const json: any = {}
                    let mapKeyArr = $map ? Object.keys($map) : []
                    mapKeyArr.forEach(mapCol => {
                        const findMapCol = viewCols.find(vcs => vcs.columnName === mapCol)
                        if (!findMapCol) {
                            viewCols.push({
                                columnCode: mapCol,
                                columnName: mapCol,
                                isHide: true
                            })
                        }

                    })
                    // 构建动态列
                    viewCols.forEach(col => {
                        if (col.displayName) {
                            let name = fields[col.columnName]
                            let colName = fields[col.columnName]
                            if (name === undefined) {
                                // 解决liunx 大小写敏感的问题
                                Object.keys(fields).forEach(vkey => {
                                    const nKey = vkey.toUpperCase()
                                    const namekey = col.columnName.toUpperCase()
                                    if (nKey === namekey) {
                                        name = fields[namekey]
                                        colName = fields[namekey]
                                    }
                                })
                            }
                            json[col.displayName] = name
                            json[col.columnName] = colName
                        } else {
                            let name = fields[col.columnName]
                            if (name === undefined) {
                                // 解决liunx 大小写敏感的问题
                                Object.keys(fields).forEach(vkey => {
                                    const nKey = vkey.toUpperCase()
                                    const namekey = col.columnName.toUpperCase()
                                    if (nKey === namekey)
                                        name = fields[namekey]

                                })
                            }
                            json[col.columnName] = name

                        }
                        json['onlyId'] = useUUID()
                    })

                    viewData.value.push(json)
                }
                loadData.loading = false
                loadData.pageIndex++
                if (rows.length <= 0 || (viewData.value.length >= data.total)) {
                    loadData.finished = true
                    // console.log('v', viewData.value)
                } else {

                }

            }
        }
    } else {
        showNotify({ type: 'danger', message: '未配置任何数据源' })
        loadData.finished = true
    }
}
const buildFilterStr = (str: string): string => {
    if (useIsChinese(str)) {
        return useStrToUnicode(str)
    }
    return str
}
const onSelect = (item: any, index: number) => {
    // selectKey.value = index
    // selectItem.value = item
    // // console.log(selectKey.value,selectItem.value)
    const existIndex = selectItem.value.findIndex((el) => el.onlyId === item.onlyId)
    if (existIndex >= 0) {
        selectItem.value.splice(existIndex, 1)
        selectKey.value.splice(existIndex, 1)
    } else {
        selectItem.value.push(item)
        selectKey.value.push(index)
    }
}
const onRightClick = () => {
    emit('onDataSave', selectItem.value)
    emit('update:isShow', false)
    selectItem.value = []
    selectKey.value = []
    searchKey.value = ''
}
const onLeftClick = () => {
    emit('update:isShow', false)
    selectItem.value = []
    selectKey.value = []
    searchKey.value = ''
}
</script>
<style lang="scss" scoped>
.van-pull-refresh :deep .van-pull-refresh__head {
    background-color: var(--yz-div-background);
}

.van-list {
    background-color: var(--yz-div-background);
}

.div-work {
    border-bottom: 1px solid var(--yz-card-color);
    width: 93%;
    margin: 5px auto;
    padding: 10px;
    // background: white;

    background-color: var(--yz-div-background);
    font-size: var(--yz-source-work);
    border-radius: 5px;
    position: relative;
    color: var(--yz-text-666);

    .icon-span {
        display: block;
        line-height: 50px;
        position: absolute;
        right: 7px;
        font-size: var(--yz-source-span);
        top: 4px;
        color: blue;
    }
}

.esb-params :deep .van-dropdown-menu__bar {
    background-color: var(--van-search-content-background);
}

.search-box {
    display: flex;
    align-items: center;
    background: #ffffff;
    // margin: 0 5px;
    // padding: 5px 0;
    color: #999;
    /* 将字体颜色设置成浅灰色 */
}

.search-box .search-text {
    // width: 80px !important;
    // padding: 5px 0 5px 10px;
    border-radius: 4px;
    // background-color: #ffffff;
    border: 1px solid #f5f5f5;
    //border: none;
    width: auto;
    font-size: 14px;
    cursor: pointer;
    margin-right: 0;
    /* 添加样式 */
    display: flex;
    align-items: center;
    height: var(--van-search-input-height);
    background: var(--van-search-content-background);
}

.search-box .search-text.search-ops {
    margin-left: 5px;
    margin-right: 5px;
}

.search-box .search-text.search-ops span {
    text-align: center;
    width: 30px !important;
}

.search-box .search-text span {
    text-align: center;
    width: 70px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
}

.search-item {
    display: flex;
    align-items: center;
}

.search-item:not(:first-child) {
    margin-top: 5px;
}

.data-brower-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}
</style>