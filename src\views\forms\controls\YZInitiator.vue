<template>
    <div>
        <van-field v-if="!field.hidden" v-model="vModel"
        v-show="!field.hidden"
        :name="field.vModel.model"
         :label="field.config.label"
        :required="field.config.required"
        :key="field.vModel.model"
        :id="field.vModel.model"
        disabled
        :rules="field.config.rules"
        :placeholder="field.placeholder" />
    </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/users';
import { YZInitiator } from '@/logic/forms/YZInitiator';
import { computed, PropType, ref } from 'vue';
import { onMounted } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { cos } from 'mathjs';
const userStore = useUserStore()
const props = defineProps({
    field: {
        type: Object as PropType<YZInitiator>,
        required: true
    },
    modelValue:{
        type:String,
        default:''
    },
    index:{
        type:Number,
        default:-1
    }
})
const emit = defineEmits(['update:modelValue'])
eventBus.on('setInitUser',{
    cb:function(params) {
      vModel.value = params
      setFun()
      eventBus.off('setInitUser')
    }
})
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue',val)       
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
eventBus.on('setFieldDisable',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.disabled = params.value 
       }
    }
})
eventBus.on('setFieldHidden',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.hidden = params.value 
       }
    }
})
</script>
<style  scoped>

</style>