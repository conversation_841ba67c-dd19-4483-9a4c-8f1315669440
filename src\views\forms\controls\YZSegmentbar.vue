<template>
    <div class="clearAfter label-segment" v-if="!field.hidden">
        <span> {{ field.config.label }}</span>
    </div>
</template>

<script lang="ts" setup>
import {ref,onMounted} from 'vue'
import { YZSegmentbar } from '@/logic/forms/YZSegmentbar';
import { eventBus } from '@/utils/eventBus';
import { PropType } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZSegmentbar>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    isFirstLoad.value = false
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
</script>
<style  lang="scss" scoped>
.clearAfter::after {
    display: none;
}


.label-segment {
    background: #F4F6F9;
    min-height: 26px;
    // line-height: 26px;
    background: var(--yz-div-background);
    margin: 10px 0px;
    padding: 2px 2px;
    // text-indent: 1em;
    font-family: PingFangSC-Medium;
    // font-size: var(--yz-com-18);
    padding-left: 1em;
    border-left: 4px solid var(--van-button-primary-border-color);
    color: #666666;
    font-weight: 500;
    display: flex;
    align-items: center;
}
</style>