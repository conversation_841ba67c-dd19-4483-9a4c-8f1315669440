<template>
    <div>
        <!-- <van-field v-if="!field.hidden" :required="field.config.required" v-model="vModel" readonly
            :rules="field.config.rules" :disabled="field.disabled" :label="field.config.label" @click="openSource"
            right-icon="search">
        </van-field> -->
        <van-field v-if="!field.hidden" :required="field.config.required" v-model="vModel" readonly
            :rules="field.config.rules" :disabled="field.disabled" :label="field.config.label" @click="openSource"
            right-icon="search">
            <template #input>
                <input class="van-field__control" readonly v-model="vModel" v-show="!vModelDisplayField">
                <span v-show="vModelDisplayField">{{ vModelDisplayField && vModel ? vModelDisplayField : '' }}</span>
            </template>
        </van-field>
        <DataSource v-model:is-show="sourceShow" :field="field" @onDataSave="onDataSave" />
    </div>
</template>
<script lang="ts" setup>
import DataSource from '@/components/DataSource.vue';
import { YZDataBower, YZDataBowerConfig } from '@/logic/forms/YZDataBower';
import { parseExpress } from '@/logic/forms/express';
import { YZField } from '@/logic/forms/formModel';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, PropType, ref, nextTick } from 'vue';
import { useFormData, useIsChinese, useParamsFormatter, useStrToUnicode } from '@/utils';
import { url } from '@/api/url';
import { usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
const props = defineProps({
    field: {
        type: Object as PropType<YZDataBower>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const sourceShow = ref<boolean>(false)
const emit = defineEmits(['update:modelValue'])
const config = props.field.config as YZDataBowerConfig
const openSource = () => {
    if (!config.getDataBoweswer()) {
        showNotify({ type: 'danger', message: '未配置任何数据源' })
        return
    }
    if (!props.field.disabled)
        sourceShow.value = true
}
const isFirstLoad = ref<boolean>(true)
const vModelDisplayField = ref('')
onMounted(() => {
    isFirstLoad.value = false
    setValueConvert(vModel.value)
})
const vModel = computed({
    get() {
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
// watch(() => vModel.value, (newValue,oldValue) => {
//     if(newValue != oldValue){
//         setValueConvert(vModel.value)
//     }
// })
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const onDataSave = (data: any) => {
    console.log("%c [ >>> data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
    // 提取map信息
    const { $map, valueField } = config.getDataBoweswer()
    const valueConvert = config.getValueConvert()
    let ValueData = data[valueField]
    if (!ValueData) {
        const vName = valueField.toLocaleUpperCase()
        Object.keys(data).forEach(item => {
            const vItem = item.toUpperCase()
            if (vItem === vName)
                ValueData = data[item]
        })
    }
    vModel.value = ValueData
    setFun()
    if ($map) {
        const mapFields: YZField[] = []
        Object.keys($map).forEach(item => {
            const name = item
            const value = $map[name] as string
            let mapKey = value ?? name
            // 获取指定的控件
            let formId = props.field.targetFormId
            let field = parseExpress.getFieldByFid(formId, value)
            // let field = parseExpress.getField(value)
            if (value.indexOf(".") > -1 || value.indexOf("子表") > -1 || value.split('.').length === 2) {
                let fex = value.split('.')[0]
                let fieldModel = value.split('.')[1]
                field = parseExpress.getFieldByFid(formId, fex)
                const fieldValue = field?.vModel.value
                if (fieldValue && fieldValue.length > 0) {
                    let newIndex = props.index
                    if (newIndex === -1) {
                        // 表示当前操作的控件是主表，但是要给字表赋值
                        fieldValue.forEach((f: any, index: number) => {
                            const items = fieldValue[index].colums as Array<any>
                            const xfield = items.find(x => x.field.vModel.model === fieldModel)
                            if (xfield) {
                                mapFields.push(xfield.field)
                            }
                        })
                    } else {
                        const items = fieldValue[newIndex].colums as Array<any>
                        const xfield = items.find(x => x.field.vModel.model === fieldModel)
                        if (xfield)
                            mapFields.push(xfield.field)
                    }
                }
            }
            if (field) {
                mapFields.push(field)
            }
            if (mapFields.length > 0) {

                mapFields.forEach(field => {
                    if (field) {

                        let newValue = data[name]
                        if (!newValue) {
                            const vName = name.toLocaleUpperCase()
                            Object.keys(data).forEach(item => {
                                const vItem = item.toUpperCase()
                                if (vItem === vName)
                                    newValue = data[item]
                            })
                        }
                        if ($map[item] === field.table) {
                            eventBus.emit('onMap', true, {
                                value: data[item],
                                model: field.vModel.model,
                                uuid: field.uuid,
                                index: props.index
                            })
                        } else if ($map[item] === field.vModel.model && field.table == '' && field.tableName == '') {
                            eventBus.emit('onMap', true, {
                                value: data[item],
                                model: field.vModel.model,
                                uuid: field.uuid,
                                index: props.index
                            })
                        }

                    }
                })
            }

        })
    }
    setValueConvert(ValueData)

}
const setValueConvert = (value: string) => {
    const valueConvert = config.getValueConvert()
    if (valueConvert) {
        const { $map, displayField, filterParam, ds } = valueConvert
        if ($map) {
            //提取map的key
            const mapKeys = Object.keys($map).map(x => x);
            //提取显示列
            mapKeys.push(displayField)
            const endKeys = mapKeys.map(x => "\"" + buildFilterStr(x) + "\"").join(',')
            const body = {
                c: btoa(`[${endKeys}]`),
                f: btoa(`{"${buildFilterStr(filterParam)}": {"op":"=","value":"${buildFilterStr(value)}"}}`),
                o: ''
            }
            // if (!isPost)
            loadValueConvert(body)
        } else if (displayField) {
            const body = {
                f: btoa(`{"${buildFilterStr(filterParam)}": {"op":"=","value":"${buildFilterStr(value)}"}}`),
            }
            loadValueConvert(body)
        }
    }
}
const buildFilterStr = (str: string): string => {
    if (useIsChinese(str)) {
        return useStrToUnicode(str)
    }
    return str
}
const loadValueConvert = async (body: any) => {

    let formId = props.field.targetFormId
    const { $map, displayField, ds } = config.getValueConvert()
    const quId = ds.type === 'esb' ? ds.esbId : ds.dataSourceId
    let newUrl: string = ''
    // if (ds.type === 'esb') {
    //     newUrl = useParamsFormatter(url.dataSource.getDataESB, {
    //         params1: ds.type,
    //         params2: quId,
    //     })
    // } else {
    //     newUrl = useParamsFormatter(url.dataSource.getTableData, {
    //         params1: quId,
    //         params2: ds.type,
    //         params3: ds.tableName
    //     })
    // }
    if (ds.type === 'table') {
        newUrl = useParamsFormatter(url.dataSource.getTableData, {
            params1: quId,
            params2: ds.type,
            params3: ds.tableName
        })
    } else if (ds.type === 'esb') {
        newUrl = useParamsFormatter(url.dataSource.getEsbDataNoPage, {
            params1: quId,
        })
    } else if (ds.type == 'form') {
        newUrl = useParamsFormatter(url.dataSource.getFormNoPage, {
            params1: ds.formId,
            params2: ds.formTable
        })
    }
    const data = await usePostBody(newUrl, {}, useFormData(body), false, false)
    if (data && data.length > 0) {
        // isPost = true 
        const dataValue = data[0]
        vModelDisplayField.value = dataValue[displayField]
        console.log("%c [ vModelDisplayField.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", vModelDisplayField.value)
        Object.keys(dataValue).forEach(item => {

            const itemValue = dataValue[item]
            if ($map) {
                let mapName = $map[item]
                if (!mapName) {
                    Object.keys($map).forEach(key => {
                        if (key.toUpperCase() === item.toUpperCase()) {
                            mapName = $map[key]
                        }
                    })
                }
                if (mapName) {
                    // 实际值
                    // map对象中的目标
                    const mapValue = mapName
                    if (mapValue) {
                        // 获取指定的控件
                        const field = parseExpress.getFieldByFid(formId, mapValue)
                        if (field) {
                            eventBus.emit('setFieldValue', true, {
                                model: mapValue,
                                uuid: field.uuid,
                                value: itemValue
                            })
                        }

                    }
                } else if (item === displayField) {
                    vModel.value = String(itemValue)
                    setFun()
                }
            }

        })

    } else {
        // 既然没数据了就把所有都设置为空
        vModelDisplayField.value = ''
        if ($map) {
            Object.keys($map).forEach(item => {
                const fieldName = $map[item]
                const field = parseExpress.getFieldByFid(formId, fieldName)
                if (field) {
                    eventBus.emit('setFieldValue', true, {
                        model: fieldName,
                        uuid: field.uuid,
                        value: ''
                    })
                }
            })
        }
    }
}

eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid &&
            props.field.uuid === params.uuid && params.index == props.field.pindex) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid &&
            props.field.uuid === params.uuid && params.index == props.field.pindex) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid &&
            props.field.uuid === params.uuid && params.index == props.field.pindex) {
            props.field.vModel.value = String(params.value)
            nextTick(() => {
                setValueConvert(vModel.value)
                setFun()
            })
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid &&
            props.field.uuid === params.uuid && params.index == props.field.pindex) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
// 监听数据MAP ??
eventBus.on("onMap", {
    cb: function (params) {
        if (
            props.field.vModel.model === params.model &&
            props.field.uuid === params.uuid && params.index == props.field.pindex
        ) {
            //   props.field.vModel.value = params.value;
            // getHistoryFormInfo()
            // debugger
            if (params.uuid === props.field.uuid) {
                props.field.vModel.value = String(params.value)
                nextTick(() => {
                    setValueConvert(vModel.value)
                })

            }
        }
    },
});
</script>
<style scoped></style>