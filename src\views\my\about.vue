<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ $t('my.AboutAPP') }}
            </template>
        </YZNav>

        <div class="cent">
            <img src="../../assets/images/linde-logo.png" alt="">
            <P>PPA 2.0</P>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    width: 100%;
    background-color: var(--yz-layout-mian);

    .cent {
        margin-top: 200px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        img {
            height: 60px;
            object-fit: cover;
        }

        p {
            margin-top: 20px;
            font-size: var(--yz-com-16);
            color: #999999;
        }
    }
}
</style>