<template>
    <div>
        <van-field  v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required" 
            :rules="field.config.rules" :readonly="field.disabled" :label="field.config.label">
            <template #input>
                <van-slider v-model="vModel" :max="field.getMax()" :min="field.getMin()" :range="field.getRange()"
                    :step="field.getStep()" />
            </template>
        </van-field>
    </div>
</template>

<script lang="ts" setup>
import { YZSlider } from '@/logic/forms/YZSlider';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, PropType, ref } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZSlider>,
        required: true
    },
    modelValue: {
        type: Number,
        default: 0
    },
    index:{
        type:Number,
        default:-1
    }
})
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
eventBus.on('setFieldDisable',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.disabled = params.value 
       }
    }
})
eventBus.on('setFieldHidden',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.hidden = params.value 
       }
    }
})
</script>
<style  scoped>

</style>