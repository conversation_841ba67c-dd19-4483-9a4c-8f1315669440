import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
export class Y<PERSON><PERSON>hildFormConfig extends YZConfig {
    public fieldControlMode:string
    public fieldDisplayText:string
    private yzChildConfig:Object
    private defaultRules:Array<any>
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZChildForm'
        this.showLabel = parmas['showlable']
        this.fieldControlMode = parmas.extends['fieldControlMode']
        this.fieldDisplayText = parmas.extends['fieldDisplayText']
        this.yzChildConfig = parmas.extends
        this.defaultRules =  [{ required: true, message: this.label? `请填写${this.label}`:'请输入必填项'}]
        if (this.required && this.writable) {
            this.rules = this.defaultRules  
        }
    }
    getChildConfig(){
        return this.yzChildConfig
    }
}
export class YZChildFormStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    init<PERSON>tyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
        }
    }
}
export class Y<PERSON><PERSON>hildForm extends Y<PERSON><PERSON>ield {
    constructor(params: any) {
        super(params)
        const modelValue = params['__vModel__'].modelValue
        this.vModel.value = modelValue
        this.vModel.optionValue = ''
        // this.disabled =params.disabled
    }
    initConfig(config: any): YZConfig {
        return new YZChildFormConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZChildFormStyle(style)
    }
}