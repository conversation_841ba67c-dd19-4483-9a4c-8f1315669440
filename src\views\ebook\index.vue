<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                HSE e-Books
            </template>
        </YZNav>
        <div class="main">
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :loosing-text="$t('Form.Release_to_refresh')"
                :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loading" :finished="finished" :finished-text="$t('Form.NoMore')"
                    :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div class="list" @click="ebookRead(item)" v-for="item in ebookList" :key="item.id">
                        <div class="list-cent">
                            <div class="list-cent-t ellipsis">{{ item.CourseName }}</div>
                            <div class="list-cent-p ellipsis2">{{ item.CourseDescription }}</div>
                        </div>
                        <div class="list-icon">
                            <div v-if="item.IsRead">
                                <van-icon class="icon" name="passed" color="#0ABA1C" />
                                <p>{{ $t('CSTC.Completed') }}</p>
                            </div>
                            <div v-else>
                                <van-icon class="icon" name="more-o" color="#999999"
                                    style="color: var(--yz-text-333) !important;" />
                                <p>
                                    {{ $t('CSTC.NotStarted') }}
                                </p>
                            </div>
                        </div>
                    </div>

                </van-list>
            </van-pull-refresh>
        </div>

        <!-- <div class="list" @click="ebookRead(item)" v-for="item in ebookList" :key="item.id">
            <div class="list-cent">
                <div class="list-cent-t ellipsis">{{ item.CourseName }}</div>
                <div class="list-cent-p ellipsis2">{{ item.CourseDescription }}</div>
            </div>
            <div class="list-icon">
                <div v-if="item.IsRead">
                    <van-icon class="icon" name="passed" color="#0ABA1C" />
                    <p>{{ $t('CSTC.Completed') }}</p>
                </div>
                <div v-else>
                    <van-icon class="icon" name="more-o" color="#999999"
                        style="color: var(--yz-text-333) !important;" />
                    <p>
                        {{ $t('CSTC.NotStarted') }}
                    </p>
                </div>
            </div>
        </div> -->

        <!-- <div class="bg" v-if="!ebookList.length">
            <van-empty :image="emptyimg" image-size="100">
                <template #description>
                    <span class="desc-title">
                        {{ $t('home.Empty_tips') }}
                    </span>
                </template>
            </van-empty>
        </div> -->
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import { GetEbookList } from '@/service/user'
import { computed, onMounted, ref } from 'vue'
import { useCore } from '@/store/core/index'
import { useUserStore } from '@/store/users';
import { useRouter } from 'vue-router'
import emptyimg from '@/assets/imgs/empty.jpg'
import { useFormData } from '@/utils'

const router = useRouter()
const core = useCore()
const user = useUserStore()
const userInfo = computed(() => user.getUserInfo)
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false); // 下拉刷新
const pageParams = ref({
    page: 1, // 当前页数, 从 1 开始
    start: 1,
    limit: 20 // 每页条数
});
const ebookList = ref([])

const onRefresh = () => { //下拉刷新
    loading.value = true;
    pageParams.value.page = 1;
    finished.value = false;
    ebookList.value = [];
    onLoad();
};

const onLoad = async () => {
    if (refreshing.value) {
        refreshing.value = false;
        ebookList.value = [];
    }
    // let s = [
    //     { "name": "User", "value": userInfo.value?.Account },
    //     { "name": "UserType", "value": new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'Linde' : 'Supplier' },
    //     { "name": "Language", "value": core.$lang.split('-')[0] }
    // ]
    // let c = [{ "Id": "FileId" }, "FileName", "CourseName", "CourseDescription", "IsRead"]
    // const formdata = {
    //     s: btoa(JSON.stringify(s)),
    //     c: btoa(JSON.stringify(c)),
    //     page: pageParams.value.page,
    //     start: (pageParams.value.page - 1) * pageParams.value.limit,
    //     limit: pageParams.value.limit
    // }

    const formdata = {
        type: new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'Linde' : 'Supplier',
        lang: core.$lang.split('-')[0],
        page: pageParams.value.page,
        limit: pageParams.value.limit
    }
    try {
        let { data } = await GetEbookList(formdata)
        console.log("%c [ ebooks--- data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
        ebookList.value = data.children || []
        pageParams.value.page++
        loading.value = false
        if (data?.children.length < pageParams.value.limit) {
            finished.value = true
        }
    } catch (error) {
        console.log("ebooks---error", error)
    }
}

// 查看ebook
const ebookRead = (item => {
    router.push(`/ebookRead?FileItem=${JSON.stringify(item)}`)
})


onMounted(() => {
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .main {
        width: 100%;
        height: 100vh;
    }


    .list {
        padding: 12px;
        margin-top: 10px;
        background-color: var(--yz-div-background);
        display: flex;

        .list-cent {
            flex: 1;

            .list-cent-t {
                font-size: 14px;
                color: var(--yz-work-user);
                line-height: 20px;
            }

            .list-cent-p {
                font-size: 12px;
                color: var(--yz-work-user);
                margin-top: 5px;
            }
        }

        .list-icon {
            width: 90px;
            text-align: center;

            .icon {
                font-size: 20px;
            }

            p {
                font-size: 12px;
                margin-top: 5px;
                // color: var(--yz-work-user);
                color: var(--yz-text-333);
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yz-text-333) !important;
}

.ellipsis2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--yz-text-333) !important;
}

.bg {
    background-color: var(--yz-div-background);
}
</style>