import { reactive, ref, onMounted } from 'vue';
import { ILoaing, IUserInfo, IUserLogin } from './loginModel';
import { useGetQuery, useRequest } from '@/utils/request';
import { url } from '@/api/url';
import { useLoingStore } from '@/store/login';
import { showNotify } from 'vant';
import { useUserStore } from '@/store/users';
import { useRouter } from 'vue-router';
import { useAesData } from '@/utils';
import i18n from '@/locale/index'
import { useEnviroMent } from '@/utils';

function useLogin(): any {
  const loginStore = useLoingStore();
  const userStore = useUserStore();
  const router = useRouter();
  const img = '@/assets/imgs/logo1.png';
  const showPage = ref(false)
  const env = useEnviroMent();

  const formData = reactive<IUserLogin>({
    userName: '<EMAIL>',
    // userName: '<EMAIL>',
    userPass: '123456',
  });
  const loading = reactive<ILoaing>({
    loading: false,
    text: '',
  });
  const onSubmit = async (): Promise<void> => {
    loading.loading = true;
    await onLogin();
    loading.loading = false;
  };
  const onLogin = async (): Promise<void> => {
    const { success, publicKey, keystore } = await useGetQuery(url.login.getPublickey); //api/auth/signin/publickey
    console.log("%c [ success ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", success)
    if (success) {
      const js = new window.JSEncrypt();
      js.setPublicKey(publicKey);
      var formNewData = new FormData();
      formNewData.append('k', keystore);
      formNewData.append('p', useAesData(publicKey, formData.userPass));
      formNewData.append('u', useAesData(publicKey, formData.userName));
      const data = await useRequest({
        url: url.login.userLogin,
        method: 'post',
        data: formNewData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
      });
      if (data && data.success) {
        loginStore.setToken(data.token);
        try {
          const { success, user } = await useGetQuery(url.login.getUserInfo);
          if (!success) {
            showNotify({ message: '无法获取用户信息，登录D失败' });
            loading.loading = false;
          } else {
            const userData: IUserInfo = user as IUserInfo;
            userStore.setUserInfo(userData);
            loading.loading = false;
            // router.push({ path: '/layout/home' }); //YZ need hide
            router.push({ path: '/' }); //Linde cf modify -- 去首页 -- 流程库 审批 申请 我的
          }
          // router.push({ path: '/layout/home' }); //YZ need hide
          router.push({ path: '/' });
        } catch (error) {
          if (location.hostname === 'localhost') {
            router.push({ path: '/' }) //本地 -- 去首页
          }
        }

      } else {
        showNotify({ message: data.errorMessage });
      }
    } else {
      console.log('~_~ 登录失败')
    }
  };

  //Linde cf modify -- 跳转登录页面（无登录凭证）
  onMounted(() => {
    if (window.location.hostname === 'localhost' || window.location.port) {
      showPage.value = true
    } else {
      window.location.href = window.location.origin
    }
  })
  return {
    onSubmit,
    formData,
    loading,
    img,
    showPage //cf modify

  };
}
export { useLogin };
