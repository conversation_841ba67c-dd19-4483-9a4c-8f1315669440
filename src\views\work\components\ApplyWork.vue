<template>
    <div>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-model:loading="loading" :finished="finished" :finished-text="$t('Form.NoMore')" @load="onLoad">
                <div class="grap"></div>
                <YZCard v-for="item in Applylist" :key="item.serialNum" :process="item" @click="onClick(item)">
                    <template #default>
                        <div class="status-div">
                            {{ item.tagItem.text }}
                        </div>
                    </template>
                </YZCard>
                <YZEmpty v-if="Applylist.length <= 0" />
            </van-list>
        </van-pull-refresh>
        <van-popup teleport="body" v-model:show="procesShow" :style="{ height: '100%' }" position="bottom">
            <renderForm :key="time" />
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { MyRequestTask } from '@/logic/forms/processHelper';
import { ProcessService } from '@/logic/forms/processHelper';
import { SearchPanel } from '@/logic/work/workModel';
import { useHomeStore } from '@/store/home';
import { useProcessStore } from '@/store/process';
import { eventBus } from '@/utils/eventBus';
import { ref, onMounted } from 'vue';
import YZCard from '@/components/YZCard.vue';
import YZEmpty from '@/components/YZEmpty.vue'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const loading = ref<boolean>(false);
const finished = ref<boolean>(false);
const refreshing = ref<boolean>(false);
const pageIndex = ref<number>(1)
const pageSize = ref<number>(10)
const Applylist = ref<Array<MyRequestTask>>([])
const porcessStore = useProcessStore()
const procesShow = ref<boolean>(false)
const homeStore = useHomeStore()
const time = ref<number>(0)
const service = new ProcessService()
const searchF = ref<SearchPanel>()
const isSearch = ref<boolean>(false)

const emits = defineEmits<{
    clearKwd: []
}>()
const onRefresh = () => {
    refreshing.value = true
    restart();
    isSearch.value = false
    emits('clearKwd')
    onLoad()

}
const restart = () => {
    pageIndex.value = 1
    pageSize.value = 10
    loading.value = true
    finished.value = false
    Applylist.value = []
}
const onLoad = async () => {
    let search = new SearchPanel({ limit: 20, start: 0, page: 1, byYear: 1, Year: new Date().getFullYear() })
    if (refreshing.value) {
        await service.getWorkCount()
        console.log('3333')

        refreshing.value = false
    }
    if (isSearch.value && searchF.value)
        search = searchF.value
    await toSearch(search)
}
const toSearch = async (search: SearchPanel) => {
    const start = (pageIndex.value - 1) * pageSize.value
    const limit = pageSize.value
    const page = pageIndex.value
    search.page = page
    search.limit = limit
    search.start = start
    const data = await service.getMyRequest(search)
    loading.value = false
    if (data.children.length <= 0 || data.total <= Applylist.value.length) {
        finished.value = true
    } else {
        Applylist.value.push(...data.children)
        pageIndex.value++
        finished.value = false
    }
}
const onClick = async (task: MyRequestTask) => {
    console.log("%c [ AwaitWork task ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", task)
    if (task.processShortName.includes('SSI')) {
        router.push({ path: '/ssi_apply', query: { next: 'apply', taskId: task.taskId, processName: task.processName, serialNum: task.serialNum } }) // 去ssi
    } else {
        porcessStore.setTaskId(task.taskId)
        time.value = new Date().getTime()
        procesShow.value = true
    }
}

eventBus.on('onBack', {
    cb: function (params?: any) {
        const appActive = porcessStore.getTabName
        if (appActive === 'ApplyWork') {
            procesShow.value = false
            homeStore.setTab(true)
            Applylist.value = []
            pageIndex.value = 1
            onLoad()

        }

    }
})
eventBus.on('ApplyWork', {
    cb: function (params) {
        pageIndex.value = 1
        Applylist.value = []
        if (params['resert']) {
            isSearch.value = false
            emits('clearKwd')
            onLoad()
        } else {
            const search = new SearchPanel(params)
            toSearch(search)
            searchF.value = search
            isSearch.value = true
        }
    }
})

onMounted(() => {
    console.log("%c [ 申请页面 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
})
</script>
<style lang="scss" scoped>
.grap {
    width: 100%;
    height: 10px;
    background-color: #f9f9f9;
}

.status-div {
    width: 51px;
    height: 21px;
    background: #E5EDFF;
    border: 0.5px solid #00a0e1;
    border-radius: 3px;
    font-family: PingFang-SC-Medium;
    font-size: var(--yz-status-div);
    color: #00a0e1;
    ;
    line-height: 21px;
    font-weight: 500;
    text-align: center;
    margin-top: 10px;
}

.van-list {
    background-color: var(--yz-layout-mian) !important; //#f8f7f7 
}

.van-list__finished-text {
    background: var(--yz-layout-mian) !important;
}

.van-tabs--line .van-tabs__wrap {
    display: none;
}
</style>
