<template>
    <div>
        <van-button size="small" hairline type="default" :disabled="!btn.enable" icon="share-o" @click="onClick"
            style="height: 32px;padding: 0px 10px;">
            {{ btn.text }}
        </van-button>
        <van-dialog v-model:show="weiTuoShow" :title="$t('Form.Task_delegation')" @confirm="onwtConfirm"
            :before-close="wtBefore" @cancel="onwtCancle" show-cancel-button>
            <div class="dialog-div">
                <van-field v-model="wtData.users" readonly :label="$t('Form.Delegate_to')" @click="wtUserClick" />
                <van-field v-model="wtData.message" rows="1" autosize :label="$t('Form.Entrusted_opinion')"
                    type="textarea" :placeholder="$t('Form.Entrusted_opinion_tips')" />
            </div>
        </van-dialog>
        <YZUserSelect v-model:show="userShow" :multiple="false" @on-save="WtUserSave" :default-users="defaultUsers" />
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { eventBus } from '@/utils/eventBus';
import { PropType, ref } from 'vue';
import YZUserSelect, { ISelectUser } from '@/components/YZUserSelect.vue';
import { reactive } from 'vue';
import { useHomeStore } from '@/store/home';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/store/users';
import { useCallBackClose, useLang, useParamsFormatter } from '@/utils';
import { url } from '@/api/url';
import { usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { useProcessStore } from '@/store/process';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const weiTuoShow = ref<boolean>(false)
const wtData = reactive({
    message: '',
    users: ''
})
const homeStore = useHomeStore()
const userShow = ref<boolean>(false)
const userIds = ref<string>()
const route = useRoute()
const userStrore = useUserStore()
const processStore = useProcessStore()
const defaultUsers = ref<Array<ISelectUser>>([])
const onClick = () => {
    weiTuoShow.value = true
}
const wtUserClick = () => {
    userShow.value = true
    defaultUsers.value = []
    if (userIds.value && wtData.users) {
        defaultUsers.value.push({
            name: wtData.users,
            account: userIds.value
        })
    }
    homeStore.setHead(false)
}
const WtUserSave = (users: any) => {
    userIds.value = ''
    wtData.users = ''
    defaultUsers.value = []
    if (users && users.length > 0) {
        userShow.value = false
        homeStore.setHead(true)
        userIds.value = users[0].account
        wtData.users = users[0].name
    }
}
const onwtConfirm = async () => {
    // 发起知会
    if (!wtData.users) {
        showNotify({ message: useLang('Form.select_EntrustedPersonnel_tips'), type: 'danger' })
        return
    }
    if (!wtData.message) {
        showNotify({ message: useLang('Form.Enter_Entrusted_opinion_tips'), type: 'danger' })
        return
    }
    const stepId = processStore.getProcLoad.stepId
    if (stepId) {
        const postJson: any = {
            uid: userIds.value,
            // accounts: [userIds.value],
            comments: wtData.message,
            // items:[
            //     {ID:stepId,StepID:stepId}
            // ]
        }
        let newUrl = useParamsFormatter(url.process.assigneUser, { params1: stepId })
        const data = await usePostBody(newUrl, {}, postJson)
        if (!data.success) {
            showNotify({ type: 'danger', message: data.message || data.errorMessage })
            console.log('error-3')

        } else {
            showNotify({ type: 'success', message: useLang('Form.EntrustSuccess') })
            weiTuoShow.value = false
            wtData.message = ''
            wtData.users = ''
            useCallBackClose(function () {
                eventBus.emit('onBack', true)
            }, processStore.getHideTabHeader)
        }
    }


}
const wtBefore = () => {
    return false
}
const onwtCancle = () => {
    weiTuoShow.value = false
    wtData.message = ''
    wtData.users = ''
}
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    height: 26vh;
}
</style>