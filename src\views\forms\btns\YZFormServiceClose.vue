
<template>
    <div class="form-service-btn">
        <van-button  size="normal"  block type="primary" 
        :disabled="!btn.enable"
        @click="onClick"
        style="padding: 0px 20px; height: 32px;">
            {{ btn.text }}
        </van-button>
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { eventBus } from '@/utils/eventBus';
import { PropType, ref } from 'vue';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const onClick=()=>{
    eventBus.emit('onBackChildForm', true)
}
</script>
<style lang="scss" scoped>
// .form-service-btn{
//     position: absolute;
//     left: 0;
//     right: 0;
//     padding: 0 10px;
// }
</style>