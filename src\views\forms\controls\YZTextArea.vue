<template>
    <div>
        <van-field v-if="!field.hidden" v-model="vModel" :name="field.vModel.model" :label="field.config.label"
            :required="field.config.required" :readonly="readOnly" :disabled="field.disabled"
            :rules="field.config.rules" type="textarea" :rows="field.getRow()" :show-word-limit="field.getShowLimit()"
            :placeholder="field.placeholder" @update:model-value="valueUpdate" />
    </div>
</template>

<script lang="ts" setup>
import { YZTextArea, YZTextAreaConfig } from '@/logic/forms/YZTextArea';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, onUnmounted, PropType, ref, watch, nextTick } from 'vue';
import { throttle, debounce } from 'lodash'
const props = defineProps({
    field: {
        type: Object as PropType<YZTextArea>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
const config = props.field.config as YZTextAreaConfig
const readOnly = computed(() => props.field.config.extends.readOnly ?? props.field.readonly)
onMounted(() => {
    isFirstLoad.value = false
    window.addEventListener('touchmove', stopTextarea, true)
    setFun() //YZ +
})
onUnmounted(() => {
    window.removeEventListener('touchmove', stopTextarea)
})
const stopTextarea = (e: any) => {
    let target = e.target
    if (target && target.tagName === 'TEXTAREA') {
        e.stopPropagation();
    }
}
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const valueUpdate = debounce(() => {
    setFun()
}, 300)
const setFun = () => {
    nextTick(() => {
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
        props.field.disableTest(props.field, props.index, isFirstLoad.value)
        props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    })
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.vModel.value = String(params.value)
        }
    }
})

eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
// 监听数据MAP
eventBus.on('onMap', {
    cb: function (params) {
        console.log("%c [ params 8]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid && params.index == props.field.pindex) {
            props.field.vModel.value = params.value
        }
    }
})
</script>
<style scoped></style>