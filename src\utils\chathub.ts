import { ProcessService } from "@/logic/forms/processHelper";
import { useLoingStore } from "@/store/login";
import * as signalR from "@microsoft/signalr";
const loginStore = useLoingStore()
const initHub = async () => {
    const interId = setInterval(async () => {
        // console.log('正在初始化链接...')
        const token = loginStore.getToken
        if (token) {
            console.log('用户身份已获取，准备链接即时通讯')
            clearInterval(interId)
            if (location.hostname !== 'localhost') { await connectionHub(token) }
        }
    }, 1000)
}
const connectionHub = async (token: string) => {
    const host = process.env.NODE_ENV === 'development' ? window.webConfig.devhost : window.webConfig.host
    var connection = new signalR.HubConnectionBuilder()
        .withUrl(host + "hubs/chathub", {
            accessTokenFactory: () => token
        }).build()
    await connection.start();
    connection.on('ReceiveMessage', async (user, action, message) => {
        if (action === 'Post' || action === 'Process') {
            // 刷新数量
            console.log("%c [ 1 刷新数量 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
            const service = new ProcessService()
            await service.getWorkCount()
            console.log("%c [ 2 刷新数量 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")

            // .then(result=>{})
        }
    })
}
export {
    initHub
}