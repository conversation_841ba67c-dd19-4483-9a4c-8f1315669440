<template>
    <div>
        <!-- <van-field v-if="!field.hidden" v-model="vModel" v-show="!field.hidden" :name="field.vModel.model"
            :label="field.config.label" :required="field.config.required" :key="field.vModel.model"
      readonly :rules="field.config.rules" :placeholder="field.placeholder">
        </van-field> -->
        <van-field v-model="vModel" v-show="!field.hidden" :name="field.vModel.model" :label="field.config.label"
            :required="field.config.required" :key="field.vModel.model" readonly :rules="field.config.rules"
            :placeholder="field.placeholder">
            <template #input>
                <input class="van-field__control" readonly v-model="vModel" v-show="!vModelDisplayField">
                <span v-show="vModelDisplayField">{{ vModelDisplayField && vModel ? vModelDisplayField : '' }}</span>
            </template>
        </van-field>
    </div>
</template>

<script lang="ts" setup>
import { YZValuetodisplay, YZValuetodisplayConfig } from '@/logic/forms/YZValuetodisplay';
import { onMounted } from 'vue';
import { computed, PropType, ref } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { watch, nextTick } from 'vue';
import { useBase64, useFormData, useIsChinese, useParamsFormatter, useStrToUnicode, useUninCode } from '@/utils';
import { url } from '@/api/url';
import { usePostBody } from '@/utils/request';
import { parseExpress } from '@/logic/forms/express';
import { YZField } from '@/logic/forms/formModel';
import { throttle } from 'lodash'
const props = defineProps({
    field: {
        type: Object as PropType<YZValuetodisplay>,
        required: true,

    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue'])
const config = props.field.config as YZValuetodisplayConfig
const isFirstLoad = ref<boolean>(true)
const vModelDisplayField = ref('')
onMounted(() => {
    isFirstLoad.value = false
    nextTick(() => {
        setValueDisplay(props.modelValue)
        setFun() //YZ + 
    })
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        // 做值列的操作
        // setValueDisplay(props.modelValue)
        return props.modelValue
    },
    set(val) {
        // console.log(val,'set')
        emit('update:modelValue', val)
    }
})
// let  isPost = false 
// watch(vModel, (newValue, oldValue) => {
//     if (newValue) {
//         const { $map, displayField, filterParam, ds } = config.getValueConvert()
//         if ($map) {
//             //提取map的key
//             const mapKeys = Object.keys($map).map(x => x);
//             //提取显示列
//             mapKeys.push(displayField)
//             const endKeys = mapKeys.map(x => "\"" + buildFilterStr(x) + "\"").join(',')
//             const body = {
//                 c: btoa(`[${endKeys}]`),
//                 f: btoa(`{"${buildFilterStr(filterParam)}": {"op":"=","value":"${buildFilterStr(newValue)}"}}`),
//                 o: ''
//             }
//             if (!isPost)
//                loadDataSource( body)
//         }
//         // 获取文本显示列
//     }
// })
const setFun = () => { //YZ + 
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const inputChange = (val: string | number) => {
    // console.log(val,'值列转换')
}
// 节流 避免多次触发
const setValueDisplay = async (newValue: string | number) => {
    let valueConvertIns = config.getValueConvert()
    if (!valueConvertIns) return
    const { $map, displayField, filterParam, ds } = valueConvertIns
    let body: any = {
        c: '',
        f: '',
        o: ''
    }
    // 存在map的情况
    if ($map) {
        //提取map的key
        const mapKeys = Object.keys($map).map(x => x);
        //提取显示列
        mapKeys.push(displayField)
        const endKeys = mapKeys.map(x => "\"" + buildFilterStr(x) + "\"").join(',')
        body = {
            c: btoa(`[${endKeys}]`),
            f: btoa(`{"${buildFilterStr(filterParam)}": {"op":"=","value":"${buildFilterStr(newValue)}"}}`),
            o: ''
        }
        // if (!isPost)
        await loadDataSource(body)
    } else {
        let endKeys = "\"" + buildFilterStr(displayField) + "\""
        body = {
            c: btoa(`[${endKeys}]`),
            f: btoa(`{"${buildFilterStr(filterParam)}": {"op":"=","value":"${buildFilterStr(newValue)}"}}`),
            o: ''
        }
        await loadDataSource(body)
    }
    // 获取文本显示列
}
const buildFilterStr = (str: string): string => {
    if (useIsChinese(str)) {
        return useStrToUnicode(str)
    }
    return str
}
const loadDataSource = async (body: any) => {
    const { $map, displayField, ds } = config.getValueConvert()
    const quId = ds.type === 'esb' ? ds.esbId : ds.dataSourceId
    let formId = props.field.targetFormId
    let newUrl: string = ''
    if (ds.type === 'table') {
        newUrl = useParamsFormatter(url.dataSource.getTableData, {
            params1: quId,
            params2: ds.type,
            params3: ds.tableName
        })
    } else if (ds.type === 'esb') { //YZ + 
        newUrl = useParamsFormatter(url.dataSource.getEsbDataNoPage, {
            params1: quId,
        })
    } else if (ds.type == 'form') { //YZ + 
        newUrl = useParamsFormatter(url.dataSource.getFormNoPage, {
            params1: ds.formId,
            params2: ds.formTable
        })
    }
    const data = await usePostBody(newUrl, {}, useFormData(body), false, false)
    if (data && data.length > 0) {
        // isPost = true 
        const dataValue = data[0]
        vModelDisplayField.value = dataValue[displayField]
        Object.keys(dataValue).forEach(item => {
            const itemValue = dataValue[item]
            let mapName = $map ? $map[item] : null
            if (!mapName && $map) {
                Object.keys($map).forEach(key => {
                    if (key.toUpperCase() === item.toUpperCase()) {
                        mapName = $map[key]
                    }
                })
            }
            if (mapName) {
                // 实际值
                // map对象中的目标
                const mapValue = mapName
                if (mapValue) {
                    const field = parseExpress.getFieldByFid(formId, mapValue)
                    // 获取指定的控件
                    // const field = parseExpress.getField(mapValue)
                    if (field) {
                        eventBus.emit('setFieldValue', true, {
                            model: mapValue,
                            uuid: field.uuid,
                            value: itemValue
                        })
                    }

                }
            } else if (item === displayField) {
                // console.log(item,itemValue)
                // vModel.value = String(itemValue)
            }
        })

    } else {
        // 既然没数据了就把所有都设置为空
        vModelDisplayField.value = ''
        if ($map) {
            Object.keys($map).forEach(item => {
                const fieldName = $map[item]
                const field = parseExpress.getFieldByFid(formId, fieldName)
                // const field = parseExpress.getField(fieldName)
                if (field) {
                    eventBus.emit('setFieldValue', true, {
                        model: fieldName,
                        uuid: field.uuid,
                        value: ''
                    })
                }
            })
        }

    }
}
// 监听数据MAP
eventBus.on('onMap', {
    cb: function (params) {
        console.log("%c [ params 11]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid && params.index == props.field.pindex) {
            props.field.vModel.value = params.value
            // 做值列的操作
            nextTick(() => {
                setValueDisplay(props.modelValue)
                setFun() //YZ + 
            })
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.vModel.value = String(params.value)
            // 做值列的操作
            nextTick(() => {
                setValueDisplay(props.modelValue)
                setFun() //YZ + 
            })
        }
    }
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
</script>
<style scoped></style>