<template>
    <div>
        <van-button size="small" hairline type="default" :disabled="!btn.enable" icon="revoke" @click="backClick"
            style="height: 32px;padding: 0px 10px;">
            {{ btn.text }}
        </van-button>

        <van-dialog v-model:show="backShow" :title="$t('Global.Cancel')" @confirm="onCancleConfirm"
            :before-close="onCancleBeforeClose" @cancel="onCancleCancle" show-cancel-button>
            <div class="dialog-div">
                <van-field v-model="message" rows="1" autosize :label="$t('Form.Reason_for_revocation')" type="textarea"
                    :placeholder="$t('Form.Reason_for_revocation_tips')" />
            </div>
        </van-dialog>
    </div>
</template>

<script lang="ts" setup>
import { url } from '@/api/url';
import { IBtnModel } from '@/logic/forms/formModel';
import { useProcessStore } from '@/store/process';
import { useUserStore } from '@/store/users';
import { useCallBackClose, useLang, useParamsFormatter } from '@/utils';
import { eventBus } from '@/utils/eventBus';
import { usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { PropType, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const backShow = ref<boolean>(false)
const message = ref<string>('')
const route = useRoute()
const userStore = useUserStore()
const router = useRouter()
const processStore = useProcessStore()
const onCancleConfirm = async () => {
    if (!message.value) {
        showNotify({ message: useLang('Form.Reason_for_revocation_tips') })
    } else {
        const instanceId = processStore.getProcLoad.taskId
        if (instanceId) {
            const data = await usePostBody(url.process.cancleTask, {}, {
                comments: message.value,
                items: [{
                    ID: instanceId,
                    TaskID: instanceId
                }]
            })
            if (data.success === false) {
                showNotify({ message: data.errorMessage, type: 'danger' })

            } else {
                showNotify({ message: useLang('Form.Cancellation_successful'), type: 'success' })
                onCancleCancle()
                useCallBackClose(function () {
                    eventBus.emit('onBack', true)
                }, processStore.getHideTabHeader)

            }
        }

    }
}
const onCancleBeforeClose = () => {
    return false
}
const onCancleCancle = () => {
    backShow.value = false
    message.value = ''
}
const backClick = () => {
    backShow.value = true
}
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}
</style>