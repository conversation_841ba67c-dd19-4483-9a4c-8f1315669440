<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="iTitle">
                {{ $t('CSTC.Generate_QR_code') }}
            </div>
        </div>

        <div class="li">
            <div class="li-n">{{ $t('CSTC.ProjectName') }}: <span style="color: red;">*</span> </div>
            <div class="li-t ellipsis" @click="router.push({ path: '/projectSelectCstc', replace: true })">
                <span v-if="name_" style="color: #00A0E1;">{{ name_ }}</span>
                <span v-else>{{ $t('Form.PleaseSelect') }}</span>
            </div>
            <div class="li-i" @click="router.push({ path: '/projectSelectCstc', replace: true })"><van-icon
                    name="search" size="25" color="#00A0E1" />
            </div>
        </div>
        <div class="li">
            <div class="li-n">{{ $t('CSTC.Region') }}: <span style="color: red;">*</span> </div>
            <div class="li-t " @click="show = true">
                <div>
                    <span v-if="region_" style="color: #00A0E1;">{{ region_ }}</span>
                    <span v-else>{{ $t('Form.PleaseSelect') }}</span>
                </div>
            </div>
            <div class="li-i" @click="show = true"><van-icon name="arrow" size="25" /></div>
        </div>

        <div class="foot">
            <van-button @click="qrCode" class="subbtn-text" color='#00A0E1' block type="primary" native-type="submit"
                :loading="Loading" :disabled="!name_ || !region_">
                {{ $t('CSTC.Generate_QR_code') }}
            </van-button>
        </div>

        <van-popup v-model:show="show" position="bottom" round :z-index="2002">
            <van-picker v-model="region" :columns="columns" @confirm="onConfirm" @cancel="show = false"
                confirm-button-text="Done" cancel-button-text="Cancel" />
        </van-popup>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { submit_QR } from '@/service/user'
import { useFormData, TodaysDateFormat, formatDate, shortTimezoneName } from '@/utils'
import { usePostBody } from '@/utils/request'

const router = useRouter()
const route = useRoute()

const list = ref([])
const show = ref(false)
const region = ref([])
const region_ = ref('')
const name_ = ref('')
const columns = ref([
    // { text: 'LEMY', value: 'LEMY' },
    // { text: 'LECN', value: 'LECN' },
    // { text: 'LEK', value: 'LEK' },
    // { text: 'SP', value: 'SP' },
])
const comeBack = () => {
    router.push({
        path: '/trainingQRCode',
        replace: true
    })
}
const onConfirm = (value) => {//Done
    region_.value = value.selectedValues[0]
    show.value = false
}
const qrParam = ref('')
const Loading = ref(false)
const formatOffsetToHhMm = (offsetInMins) => {
    let negative = offsetInMins < 0 ? "+" : "-";
    let positiveMins = Math.abs(offsetInMins);
    let hours = Math.floor(positiveMins / 60);
    let mins = Math.floor((positiveMins - ((hours * 3600)) / 60));
    if (hours < 10) { hours = "0" + hours; }
    if (mins < 10) { mins = "0" + mins; }
    return negative + hours + ':' + mins;
}

const TimezoneOffset = ref(shortTimezoneName(new Date()))  //ref(formatOffsetToHhMm(new Date().getTimezoneOffset()))
const qrCode = async () => { // 生成二维码
    const local_params_ = {
        "header": {
            "formId": "***************",
            "appId": ***************,
            "formState": "New"
        },
        "formData": {
            "IssuerAccount": "<EMAIL>",
            "CourseID": "***************",
            "TrainingCategory": "Permit to Work ",
            "UserType": "Linde", "CaseNo": null,
            "IssuerName": "Chunyao Hou",
            "ProjectName": "Office:LED-\u9ec4\u6d77\u6e14\u4e1a\u5382\u533a",
            "Region": "LECN",
            "ProjectNo": "LE-CNDL HuangHaiYuYe",
            "$$$var1478": "",
            "$$$var1479": "<div style=\"text-align: center;\"><font size=\"6\" color=\"#000000\">2024-11-29 10:23:01</font></div>",
            "CreateBy": "<EMAIL>",
            "CreateTime": null,
            "UpdateBy": "<EMAIL>",
            "UpdateTime": null
        }
    }
    const params = {
        "header": {
            "formId": "***************",
            "appId": ***************,
            "formState": "New"
        },
        "formData": {
            "IssuerAccount": manyParams.value?.IssuerAccount,
            "CourseID": manyParams.value?.CourseID,
            "TrainingCategory": manyParams.value?.TrainingCategory,
            "UserType": manyParams.value?.UserType,
            "CaseNo": null,
            "IssuerName": manyParams.value?.Issuer,
            "ProjectName": route.query?.name_,
            "Region": region_.value, //route.query?.region
            "ProjectNo": route.query?.num_,
            "SupplierCode": route.query?.SupplierCode, //
            "SupplierName": route.query?.SupplierName, //
            "$$$var1478": "",
            "$$$var1479": `<div style=\"text-align: center;\"><font size=\"6\" color=\"#000000\">${new Date().toLocaleString()}</font></div>`,
            "CreateBy": manyParams.value?.CreateBy,
            "CreateTime": null,
            "UpdateBy": manyParams.value?.UpdateBy,
            "UpdateTime": null,
            "IssueDateTime": formatDate(new Date()), //TodaysDateFormat(), //new Date().toLocaleString().replace(/(\d{4})[^\d](\d{1,2})[^\d](\d{1,2}).*/, '$1-$2-$3').replace(/-(\d)\b/g, '-0$1')
            "TimezoneOffset": TimezoneOffset.value,
        }
    }


    console.log("%c [ submit_QR --- params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)

    if (location.hostname === 'localhost') {
        const localRes = {
            "Key": "617246208192581",
            "SN": "DT2024110034",
            "PostResult": "Saved",
            "success": true
        }
        qrParam.value = localRes.SN
    } else {
        Loading.value = true
        try {
            const sub_res = await submit_QR(params)
            console.log("%c [ sub_res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", sub_res)
            if (sub_res.status === 200) {
                qrParam.value = sub_res?.data?.SN
                Loading.value = false
            }
        } catch (error) {
            Loading.value = false
            console.log("%c [ submit_QR_error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
        }
    }
    router.push({
        path: '/qrCodeCstc',
        query: {
            qrParam: qrParam.value //"DT2024110051"
        },
        replace: true
    })
}
const manyParams = ref()

const loadRegion = async () => {
    // f=eyJUeXBlIjp7Im9wIjoiPSIsInZhbHVlIjoiUmVnaW9uIn19&c=WyJFblZhbHVlIiwiRW5WYWx1ZSJd&o=
    const ff = window.btoa(JSON.stringify({ "Type": { "op": "=", "value": "Region" } }))
    const cc = window.btoa(JSON.stringify(["EnValue", "EnValue"]))
    const formdata = useFormData({
        o: '',
        f: ff,
        c: cc,
    })
    try {
        const data = await usePostBody('bpm/datasource/527365695868997/table/MS_Dictionary', {}, formdata) //区域
        console.log("%c [ 区域 data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
        columns.value = data?.map(item => ({
            text: item.EnValue,
            value: item.EnValue
        })) || []
        // columns.value = []  // 清空原有数据
        // data?.forEach(item => {
        //     columns.value.push({ text: item.EnValue, value: item.EnValue })
        // })
    } catch (error) {
        console.log("区域---error", error)
    }
}
onMounted(() => {
    loadRegion()
    name_.value = route.query?.name_ //projectDetailSctc 传过来的的 projectName
    manyParams.value = JSON.parse(localStorage.getItem('manyParams'))
    console.log("%c [ --- manyParams.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", manyParams.value)
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;
        position: relative;
        box-shadow: 0 .0625rem .25rem rgba(0, 0, 0, .1);

        .back {
            position: absolute;
            left: 10px;
            top: 50%;
            width: 40px;
            box-sizing: border-box;
            transform: translate(0, -50%);

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .iTitle {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: var(--yz-text-333);
            font-weight: 500;
            text-align: center;
        }
    }

    .li {
        height: 44px;
        padding: 0 12px;
        display: flex;
        align-items: center;
        background-color: var(--yz-div-background);
        border-bottom: 1px solid #DCDFE6;

        &:last-child {
            border: none;
        }

        .li-n {
            width: 100px;
        }

        .li-t {
            height: 100%;
            flex: 1;
            display: flex;
            align-items: center;
        }

        .li-i {
            width: 30px;
        }

        .pleasehoder {
            color: #999;
        }
    }

    .foot {
        width: 100%;
        box-sizing: border-box;
        padding: 16px;
        position: fixed;
        bottom: 0;
        z-index: 99;
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

}
</style>