export  interface IUserLogin {
    userName:string,
    userPass:string
}
export interface ILoaing {
    loading:boolean;
    text:string;
}
export interface ILoginToken {
    expiresIn:number;
    success:boolean;
    token:string;
}
export interface IUserInfo {
    Birthday:string;
    Account:string;
    CostCenter:string;
    DateHired:string;
    Description:string;
    Disabled:boolean;
    DisplayName:string;
    EMail:string;
    FriendlyName:string;
    HRID:string;
    HomePhone:string;
    Mobile:string;
    Office:string;
    OfficePhone:string;
    Sex:string;
    WWWHomePage:string;
    PortraitUrl:string;
 }
 