import { useLocalStorage, useSessionStorage } from "@/utils";
import { defineStore } from "pinia";
import { ConfigProviderTheme } from "vant";
import { IHomeState } from "../storeModel";
import {ConstConfig } from '@/utils/localConst'
import { IProcessInfo } from "@/logic/home/<USER>";
const homeId = 'homeid'
const useHomeStore = defineStore(homeId, {
   getters: {
      getActivePath: (state): string => {
         return state.activePath
      },
      getTheame: (state):ConfigProviderTheme => {
         const value = useLocalStorage.getSecretObject<ConfigProviderTheme>(ConstConfig.home.getTheme)
         if (value){
            state.theme = value
            return state.theme 
         }
         return state.theme
      },
      getTab:(state)=>state.showtab,
      headshow:(state)=>state.headerShow,
      getAllCount:(state)=>state.allCount,
      getWorkCount:(state)=>state.workCount,
      getReadCount:(state)=>state.readCount,
      footShow:(state)=>state.showFootBar,
      getFontSize(state) {
         const value = useLocalStorage.get(ConstConfig.home.getSize)
         if (value){
            state.fontSize = value
            return state.fontSize
         }
         return state.fontSize
      }
   },
   state: (): IHomeState => {
      return {
         activePath: '/layout/home',
         theme: 'light',
         fontSize:'中',
         showtab:true ,
         headerShow:true,
         allCount:0,
         workCount:0,
         readCount:0,
         showFootBar:true,
         historyData:[]
      }
   },
   actions: {
      setActivePath(path: string) {
         this.$state.activePath = path
      },
      setTheme(theme: string | number) {
         this.$state.theme = (theme === 'light' || theme === 0) ? 'light' : 'dark'
         useLocalStorage.setSecretObject<ConfigProviderTheme>(ConstConfig.home.setTheme,this.$state.theme)
      },
      setTab(show:boolean) {
         this.$state.showtab = show
      },
      setHead(show:boolean) {
         this.$state.headerShow = show 
      },
      setAllCount(count:number) {
         this.$state.allCount =count
      },
      setWorkCount(count:number) {
         this.$state.workCount = count
         const sum = count + this.$state.readCount
         this.setAllCount(sum)
      },
      setReadCount(count:number) {
         this.$state.readCount = count
         const sum = count + this.$state.workCount
         this.setAllCount(sum)
      },
      setFootBar(isShow:boolean) {
         this.$state.showFootBar = isShow
      },
      setFontSize(size:string) {
         this.$state.fontSize = size
         useLocalStorage.set(ConstConfig.home.setSize,size)
      }
   }
})
export {
   useHomeStore
}