const enUSMessage = {
    Global: {
        Back: 'Back',
        Save: 'Save',
        Search: 'Search',
        Reset: 'Reset',
        HomeTitle: 'Home',
        Opinions: 'Opinions',
        HandleTime: 'Handle time',
        Status: 'Status',
        Signer: 'Signer',
        Select: 'Select',
        Selected: 'Selected',
        Confirm: 'Confirm',
        Cancel: 'Cancel',
        Success: 'success',
        Fail: 'fail',

    },
    Form: {
        Filter: 'Filter',
        Creator: 'Creator',
        Form: 'Form',
        Switch_jobs: 'Switch jobs',
        More: 'More',
        No_fill_tips: 'No comments filled in',
        OpinionTips: 'Please enter your comments',
        AuditCord: 'Approval record',
        Signature_information: 'Countersign information',
        Retrieve: 'Retrieve',
        Notify: 'Notify',
        Urging: 'Urging',
        Agree: 'Agree',
        Refuse: 'Refuse',
        Return: 'Return',
        Reason_for_revocation: 'Cancel reason',
        Reason_for_revocation_tips: 'please enter cancel reason',
        Return_applicant: 'Return start',
        Back_a_step: 'Return',
        ReturnTo: 'Return to',
        TaskReturn: 'Return task',
        MissionRejection: 'Task refuse',
        Reason_for_rejection: 'Reject reason',
        Return_to_submitter: 'Return task to start',
        SpecificSteps: 'Specific step',
        applicant: 'Applicant',
        ReturnOpinion: 'Reason for return',
        Entrust: 'Entrust',
        Delegate_to: 'entrusted to',
        Task_delegation: 'Task entrust',
        Entrusted_opinion: 'entrusted opinion',
        Entrusted_opinion_tips: 'please enter entrusted opinion',
        Read: 'Read',
        Submit: 'Submit',
        ProcessInformation_tips: 'Unable to obtain valid process information, please contact the administrator',
        Select_Signatory_tips: 'Please select the signer!',
        SubmitConfirmation_tips: 'Submit Confirm',
        SubmitTRInspection: 'Submit To Re-inspection',
        NotifyTo: 'Notify to',
        EntrustTo: 'Entrust to',
        Processed: 'Processed',
        Processing: 'Processing',
        batchApprove: 'batch Approve',
        batchNotify: 'batch Notify',
        FieldLength_tips: 'The field maximum length is',
        Enter_tips: 'Please enter ',
        Enter_Required_fields_tips: 'Please enter the required fields',
        SelectUser: 'Select User',
        EntrustedTo: 'Entrusted to',
        EntrustedOpinion: 'Entrusted opinion',
        SearchUsers: 'Search users',
        Selected: 'Selected',
        NotifyOpinion: 'Comments',
        NotifyPersonnel_tips: 'Please select the notification personnel!',
        NotifyOpinion_tips: 'Please enter notify opinion',
        NotifySuccess: 'Notification successful',
        Select_Username_tips: 'Please select user name',
        upload: 'Please upload',
        file: 'file',
        addRow: 'Add row',
        PleaseSelect: 'Please select ',
        Select_Required_fields_tips: 'Please select required fields',
        SelectUser_tips: 'Please select user',
        SelectDepart: 'Please select department',
        SelectAddress_tips: 'Please enter detailed address',
        SystemGenerate: 'System auto generated',
        DetailAddress: 'Detail address',
        keywordsSearch: 'please enter search keywords',
        OwnerAttribute: 'Owner attribute',
        WindowSelection: 'Data select',
        CallPhone: 'call phone',
        EntrustSuccess: 'Entrust success',
        AddSign_tips: 'Add signature',
        select_EntrustedPersonnel_tips: 'Please select entrusting person!',
        Enter_Entrusted_opinion_tips: 'Please enter entrusting opinion',
        OperateConfirm: 'Operation confirmation',
        Select_Confirm_tips: 'Are you sure to agree with the selected task?',
        HistoryOfApproval: 'Approval history',
        SignerGenerate: 'Signer: The system starts automatically',
        // start 
        Release_to_refresh: 'Release to refresh',
        Pull_down_to_refresh: 'Pull down to refresh',
        NoMore: 'No more',
        Loading: 'Loading...',
        Keywords: 'Keywords',
        CheckingDateTime: 'Checking date time',
        ProcessingCompleted: 'Processing completed'

    },
    login: {
        username: 'Username',
        Enter_name_tips: 'Please input a username',
        password: 'Password',
        Enter_password_tips: 'Please input a password',
        Login: 'Login'
    },
    home: {
        Process: 'Process',
        MyPopularProcess: 'My Collection',
        ManagerProcess: 'Manage common processes',
        ProcessList: 'Process',
        SearchKeywords: 'Search keywords',
        ManageProcessesDesc: ' You can add common processes to the App homepage, or press and drag to adjust the application order ',
        MyCollection: 'My collection',
        AllProcesses: 'All Processes',
        All: 'All',
        Empty: 'No Description',
        ProcessMax_tips: 'The above processes are shown on the home page (it is recommended that there are at most 8)',
        SuccessfullyCollected: 'Successful collection',
        CollectionFailed: 'Collection failure',
        CancelSuccess: 'Cancel successfully',
        CancelFailed: 'Cancellation failure',
        RecentlyUsed: "RecentlyUsed use",
        CommonProcess: 'Common process',
        ForceUpgrade_tips: 'New version discovered, please force upgrade!',
        Contain: 'Contain'
    },
    my: {
        New_message_notification: 'New message notification',
        Out_of_office_settings: 'Out of office settings',
        LanguageSettings: 'Language settings',
        version: 'Version information',
        about: 'about',
        ExitLogin: 'Exit login',
        InOffice: 'In the company',
        OutOffice: 'Out of office',
        OutTime: 'Go out only at',
        OutBegin: 'Start time',
        OutEnd: 'End time',
        SelectDate: 'Select Date',
        SelectTime: 'Select Time',
        simplified: 'Simplified Chinese',
        english: 'English',
        korean: 'Korean',
        german: 'German',
        system: 'General settings',
        dark: 'dark',
        lint: 'light',
        model: 'model',
        setSuccess: 'Successful setting',
        Font: 'FontSize',
        small: 'small',
        medium: 'medium',
        large: 'large',
        UserCenter: 'Personal',
        LogOut: 'Exit',
        SwitchEnvironment: 'Switch Environment',
        prod80: '8.0 Production Environment',
        dev80: '8.0 Dev Environments',
        test80: '8.0 Test Environment',
        newProd: '6.7 Production Environment',
        dev_: '6.7 Dev Environments',
        test_: '6.7 Test Environment',
        GlobalENV: 'Global Environment',
        identity: 'PPA Environment',
        AccountSecurity: 'Account security',
        CancelAccount: 'Cancel Account',
        AboutAPP: 'About APP'
    },
    Work: {
        Year: 'Year',
        Month: 'Month',
        Quarter: 'Quarter',
        Date: 'Date',
        ProcessName: 'Process name',
        TaskNum: 'Task ID',
        Keyword: 'Keyword',
        AwaitWork: 'Await work',
        NotifyWork: 'Notify work',
        Read: 'Read',
        ApplyWork: 'Apply work',
        CompleteWork: 'Complete work',
        PerYear: 'Per annum',
        PerMonth: 'Per mensem',
        PerQuarter: 'Quarterly',
        PerDay: 'Certain day',
        Running: 'Running',
        Aborted: 'Aborted',
        Approved: 'Approved',
        Rejected: 'Rejected',
        Initiator: 'Initiator',
        WithdrawToRefill: 'Withdraw to refill',
        RetrieveToRefill: 'Retrieve to refill',
        RetrieveToApproval: 'Retrieve to approval',
        WithdrawApplication: 'Withdraw application',
        HandleComments: 'Handle comments',
        is: "'s ",

    },
    LayOut: {
        Home: 'Home',
        Work: 'Work',
        My: 'My'
    },
    New: { //Linde +
        Add: 'Add',
        NoProcess: 'No Process',
        Search: "Search",
        Approve: "Approve",
        Apply: "Apply",
    },
    CSTC: {
        TQR: 'Training QR Code',
        SignIn: 'Sign in',
        SignOut: 'Sign out',
        TrainingRecords: 'Training History',
        TrainingRecordDetails: 'Training record details',
        Generate_QR_code: 'Generate QR Code',
        ProjectNo: 'Project Number',
        ProjectNo_Name: 'Project Number/Name',
        GPS: 'GPS Location',
        Sign_in_successful: 'Sign in successfully',
        Sign_in_failed: 'Sign in failed',
        Sign_out_successful: 'Sign out successfully',
        Sign_out_failed: 'Sign out failed',
        Company: 'Company',
        EMail: 'E-Mail',
        JobTitle: 'Switch_jobs Title',
        ProjectName: 'Project Name',
        TrainingCategory: 'Training Category',
        CheckInDateTime: 'Training Date',
        CheckInValidationDuration: 'Valid Until',
        CheckInAccount: 'Sign in account',
        CheckInTime: 'Check-in time',
        Sign_in_name: 'Sign in name',
        ProjectLocation: 'Project Number \\ Office location',
        SupplierCode: 'Supplier code',
        SupplierName: 'Supplier name',
        Region: 'Region',
        CheckOTime: 'Check out time',
        Sign_out_account: 'Sign out of account',
        Sign_off_name: 'Sign out name',
        ValidityValue: 'Validity period value',
        NotCheckedOut: 'Did not check out', //Not checked out
        NotStarted: 'Not started',
        Completed: 'Completed',
        submit: 'Submit',
        confirm_Readed: 'I confirm that I have read the file contents.',
        SignInSuccessfully: 'Sign in successfully',
        SignInFailed: 'Sign-in failed',
        SignOutSuccessfully: 'Sign out successfully',
        SignOutFailed: 'Sign out failed',
        Location_tips: 'Your browser does not support geographical function',
        Location_failed_tips: 'Failed to get location information',
        enable_location_information_tips: 'Please enable location information',
    },
    UAUC: {
        ReportDate: 'Issue Date',
        Speaker: 'Issuer',
        ProjectOfficeName: 'Project \\ Office Name',
        WorkArea: 'Work Area',
        ResponsibleUnit: 'Responsible Company',
        Responsibility_related: 'Responsible Person',
        ProcessingTime: 'Due Time Frame',
        DueTimeFrameAdditionalInfo: 'Due Time Frame Additional Info',
        HazardClassification: 'Hazard Category',
        Informant: 'Notified Party',
        ProblemDescription: 'Observation Finding',
        Picture_of_the_problems: 'Observation Picture',
        Suggest_corrective_measures: 'Proposed Corrective Action',
        Username_input_tips: 'Please enter in username',
        Project_Office_Name_input_tips: 'Please enter in Project/Office Name',
        work_area_input_tips: 'Please enter the work area',
        responsible_unit_input_tips: 'Please enter the responsible unit',
        description_of_the_problem_unit_input_tips: 'Please enter a description of the problem',
        hazard_classification_type_unit_select_tips: 'Please select a hazard classification type',
        the_informant_select_tips: 'Please select the informant',
        corrective_measures_enter_tips: 'Please enter corrective measures',
        processing_time_select_tips: 'Please select a processing time',
        SubmissionFailed: 'Submission Failed',
        SubmissionSuccessful: 'Submission Successful',
        Check_reading_tips: 'Please check the prompt you have read',
        select_file_tips: 'Please select file!',
        WrongFileTypeUploaded: 'Wrong file type uploaded!',
        SelectPicture: 'Select picture',
        AuditReminder: 'Approved successfully, the system will automatically redirect',
    },
    SSI: {
        ToolEquipmentRegister: 'Registration',
        Preliminary: 'Preliminary',
        Daily: 'Daily',
        Monthly: 'Monthly',
        Quarterly: 'Quarterly',
        Barcode: 'Barcode',
        ScanBarcode: 'Scan Barcode',
        GeneralInformation: 'General Information',
        TechnicalInformation: 'Technical Information',
        EntryDate: 'Register Date',
        Contractor: 'Contractor',
        ContractorCode: 'Contractor Code',
        ContractorName: 'Contractor Name',
        Equipment: 'Equipment/Tool Type',
        EquipmentType: 'Equipment Type',
        EquipmentTool: 'Tool / Equipment Registration',
        Manufacture: 'Manufacture',
        ContractorHSE: 'Contractor Site HSE',
        SerialNo: 'Serial No.',
        Certificate_Other_Documents: 'Certificate / Other Documents',
        OperatorName: 'Operator Name',
        Competence_Certificate_Card: 'Competence Certificate / Card',
        Model: 'Model',
        PlateNo: 'Plate No.',
        SWL_Capacity_Tonnes: 'SWL/Capacity(Tonnes)',
        Size_Diameter_meter: 'Size/Diameter(meter)',
        Length: 'Length(meter)',
        Remarks: 'Remarks',
        CalibrationExpiryDate: 'Calibration Expiry Date',
        manufacturers_name_enter_tips: 'Please enter Manufacturer',
        serial_number_enter_tips: 'Please enter Serial No.',
        operator_select_tips: 'Please select operator',
        Calibration_Expiry_Date_select_tips: 'Please select Calibration Expiry Date',  //请选择校准到期日
        applicable_model_enter_tips: 'Please enter Model',
        license_plate_number_enter_tips: 'Please enter Plate No.',
        SWL_capacity_enter_tips: 'Please enter SWL/Capacity(Tonnes)',
        size_diameter_enter_tips: 'Please enter Size / Diameter(meter)',
        length_enter_tips: 'Please enter Length(meter)',
        InspectionChecklist: 'Inspection Checklist',
        InspectionAttachment: 'Inspection Attachment',
        EvaluationResult: 'Evaluation Result',
        DateOfInspection: 'Date of Inspection',
        BarcodeNo: 'Barcode No.',
        explain_the_reason_tips: 'Please explain the reason',
        upload_attachment_tips: 'Please upload the relevant attachment',
        Comments: 'Sign-up and approval opinions',
        approval_opinion_enter_tips: 'Please enter your approval opinion',
        Checklist_Assessment_Results_tips: 'Please check the list and evaluate the results',
        scan_barcode_tips: 'Please scan the barcode',
        Supplier_User_Role_Required_Tips: "Please make sure to add user with 'Contractor Operators' role on 'Contractor User Role' menu first",
        InspectionType: 'Inspection Type',
        InspectionDate: 'Inspection Date',
        InspectionPlanDate: 'Inspection Plan Date',
        InspectionDueDate: 'Inspection Due Date',
        InspectionHistoryDetail: 'Inspection History Detail',
        Inspector: 'Inspector',
        PendingPreliminaryInspection: 'Pending Preliminary Inspection',
        PendingDailyInspection: 'Pending Daily Inspection',
        PendingMonthlyInspection: 'Pending Monthly Inspection',
        PendingQuarterlyInspection: 'Pending Quarterly Inspection',
        no_applicable_Tips: 'Not applicable cannot be selected for all Item Results.',
        Pass: 'Pass',
        Rejected: 'Rejected',
        Pending: 'Pending',
        NotApplicable: 'Not Applicable',
        Running: 'Running',
        Approved: 'Approved',
        sysStop: 'System stop',
        sysInform: 'System Inform',
        sysEntrust: 'System Entrust',
        sysTransfer: 'System Transfer',
        Start: 'Start',
        End: 'End',
        Submit: 'Submit',
        'Equipment/Tool Register': 'Equipment/Tool Register',
        'Preliminary Inspection': 'Preliminary Inspection',
        'Repair Equipment/Tool ': 'Repair Equipment/Tool',
        'Daily Inspection': 'Daily Inspection',
        'Monthly Inspection': 'Monthly Inspection',
        'Quarterly Inspection': 'Quarterly Inspection',
        'Submit to Re-inspection': 'Submit to Re-inspection',
        Camera_permission_tips: 'Please set camera access permissions',
    }
}
export default enUSMessage