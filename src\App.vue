<template>
  <div>
    <van-config-provider :theme="homeStore.getTheame" :theme-vars="themVarValues">
      <router-view v-slot="{ Component }">
        <keep-alive>
          <component v-if="route.meta.keepalive" :is="Component" />
        </keep-alive>
        <component v-if="!route.meta.keepalive" :is="Component" />
      </router-view>
    </van-config-provider>
  </div>
</template>
<script lang="ts" setup>
import { onMounted } from 'vue';
import { useHomeStore } from './store/home';
import { useCore } from '@/store/core/index'
import { themVarValues } from './theamVar';
import { useChangLang } from './utils';
import { sso_accesstoken, sso_login, checkLogin, autoLogin } from '@/service/user'
import { useRoute } from 'vue-router'

const homeStore = useHomeStore()
const core = useCore()
const route = useRoute()


onMounted(() => {
  let browserLanguage: any;
  let lan = ((navigator as any).browserLanguage || navigator.language).toLowerCase()
  console.log("%c [ 浏览器语言 2222--- lan ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", lan)
  if (lan.indexOf('zh') > -1) {
    browserLanguage = 'zh-CN'
  } else if (lan.indexOf('en') > -1) {
    browserLanguage = 'en'
  } else if (lan.indexOf('de') > -1) {
    browserLanguage = 'de-DE'
  } else if (lan.indexOf('ko') > -1) {
    browserLanguage = 'ko-KR'
  }
  const lang = core.$lang
  console.log("%c [ APP.vue --- core.$lang ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", lang)
  useChangLang(browserLanguage)
  window.document.documentElement.setAttribute("data-theme", homeStore.getTheame);
  window.document.documentElement.setAttribute("data-size", homeStore.getFontSize);
})
</script>
<style>
.axios-request {
  background: transparent;

}

.axios-request .van-loading__spinner {
  color: #666;
}

:root:root {
  --van-field-disabled-text-color: --van-text-color;
  --van-field-input-disabled-text-color: #9a9696;
}
</style>
