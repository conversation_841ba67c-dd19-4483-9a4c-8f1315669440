<template>
    <div>
        <van-popup v-model:show="isShow" teleport="body" position="bottom" :style="{ height: '100%' }">
            <van-nav-bar :title="$t('Form.SelectUser')" :left-text="$t('Global.Cancel')"
                :right-text="$t('Global.Confirm')" left-arrow @click-right="rightClick" @click-left="leftClick" />
            <div>
                <van-search v-model="searchkey" :placeholder="$t('Form.SearchUsers')" @search="onSearch"
                    @clear="onClear" style="padding: 0px; width:95%;margin:0px auto" />
            </div>
            <div class="path-line">
                <div class="li-div" v-for="item in pathArrys" :key="item.name" @click="handlerPath(item)">
                    {{ item.name }}
                    <van-icon name="arrow" style="margin-left: -13px;" v-if="item.icon" />
                </div>
            </div>
            <!-- 多选/单选用户 -->
            <div class="select-user" v-if="selectArrys.length > 0">
                <div class="puser" v-for="(user, index) in selectArrys" :key="index">
                    {{ user.name }}({{ user.account }})
                    <span @click.stop="onRemove(user)">
                        <van-icon name="cross" class="badge-icon" />
                    </span>
                </div>
            </div>
            <van-cell-group>
                <div>
                    <van-cell v-for="item in orgItems" :key="item.text">
                        <!--部门-->
                        <div class="item" v-if="item.type === 'ou'" @click="departChange(item.data)">
                            <i class="fa fa-folder-o folder-icon"></i>
                            <div class="item-cont">
                                <div class="item-cont-inner">{{ item.data?.getName() }}</div>
                            </div>
                        </div>
                        <!--个人-->
                        <div class="item" v-if="item.type === 'user'" @click="userChange(item)">
                            <van-icon class="item-icon" name="manager-o"></van-icon>
                            <div class="item-cont">
                                <div class="item-cont-inner">{{ item.data?.getName() }}
                                    <div class="check">
                                        <van-icon name="success" v-show="isSelect(item.data?.getCode())" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </van-cell>
                </div>
            </van-cell-group>
            <!-- 空数据 -->
            <van-empty description="empty" v-show="orgItems.length <= 0" />
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { useGetQuery } from '@/utils/request';
import { ref, computed, onMounted } from 'vue';
import { url } from '@/api/url';
import { OrgModel, OrgModelData } from '@/logic/forms/formModel';
import { useParamsFormatter } from '@/utils';
import { watch } from 'vue';
import { watchEffect } from 'vue';
import { once } from 'lodash';
import i18n from '@/locale/index'

export interface IBarPath {
    name: string;
    ouid: string;
    icon: boolean;
    providerName: string;
    code: string;
    level: string,
    sid: string
}
export interface ISelectUser {
    name: string;
    account: string;
}
const props = defineProps({
    show: {
        type: Boolean,
        required: true,
        default: true
    },
    multiple: {
        type: Boolean,
        required: false,
        default: false
    },
    defaultUsers: {
        type: Array<ISelectUser>,
        required: false,
        default: []
    }
})
const isShow = computed(() => props.show)
const emit = defineEmits(['update:show', 'onSave'])
const leftClick = () => {
    selectArrys.value = []
    pathArrys.value = []
    emit('update:show', false)
    initOrgTree()
}
const rightClick = () => {
    // 如果是人员单选,去请求一下map的接口
    if (!props.multiple) {
        getUserMap(selectArrys.value)
    } else emit('onSave', selectArrys.value)
    selectArrys.value = []
    searchkey.value = ''
    initOrgTree()
    emit('update:show', false)
}
const getUserMap = async (selectArrys: any[]) => {
    const newUrl = useParamsFormatter(url.org.getUserMapData, {
        params1: selectArrys[0]?.account || '',
    })
    const data = await useGetQuery(newUrl)
    emit('onSave', selectArrys, data)
}
const searchkey = ref<string>('')
const pathArrys = ref<Array<IBarPath>>([])
const orgItems = ref<Array<OrgModel>>([])
const selectArrys = ref<Array<ISelectUser>>([])
const onceCall = ref<boolean>(false)
onMounted(async () => {
    if (!onceCall.value) {
        initOrgTree()
        onceCall.value = true
    }
})
const initOrgTree = async () => {
    const data = await useGetQuery(url.org.getOrgs, {}, false)
    // console.log("%c [ --- 部门初始化 bpm/org/rootnodes ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
    builderOrg(data)
    pathArrys.value = [{ name: i18n.global.t('home.All'), ouid: 'all', icon: false, providerName: '', code: '', sid: '', level: '' }]
}
const departChange = async (modelData: OrgModelData | any) => {
    const model = modelData as OrgModelData
    const newurl = useParamsFormatter(url.org.getOrgChildren, {
        params1: model.providerName,
        params2: model.OUID
    })
    const data = await useGetQuery(newurl)
    builderOrg(data)
    buildPath({ name: model.Name, icon: false, ouid: model.OUID, providerName: model.providerName, code: model.Code, sid: model.SID, level: model.Level })
    buildUser(model)
}
const builderOrg = (children: Array<any>) => {
    orgItems.value = []
    if (children && children.length > 0) {
        children.forEach((item: any) => {
            const data = new OrgModel(item, 'ou')
            orgItems.value.push(data)
        })
    }
}
const handlerPath = (model: IBarPath) => {
    if (model.ouid === 'all') {
        initOrgTree()
        pathArrys.value = [{ name: i18n.global.t('home.All'), ouid: 'all', icon: false, providerName: '', code: '', sid: '', level: '' }]
    } else {
        departChange({
            Code: model.code,
            SID: model.sid,
            Name: model.name,
            providerName: model.providerName,
            OUID: model.ouid,
            Level: model.level,
            getName() {
                return this.Name
            }
        })
    }
}
const userChange = (model: OrgModel) => {
    const account = model.data?.getCode() as string
    const userName = model.data?.getName() as string
    if (props.multiple) {
        const item = selectArrys.value.find(x => x.account === account)
        if (!item)
            selectArrys.value.push({ name: userName, account: account })
    } else {
        selectArrys.value = []
        selectArrys.value.push({
            name: userName,
            account: account
        })
    }
}
const buildPath = (model: IBarPath) => {
    const item = pathArrys.value.find(x => x.name === model.name)
    const newArrys: IBarPath[] = []
    if (item) {
        for (let i = 0; i < pathArrys.value.length; i++) {
            if (pathArrys.value[i].name === model.name) {
                newArrys.push({

                    icon: false,
                    name: model.name,
                    ouid: model.ouid,
                    code: model.code,
                    level: model.level,
                    sid: model.sid,
                    providerName: model.providerName
                })
                break;
            } else {
                newArrys.push({
                    icon: true,
                    name: pathArrys.value[i].name,
                    ouid: pathArrys.value[i].ouid,
                    code: pathArrys.value[i].code,
                    level: pathArrys.value[i].level,
                    sid: pathArrys.value[i].sid,
                    providerName: pathArrys.value[i].providerName
                })
            }
        }
        pathArrys.value = newArrys

    } else {
        pathArrys.value.forEach(item => {
            item.icon = true
        })
        pathArrys.value.push(model)
    }
}
const onSearch = async () => {
    if (!searchkey.value) {
        initOrgTree()
    } else {
        const children = await useGetQuery(url.org.searchUser, {
            wd: searchkey.value,
            page: 1,
            start: 0,
            limit: 1000
        })
        orgItems.value = []
        children.forEach((item: any) => {
            const model = new OrgModel(item, 'user')
            orgItems.value.push(model)
        })
    }
}
const onClear = () => {
    initOrgTree()
}
const isSelect = (account?: string) => {
    const item = selectArrys.value.find(x => x.account === account)
    return (item === undefined) ? false : true
}
const onRemove = (user: ISelectUser): void => {
    const findex = selectArrys.value.findIndex(x => x.account === user.account)
    if (findex > -1)
        selectArrys.value.splice(findex, 1)

}
const buildUser = async (model: OrgModelData) => {
    const newUrl = useParamsFormatter(url.org.getOrgUsers, {
        params1: model.providerName,
        params2: model.OUID
    })
    const data = await useGetQuery(newUrl)
    if (data && data.length > 0) {
        data.forEach((item: any) => {
            const data = new OrgModel(item, 'user')
            orgItems.value.push(data)
        })
    }
}
watchEffect(() => {
    selectArrys.value = props.defaultUsers
})
</script>
<style lang="scss" scoped>
.path-line {
    height: 30px;
    width: 100%;
    background: #efefef;
    margin-top: 5px;
    line-height: 30px;
    text-indent: 0.4em;
    font-size: var(--yz-com-15);
    color: #918c8c;

    .li-div {
        float: left;
        margin-left: 5px;
    }
}

.item {
    height: 30px;
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .item-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: var(--yz-com-22);
        border-radius: 4px;
        overflow: hidden;
        float: left;
        color: var(--van-nav-bar-text-color);

        :deep .van-icon__image {
            width: 100% !important;
            height: 100% !important;
        }

    }

    .item-cont {
        height: 100%;
        overflow: hidden;

        .check {
            width: 20px;
            height: 20px;
            float: right;
            text-align: center;
            line-height: 20px;
            color: #12bc2b;
        }
    }

    .folder-icon {
        float: left;
        margin-right: 8px;
        font-size: 20px;
    }


}

.item-cont:before {
    content: "";
    width: 0;
    font-size: 0;
    height: 100%;
    visibility: hidden;
    display: inline-block;
    vertical-align: middle;
}

.item-cont-inner {
    width: 100%;
    display: inline-block;
    vertical-align: middle;
    font-size: var(--yz-com-14);
    color: var(--van-field-label-color);
    text-align: left;
}

.select-user {
    width: 100%;
    overflow-y: scroll;
    padding: 10px 0px;

    .puser {
        padding: 3px;
        height: 30px;
        border: 1px dashed #efefef;
        margin: 5px 5px;
        float: left;
        line-height: 30px;
        font-size: var(--yz-com-12);
        position: relative;

        span {
            width: 20px;
            height: 20px;
            position: absolute;
            top: -7px;
            right: -10px;
            display: block;
            text-align: center;
            background-color: #ff2727;
            border-radius: 50%;
            line-height: 20px;
            color: #fff;
        }
    }
}
</style>