import type { ConfigProviderThemeVars } from "vant";
// const themVarValues: ConfigProviderThemeVars   = {
//     buttonPrimaryBackground:'#F18D02',
//     buttonPrimaryBorderColor:'#fd7d23',
//     buttonSuccessBackground:'linear-gradient(to right, #FFE675, #F19F00)',
//     buttonSuccessBorderColor:'linear-gradient(to right, #FFE675, #F19F00)',
//     buttonDangerBackground:'#FA600F',
//     buttonDangerBorderColor:'#fd7d23',
//     buttonWarningColor:'#D42D33',
//     buttonDangerColor:'#FA600F',
//      fieldInputTextColor:'#F18D02',
//      radioLabelColor:'#F18D02',
//      checkboxDisabledLabelColor:'#F18D02',
//      checkboxLabelColor:'#F18D02'
//     }
 const themVarValues:any={}
export {
    themVarValues
}