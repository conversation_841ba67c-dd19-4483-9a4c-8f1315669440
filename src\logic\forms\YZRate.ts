import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
export class YZRateConfig extends YZConfig {
    private defaultRules:Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZRate'
        this.defaultRules =  [{ required: true, message: this.label? `${this.label}值不能小于或等于0` :'请输入必填项',validator: this.rateValidator }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules  
        }
    }
    rateValidator(a:number,b:any):boolean {
        if (a<=0) {
            return false 
        }
        return true 
    }
    getDefaultRule():Array<any> {
        return this.defaultRules
    }
}
export class YZRateStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZRate extends Y<PERSON><PERSON>ield {
    private count:number;
    constructor(params: any) {
        super(params)
        this.vModel.value = params['__config__'].defaultValue
        this.count = params['max']
    }
    initConfig(config: any): YZConfig {
        return new YZRateConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZRateStyle(style)
    }
    getCount():number {
        return this.count
    }
}
