<template>
    <div>
        <van-field v-if="!field.hidden" v-model="vModel" v-show="!field.hidden" :name="field.vModel.model"
            :label="field.config.label" :required="field.config.required" :key="field.vModel.model"
            :readonly="field.disabled" right-icon="location-o" :rules="field.config.rules" type="textarea"
            @click="onMapClick" :placeholder="field.placeholder" />
        <van-popup teleport="body" v-model:show="mapShow" position="bottom" :style="{ height: '100%' }">
            <van-nav-bar title="位置" :left-text="$t('Global.Close')" :right-text="$t('Global.Confirm')" left-arrow
                @click-right="rightClick" @click-left="leftClick" />
            <van-field v-model="nowPositon" label="当前位置" type="textarea" autosize readonly />
            <p class="ptip" v-if="isMapLoad">正在处理地图数据,请稍后....</p>
            <div id="container" v-if="mapShow"></div>
            <!-- mapShow -- modify -->
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/users';
import { YZGelocation, YZGelocationConfig } from '@/logic/forms/YZGelocation';
import { computed, onMounted, PropType, ref, onUnmounted } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { showNotify } from 'vant';
import { reactive } from 'vue';
import { parseExpress } from '@/logic/forms/express';
const userStore = useUserStore()
const nowPositon = ref<string>('')
const isMapLoad = ref<boolean>(true)
const map = ref<any>()
const props = defineProps({
    field: {
        type: Object as PropType<YZGelocation>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const config = props.field.config as YZGelocationConfig
const mapShow = ref<boolean>(false)
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
const positionInfo = reactive({
    lng: '',
    lat: '',
    smpleress: '',
    address: ''

})
onMounted(() => {
    isFirstLoad.value = false
    window.addEventListener('touchmove', stopTextarea, true)
})
onUnmounted(() => {
    window.removeEventListener('touchmove', stopTextarea)
})
const stopTextarea = (e: any) => {
    let target = e.target
    if (target && target.tagName === 'TEXTAREA') {
        e.stopPropagation();
    }
}
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const onMapClick = () => {
    if (!props.field.disabled) {
        mapShow.value = true
        setTimeout(() => {
            if (config.getLimit()) {
                map.value = new AMap.Map('container', {
                    zoom: 11,//级别
                    expandZoomRange: false,
                    zoomEnable: false,
                    offset: [10, 20], //定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
                    zoomToAccuracy: true,   //定位成功后是否自动调整地图视野到定位点
                    dragEnable: false,
                    viewMode: '3D'//使用3D视图
                });
            } else {
                map.value = new AMap.Map('container', {
                    zoom: 11,//级别
                    offset: [10, 20], //定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
                    zoomToAccuracy: true,   //定位成功后是否自动调整地图视野到定位点
                    viewMode: '3D'//使用3D视图
                });
            }

            map.value.on("complete", function () {
                isMapLoad.value = false
                // 初始化当前位置
                initThisPosition(map.value)
                // if (!config.getLimit()) {
                // 绑定点击事件,切换坐标
                bindClick(map.value)
                // }

            });
        }, 1500)
    }

}
const rightClick = () => {
    vModel.value = nowPositon.value
    setFun()
    mapShow.value = false
    isMapLoad.value = true
    if (config.getMap()) {
        const $map = config.getMap()
        Object.keys($map).forEach(item => {
            const name = item
            const value = $map[name] as string
            let mapKey = value ?? name
            // 获取指定的控件
            let formId = props.field.targetFormId
            let field = parseExpress.getFieldByFid(formId, value)
            // let field = parseExpress.getField(value)
            if (value.indexOf(".") || value.indexOf("字表") > -1 || value.split('.').length === 2) {
                let fex = value.split('.')[0]
                let fieldModel = value.split('.')[1]
                // field = parseExpress.getField(fex)
                field = parseExpress.getFieldByFid(formId, fex)
                const fieldValue = field?.vModel.value
                if (fieldValue && fieldValue.length > 0) {
                    const newIndex = props.index
                    if (newIndex > -1) {
                        const items = fieldValue[newIndex].colums as Array<any>
                        const xfield = items.find(x => x.field.vModel.model === fieldModel)
                        field = xfield.field
                    }
                }
            }
            let mapValue: string = ''
            if (name === 'address') {
                mapValue = positionInfo[name]
            } else if (name === 'latitude') {
                mapValue = positionInfo['lat']
            } else if (name === 'longitude') {
                mapValue = positionInfo['lng']
            }
            if (field) {
                eventBus.emit('onMap', true, {
                    value: String(mapValue),
                    model: field.vModel.model,
                    uuid: field.uuid,
                    index: props.index
                })
            }
        })

    }
}
const leftClick = () => {
    mapShow.value = false
}
const initThisPosition = (map: any) => {
    map.plugin('AMap.Geolocation', function () {
        const geolocation = new AMap.Geolocation({
            enableHighAccuracy: true, //是否使用高精度定位，默认:true
            timeout: 30000, //超过10秒后停止定位，默认：无穷大
            buttonOffset: new AMap.Pixel(10, 20), //定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
            zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
            buttonPosition: 'RB',
            panToLocation: true,
            GeoLocationFirst: true,
            useNative: true
        });
        map.addControl(geolocation);
        geolocation.getCurrentPosition();
        AMap.event.addListener(geolocation, 'complete', onComplete);
        //返回定位信息
        AMap.event.addListener(geolocation, 'error', onError);
        //返回定位出错信息
    });
}
const onComplete = (val: any) => {
    const postion = val.position
    var marker = new AMap.Marker();;
    var lnglat = [postion.lng, postion.lat]
    // map.value.add(marker);
    // marker.setPosition(lnglat);
    oldPositon.value = marker
    if (val.addressComponent) {
        converAddress(lnglat, val.addressComponent.adcode)
    } else {
        converAddress(lnglat)
    }

}
const converAddress = (lnglat: string[], city?: string) => {
    var geocoder = new AMap.Geocoder({
        city: city,
        radius: 500 //范围，默认：500
    });
    geocoder.getAddress(lnglat, function (status: any, result: any) {
        if (status === 'complete' && result.regeocode) {
            var address = result.regeocode.formattedAddress;
            positionInfo.lng = lnglat[0]
            positionInfo.lat = lnglat[1]
            positionInfo.address = address
            if (result.regeocode.addressComponent) {
                const item = result.regeocode.addressComponent
                const rangeress = `${item.province}${item.city}${item.district}${item.street}${item.streetNumber}`
                const smpleress: string = `${item.province}${item.city}${item.district}${item.street}${item.streetNumber}${item.township}`
                positionInfo.smpleress = smpleress
                props.field.vModel.optionValue = positionInfo
                // 限定范围
                if (config.getLimit()) {
                    limitMap(map.value)
                }
                //限定范围
                // GetlimitRange(rangeress,item.city)
            }
            nowPositon.value = positionInfo.smpleress ?? address
            console.log('位置信息', positionInfo)

        } else {
            console.log('解析失败', result, status)
            // log.error('根据经纬度查询地址失败')
        }
    });
}
const GetlimitRange = (keywords: string, city: string) => {
    AMap.plugin('AMap.PlaceSearch', function () {
        var autoOptions = {
            city: city
        }
        var placeSearch = new AMap.PlaceSearch(autoOptions);
        placeSearch.search(keywords, function (status: any, result: any) {
            console.log('限定范围', result)
        })
    })
}
const onError = (error: any) => {
    console.log('地图定位失败', error)
    showNotify({
        type: 'danger',
        message: '地图定位失败: ' + error.message
    })
}
const oldPositon = ref<any>()
const bindClick = (map: any) => {
    AMap.event.addListener(map, "click", function (e: any) {
        const lnglag: string[] = [e.lnglat.lng, e.lnglat.lat]
        if (oldPositon.value) {
            map.remove(oldPositon.value)
        }
        addMaker(lnglag)
        converAddress(lnglag)
    });
}

const addMaker = (lnglag: string[]): void => {
    var marker = new AMap.Marker({
        icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
        position: lnglag
    });
    map.value.add(marker)
    oldPositon.value = marker
}
const limitMap = (map: any, lnglag?: string[]) => {
    // var bounds = map.getBounds();
    var mybounds = new AMap.Bounds([positionInfo.lng, positionInfo.lat], [positionInfo.lng, positionInfo.lat]);
    map.setLimitBounds(mybounds);
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
eventBus.on('onMap', {
    cb: async function (params) {
        console.log("%c [ params 1]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
            vModel.value = params.value
            setFun()
        }
    }
})
</script>
<style scoped>
#container {
    width: 95%;
    margin: 0px auto;
    height: calc(100vh - 120px);
    padding: 3px;
    border: 1px dashed #dbd3d3;
}

.ptip {
    text-align: center;
    font-size: var(--yz-com-12);
    color: #b9b3b3;
}
</style>