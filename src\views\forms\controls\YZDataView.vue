<template>
  <div v-if="!field.hidden">
    <van-cell>
      <van-cell style="padding-left: 0;padding-top: 0;">
        <div class="van-cell__title van-field__label"><label>{{ field.config.label }}</label></div>
      </van-cell>
      <!-- <vantMobile class="vant-table-dom" :columns="cols" :data="list"></vantMobile> -->
      <div class="approval_h" style="background: #ffffff; " v-show="list?.length">
        <van-overlay :show="showLoading">
          <div class="wrapper">
            <van-loading type="spinner" />
          </div>
        </van-overlay>
        <van-steps direction="vertical" :active="activeStep">
          <van-step v-for="(item, idx) in list" :key="idx">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div style="height: 38px;">
                <p style="font-size: 12px; position: absolute; left: 0;" v-show="item?.Sign">
                  [ {{ item?.Sign }} ] {{ item?.StepName }}</p>
                <p style="font-size: 12px; position: absolute; left: 0; top:30px;">
                  {{
                    new Intl.DateTimeFormat(navigator?.language, options).format(new Date(item?.SignAt + 'Z'))
                  }}</p>
              </div>
              <div style="font-size: 14px; overflow-wrap: anywhere; margin-top: 7px;">
                {{ item?.SelAction }}
              </div>
            </div>
          </van-step>
        </van-steps>
      </div>

      <van-pagination style="margin-top: 10px;" v-show="isPage" v-model="page" :total-items="total"
        :page-count="pageCount" @change="getData()" />
    </van-cell>

  </div>
</template>
<script lang="ts" setup>
// import vantMobile from 'vant-table'
import { url } from "@/api/url";
import { YZDataView, YZDataViewConfig } from "@/logic/forms/YZDataView";
import { useUserStore } from "@/store/users";
import { decodeBase64, useFormData, useIsChinese, useParamsFormatter, useStrToUnicode } from "@/utils";
import { usePostBody, usePostQuery } from "@/utils/request";
import { onMounted, ref, shallowRef } from "vue";
import { eventBus } from '@/utils/eventBus';
import { formBuilder } from '@/logic/forms/formBuilder';
import { useHomeStore } from '@/store/home';
const homeStore = useHomeStore()

const options: any = {
  hour12: false,
  day: "2-digit",
  month: "2-digit",
  year: "numeric",
  hour: "2-digit",
  minute: "2-digit",
  timeZoneName: "short"
}

const props = defineProps({
  field: {
    type: Object as PropType<YZDataView>,
    required: true,
  },
  modelValue: {
    type: String,
    default: "",
  },
  optionValue: {
    type: String,
    default: "",
  },
  index: {
    type: Number,
    default: -1,
  },
});
const user = useUserStore();
const config = props.field.config as YZDataViewConfig;
const list = ref([])
const cols = ref([]) as any
const page = ref<number>(1)
const start = ref<number>(0)
const limit = ref<number>(10)
const isPage = ref<boolean>(false)
const total = ref<number>(0)
const pageCount = ref<number>(0)
const isFirstLoad = ref<boolean>(true)
const showLoading = ref<boolean>(false)
onMounted(() => {
  getData();
  // props.field.expressTest(props.field, props.index, isFirstLoad.value)
  // props.field.disableTest(props.field, props.index, isFirstLoad.value)
  // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
  // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
  isFirstLoad.value = false
});
const vantEmptyTheme = () => {
  console.log("%c [ homeStore.getTheame ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", homeStore.getTheame)
  if (homeStore.getTheame === 'dark') {
    document.documentElement.style.setProperty('--el-bg-color', '#000');
  } else {
    document.documentElement.style.setProperty('--el-bg-color', '#fff');
  }
}

const setFilter = (filterObj: any) => {
  const fields = formBuilder.formConfig?.getFieldsRecord()
  let ff = 'e30='
  if (filterObj) {
    let strs = '{'
    Object.keys(filterObj).forEach(key => {
      const item = filterObj[key]
      if (fields && fields.length > 0) {
        let valueData = item.value
        if (item.field) {
          if (item.field.indexOf('.') > -1) {
            // 读取明细表
            const fieldItem = fields.find(x => x.tableName == item.field)
            if (fieldItem) {
              valueData = fieldItem.vModel.value
            }
          } else {
            const fieldItem = fields.find(x => x.vModel.model == item.field)
            if (fieldItem?.config.ctype === 'YZDept' || fieldItem?.config.ctype === 'YZSelect') {//modify
              valueData = fieldItem?.vModel.optionValue
            } else {
              valueData = fieldItem?.vModel.value
            }

          }
        }

        const nkey: string = buildFilterStr(key)
        strs += `"${nkey}":{"op":"${item.op}","value":"${buildFilterStr(valueData)}"},`
      }
    })
    if (strs.lastIndexOf(',') > -1)
      strs = strs.substring(0, strs.lastIndexOf(','))
    strs += "}"
    ff = btoa(strs)
  }
  return ff
}

const buildFilterStr = (str: string): string => {
  if (useIsChinese(str)) {
    return useStrToUnicode(str)
  }
  return str
}
const getData = () => {
  let dsConfig = config.getDataViewData();
  const { ds, enablePaging, gridSettingColumns, pageSize } = dsConfig;
  limit.value = pageSize
  isPage.value = enablePaging
  start.value = (page.value - 1) * limit.value
  cols.value = gridSettingColumns.map(item => {
    return {
      prop: item.dataIndex,
      title: item.text,
      width: item.width,
      align: item.align
    }
  })
  if (ds) {
    const { type } = ds;
    let newUrl = "";
    const { dataSourceId, tableName, orderBy, esbId, filter } = ds;
    if (type === "esb") {
      newUrl = useParamsFormatter(enablePaging ? url.dataSource.getEsbDataPaging : url.dataSource.getEsbDataNoPage, {
        params1: esbId,
      });
      const formdata = useFormData({
        f: setFilter(filter),
        c: "",
        o: orderBy ? decodeBase64(orderBy) : '', //YZ +
        page: page.value,
        start: start.value,
        limit: limit.value
      });
      usePostBody(newUrl, {}, formdata)
        .then((result) => {
          if (enablePaging) {
            if (result.success) {
              list.value = result.children
              total.value = result.total
              pageCount.value = Math.ceil(total.value / limit.value)
            }
          } else {
            if (result) {
              list.value = result
              // total.value =result.children.length
            }
          }
        })
        .catch((error) => {
          console.log(98, error);
        });
    } else if (type === "table") { //YZ +
      showLoading.value = true

      // enablePaging 开启分页
      newUrl = useParamsFormatter( //bpm/datasource/527365695868997/table/VW_TaskHistory
        enablePaging
          ? url.dataSource.getDataSource
          : url.dataSource.getTableData,
        {
          params1: dataSourceId,
          params2: type,
          params3: tableName,
        }
      );
      const formdata = useFormData({
        f: setFilter(filter),
        c: "",
        o: orderBy ? decodeBase64(orderBy) : '', //YZ +
        page: page.value,
        start: start.value,
        limit: limit.value
      });
      usePostBody(newUrl, {}, formdata) //YZ +
        .then((result) => { // 审批历史 VW_TaskHistory result
          if (enablePaging) {
            if (result.children) {
              list.value = result.children
              total.value = result.total
              pageCount.value = Math.ceil(total.value / limit.value)
              showLoading.value = false
            }
          } else {
            if (result) {
              list.value = result.reverse()
              showLoading.value = false
            }
          }
        })
        .catch((error) => {
          showLoading.value = false
          console.log(98, error);
        });
    } else if (type === "form") {
      // enablePaging 开启分页
      newUrl = useParamsFormatter(
        enablePaging ? url.dataSource.getFormPage : url.dataSource.getFormNoPage,
        {
          params1: ds.formId,
          params2: ds.formTable,
        }
      );
      const formdata = useFormData({
        f: setFilter(filter),
        c: "",
        o: orderBy ? decodeBase64(orderBy) : '',
        page: page.value,
        start: start.value,
        limit: limit.value
      });
      usePostBody(newUrl, {}, formdata)
        .then((result) => {
          if (enablePaging) {
            if (result.children) {
              list.value = result.children
              // console.log(total)
              total.value = result.total
              pageCount.value = Math.ceil(total.value / limit.value)
            }
          } else {
            if (result) {
              list.value = result
              // total.value =result.children.length
            }
          }
        })
        .catch((error) => {
          console.log(98, error);
        });
    }
  }
};
eventBus.on('setFieldHidden', {
  cb: function (params) {
    if (params.uuid === props.field.uuid) {
      props.field.hidden = params.value
    }
  }
})
eventBus.on('setDataViewReload', {
  cb: function (parasm) {
    if (parasm.uuid === props.field.uuid) {
      getData()
    }
  }
})
</script>
<style>
.vant-table {
  width: auto !important;
}

.vant-table-dom {
  overflow: unset;
}

.vant-table-dom .vant-table-track {
  overflow-x: auto;
  overflow-y: hidden;
}

.vant-table-dom .vant-tbody--empty {
  background-color: var(--van-background-2) !important;
}

.vant-table-dom .vant-tbody .vant-tr .vant-td {
  background-color: var(--van-background-2) !important;
  padding: 12px 0 12px 8px;
}

.vant-table-dom .vant-thead th.vant-th {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 12px 2px 12px 2px;
}

::v-deep(.van-step--vertical) {
  height: 38px !important;
}
</style>