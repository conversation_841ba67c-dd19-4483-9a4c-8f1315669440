import i18n from "@/locale";
import CryptoJS, { mode, pad } from "crypto-js";
import dayjs from "dayjs";
import x2js from "x2js";
import { create, all, exp } from "mathjs";
import { useCore } from "@/store/core";
import store from "@/store";
import dd from "dingtalk-jsapi";
import { Locale, useCurrentLang } from "vant";
import zhCN from "vant/es/locale/lang/zh-CN";  // cf +
import enUS from "vant/es/locale/lang/en-US";  // cf +
import koKR from "vant/es/locale/lang/ko-KR";  // cf +
import deDE from "vant/es/locale/lang/de-DE-formal";  // cf + 

const core = useCore(store);
// 加密配置
const cfg = {
  mode: CryptoJS.mode.ECB,
  padding: CryptoJS.pad.Pkcs7,
};
const Entersecret = "2XNN4K8LC0ELVWN4";
/**
 * 语言翻译
 * @param title 语言key值
 * @returns  返回翻译后的语言
 */
export function useLang(title: string): string {
  const { te, t } = i18n.global;
  if (te(title)) return t(title);
  const titles = title.split(".");
  if (titles.length > 1) {
    return titles[1];
  } else {
    return title;
  }
  return title
}
/**
 *  切换语言
 * @param lang 语言
 */
export function useChangLang(lang: string): void {  //vant设置语言
  console.log("%c [useChangLang ---vant语言调用---->>> lang ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", lang)
  i18n.global.locale = lang || "en" // en 备用语言
  switch (lang) {
    case "zh-CN":
      Locale.use("zh-CN", zhCN);
      break;
    case "en":
      Locale.use("en-US", enUS);
      break;
    case "ko-KR":
      Locale.use("ko-KR", koKR);
      break;
    case "de-DE":
      Locale.use("de-DE", deDE);
      break;
    default:
      Locale.use("zh-CN", zhCN);
      break;
  }
  core.setLang(lang);
  const currentLang = useCurrentLang();
  console.log("%c [ vant 检测---当前语言 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", currentLang.value)
}

/**
 *  读取本地存储
 * 存入在sessionStoreage中
 */
export const useSessionStorage = {
  get(key: string): string {
    return sessionStorage.getItem(key) as string;
  },
  getObject<T>(key: string): T | null {
    let json = sessionStorage.getItem(key) as string;
    return json === null ? json : (JSON.parse(json) as T);
  },
  getSecret(key: string): string {
    let data = sessionStorage.getItem(key) as string;
    if (data === null) return data;
    let value = CryptoJS.AES.decrypt(data, Entersecret, cfg).toString(
      CryptoJS.enc.Utf8
    );
    return value;
  },
  getSecretObject<T>(key: string): T | null {
    let data = sessionStorage.getItem(key) as string;
    if (data === null) return data;
    let value = CryptoJS.AES.decrypt(data, Entersecret, cfg).toString(
      CryptoJS.enc.Utf8
    );
    return JSON.parse(value) as T;
  },
  set(key: string, data: string): void {
    sessionStorage.setItem(key, data);
  },
  setObject<T>(key: string, data: T): void {
    let json = JSON.stringify(data);
    sessionStorage.setItem(key, json);
  },
  setSecret(key: string, data: string): void {
    let json = CryptoJS.AES.encrypt(data, Entersecret, cfg).toString();
    sessionStorage.setItem(key, json);
  },
  setSecretObject<T>(key: string, data: T): void {
    let json = JSON.stringify(data);
    let seData = CryptoJS.AES.encrypt(json, Entersecret, cfg).toString();
    sessionStorage.setItem(key, seData);
  },
  remove(key: string): void {
    sessionStorage.removeItem(key);
  },
};
/**
 *  读取本地存储
 * 存入在sessionStoreage中
 */
export const useLocalStorage = {
  get(key: string): string {

    return localStorage.getItem(key) as string;
  },
  getObject<T>(key: string): T | null {
    let json = localStorage.getItem(key) as string;
    return json === null ? json : (JSON.parse(json) as T);
  },
  getSecret(key: string): string {
    let data = localStorage.getItem(key) as string;
    if (data === null) return data;
    let value = CryptoJS.AES.decrypt(data, Entersecret, cfg).toString(
      CryptoJS.enc.Utf8
    );
    return value;
  },
  getSecretObject<T>(key: string): T | null {
    let data = localStorage.getItem(key) as string;
    if (data === null) return data;
    let value = CryptoJS.AES.decrypt(data, Entersecret, cfg).toString(
      CryptoJS.enc.Utf8
    );
    return JSON.parse(value) as T;
  },
  set(key: string, data: string): void {
    localStorage.setItem(key, data);
  },
  setObject<T>(key: string, data: T): void {
    let json = JSON.stringify(data);
    localStorage.setItem(key, json);
  },
  setSecret(key: string, data: string): void {
    let json = CryptoJS.AES.encrypt(data, Entersecret, cfg).toString();
    localStorage.setItem(key, json);
  },
  setSecretObject<T>(key: string, data: T): void {
    let json = JSON.stringify(data);
    let seData = CryptoJS.AES.encrypt(json, Entersecret, cfg).toString();
    localStorage.setItem(key, seData);
  },
  remove(key: string): void {
    localStorage.removeItem(key);
  },
};
/**
 *
 * @returns 返回当前平台
 */
export function usePlatForm() {
  let platForm = navigator.userAgent.toLowerCase();
  if (platForm.indexOf("win32") >= 0 || platForm.indexOf("wow32") >= 0) {
    return "Windows";
  } else if (platForm.indexOf("win64") >= 0 || platForm.indexOf("wow64") >= 0) {
    return "Windows";
  } else {
    return "Mac";
  }
}
/**
 * 将普通对象转换为formData
 * @param obj 对象
 * @returns 返回formData对象
 */
export function useFormData(obj: any): FormData {
  const formData = new FormData();
  Object.keys(obj).forEach((key) => {
    formData.append(key, obj[key]);
  });
  return formData;
}
export function useQuto(date: Date): number {
  const month = date.getMonth() + 1;
  if (month >= 1 && month <= 3) {
    return 1;
  } else if (month > 3 && month <= 6) {
    return 2;
  } else if (month > 6 && month <= 9) {
    return 3;
  } else {
    return 4;
  }
}
/**
 * 将日期进行格式化成段日期
 * @param date 日期字符串或日期格式
 * @returns 如：2023-09-01
 */
export function useShortTime(date: any) {
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
}
/**
 *  获取dayjs 的实例
 * @param date 日期字符串或标准日期格式
 * @returns 返回第三方库dayjs的实例
 */
export function useDayjs(date?: any) {
  return dayjs(date);
}
/**
 * 将日期进行格式化成段日期
 * @param date 日期字符串或日期格式
 * @returns 如：2023/09/01
 */
export function useShortDate(date: any) {
  return dayjs(date).format("YYYY/MM/DD");
}
export function useShortTimeArrys(date: any) {
  const newDate = new Date(date);
  const year = newDate.getFullYear();
  const month = newDate.getMonth() + 1;
  const day = newDate.getDate();
  const newMonth = month >= 10 ? month : "0" + month;
  const newDay = day >= 10 ? day : "0" + day;
  return [String(year), String(newMonth), String(newDay)];
}
export function useTimeDiff(start: string, end: string) {
  const date1 = dayjs(end);
  const value = date1.diff(start, "day");
  return value;
}
export function useJsonToXml(object: object): string {
  const xml = new x2js();
  return xml.js2xml(object);
}
export function useX2js(): x2js {
  return new x2js();
}
export function useDeepClone(target: any): any {
  // 定义一个变量
  let result: any = {};
  // 如果当前需要深拷贝的是一个对象的话
  if (typeof target === "object") {
    // 如果是一个数组的话
    if (Array.isArray(target)) {
      result = []; // 将result赋值为一个数组，并且执行遍历
      for (let i in target) {
        // 递归克隆数组中的每一项
        result.push(useDeepClone(target[i]));
      }
      // 判断如果当前的值是null的话；直接赋值为null
    } else if (target === null) {
      result = null;
      // 判断如果当前的值是一个RegExp对象的话，直接赋值
    } else if (target.constructor === RegExp) {
      result = target;
    } else {
      // 否则是普通对象，直接for in循环，递归赋值对象的所有值
      result = {};
      for (let i in target) {
        result[i] = useDeepClone(target[i]);
      }
    }
    // 如果不是对象的话，就是基本数据类型，那么直接赋值
  } else {
    result = target;
  }
  // 返回最终结果
  return result;
}
export function useBase64(data: any) {
  const value = CryptoJS.enc.Utf8.parse(data);
  return CryptoJS.enc.Utf8.stringify(value);
}
export function decodeBase64(data: any) {
  const value = CryptoJS.enc.Utf8.parse(data);
  return CryptoJS.enc.Base64.stringify(value);
}
export function useUninCode(value: string) {
  let newValue: string = "";
  for (var i = 0; i < value.length; i++) {
    newValue += "\\u" + left_zero_4(value.charCodeAt(i).toString(16));
  }
  return newValue;
}
function left_zero_4(str: string) {
  if (str != null && str != "" && str != "undefined") {
    if (str.length == 2) {
      return "00" + str;
    }
  }
  return str;
}
export function useUUID(): string {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
// 使用weakmap解决循环引用
// 优点：
// 1 .WeakMap来记录对象是否被克隆,主要考虑一下三点。
//  2 .WeakMap对象是key=>value形式，不会重复记录
//  3 .WeakMap是弱引用，如果不在使用，空间会直接释放

export function deepCopy(obj: any, hash = new WeakMap()) {
  // 不是对象（普通值类型/function）,null,undefined,正则，Date都会直接返回
  if (obj == null || typeof obj != "object") {
    return obj;
  }
  if (obj instanceof RegExp) {
    return new RegExp(obj);
  }
  if (obj instanceof Date) {
    return new Date(obj);
  }

  // 判断是否循环引用的（判断属性是不是存在了）
  if (hash.get(obj)) return hash.get(obj);

  let cloneObj = new obj.constructor();

  // 存obj
  hash.set(obj, cloneObj);

  for (let key in obj) {
    // in 循环会遍历原型链的，所以需要判断是否是当前对象的属性
    if (obj.hasOwnProperty(key)) {
      cloneObj[key] = deepCopy(obj[key], hash);
    }
  }

  return cloneObj;
}

/**
 *  格式化url参数，与其它参数不一样
 * obj的数据key必须与url 上的占位符一致
 * @param obj 参数值
 * @param url 需要格式化的地址
 */
export const useParamsFormatter = (url: string, obj: any): string => {
  let newUrl = url;
  Object.keys(obj).forEach((item) => {
    const newParams = item.indexOf("@") > -1 ? item : "@" + item;
    const value = obj[item];
    newUrl = newUrl.replace(newParams, value);
  });
  return newUrl;
};
export const useTimeZHcn = (time: any) => {
  const date = new Date(time);
  const nowDate = new Date();
  const setYear = date.getFullYear();
  const setMonth = date.getMonth() + 1;
  const setDay = date.getDate();
  const setHour = date.getHours();
  const setMint = date.getMinutes();
  const setSecond = date.getSeconds();
  const nowYear = nowDate.getFullYear();
  const nowMonth = nowDate.getMonth() + 1;
  const nowDay = nowDate.getDate();
  let text = `${nowYear}-${nowMonth}-${nowDay}`;
  if (setYear != nowYear) return `${nowYear - setYear}年前`;
  if (setMonth != nowMonth) return `${nowMonth - setMonth}月前`;
  const day = nowDay - setDay;
  switch (day) {
    case 0:
      if (setHour > 12) {
        text = `下午 ${setHour}:${setMint}`;
      } else {
        text = `上午 ${setHour}:${setMint}`;
      }

      break;
    case 1:
      text = `昨天 ${setHour}:${setMint} `;
      break;
    case 2:
      text = `前天 ${setHour}:${setMint} `;
      break;
    default:
      text = `${day}天前`;
      break;
  }
  return text;
};
export const useMath = () => {
  const mathjs = create(all, {});
  return mathjs;
};
export const useQueryParams = (variable: string, url?: string) => {
  let query = url ? url : window.location.search.substring(1);
  if (!query.startsWith("?")) {
    const endIndex = query.lastIndexOf("?");
    if (endIndex > -1) {
      query = query.substring(endIndex);
      if (query.startsWith("?")) {
        query = query.substring(1);
      }
    }
  }
  var vars = query.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    if (pair[0] == variable) {
      return pair[1];
    }
  }
  return false;
};
export function useQueryHashParams(name: string) {
  const hash = window.location.hash;
  const dictionary: Record<string, any> = {};
  if (hash) {
    const params = hash.split("?");
    if (params && params.length > 0) {
      const hashItems = params[1];
      if (hashItems) {
        const $items = hashItems.split("&");
        if ($items && $items.length > 0) {
          $items.forEach((item) => {
            const keyItem = item.split("=");
            if (keyItem && keyItem.length > 0) {
              const dicKey = keyItem[0];
              const dicValue = keyItem[1];
              dictionary[dicKey] = dicValue;
            }
          });
        }
      }
    }
  }
  if (dictionary[name]) {
    return dictionary[name];
  }
  return false;
}

export const isArray = (arr: any) => {
  const toString = Object.prototype.toString;
  const isArray =
    Array.isArray ||
    function (arg) {
      return toString.call(arg) === "[object Array]";
    };
  return isArray(arr);
};
export const isDefined = (a: any) => {
  return typeof a !== "undefined";
};

export const useCheckPhone = (mobile: string): boolean => {
  var tel = /^0\d{2,3}-?\d{7,8}$/;
  var phone = /^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1}))+\d{8})$/;
  if (mobile.length == 11) {
    //手机号码
    if (phone.test(mobile)) {
      return true;
    }
  } else if (mobile.length == 13 && mobile.indexOf("-") != -1) {
    //电话号码
    if (tel.test(mobile)) {
      return true;
    }
  }
  return false;
};
export const dataURLtoBlob = (dataurl: string): any => {
  var arr = dataurl.split(",");
  if (arr && arr.length > 0) {
    const mime = arr[0].match(/:(.*?);/)?.[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  }
};

export const dataURLtoArrys = (dataurl: string): any => {
  var arr = dataurl.split(",");
  if (arr && arr.length > 0) {
    const mime = arr[0].match(/:(.*?);/)?.[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return u8arr;
  }
};
export const dataToFile = (dataurl: any, filename: string) => {
  let arr = dataurl.split(",");
  let mime = arr[0].match(/:(.*?);/)[1];
  let suffix = mime.split("/")[1];
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], `${filename}.${suffix}`, {
    type: mime,
  });
};
export const useAesData = (secretKey: string, value: string): string => {
  let key = CryptoJS.enc.Utf8.parse(secretKey);
  let encryptStr = CryptoJS.AES.encrypt(value, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  }).toString();
  return encryptStr;
};
// export const useQutoRange = (num: number, year: number) => { //YZ -
//   const quto: any = {
//     PostDate1: "",
//     PostDate2: "",
//   };
//   if (num === 1) {
//     quto.PostDate1 = `${year}-01-01T00:00:00`;
//     quto.PostDate2 = `${year}-03-01T00:00:00`;
//   } else if (num === 2) {
//     quto.PostDate1 = `${year}-04-01T00:00:00`;
//     quto.PostDate2 = `${year}-06-01T00:00:00`;
//   } else if (num === 3) {
//     quto.PostDate1 = `${year}-07-01T00:00:00`;
//     quto.PostDate2 = `${year}-09-01T00:00:00`;
//   } else {
//     quto.PostDate1 = `${year}-10-01T00:00:00`;
//     quto.PostDate2 = `${year}-12-01T00:00:00`;
//   }
//   return quto;
// };
export const useQutoRange = (num: number, year: number) => { //YZ +
  const quto: any = {
    PostDate1: "",
    PostDate2: "",
  };
  if (num === 1) {
    quto.PostDate1 = `${year}-01-01T00:00:00`;
    quto.PostDate2 = `${year}-04-01T00:00:00`;
  } else if (num === 2) {
    quto.PostDate1 = `${year}-04-01T00:00:00`;
    quto.PostDate2 = `${year}-07-01T00:00:00`;
  } else if (num === 3) {
    quto.PostDate1 = `${year}-07-01T00:00:00`;
    quto.PostDate2 = `${year}-10-01T00:00:00`;
  } else {
    quto.PostDate1 = `${year}-10-01T00:00:00`;
    quto.PostDate2 = `${year + 1}-01-01T00:00:00`;
  }
  return quto;
};
export const useCallBackClose = (fn: Function, headShow: boolean = false) => {
  if (useEnviroMent().isComWeChat && headShow) {
    window.wx.closeWindow();
  } else if (useEnviroMent().isDingTalk && headShow) {
    dd.biz.navigation.close({
      onSuccess: function (result: any) { },
      onFail: function (err: any) { },
    });
  } else {
    fn();
  }
};
export const useEnviroMent = (): IEnviroMent => {
  const agent = window.navigator.userAgent.toLocaleLowerCase();
  const env: IEnviroMent = {
    isWeChat: false,
    isComWeChat: false,
    isH5: false,
    isDingTalk: false,
    isUniApp: false,
    isFeiShu: false,
    isThird: false,
  };
  if (/wxwork/.test(agent)) {
    env.isComWeChat = true;
  } else if (/micromessenger/.test(agent)) {
    env.isWeChat = true;
  } else if (/dingtalk/.test(agent)) {
    env.isDingTalk = true;
  } else if (/uni-app/.test(agent)) {
    env.isUniApp = true;
  } else if (
    /feishu/.test(agent) ||
    window.location.href.indexOf("feishu") > -1
  ) {
    env.isFeiShu = true;
  } else if (window.location.href.indexOf("third") > -1) {
    env.isThird = true;
  } else {
    env.isH5 = true;
  }
  return env;
};
export function useIsChinese(temp: string) {
  // if (/[^\u4e00-\u9fa5]/.test(temp)) return false
  // return true;
  return /[\u4E00-\u9FFF]/.test(temp);
}
export function useStrToUnicode(chineseStr: string) {
  // if (chineseStr == '') {
  //     return chineseStr;
  // }
  // let unicodeStr = '';
  // for (let i = 0, iLength = chineseStr.length; i < iLength; i++) {
  //     unicodeStr += '\\u' + chineseStr.charCodeAt(i).toString(16);
  // }
  // // console.log(unicodeStr,'unicodeStr')
  // return unicodeStr;
  let unicodeStr = "";
  for (let i = 0; i < chineseStr.length; i++) {
    let char = chineseStr.charAt(i);
    if (/[^\u0000-\u00FF]/.test(char)) {
      // 判断是否为中文字符就编码
      let unicode = char.charCodeAt(0).toString(16);
      while (unicode.length < 4) {
        unicode = "0" + unicode;
      }
      unicodeStr += "\\u" + unicode;
    } else {
      unicodeStr += char;
    }
  }
  return unicodeStr;
}

export const TodaysDateFormat = () => { // 今日日期格式 2024-12-20 12:34:56
  const today = new Date();
  const dateString = today.getFullYear() + '-' +
    String(today.getMonth() + 1).padStart(2, '0') + '-' +
    String(today.getDate()).padStart(2, '0') + ' ' +
    String(today.getHours()).padStart(2, '0') + ':' +
    String(today.getMinutes()).padStart(2, '0') + ':' +
    String(today.getSeconds()).padStart(2, '0');
  return dateString;
};
export const formatDate = (isoDate) => { //具体到秒的时间
  const date = new Date(isoDate);
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, '0');
  const dd = String(date.getDate()).padStart(2, '0');
  const hh = String(date.getHours()).padStart(2, '0');
  const min = String(date.getMinutes()).padStart(2, '0');
  const ss = String(date.getSeconds()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd} ${hh}:${min}:${ss}`;
};
export const shortTimezoneName = (date) => { //GMT+8 时区
  let options = {
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
  };
  let parts = new Intl.DateTimeFormat(navigator?.language, options).formatToParts(date);
  let timeZoneName = parts.find(p => p.type === 'timeZoneName').value;
  return timeZoneName;
}
export interface IEnviroMent {
  isWeChat: boolean;
  isH5: boolean;
  isComWeChat: boolean;
  isDingTalk: boolean;
  isUniApp: boolean;
  isFeiShu: boolean;
  isThird: boolean;
}
