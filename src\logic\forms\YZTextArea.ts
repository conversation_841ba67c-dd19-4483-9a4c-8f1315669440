

import { useLang } from "@/utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>g, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>ty<PERSON> } from "./formModel";
export class YZTextAreaConfig extends YZConfig {
    private defaultRules: Array<any>;
    private maxLength: number
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZTextArea'
        this.maxLength = parmas['extends'].maxLength ?? 500
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable || this.forceValidation && this.validators && this.validators.find(item => parmas.groupIdArr.includes(item.validationGroup))) {
            this.defaultRules = [...this.defaultRules, { validator: (val: any) => { return val.length < this.maxLength }, message: `${useLang('Form.FieldLength_tips')}${this.maxLength}` }]
        } else {
            this.defaultRules = [{ validator: (val: any) => { return val.length < this.maxLength }, message: `${useLang('Form.FieldLength_tips')}${this.maxLength}` }]
        }
        this.rules = this.defaultRules
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
    getMaxLength(): number {
        return this.maxLength
    }
}
export class YZTextAreaStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZTextArea extends YZField {
    /**
     * 私有控件属性或自定义重写控件属性
     */
    private row: number;
    private showLimit: boolean;
    constructor(parms: any) {
        super(parms)
        this.readonly = parms['__config__'].extends.readOnly ?? parms.readonly
        this.row = parms.autosize.minRows
        this.showLimit = parms['show-word-limit']
    }
    initConfig(config: any): YZConfig {
        return new YZTextAreaConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZTextAreaStyle(style)
    }
    getRow() {
        return this.row
    }
    getShowLimit() {
        return this.showLimit
    }
    getMaxCount() {
        return this.row * 30
    }
}