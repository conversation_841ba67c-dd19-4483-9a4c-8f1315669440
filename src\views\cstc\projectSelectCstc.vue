<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div class="btn">
                <van-icon name="filter-o" size="28" @click="rightShow" />
                <van-popup v-model:show="isShow" position="right" :style="{ width: '82%', height: '100%' }">
                    <div>
                        <div class="li_">
                            <span class="name">{{ $t('CSTC.ProjectNo') }}</span>
                            <van-field v-model="p_no" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">{{ $t('CSTC.ProjectName') }}</span>
                            <van-field v-model="p_name" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">{{ $t('CSTC.GPS') }}</span>
                            <van-field v-model="GPS_" clearable />
                        </div>
                    </div>
                    <div
                        style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
                        <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset')
                            }}</van-button>
                        <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm">{{
                            $t('Global.Confirm')
                        }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="list">
            <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                :loosing-text="$t('Form.Release_to_refresh')" :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loadData.loading" :finished="loadData.finished"
                    :finished-text="$t('Form.NoMore')" :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div v-for="(i, idx) in viewColsData" :key="idx">
                        <div class="li" @click="toDetailCSTC(i)">
                            <div class="li-l">
                                <!-- 项目号 -->
                                <div class="li-n ellipsis"><span class="name">{{ $t('CSTC.ProjectNo') }}</span>
                                    <span class="val_">
                                        {{ i.ProjectNumber }}
                                    </span>
                                </div>
                                <!-- 项目名称 -->
                                <div class="li-m ellipsis"><span class="name">{{ $t('CSTC.ProjectName') }}</span>
                                    <span class="val_">{{
                                        i.ProjectName }}</span>
                                </div>
                                <div class="li-m ellipsis"><span class="name">{{ $t('CSTC.GPS')
                                        }}</span>{{
                                            i.GPSLocation }}</div>
                            </div>
                            <div class="li-r">
                                <van-icon name="arrow" size="28" />
                            </div>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed, ref, reactive, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useFormData } from '@/utils'
import { useUserStore } from "@/store/users"
import { useBase64 } from '@/utils';
import { usePostBody, useGetQuery } from '@/utils/request'
import { url } from '@/api/url';

const user = useUserStore()
const router = useRouter()
const userInfo = computed(() => user.getUserInfo)
const searchValue = ref('')
const isShow = ref(false)
const p_no = ref('')
const p_name = ref('')
const GPS_ = ref('')
const searchValue_ = ref('')
const query_type = ref('ProjectNumber')
// const config = props.field.config
const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    // pageSize: config.getDataBoweswer()?.pageSize || 10
    pageSize: 20
})
const viewColsData = ref([])
const comeBack = () => {
    router.push({
        path: '/generateQRCode',
        replace: true
    })
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const onSearch = () => {
    loadData.pageIndex = 1
    console.log("%c [ onSearch ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value)
    onLoad()
}
const rest = () => {
    p_no.value = ''
    p_name.value = ''
    GPS_.value = ''
    searchValue_.value = ''
    query_type.value = 'ProjectNumber'
}
const confirm = () => {
    if (p_no.value) {
        searchValue_.value = p_no.value
        query_type.value = 'ProjectNumber'
    } else if (p_name.value) {
        searchValue_.value = p_name.value
        query_type.value = 'ProjectName'
    } else if (GPS_.value) {
        searchValue_.value = GPS_.value
        query_type.value = 'GPSLocation'
    }

    // else if (p_no.value && p_name.value) {
    //     searchValue_.value = p_no.value + p_name.value
    //     query_type.value = 'ProjectNumber'
    // } else if (p_no.value && GPS_.value) {
    //     searchValue_.value = p_no.value + GPS_.value
    //     query_type.value = 'ProjectNumber'
    // } else if (p_name.value && GPS_.value) {
    //     searchValue_.value = p_name.value + GPS_.value
    //     query_type.value = 'ProjectName'
    // } else if (p_no.value && p_name.value && GPS_.value) {
    //     searchValue_.value = p_no.value + p_name.value + GPS_.value
    //     query_type.value = 'ProjectNumber'
    // } else {

    // }
    loadData.pageIndex = 1
    onLoad()
    isShow.value = false
}


const onRefresh = () => {
    searchValue.value = '' // 清空搜索框
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    viewColsData.value = []
    rest()
    onLoad()
}

const onLoad = async () => {
    const { success, user } = await useGetQuery(url.login.getUserInfo);
    if (success) {
        console.log('搜索不调用 ????? ')
        const SubjectType = new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'Linde' : 'Supplier'
        const ff = window.btoa(JSON.stringify({ "SubjectType": { "op": "=", "value": SubjectType }, "UserAccount": { "op": "=", "value": user?.Account } }))
        const cc = window.btoa(JSON.stringify(["ProjectNumber", "ProjectName", "SupplierCode", "SupplierName", "GPSLocation"]))
        if (loadData.refreshing) {
            loadData.refreshing = false
            viewColsData.value = []
        }
        const search = [{ "name": "all", "op": "like", "value": searchValue.value, "isAll": true }]
        console.log("%c [ 全局搜 searchValue.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value)
        const search_ = [{ "name": query_type.value, "op": "like", "value": searchValue_.value, "isAll": false }]
        console.log("%c [ search_ ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", search_)
        console.log("%c [ 条件搜 searchValue_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue_.value)
        const formdata = useFormData({
            o: '', //decodeBase64(ds.orderBy),
            // start: searchValue.value ? 0 : searchValue_.value ? 0 : (loadData.pageIndex - 1) * loadData.pageSize,
            // page: searchValue.value ? 1 : searchValue_.value ? 1 : loadData.pageIndex,
            start: (loadData.pageIndex - 1) * loadData.pageSize,
            page: loadData.pageIndex,
            limit: loadData.pageSize,
            f: ff,
            c: cc,
            lc: cc, // 查询列 同上
            s: btoa(window.unescape(encodeURIComponent(JSON.stringify(searchValue_.value ? search_ : search))))
        })
        console.log("%c [ searchValue_.value ? search_ : search ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue_.value ? search_ : search)

        try {
            const data = await usePostBody('bpm/datasource/527365695868997/table/VW_UAUC_ProjectSupplier/paging', {}, formdata) //项目名称，项目编号的接口
            if (data.success) {
                viewColsData.value = data.children || []
                loadData.pageIndex++
                loadData.loading = false
                if (data.children.length < loadData.pageSize) {
                    loadData.finished = true
                    console.log("停止刷新 2")
                }
                rest()
            }
        } catch (error) {
            console.log("projectSelectSctc---error", error)
        }

    } else {
        console.log('getUserInfo error')
    }
}
const toDetailCSTC = (i) => {
    router.push({
        path: '/projectDetailCstc',
        query: {
            ProjectNumber: i.ProjectNumber,
            ProjectName: i.ProjectName,
            SupplierCode: i.SupplierCode,
            SupplierName: i.SupplierName,
            region: i.GPSLocation
        },
        replace: true
    })
}

onMounted(() => {

})
onUnmounted(() => {

})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;

            .li_ {
                padding: 20px 13px 0px 13px;

                .van-field {
                    border-radius: 13px;
                    padding: 3px;
                    margin-top: 10px;
                    background: #f8f8f8;
                }

                .name {
                    color: #999999;

                }
            }
        }
    }

    .list {
        margin-top: 10px;
        width: 100%;
        height: 100vh;

        .li {
            border-bottom: 1px solid #DCDFE6;
            background-color: var(--yz-div-background);
            padding: 10px 12px;
            display: flex;
            align-items: center;

            .li-l {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #444444;

                .name {
                    color: #999999;
                    margin-right: 10px;
                }

                .val_ {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .li-m {
                    margin-top: 10px;
                }
            }

            .li-r {
                width: 20px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

::v-deep(.van-field__control) {
    color: #333 !important;
}
</style>