<template>
    <div></div>
</template>
<script lang="ts" setup>
import { url } from '@/api/url';
import { IUserInfo } from '@/logic/login/loginModel';
import { wechatLogin } from '@/router/wechat';
import { useLoingStore } from '@/store/login';
import { useProcessStore } from '@/store/process';
import { useUserStore } from '@/store/users';
import { useParamsFormatter, useQueryHashParams, useQueryParams } from '@/utils';
import { useGetQuery, usePostBody } from '@/utils/request';
import { showLoadingToast, showNotify } from 'vant';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
const loginStore = useLoingStore()
const userStore = useUserStore()
const processStore = useProcessStore()
const router = useRouter()
let loading: any = {}

onMounted(async () => {
    loading = showLoadingToast({
        forbidClick: true,
        duration: 0
    });
    try {
        const code = useQueryParams("code")
        if (loginStore.getToken) {
            buildParamsToForm()
            const sdk = await buidlSignature()
            if (sdk) {
                buildWechatSdk(sdk, async function (val: boolean) { })
            }
        } else {
            if (code) {
                const newUrl = useParamsFormatter(url.login.getWeChatUser, {
                    params1: code
                })
                const { success, auth, errorMessage } = await useGetQuery(newUrl, {}, false)
                if (!success) {
                    showNotify({
                        type: 'danger',
                        message: '平台认证失败:' + errorMessage
                    })
                } else {
                    const sdk = await buidlSignature()
                    if (sdk) {
                        buildWechatSdk(sdk, async function (val: boolean) {
                            if (val) {
                                loginStore.ssoLogin(auth)
                            }
                        })
                    }
                }

            } else {
                await wechatLogin()
            }
        }
        loading.close()
    } catch (error) {
        loading.close()
    }

})
const buildJwtToken = async ({ AppkeyName, AccessToken, Account }: any) => {
    const { success, token, errorMessage } = await usePostBody(url.login.thirdAuth, {
    }, {
        appKeyName: AppkeyName,
        AccessToken: AccessToken,
        Account: Account
    }, false)
    if (!success) {
        showNotify({
            type: 'danger',
            message: errorMessage
        })
    } else {
        // 设置实际token 
        loginStore.setToken(token)
        //获取用户信息
        const { success, user } = await useGetQuery(url.login.getUserInfo, false)
        if (success) {
            userStore.setUserInfo(user as IUserInfo)
            loading.close()
            router.push({ path: '/layout/home', replace: true })
        }
        console.log("%c [ wechat ? token ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", token)

    }
}
const buidlSignature = async () => {
    const { success, timestamp, signature, nonceStr, errorMessage } = await useGetQuery(url.login.getWeChatJssdk, {
        url: window.location.href.split('#')[0]
    }, false)
    if (!success) {
        showNotify({ type: 'danger', message: errorMessage });
        return null
    }
    return {
        timestamp,
        signature,
        nonceStr
    }
}
const buildWechatSdk = ({ timestamp, signature, nonceStr }: any, fn: Function): void => {
    window.wx.config({
        beta: true,
        debug: false,
        appId: window.webConfig.weChat.appid, // 必填，企业微信的corpid，必须与当前登录的企业一致
        timestamp: timestamp, // 必填，生成签名的时间戳
        nonceStr: nonceStr, // 必填，生成签名的随机串
        signature: signature,// 必填，签名，见附录-JS-SDK使用权限签名算法
        jsApiList: ['chooseImage', 'previewFile']//必填，传入需要使用的接口名
    })
    window.wx.ready(function () {
        showNotify({ type: 'success', message: '认证成功' })
        fn(true)
    })
    window.wx.error(function () {
        alert('认证失败')
        fn(false)
    })
}
const buildParamsToForm = async () => {
    const data = await userStore.setUserInfoAsync();
    if (data) {
        let app = useQueryHashParams("app")
        if (!app)
            app = useQueryParams("app")
        if (app) {
            processStore.setHideTabHeader(true)
            if (app === 'process') {
                // 待办
                let pid = useQueryHashParams("pid")
                if (!pid)
                    pid = useQueryParams("pid")
                if (pid)
                    pid = String(pid)
                processStore.setStepId(pid)
                router.push({ path: '/layout/reform', replace: true })
            } else if (app === 'read' || app === 'openTask') {
                const appType = app == 'read' ? app : 'read'
                // 已办
                let tid = useQueryHashParams("tid")
                if (!tid)
                    tid = useQueryParams("tid")
                if (tid)
                    tid = String(tid)
                processStore.setTaskId(tid)
                processStore.setLoadType(appType) //原本app
                router.push({ path: '/layout/reform', replace: true })
            }
        } else {
            router.push({ path: '/layout/home', replace: true })
        }
    } else {
        showNotify({
            type: 'danger',
            message: '无法获取用户信息'
        })
    }
}
</script>
<style scoped></style>