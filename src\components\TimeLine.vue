<template>
    <van-step>
        <!-- 主要步骤 -->
        <div class="step-div">
            <div v-if="hdata.isEndStep">
                <p class="step-name">{{ hdata.NodeDisplayName }}</p>
                <p class="step-p" style="color:red"> {{ endString(hdata.TaskState) }}</p>
            </div>
            <div v-else>
                <p class="step-name">
                    <span style="color: red;" v-if="hdata.NodeDisplayName == 'SysTaskOpt'">
                        {{ hdata.SelActionDisplayString }}
                    </span>
                    <span v-else>
                        {{ hdata.NodeDisplayName }}
                    </span>
                </p>
                <p class="step-p">{{ $t("my.OutBegin") }}： <span class="step-span">{{ useTimeZHcn(hdata.ReceiveAt)
                        }}</span></p>
                <p class="step-p" :style="hdata.handlerColor"> {{ hdata.handlerText }}</p>
                <p class="step-p" style="color:red" v-if="hdata.otherRedText"> {{ hdata.otherRedText }}</p>
                <p class="step-p">{{ $t("Global.Opinions") }}： <span class="step-span" style="color: #ee7739;">{{
                    hdata.Comments }}</span></p>
                <p class="step-p">{{ $t("Global.HandleTime") }}： <span class="step-span" v-if="hdata.Finished">{{
                    useTimeZHcn(hdata.FinishAt) }}</span></p>
                <p class="step-p">{{ $t("Global.Status") }}:
                    <van-tag v-if="hdata.Finished" type="success">
                        <!-- 已处理 -->
                        {{ $t('Form.Processed') }}
                    </van-tag>
                    <van-tag v-else-if="!hdata.Finished" type="danger">
                        <!-- 处理中 -->
                        {{ $t('Form.Processing') }}
                    </van-tag>
                </p>
            </div>

            <!-- 知会 -->
            <div v-if="hdata.fllowSteps && hdata.fllowSteps.length > 0">
                <div class="child-div" v-for="zh in hdata.fllowSteps" :key="zh.StepID">
                    <b>{{ zh.RecipientDisplayName }}({{ zh.RecipientAccount }})</b>
                    <van-tag :type="zh.Finished ? 'success' : 'warning'">
                        <!-- true 已阅 false 待阅 -->
                        {{ zh.Finished ? $t('Work.Read') : $t('Work.NotifyWork') }}
                    </van-tag>
                    <p class="step-p" v-if="zh.Finished" style="margin: 0px;">
                        {{ $t('Work.HandleComments') }}:{{ zh.Comments }}
                    </p>
                </div>
            </div>
            <!-- 加签 -->
            <div v-if="hdata.consignSteps && hdata.consignSteps.length > 0">
                <div class="child-div" v-for="zh in hdata.consignSteps" :key="zh.StepID">
                    <b>{{ zh.RecipientDisplayName }}({{ zh.RecipientAccount }})</b>
                    <van-tag :type="zh.Finished ? 'success' : 'warning'">
                        {{ zh.Finished ? '加签已处理' : '正在加签...' }}
                    </van-tag>
                    <p class="step-p" v-if="zh.Finished" style="margin: 0px;">
                        {{ $t('Work.HandleComments') }}:{{ zh.Comments }}
                    </p>
                </div>
            </div>
        </div>
    </van-step>
</template>

<script lang="ts" setup>
import { useParamsFormatter, useTimeZHcn } from '@/utils';
import { ref, PropType } from 'vue';
const props = defineProps({
    hdata: {
        type: Object as PropType<any>,
        default: [],
        required: true
    }
})
const endString = (state: string) => {
    if (state === "Rejected") {
        return '任务已拒绝'
    } else if (state === "Aborted") {
        return "任务已撤销"
    } else if (state === "Approved") {
        return '任务已批准'
    }
}
</script>
<style lang="scss" scoped>
.fature-div {
    width: 95%;
    background: var(--yz-div-background);
    margin: 5px auto;
    height: calc(100vh - 128px);
    overflow-x: hidden;
    overflow-y: scroll;


}

.step-div {
    width: 100%;
    background: var(--yz-div-background);

    .step-name {
        font-family: PingFangSC-Medium;
        font-size: var(--yz-time-14);
        color: #333333;
        font-weight: 500;

        .action-step {
            font-weight: 600;
            color: #000;
            font-size: var(--yz-time-16);
        }
    }

    .step-p {
        font-family: PingFangSC-Regular;
        font-size: var(--yz-time-14);
        color: #353434;
        font-weight: 400;
        margin: 7px;

        .step-span {
            font-family: PingFangSC-Regular;
            font-size: var(--yz-time-14);
            color: #999999;
            font-weight: 400;
        }
    }

    .child-div {
        margin: 0px auto;
        border-radius: 5px;
        border: 1px solid #0cc160;
        line-height: 30px;
        padding-left: 5px;
        margin: 5px;
    }
}

.step-icon {
    width: 17px;
    height: 17px;
    background: #2AA84D;
    color: #fff;
    line-height: 17px;
    text-align: center;
    border-radius: 50%;
}
</style>