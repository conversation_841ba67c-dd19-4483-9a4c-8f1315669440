import { useLang } from "@/utils";
import { <PERSON><PERSON><PERSON><PERSON>fig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
export class Y<PERSON><PERSON>witchConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZSwitch'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZSwitchStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZSwitch extends Y<PERSON><PERSON>ield {
    private activeValue: any;
    private activeColor: string;
    private inactiveValue: any;
    private inActiveColor: string
    constructor(params: any) {
        super(params)
        this.vModel.value = params['__vModel__'].modelValue ? 1 : 0
        this.activeValue = params['active-value']
        this.activeColor = params['active-color']
        if (!this.activeColor)
            this.activeColor = '#1989fa'
        this.inactiveValue = params['inactive-value']
        this.inActiveColor = params['inactive-color']
        if (!this.inActiveColor)
            this.inActiveColor = 'rgba(120, 120, 128, 0.16)'
        // this.disabled = params.disabled
    }
    initConfig(config: any): YZConfig {
        return new YZSwitchConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZSwitchStyle(style)
    }
    getActiveValue(): any {
        return this.activeValue
    }
    getActiveColor(): string {
        return this.activeColor
    }
    getInActiveValue(): any {
        return this.inactiveValue
    }
    getInActiveColor(): string {
        return this.inActiveColor
    }
}
export interface ICheckboxOption {
    label: string;
    value: number
}
// export class YZSwitchOption extends YZOption<Array<ICheckboxOption>> {
//     constructor(options:any) {
//         super(options)
//     }
//     initOption(array: Array<any>): ICheckboxOption[] {
//         const options:ICheckboxOption[] =[]
//         if (array) {
//             array.forEach(({label,value}) => {
//                  options.push({
//                     label,
//                     value
//                  })
//             });
//         }
//         return options
//     }
// }