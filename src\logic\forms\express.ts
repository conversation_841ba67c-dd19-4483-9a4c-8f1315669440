import { isArray } from "@/utils";
import { formBuilder } from "./formBuilder"
import { YZField } from "./formModel";
import { formpt } from "./formp";
var v: any = {}
// 特殊符号
export class parseExpress {
   static ExecuteExpress(express: string, newValues: Array<any>, vars: Array<any>) {
      var rv
      for (let i = 0; i < vars.length; i++) {
         let $var = vars[i];
         let value = newValues[i];
         if (isArray(value)) {
            for (let j = 0; j < value.length; j++) {
               value[j] = value[j] === undefined || value[j] === null ? '' : value[j];
            }
         }
         else {
            if (value === undefined || value === null)
               value = '';
         }

         v[$var] = value;
      }
      const ptpase = new formpt(express)
      rv = ptpase.evaluate(v);
      if (isArray(rv)) //express指向明细表时返回值为数组
         rv = rv.toString();
      return rv;
   }
   static getFileds(): YZField[] {
      return formBuilder.formConfig?.getFields()!
   }
   static getField(model: string): YZField | undefined {
      return this.getFileds().find(x => x.vModel.model === model)
   }
   static getFiledsByFid(fid:string): YZField[] {
      return formBuilder.formConfig?.getFieldsRecord()!
   }
   static getFieldByFid(fid:string,model: string): YZField | undefined {
      if(model.includes('.')){
         return this.getFiledsByFid(fid).find(x => x.table === model)
      }else return this.getFiledsByFid(fid).find(x => x.vModel.model === model && x.table == '')
   }
   // 用于子表单和历史表单查看的删除
   // static delFieldsRecordByTid(fid:string){
   //    return formBuilder.formConfig?.delFieldsRecordByTid(fid)!
   // }
   static delFieldsRecordArrByLast(){
      return formBuilder.formConfig?.delFieldsRecordArrByLast()
   }
}