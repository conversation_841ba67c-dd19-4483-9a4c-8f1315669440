<template>
    <div>
        <van-field  v-model="vModel" :name="field.vModel.model"
            label="动态组件" required
             :rules="[{ required: true, message: `请输入  ` }]">
        </van-field>
    </div>
</template>

<script>
export default {
    props: {
        field: {
            required: true,
        },
        modelValue: {
            default: ''
        },
        index: {
            default: -1
        }
    },
    computed:{
        vModel: {
           get(){
              return  this.modelValue
           },
           set(val) {
             this.$emit('update:modelValue', val)
           }
        }
    },
    setup(props) {
        console.log('当前控件实例',props.field)
        console.log('当前默认数据',props.modelValue)
    }
}
</script>
<style  scoped></style>