<template>
    <div>
        <van-field v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required" is-link
            v-model="vModel" :placeholder="field.placeholder" :rules="field.config.rules" @click="onMuliteUserShow"
            :readonly="true" :label="field.config.label">
        </van-field>
        <YZUserSelect v-model:show="userShow" @onSave="onSave" :multiple="true" :default-users="defaultUsers" />
    </div>
</template>

<script lang="ts" setup>
import { YZUsers, YZUsersConfig } from '@/logic/forms/YZUsers';
import { computed, onMounted, PropType, ref } from 'vue';
import YZUserSelect, { ISelectUser } from '@/components/YZUserSelect.vue';
import { eventBus } from '@/utils/eventBus';
import { onUnmounted } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZUsers>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        index: -1
    }
})
const emit = defineEmits(['update:optionValue', 'update:modelValue'])
const config = props.field.config as YZUsersConfig
const isFirstLoad = ref<boolean>(true)
const defaultUsers = ref<Array<ISelectUser>>([])
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.optionValue
    },
    set(val) {
        emit('update:optionValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const userShow = ref<boolean>(false)
const onSave = (user: ISelectUser[]) => {
    defaultUsers.value = []
    if (user && user.length > 0) {
        vModel.value = user.map(x => x.name).join(',')
        const value = user.map(x => x.account).join(',')
        emit('update:modelValue', value)
    }else {
        vModel.value =''
        emit('update:modelValue', '')
    }
    setFun()
}
eventBus.on('setMultipleUser', {
    cb: function (params) {
        if (params['uuid'] === props.field.uuid) {
            const values = params.data.map((x: any) => x.text).join(',')
            const uids = params.data.map((x: any) => x.value).join(',')
            props.field.vModel.value = uids
            props.field.vModel.optionValue = values
            setFun()
        }
    }
})
onUnmounted(() => {
    eventBus.off('setMultipleUser')
})
const onMuliteUserShow = () => {
    if (!props.field.disabled) {
        defaultUsers.value = []
        if (props.modelValue && props.optionValue) {
            const names = props.modelValue.split(',')
            const accounts = props.optionValue.split(',')
            for (let i = 0; i < names.length; i++) {
                defaultUsers.value.push({
                    name: names[i],
                    account: accounts[i]
                })
            }
        }
        userShow.value = true
    }
   
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
             if(params.value ||  params ===1) {
                // 禁用验证
                props.field.config.required =false 
                props.field.config.rules = []
             } else {
                // 启用验证
                props.field.config.required = true 
                props.field.config.rules = config.getDefaultRule()     
             }
        }
    }
})
</script>
<style  scoped></style>