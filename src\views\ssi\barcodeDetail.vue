<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">{{ $t('SSI.InspectionHistoryDetail') }}</div>
        </div>

        <div class="grap"></div>

        <div class="list">
            <div class="li">
                <div class="li-name">{{ $t('SSI.InspectionType') }}：</div>
                <div class="li-info">{{ item?.inspectionType ? $t('SSI.' + item?.inspectionType) :
                    item?.inspectionType }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('SSI.InspectionPlanDate') }}：</div>
                <div class="li-info">{{ item?.InspectionPlanDate ? new Intl.DateTimeFormat(navigator?.language,
                    options).format(new Date(item?.InspectionPlanDate + 'Z')) : '' }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('SSI.InspectionDueDate') }}：</div>
                <div class="li-info">{{ item?.InspectionDueDate ? new Intl.DateTimeFormat(navigator?.language,
                    options).format(new Date(item?.InspectionDueDate + 'Z')) : '' }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('SSI.InspectionDate') }}：</div>
                <div class="li-info">{{ item?.inspectionDate && new Intl.DateTimeFormat(navigator?.language,
                    options_).format(Date.parse(item?.inspectionDate + 'Z')) }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('SSI.EvaluationResult') }}：</div>
                <div class="li-info">{{ item?.result ? $t('SSI.' + item?.result) : item?.result
                }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('SSI.Inspector') }}：</div>
                <div class="li-info">{{ item?.Inspector }}</div>
            </div>


        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const item = ref()
const options = ref({
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    timeZoneName: "short"
})
const options_ = ref({
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
})
const comeBack = () => {
    router.push({
        path: '/ScanBarcode',
        query: {
            qs_title: 'SSI.ScanBarcode',
            type: 'in',
            symbol_: 'in',
            equipmentId: route.query?.equipmentId,
            equipmentType: route.query?.equipmentType,
            serialNo: route.query?.serialNo,
            barcodeNo_: route.query?.barcodeNo_,
        },
        replace: true
    })
}
onMounted(() => {
    item.value = JSON.parse(route.query.i)
    console.log("%c [ item.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item.value)
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 0.9;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .grap {
        width: 100%;
        height: 10px;
        background-color: #f9f9f9;
    }

    .list {
        padding: 10px 12px;

        .li {
            font-size: var(--yz-com-14);
            margin-bottom: 16px;
            display: flex;
            align-items: center;

            .li-name {
                color: #999999;
                width: 185px;
            }

            .li-info {
                color: #333333;
                flex: 1;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>