<template>
    <div>
    </div>
</template>

<script lang="ts" setup>
import * as dd from 'dingtalk-jsapi'; // 此方式为整体加载，也可按需进行加载
import { url } from '@/api/url';
import { IUserInfo } from '@/logic/login/loginModel';
import { useLoingStore } from '@/store/login';
import { useUserStore } from '@/store/users';
import { useParamsFormatter, useQueryHashParams, useQueryParams } from '@/utils';
import { useGetQuery, usePostBody } from '@/utils/request';
import { showLoadingToast, showNotify } from 'vant';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useProcessStore } from '@/store/process';
const processStore = useProcessStore()
const loginStore = useLoingStore()
const userStore = useUserStore()
const router = useRouter()
// let loading: any = {}
onMounted(async () => {
    const loading = showLoadingToast({
        message: '正在进行身份认证...',
        forbidClick: true,
        duration: 0
    });
    if (loginStore.getToken) {
        console.log('当前获取到身份，准备构建单点')
        loading.close()
        buildParamsToForm()
        buidlSignature()
        .then(auth => {
            if (auth) {
                buildDingTalkSdk(auth, function (val: boolean) {})
                loading.close()
            }else {
                loading.close()
            }
        }).catch(error => {
            loading.close()
        })
        loading.close()
    } else {
        console.log('当前无任何身份',window.h5sdk)
        window.h5sdk.ready(() => {
            window.tt.requestAuthCode({
                appId: window.webConfig.feishu.appid,
                success: (info: any) => {
                    console.log('授权码',info)
                    const newUrl = useParamsFormatter(url.login.getFeiShuUser, {
                        params1: info.code
                    })
                    console.log('获取用户信息',newUrl)
                    useGetQuery(newUrl)
                        .then(result => {
                            if (result.success) {
                                console.log('用户信息获取成功',result)
                                console.log('用户信息获取成功loading',loading)
                                buidlSignature()
                                    .then(auth => {
                                        console.log('构建签名成功',loading)
                                        if (auth) {
                                            buildDingTalkSdk(auth, function (val: boolean) {
                                                if (val) {
                                                    loginStore.ssoLogin(result.auth)
                                                }
                                            })
                                            console.log('飞书内部走完？',loading)
                                            loading.close()
                                        }else {
                                            loading.close()
                                        }
                                    }).catch(error => {
                                        alert('服务端验证签名失败:' + JSON.stringify(error))
                                        loading.close()
                                    })
                                    loading.close()

                            } else {
                                showNotify({ type: 'danger', message: '平台认证失败: ' + result.errorMessage })
                                loading.close()
                            }
                        })
                    console.info(info.code,'infocode')

                    loading.close()
                },
                fail: (error: any) => {
                    console.error(error)

                    loading.close()
                }
            });
        });

    }

})

const buidlSignature = async () => {
    const urls = window.location.href.split('#')[0]
    const { success, timestamp, signature, nonceStr, errorMessage } = await useGetQuery(url.login.getFeiShuSdk, {
        url: urls
    }, false)
    if (!success) {
        showNotify({ type: 'danger', message: errorMessage });
        return null
    }
    return {
        timestamp,
        signature,
        nonceStr
    }
}
const buildDingTalkSdk = ({ timestamp, signature, nonceStr }: any, fn: Function): void => {
    console.log('fs',window.webConfig.feishu.appid)
    window.h5sdk.config({
        appId: window.webConfig.feishu.appid,
        timestamp: timestamp,
        nonceStr: nonceStr,
        signature: signature,
        jsApiList: [],
        //鉴权成功回调
        onSuccess: (res: any) => {
            fn(true)
        },
        //鉴权失败回调
        onFail: (err: any) => {
            alert('失败: ' + JSON.stringify(err))
            fn(false)
        },
    });
}
const buildParamsToForm = async () => {
    const data = await userStore.setUserInfoAsync();
    if (data) {
        const app = useQueryParams("app")
        console.log('apaps',app)
        if (app) {
            processStore.setHideTabHeader(true)
            if (app === 'process') {
                // 待办
                const pid = String(useQueryParams("pid"))
                processStore.setStepId(pid)
                router.push({ path: '/layout/reform', replace: true })
            } else if (app === 'read' || app === 'openTask') {
                const appType = app == 'read' ? app :'read'
                // 已办
                const tid = String(useQueryParams("tid"))
                processStore.setTaskId(tid)
                processStore.setLoadType(appType) //原本app
                router.push({ path: '/layout/reform', replace: true })
            }
        } else {
            router.push({ path: '/layout/home', replace: true })
        }
    } else {
        showNotify({
            type: 'danger',
            message: '无法获取用户信息'
        })
    }

}
</script>
<style  scoped></style>