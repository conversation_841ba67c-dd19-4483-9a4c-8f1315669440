
<template>
    <div>
        <van-field v-model="consign.users" readonly right-icon="search" label="加签人员" @click="jqUserClick" />
        <van-field readonly label="加签顺序">
            <template #input>
                <van-radio-group direction="horizontal" v-model="consign.jxsx">
                    <van-radio name="Parallel">平行签核</van-radio>
                    <van-radio name="Serial">顺序签核</van-radio>
                </van-radio-group>
            </template>
        </van-field>
        <van-field v-model="consign.users" readonly label="加签后">
            <template #input>
                <van-radio-group direction="horizontal" v-model="consign.jqh">
                    <van-radio name="Return">回到本关卡</van-radio>
                    <van-radio name="Forward">进入下一关卡</van-radio>
                </van-radio-group>
            </template>
        </van-field>
        <YZUserSelect v-model:show="userShow" :multiple="true" @on-save="jqUserSave" :default-users="defaultUsers" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import YZUserSelect, { ISelectUser } from '@/components/YZUserSelect.vue';
import { reactive } from 'vue';
import { useHomeStore } from '@/store/home';
import { useProcessStore } from '@/store/process';
import { watch } from 'vue';
const consign = reactive({
    show: false,
    users: '',
    userArrys: [],
    jxsx: 'Parallel',
    jqh: 'Return',
    message: ''
})
const userShow = ref<boolean>(false)
const homeStore = useHomeStore()
const processStore = useProcessStore()
const defaultUsers = ref<Array<ISelectUser>>([])
const jqUserClick = () => {
    userShow.value = true
    if (consign.users && consign.userArrys.length > 0) {
        const users = consign.users.split(',')
        for (let i = 0; i < users.length; i++) {
            defaultUsers.value.push({
                name: users[i],
                account: consign.userArrys[i]
            })
        }
    }
    homeStore.setHead(false)
}
const jqUserSave = (users: any) => {
    consign.users = ''
    consign.userArrys = []
    if (users && users.length > 0) {
        homeStore.setHead(true)
        consign.userArrys = users.map((x: any) => x.account)
        consign.users = users.map((x: any) => x.name).join(',')
        userShow.value = false
    }
}
watch(consign, (newValue, oldValue) => {
    processStore.setConsign(newValue)
},
    {
        immediate: true
    })

</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    height: 36vh;
}
</style>