import { useProcessStore } from "@/store/process";
import { Y<PERSON><PERSON>onfig, Y<PERSON><PERSON>ield, YZStyle } from "./formModel";
import { useParamsFormatter } from "@/utils";
import { useGetQuery } from "@/utils/request";
import { eventBus } from "@/utils/eventBus";
import { url } from "@/api/url";
import { useLang } from "@/utils";
const store = useProcessStore()
export class YZInitiatorConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZOwner'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class Y<PERSON><PERSON>nitiatorStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZOwner extends YZField {
    constructor(params: any) {
        super(params)

        this.vModel.value = params['__vModel__'].modelValue
        const info = store.getPostInfoData
        if (info) {
            const newUrl = useParamsFormatter(url.process.getInitUser, {
                params1: info.initiatorAccount ?? info.processInstance.initiatorAccount
            })
            useGetQuery(newUrl)
                .then(result => {
                    this.vModel.optionValue = result.account
                    eventBus.emit('setOwnerUser', false, result.name ?? result.shortName)
                }).catch(error => {
                    console.error('初始化用户失败', error)
                })
        }
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZInitiatorConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZInitiatorStyle(style)
    }
}