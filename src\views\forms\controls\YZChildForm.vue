<template>
  <div v-if="!field.hidden" v-show="!field.hidden">
    <van-field :name="field.vModel.model" :required="field.config.required" :readonly="field.readonly"
      :disabled="field.disabled" :rules="field.config.rules" :label="field.config.label" v-model="vModel">
      <template #input>
        <div class="history-form-link">
          <!-- 按钮模式 -->
          <van-button :disabled="field.disabled" v-if="config.fieldControlMode == 'button'" @click="openForm()"
            type="default" size="small">{{ config.fieldDisplayText }}</van-button>
          <!-- 文字链接模式 -->
          <span v-if="config.fieldControlMode == 'hyperlink'" @click="openForm()">{{ config.fieldDisplayText }}</span>
        </div>
      </template>
    </van-field>
    <!-- {{ config.extends.formServiceOpenMode }}
    {{ popType[config.extends.formServiceOpenMode] }} -->
    <van-popup v-model:show="procesShow" teleport="#app" :style="{
      height: config.extends.popupWndHeight,
      // width:config.extends.popupWndWidth
    }" position="bottom">
      <ChildForm :key="time" ref="reformReadRef" :title="formService.title" :formService="formService" @onBack="onBack"
        :noShowHeader="true" />
    </van-popup>

    <!-- <van-dialog v-model:show="showDialog" title="标题" show-cancel-button>
      <img src="https://fastly.jsdelivr.net/npm/@vant/assets/apple-3.jpeg" />
    </van-dialog> -->
  </div>
</template>

<script lang="ts" setup>
import ChildForm from "@/components/ChildForm.vue";
import { YZChildForm, YZChildFormConfig } from "@/logic/forms/YZChildForm";
import { eventBus } from "@/utils/eventBus";
import {
  computed,
  onMounted,
  PropType,
  ref,
  watch,
  nextTick,
  reactive,
} from "vue";
import { url } from "@/api/url";
import { useParamsFormatter } from "@/utils";
import { useGetQuery, usePostBody } from "@/utils/request";
import { useRouter } from "vue-router";
//   import ReformRead from "@/components/ReformRead.vue";
import { useProcessStore } from "@/store/process";
import { jsx } from "vue/jsx-runtime";
import { showNotify } from "vant";
import { formBuilder } from "@/logic/forms/formBuilder";
import { YZField } from "@/logic/forms/formModel";
import { useCore } from '@/store/core';
// import { parseExpress } from "@/logic/forms/express";
const processStore = useProcessStore();
const props = defineProps({
  field: {
    type: Object as PropType<YZChildForm>,
    required: true,
  },
  modelValue: {
    type: [String, Number],
    default: 0,
  },
  index: {
    type: Number,
    defualt: -1,
  },
});
const testShow = ref<boolean>(false);
const time = ref<number>(0);
const router = useRouter();
const procesShow = ref<boolean>(false);
const showDialog = ref<boolean>(false)
const core = useCore()
const formService = reactive({
  formId: '',
  appId: '',
  title: '',
  formState: '',
  loadType: "",
  showSaveButton: false,
  viewModel: {},
  map: {},
  formKey: '',
  uuid: ''
});
const fields = ref<any>([])
const reformReadRef = ref<any>(null);
const config = props.field.config as YZChildFormConfig;
const childFormConfig: any = config.getChildConfig()
// const formServiceOpenMode = config.extends.formServiceOpenMode
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
const vModel = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})
const setFun = () => {
  props.field.expressTest(props.field, props.index, isFirstLoad.value)
  props.field.disableTest(props.field, props.index, isFirstLoad.value)
  props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
  props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
onMounted(() => {
  isFirstLoad.value = false
})
// 打开表单
const openForm = async () => {
  const data = await childFormData()
  if (data.success) {
    time.value = new Date().getTime();
    formService.formId = data.app.formId
    formService.appId = data.app.appId
    formService.showSaveButton = data.app.showSaveButton
    formService.viewModel = data.app.viewModel
    formService.title = `${childFormConfig.formService.fmname} ${data.formState}`
    formService.formState = data.formState
    formService.map = childFormConfig.$map ?? null
    formService.formKey = data.formKey
    formService.uuid = props.field.uuid
    procesShow.value = true

  }
};
const getValue = (field:any) => {
  if(field.config.ctype == 'YZSelect'){
    return field.vModel.optionValue
  }else if(field.config.ctype == 'YZImageUpload' || field.config.ctype == 'YZUploader'){
    return field.vModel.optionValue
  }
  else{
    return field.vModel.value
  }
}
const childFormData = async () => {
  const loadInfo = processStore.getProcLoad
  let formState: string = ''
  let formKey: string | number = vModel.value || -1
  let formStateNeedBring = false
  const field = props.field
  if (field.disabled && (!formKey || formKey == -1)) return
  const {
    formServiceDataBind,
    formServiceNewState,
    formServiceReadState,
    formServiceEditState,
    formService,
    $bring,
    formServiceState
  } = childFormConfig
  if (formServiceDataBind) {
    if (!vModel.value) {
      formState = formServiceNewState;
      formKey = -1
      formStateNeedBring = true
    }
    else if (loadInfo.loadType == 'read' || field.disabled) {
      formState = formServiceReadState;
      formKey = vModel.value
    }
    else {
      formState = formServiceEditState;
      formKey = vModel.value
    }
  } else {
    formStateNeedBring = true;
    formState = formServiceState
  }
  if (!formState) {
    showNotify({ message: '控件没有绑定表单服务' });
    return false
  }
  const newUrl = useParamsFormatter(url.form.getChildFormData, {
    params1: formService.fmid,
    params2: formKey,
    params3: encodeURI(formState)
  });
  const data = await useGetQuery(newUrl, {}, false);
  if (data.code === 1) {
    showNotify({ message: data.message });
    return;
  }
  // 带入数据
  if ($bring && $bring.mapping && formStateNeedBring) {
    const fields = formBuilder.formConfig?.getFieldsRecord()
    let bringObj = $bring.mapping
    // 数据信息
    let viewModel: any = props.field.viewModel
    // 带出字段键值数组
    let bringObjKarr: any = Object.keys(bringObj)
    // 存在带出
    if (bringObj && bringObjKarr.length > 0) {
      // if(viewModel)
      bringObjKarr.forEach((item: any) => {
        // 存在当前key的字段？？？ 
        if (viewModel && viewModel.schema[item]) {
          // 获取对应的值
          let objV = bringObj[item]
          if (viewModel.schema[item].columns) {
            // 是主表？？
            if (item == '$$root') {
              let objVKeyArr = Object.keys(objV)
              objVKeyArr.forEach((om) => {
                if (om == '$$root') {
                  Object.keys(objV[om]).forEach(ov => {
                    let curField:any = fields?.find((el: any) => (el.vModel.model == objV[om][ov] && !el.table && !el.tableName)) 
                    data.app.viewModel.data[ov] = getValue(curField) //curField.vModel.value
                  })
                }else {
                  Object.keys(objV[om]).forEach(ov => {
                    let curField:any = fields?.find((el: any) => (el.vModel.model == objV[om][ov] && !el.table && !el.tableName)) 
                    if(curField){
                      let obj: any = {}
                        Object.keys(data.app.viewModel.schema[om].columns).forEach((tc: any) => {
                          if (tc !== 'ItemId' && tc !== 'ParentItemId' && tc != 'TaskID') {
                            obj[tc] = data.app.viewModel.schema[om].columns[tc].defaultValue || ''

                          }
                        })
                        obj[ov] = getValue(curField) 
                        data.app.viewModel.data[om].push(obj)
                    }
                    })
                }
              })
            } else {
              let objVKeyArr: any = Object.keys(objV)
              // 找到table
              let tableField = fields?.find((el: any) => (el.vModel.model == item))
              if (tableField) {
                objVKeyArr.forEach((vk: any) => {
                  if (vk == '$$root') {
                    Object.keys(objV[vk]).forEach(ov => {
                      let tableCols = tableField.vModel.value[props.field.pindex >= 0 ? props.field.pindex : 0].colums
                      let rootField = tableCols?.find((el: any) => (el.field.vModel.model == objV[vk][ov])) || ''
                      data.app.viewModel.data[ov] = getValue(rootField.field) //rootField.field.vModel.value
                    })

                    fields?.find((el: any) => (el.vModel.model == item))
                  } else {
                    // 外面表单的col
                    let tableCols = tableField.vModel.value[props.field.pindex >= 0 ? props.field.pindex : 0].colums
                    Object.keys(objV[vk]).forEach(ov => {
                      let colField = tableCols.find((el: any) => (el.field.vModel.model == objV[vk][ov])) || ''
                      if (colField) {
                        // 当前下标为空的时候
                        let obj: any = {}
                        Object.keys(data.app.viewModel.schema[vk].columns).forEach((tc: any) => {
                          if (tc !== 'ItemId' && tc !== 'ParentItemId' && tc != 'TaskID') {
                            obj[tc] = data.app.viewModel.schema[vk].columns[tc].defaultValue || ''

                          }
                        })
                        obj[ov] = getValue(colField.field) //colField.field.vModel.value
                        data.app.viewModel.data[vk].push(obj)
                      }
                    })
                  }
                })
              }
            }
          }

        }

      })
    }

  }
  data.formKey = formKey
  data.formState = formState
  // debugger
  // const test = useParamsFormatter(url.form.childFormSubmit, {
  //   params1: '498347951382597',
  //   params2:'519538546663493',
  //   params3:'%E4%BF%AE%E6%94%B9'
  // });
  // const testData = await usePostBody(test,{}, {
  //   header: {
  //     formId: "498347130638405",
  //     appId: 498347951382597,
  //     formState: "修改"
  //   },
  //   "formData": {
  //     "设备编号": "1",
  //     "TaskID": "519538546663493",
  //     "ItemId": "522064905392197",
  //     "设备名称": "2",
  //     "设备品牌": "",
  //     "设备购入日期": null,
  //     "设备说明书": "",
  //     "图片1": ""
  //   }
  // });
  return data;




}
const onBack = (params: any) => {
  procesShow.value = false
  let formId = props.field.targetFormId
  if (params) {
    vModel.value = params.Key
    const { map } = formService as any
    setFun()
    if (map) {
      const fields = core.getFieldsRecord()
      Object.keys(map).forEach(mapKey => {
        let value = params[mapKey]
        let fieldName = map[mapKey]
        if (fieldName.indexOf(".") > -1 || fieldName.indexOf("子表") > -1 || fieldName.split('.').length === 2) {
          let fex = fieldName.split('.')[0] //主表名
          let fieldModel = fieldName.split('.')[1] //字段名
          let tableFields = fields.find(x => x.vModel.model === fex) //字表字段
          const fieldValue = tableFields?.vModel.value //子表每列
          if (fieldValue && fieldValue.length > 0) {
            fieldValue.forEach((x: any) => {
              x.colums.forEach((e: any) => {
                if (e.field.vModel.model == fieldModel) {
                  e.field.vModel.value = value
                }
              })
            })
          }
        } else {
          let field = fields.find(item => item.vModel && item.vModel.model === fieldName)
          if (field) {
            field.vModel.value = value
          }
        }
      })
    }
  }
}
eventBus.on("setFieldValue", {
  cb: function (params) {
    if (params.uuid === props.field.uuid &&
    props.field.vModel.model === params.model && params.index === props.field.pindex) {
      props.field.vModel.value = String(params.value);
      // getHistoryFormInfo()
    }
  },
});
eventBus.on("setFieldDisable", {
  cb: function (params) {
    if (params.uuid === props.field.uuid && params.index === props.field.pindex) {
      props.field.disabled = params.value;
    }
  },
});
eventBus.on("setFieldHidden", {
  cb: function (params) {
    if (params.uuid === props.field.uuid && params.index === props.field.pindex) {
      props.field.hidden = params.value;
    }
  },
});
// 监听数据MAP
eventBus.on("onMap", {
  cb: function (params) {
    if (
      props.field.vModel.model === params.model &&
      props.field.uuid === params.uuid && params.index === props.field.pindex
    ) {
      props.field.vModel.value = params.value;
      // getHistoryFormInfo()
    }
  },
});
</script>
<style lang="scss" scoped>
.history-form-link {
  width: 100%;
  color: #409eff;
}
</style>