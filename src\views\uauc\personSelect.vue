<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div class="btn">
                <van-icon name="filter-o" size="20" @click="rightShow" />
                <van-popup v-model:show="isShow" position="right" :style="{ width: '82%', height: '100%' }">
                    <div>
                        <div class="li_">
                            <span class="name">User Account</span>
                            <van-field v-model="UserAccount" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">User Name</span>
                            <van-field v-model="UserName" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">Role Name</span>
                            <van-field v-model="RoleName" clearable />
                        </div>
                    </div>
                    <div
                        style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
                        <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset')
                        }}</van-button>
                        <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm1">{{
                            $t('Global.Confirm')
                            }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="list">
            <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                :loosing-text="$t('Form.Release_to_refresh')" :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loadData.loading" :finished="loadData.finished"
                    :finished-text="$t('Form.NoMore')" :loading-text="$t('Form.Loading')" @load="onLoad">
                    <van-checkbox-group v-model="checked">
                        <div class="li" v-for="(item, index) in persons" :key="index" @click="toggle(index)">
                            <div class="li-c">
                                <van-checkbox :name="item" :ref="el => checkboxRefs[index] = el" @click.stop />
                            </div>
                            <div class="li-l">
                                <div class="li-n ellipsis"><span class="name">User Account</span>{{ item.UserAccount }}
                                </div>
                                <div class="li-m ellipsis"><span class="name">User Name</span>{{ item.UserName }}</div>
                                <div class="li-m ellipsis"><span class="name">Role Name</span>{{ item.RoleEnName }}
                                </div>
                            </div>
                            <!-- <div class="li-r" @click="toPersonDetail(item)">
                                <van-icon name="arrow" size="20" />
                            </div> -->
                        </div>
                    </van-checkbox-group>
                </van-list>
            </van-pull-refresh>
        </div>
        <div class="foot">
            <van-button class="subbtn-text" color='#00A0E1' block type="primary" native-type="submit" @click="confirm"
                :disabled="checked.length > 0 ? false : true">
                {{ $t('Global.Confirm') }} <span v-show="checked.length > 0">( {{ checked.length || '' }} )</span>
            </van-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed, ref, reactive, onBeforeUpdate, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useFormData } from '@/utils'
import { usePostBody } from '@/utils/request'
import { useUserStore } from "@/store/users"
import { HSE } from '@/store/hse'

const store = HSE()
const router = useRouter()
const route = useRoute()
const user = useUserStore()

const searchValue = ref(' ')
const checked = ref([])
const checkboxRefs = ref([])
const persons = ref([])
const isShow = ref(false)
const UserName = ref('')
const UserAccount = ref('')
const RoleName = ref('')

const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    // pageSize: config.getDataBoweswer()?.pageSize || 10
    pageSize: 20
})
const toggle = (index) => {
    checkboxRefs.value[index]?.toggle();
}
onBeforeUpdate(() => {
    checkboxRefs.value = [];
})
const onSearch = () => {
    loadData.pageIndex = 1
    onLoad()
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const rest = () => {
    UserAccount.value = ''
    UserName.value = ''
    RoleName.value = ''
}
const searchParams = computed(() => {
    return [
        { name: 'UserAccount', op: 'like', value: UserAccount.value, isAll: false },
        { name: 'UserName', op: 'like', value: UserName.value, isAll: false },
        { name: 'RoleName', op: 'like', value: RoleName.value, isAll: false }
    ].filter(param => param.value)
})
const search_ = ref([])
const confirm1 = () => {
    searchValue.value = ''
    search_.value = searchParams.value;
    loadData.pageIndex = 1
    isShow.value = false
    onLoad()
}
const comeBack = () => {
    router.push({
        path: '/uauc',
        replace: true
    })
}
const onRefresh = () => {
    searchValue.value = ' ' // 清空搜索框
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    persons.value = []
    rest()
    onLoad()
}
const onLoad = async () => {
    const ff = window.btoa(JSON.stringify({ "ProjectNumber": { "op": "=", "value": route.query?.ProjectNo }, "SupplierCode": { "op": "=", "value": route.query?.SupplierCode }, "SubjectType": { "op": "=", "value": "ResponsiblePersonName" } }))
    const cc = window.btoa(JSON.stringify(["UserAccount", "UserName", "RoleEnName"]))
    if (loadData.refreshing) {
        loadData.refreshing = false
        persons.value = []
    }
    const search = [{ "name": "all", "op": "like", "value": searchValue.value !== ' ' ? searchValue.value.trim() : ' ', "isAll": true }]
    const formdata = useFormData({
        o: '',
        start: (loadData.pageIndex - 1) * loadData.pageSize,
        page: loadData.pageIndex,
        limit: loadData.pageSize,
        f: ff,
        c: cc,
        lc: cc, // 查询列 同上
        s: btoa(window.unescape(encodeURIComponent(JSON.stringify(searchValue.value ? search : search_.value))))
    })
    console.log("%c [ personSelect searchValue.value ? search : search_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value ? search : search_.value)
    try {
        const res = await usePostBody('bpm/datasource/***************/table/VW_UAUC_ProjectSupplier/paging', {}, formdata) //项目名称，项目编号的接口
        console.log("%c [ personSelect res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res)
        if (res.success) {
            persons.value = res.children || []
            loadData.pageIndex++
            loadData.loading = false
            if (res.children.length < loadData.pageSize) {
                loadData.finished = true
                console.log("personSlectUAUC --- 停止刷新")
            }
            rest()
        }
    } catch (error) {
        console.log("UAUC --- personSlect --- error", error)
    }
}

const confirm = () => {
    store.ProblemOwner = checked.value
    // console.log("%c [ checked.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", checked.value)
    router.push({ path: '/uauc', replace: true })
}
const toPersonDetail = ({ UserAccount, UserName, RoleName }) => {
    const data = {
        UserAccount,
        UserName,
        RoleName
    }
    router.push({
        path: '/personDetail',
        query: {
            persons: JSON.stringify(data)
        },
        replace: true
    })
}
// const checkedPersons = ref([])

onMounted(() => {
    // checkboxRefs.value[2]?.toggle();
    // if (checkboxRefs.value.length) {
    //     checkboxRefs.value[2].checked.value = true
    //     console.log("%c [onMounted  checkboxRefs.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", checkboxRefs.value)
    // }

    const Persons_ = route.query.Persons_ && JSON.parse(route.query?.Persons_)
    // console.log("%c [ 1 checked.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", checked.value)
})

</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #f9f9f9;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;

            .li_ {
                padding: 20px 13px 0px 13px;

                .van-field {
                    border-radius: 13px;
                    padding: 3px;
                    margin-top: 10px;
                    background: #f8f8f8;
                }

                .name {
                    color: #999999;

                }
            }
        }
    }

    .list {
        margin-top: 10px;
        margin-bottom: 80px;
        width: 100%;
        height: 100vh;

        .li {
            border-bottom: 1px solid #DCDFE6;
            background-color: var(--yz-div-background);
            padding: 10px 12px;
            display: flex;
            align-items: center;

            .li-c {
                width: 36px;
            }

            .li-l {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #444444;

                .name {
                    color: #999999;
                    margin-right: 10px;
                }

                .li-m {
                    margin-top: 10px;
                }
            }

            .li-r {
                width: 20px;
            }
        }
    }

    .foot {
        width: 100%;
        box-sizing: border-box;
        padding: 16px;
        position: fixed;
        bottom: 0;
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>