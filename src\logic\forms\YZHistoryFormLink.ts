import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
export class YZHistoryFormLinkConfig extends YZConfig {
    private fieldEmptyText:string
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZHistoryFormLink'
        this.showLabel = parmas['showlable']
        this.fieldEmptyText = parmas['fieldEmptyText']
        
    }
    getFieldEmptyText(){
        return this.fieldEmptyText
    }
}
export class YZHistoryFormLinkStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
        }
    }
}
export class YZHistoryFormLink extends YZField {
    constructor(params: any) {
        super(params)
        const modelValue = params['__vModel__'].modelValue
        this.vModel.value = modelValue
        this.vModel.optionValue = ''
    }
    initConfig(config: any): YZConfig {
        return new YZHistoryFormLinkConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZHistoryFormLinkStyle(style)
    }
}