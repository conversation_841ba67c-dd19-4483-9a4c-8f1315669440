import { useLang } from "@/utils";
import { YZ<PERSON>onfig, Y<PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
export class YZDataViewConfig extends YZConfig {
    private defaultRules: Array<any>;
    public ds: any;
    private enablePaging: boolean;
    private gridSettingColumns: Array<any>;
    private pageSize: number;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZDataView'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.ds = parmas.ds
        this.enablePaging = parmas.enablePaging
        this.gridSettingColumns = parmas.gridSettingColumns
        this.pageSize = parmas.pageSize
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
    getDataViewData() {
        return {
            ds: this.ds,
            enablePaging: this.enablePaging,
            gridSettingColumns: this.gridSettingColumns,
            pageSize: this.pageSize
        }
    }
}
export class YZDataViewStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
        }
    }
}
export class YZDataView extends YZField {
    constructor(params: any) {
        super(params)
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZDataViewConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {
        return new YZDataViewStyle(style)
    }
}