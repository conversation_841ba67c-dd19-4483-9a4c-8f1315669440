<template>
  <div v-if="showPage"> <!-- v-if="showPage" we -->
    <div>
      <div class="login-user" style="text-align: center;">
        <div v-if="loginLogoDom" v-html="loginLogoDom" style="width: 100%;"></div>
        <img v-else src="@/assets/imgs/logo.jpg" style="width: 8rem;" alt="" />
      </div>
      <div class="form-div">
        <van-form @submit="onSubmit">
          <van-field v-model="formData.userName" :label="$t('login.username')"
            :placeholder="$t('login.Enter_name_tips')" :rules="[{
              required: true,
              trigger: 'onSubmit',
              message: $t('login.Enter_name_tips')
            }
            ]" />
          <van-field v-model="formData.userPass" type="password" :label="$t('login.password')"
            :placeholder="$t('login.Enter_password_tips')" autocomplete="on"
            :rules="[{ required: true, message: $t('login.Enter_password_tips') }]" />
          <div style="margin: 16px">
            <van-button block type="primary" :loading="loading.loading" :loading-text="loading.text"
              native-type="submit">{{ $t('login.Login') }}</van-button>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useLogin } from '@/logic/login/index'
import { useRouter, useRoute } from 'vue-router'

const { onSubmit, formData, loading, img, showPage } = useLogin()
const loginLogoDom = ref<string>(window.webConfig.loginLogoHtml)
</script>
<style lang="scss" scoped>
.login-user {
  // max-width: 128px;
  // height: 68px;
  margin-top: 150px;
  margin-bottom: 10%;
  margin-left: auto;
  margin-right: auto;
  border-radius: 5px;

  img {
    width: 100%;
    // height: 68px;
    border-radius: 5px;
    object-fit: contain;
  }
}

.form-div {
  width: 90%;
  margin: 10px auto;
}
</style>
