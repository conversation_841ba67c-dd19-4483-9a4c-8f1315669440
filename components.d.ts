/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DataSource: typeof import('./src/components/DataSource.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    TimeLine: typeof import('./src/components/TimeLine.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanBadge: typeof import('vant/es')['Badge']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCol: typeof import('vant/es')['Col']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDivider: typeof import('vant/es')['Divider']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanRow: typeof import('vant/es')['Row']
    VanSearch: typeof import('vant/es')['Search']
    VanStep: typeof import('vant/es')['Step']
    VanStepper: typeof import('vant/es')['Stepper']
    VanSteps: typeof import('vant/es')['Steps']
    VanSwitch: typeof import('vant/es')['Switch']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTag: typeof import('vant/es')['Tag']
    YZCard: typeof import('./src/components/YZCard.vue')['default']
    YZConsign: typeof import('./src/components/YZConsign.vue')['default']
    YZEmpty: typeof import('./src/components/YZEmpty.vue')['default']
    YZGrid: typeof import('./src/components/YZGrid.vue')['default']
    YZLoading: typeof import('./src/components/YZLoading.vue')['default']
    YZNav: typeof import('./src/components/YZNav.vue')['default']
    YZOrgSelect: typeof import('./src/components/YZOrgSelect.vue')['default']
    YZUserSelect: typeof import('./src/components/YZUserSelect.vue')['default']
  }
}
