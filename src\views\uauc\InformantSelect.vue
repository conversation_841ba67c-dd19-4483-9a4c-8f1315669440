<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div class="btn">
                <van-icon name="filter-o" size="20" @click="rightShow" />
                <van-popup v-model:show="isShow" position="right" :style="{ width: '82%', height: '100%' }">
                    <div>
                        <div class="li_">
                            <span class="name">Account</span>
                            <van-field v-model="User_Account" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">Name</span>
                            <van-field v-model="User_Name" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">Role Name</span>
                            <van-field v-model="Role_Name" clearable />
                        </div>
                    </div>
                    <div
                        style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
                        <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset')
                            }}</van-button>
                        <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm1">{{
                            $t('Global.Confirm')
                            }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="list">
            <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                :loosing-text="$t('Form.Release_to_refresh')" :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loadData.loading" :finished="loadData.finished"
                    :finished-text="$t('Form.NoMore')" :loading-text="$t('Form.Loading')" @load="onLoad">
                    <van-checkbox-group v-model="checked">
                        <div class="li" v-for="(item, index) in Informants" :key="index" @click="toggle(index)">
                            <div class="li-c">
                                <van-checkbox :name="item" :ref="el => checkboxRefs[index] = el" @click.stop />
                            </div>
                            <div class="li-l">
                                <div class="li-n ellipsis"><span class="name">Account</span>{{ item.User_Account }}
                                </div>
                                <div class="li-m ellipsis"><span class="name">Display Name</span>{{ item.User_Name }}
                                </div>
                                <div class="li-m ellipsis"><span class="name">Role Name</span>{{ item.Role_Name }}</div>
                            </div>
                            <!-- <div class="li-r" @click="toInformantDetail(item)">
                                <van-icon name="arrow" size="20" />
                            </div> -->
                        </div>
                    </van-checkbox-group>
                </van-list>
            </van-pull-refresh>
        </div>
        <div class="foot">
            <van-button class="subbtn-text" color='#00A0E1' block type="primary" native-type="submit" @click="confirm"
                :disabled="checked.length > 0 ? false : true">
                {{ $t('Global.Confirm') }} <span v-if="checked.length > 0">( {{ checked.length || '' }} )</span>
            </van-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed, ref, reactive, onBeforeUpdate, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useFormData } from '@/utils'
import { usePostBody } from '@/utils/request'
import { useUserStore } from "@/store/users"
import { HSE } from '@/store/hse'

const store = HSE()
const router = useRouter()
const route = useRoute()

const user = useUserStore()

const checked = ref([])
const checkboxRefs = ref([])
const Informants = ref([])
const searchValue = ref(' ')
const isShow = ref(false)
const p_no = ref('')
const p_name = ref('')
const User_Account = ref('')
const User_Name = ref('')
const Role_Name = ref('')
const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    // pageSize: config.getDataBoweswer()?.pageSize || 10
    pageSize: 20
})
const toggle = (index) => {
    console.log("%c [ index ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", index)
    checkboxRefs.value[index]?.toggle();
}
onBeforeUpdate(() => {
    checkboxRefs.value = [];
})
const comeBack = () => {
    router.push({
        path: '/uauc',
        replace: true
    })
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const onSearch = () => {
    loadData.pageIndex = 1
    onLoad()
}
const rest = () => {
    User_Account.value = ''
    User_Name.value = ''
    Role_Name.value = ''
}
const searchParams = computed(() => {
    return [
        { name: 'User_Account', op: 'like', value: User_Account.value, isAll: false },
        { name: 'User_Name', op: 'like', value: User_Name.value, isAll: false },
        { name: 'Role_Name', op: 'like', value: Role_Name.value, isAll: false },
    ].filter(param => param.value)
})
const search_ = ref()
const confirm1 = () => {
    searchValue.value = ''
    search_.value = searchParams.value;
    console.log("%c [ searchParams.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchParams.value)
    loadData.pageIndex = 1
    onLoad()
    isShow.value = false
}
const onRefresh = () => {
    searchValue.value = ' '
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    Informants.value = []
    rest()
    onLoad()
}
const onLoad = async () => {
    const ff = window.btoa(JSON.stringify({ "Project_Number": { "op": "=", "value": route.query?.ProjectNo }, "Supplier_Code": { "op": "=", "value": route.query?.SupplierCode } }))
    const cc = window.btoa(JSON.stringify(["User_Account", "User_Name", "Role_Name"]))
    if (loadData.refreshing) {
        loadData.refreshing = false
        Informants.value = []
    }
    const search = [{ "name": "all", "op": "like", "value": searchValue.value !== ' ' ? searchValue.value.trim() : ' ', "isAll": true }]
    console.log("%c [ 告知人 search ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", search)
    const formdata = useFormData({
        o: '',
        start: (loadData.pageIndex - 1) * loadData.pageSize,
        page: loadData.pageIndex,
        limit: loadData.pageSize,
        f: ff,
        c: cc,
        lc: cc, // 查询列 同上
        s: btoa(window.unescape(encodeURIComponent(JSON.stringify(searchValue.value ? search : search_.value))))
    })
    console.log("%c [ 告知人 searchValue.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value)
    console.log("%c [ search_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", search_.value)

    try {
        // 告知人 api 
        const res = await usePostBody(`bpm/datasource/esb/649130778099781/paging?_dc=${Date.now()}`, {}, formdata) //项目名称，项目编号的接口
        console.log("%c [ 告知人--- res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res)
        if (res.success) {
            Informants.value = res.children || []
            loadData.pageIndex++
            loadData.loading = false
            if (res.children.length < loadData.pageSize) {
                loadData.finished = true
                console.log("告知人---SelectUAUC --- 停止刷新")
            }
            rest()
        }
    } catch (error) {
        console.log("告知人 --- Select --- error", error)
    }
}

const confirm = () => {
    store.Informant = checked.value
    router.push({ path: '/uauc', replace: true })
}
const toInformantDetail = ({ User_Account, User_Name, Role_Name }) => {
    const data = {
        User_Account,
        User_Name,
        Role_Name
    }
    router.push({
        path: '/InformantDetail',
        query: {
            Informants: JSON.stringify(data)
        },
        replace: true
    })
}
onMounted(() => {

    const Persons_ = route.query.Persons_ && JSON.parse(route.query?.Persons_)


    // checked.value = checked_
    // console.log("%c [ checked.value  ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", checked.value)
})
onUnmounted(() => {

})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #f9f9f9;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;

            .li_ {
                padding: 20px 13px 0px 13px;

                .van-field {
                    border-radius: 13px;
                    padding: 3px;
                    margin-top: 10px;
                    background: #f8f8f8;
                }

                .name {
                    color: #999999;

                }
            }
        }
    }

    .list {
        margin-top: 10px;
        margin-bottom: 80px;
        width: 100%;
        height: 100vh;

        .li {
            border-bottom: 1px solid #DCDFE6;
            background-color: var(--yz-div-background);
            padding: 10px 12px;
            display: flex;
            align-items: center;

            .li-c {
                width: 36px;
            }

            .li-l {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #444444;

                .name {
                    color: #999999;
                    margin-right: 10px;
                }

                .li-m {
                    margin-top: 10px;
                }
            }

            .li-r {
                width: 20px;
            }
        }
    }

    .foot {
        width: 100%;
        box-sizing: border-box;
        padding: 16px;
        position: fixed;
        bottom: 0;
        // z-index: 99;
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>