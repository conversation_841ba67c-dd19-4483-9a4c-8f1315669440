<template>
    <div>
        <van-button size="small" hairline type='default' :disabled="!btn.enable" @click="rejectOnClick"
            style="height: 32px;padding: 0px 20px;">
            {{ btn.text }}
        </van-button>

        <!-- 退回弹框 -->
        <van-dialog teleport="body" v-model:show="rejectShow" :title="$t('Form.MissionRejection')"
            :closeOnPopstate="false" @confirm="rejctConfirm" @cancel="rejectCancle" :before-close="rejectBefore"
            show-cancel-button>
            <div class="dialog-div">
                <van-field v-model="message" rows="1" autofocus autosize :label="$t('Form.Reason_for_rejection')"
                    type="textarea" />
            </div>
        </van-dialog>
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { useUserStore } from '@/store/users';
import { useCallBackClose, useLang, useParamsFormatter } from '@/utils';
import { eventBus } from '@/utils/eventBus';
import { PropType, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { url } from '@/api/url'
import { useGetQuery, usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { reactive } from 'vue';
import { onMounted } from 'vue';
import { useProcessStore } from '@/store/process';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const rejectShow = ref<boolean>(false)


const route = useRoute()
const userStore = useUserStore()
const router = useRouter()
const taskList = ref<any[]>([])
const stepData = ref<Array<any>>([])
const taskIdData = ref<string>('')
const message = ref<string>('')
const processStore = useProcessStore()
const rejectOnClick = async () => {
    rejectShow.value = true
}
const rejctConfirm = async () => {
    if (!message.value) {
        showNotify({ message: useLang('Form.Reason_for_rejection_tips'), type: 'danger' })
        return
    }
    const taskId = processStore.getProcLoad.taskId
    taskIdData.value = taskId
    const newUrl = useParamsFormatter(url.process.rejectTask, {
        params1: taskId
    })
    const data = await usePostBody(newUrl, {}, { comments: message.value, })
    if (!data.success) {
        showNotify({ message: useLang('Form.Rejection_of_fail_tips'), type: 'danger' })
    } else {
        showNotify({ message: useLang('Form.Rejection_of_success_tips'), type: 'success' })
        rejectCancle()
        useCallBackClose(function () {
            eventBus.emit('onBack', true)
        }, processStore.getHideTabHeader)
    }
}
const rejectCancle = () => {
    message.value = ''
    rejectShow.value = false
}
const rejectBefore = () => {
    return false
}

</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    height: 26vh;
}
</style>