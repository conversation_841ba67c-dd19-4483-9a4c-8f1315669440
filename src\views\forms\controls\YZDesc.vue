<template>
    <div class="clearAfter" v-if="!field.hidden">
        <div v-html="field.getDesc()" class="desc"></div>
    </div>
</template>

<script lang="ts" setup>
import { YZDesc } from '@/logic/forms/YZDesc';
import { eventBus } from '@/utils/eventBus';
import { onMounted, PropType, ref } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZDesc>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue',{
    cb:function(params) {
        if (params.uuid === props.field.uuid) {
            props.field.setDesc(params.value)
       }
    }
})
</script>
<style  lang="scss" scoped>
.clearAfter::after {
    display: none;

}

.desc {
 
    font-size: var( --yz-btn-14);
    padding: 10px 5px;
    font-family: PingFangSC-Medium;
    font-size: var( --yz-btn-14);
    color: #777777;
    line-height: 26px;
    font-weight: 500;
}
</style>