import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON>ty<PERSON> } from "./formModel";
import { useDefaultValueSetting } from "./DefaultFieldSet";
import { FieldType } from "vant";
import { useLang } from "@/utils";
export class DymicPanelConfig extends YZConfig {
    private vtype: string;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = parmas.ctype
        this.vtype = parmas['extends'].vtype
        if (this.required && !this.rules) {
            this.rules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        }
    }
}
export class DymicPanelStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class DymicPanelBox extends Y<PERSON><PERSON>ield {
    textType: FieldType;
    constructor(params: any) {
        super(params)
        this.textType = 'text'
        const config = params['__config__']
        if (config && config['defualtValue']) {
            const code = config['defualtValue'].code
            if (code) {
                this.vModel.value = useDefaultValueSetting(code)
            }
        }
    }
    initConfig(config: any): YZConfig {
        const newConfig = new DymicPanelConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {
        return new DymicPanelStyle(style)
    }
}