<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                UAUC Tracking
            </template>
        </YZNav>
        <div class="form">
            <van-form label-width="auto" @submit="Submit">
                <van-cell-group class="cell" inset>
                    <van-field v-model="timeVal" is-link readonly name="datePicker" error :label="$t('UAUC.ReportDate')"
                        input-align="left" label-align="left" />
                    <!-- @click="showTimePicker = true" -->
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <van-field v-model="Account" readonly :label="$t('UAUC.Speaker')" error input-align="left"
                        label-align="left" />
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <van-field v-model="ProjectName" @click="router.push({ path: '/projectSelectUauc', replace: true })"
                        required rightIcon="search" :label="$t('UAUC.ProjectOfficeName')" readonly error
                        :rules="[{ required: true, message: $t('UAUC.Project_Office_Name_input_tips') }]"
                        input-align="left" label-align="left" />
                    <van-field v-model="ProjectNo" :label="$t('CSTC.ProjectLocation')" readonly error input-align="left"
                        label-align="left" />
                    <van-field v-model="WorkArea" @update:model-value="store.setWorkArea($event)" error required
                        clearable :label="$t('UAUC.WorkArea')"
                        :rules="[{ required: true, message: $t('UAUC.work_area_input_tips') }]" input-align="left"
                        label-align="left" />
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <van-field v-model="SupplierName"
                        @click="router.push({ path: '/supplierSelect', query: { ProjectNo: ProjectNo }, replace: true })"
                        required rightIcon="search" :label="$t('UAUC.ResponsibleUnit')" readonly error
                        :rules="[{ required: true, message: $t('UAUC.responsible_unit_input_tips') }]"
                        input-align="left" label-align="left" />
                    <van-field v-model="ProblemUserName"
                        @click="router.push({ path: '/personSelect', query: { ProjectNo: ProjectNo, SupplierCode: SupplierCode }, replace: true })"
                        is-link :label="$t('UAUC.Responsibility_related')" readonly error input-align="left"
                        label-align="left" />
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <van-field v-model="ProcessingTime_name"
                        @click="router.push({ path: '/processTimeSelect', replace: true })" rightIcon="search"
                        :label="$t('UAUC.ProcessingTime')" readonly error
                        :rules="[{ required: false, message: $t('UAUC.processing_time_select_tips') }]"
                        input-align="left" label-align="left" />
                    <!-- 处理时间：other 的补充说明框  -->
                    <!-- <van-field v-if="OthInExplanation" v-model="DueTimeFrameAdditionalInfo" error required clearable
                        rows="3" autosize :label="$t('UAUC.DueTimeFrameAdditionalInfo')" type="textarea"
                        input-align="left" :rules="[{ required: true, message: $t('UAUC.Other_time_explanations') }]"
                        label-align="left" /> -->
                    <!-- 处理时间：other 的补充说明框  -->
                    <van-field v-model="Classification_name"
                        @click="router.push({ path: '/HazardClassification', replace: true })" required
                        rightIcon="search" :label="$t('UAUC.HazardClassification')" readonly error
                        :rules="[{ required: true, message: $t('UAUC.hazard_classification_type_unit_select_tips') }]"
                        input-align="left" label-align="left" />
                    <van-field v-model="Informant"
                        @click="router.push({ path: '/InformantSelect', query: { ProjectNo: ProjectNo, SupplierCode: SupplierCode }, replace: true })"
                        rightIcon="search" :label="$t('UAUC.Informant')" readonly error
                        :rules="[{ required: false, message: $t('UAUC.the_informant_select_tips') }]" input-align="left"
                        label-align="left" disabled />
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <van-field v-model="Description" error required clearable rows="3" autosize
                        :label="$t('UAUC.ProblemDescription')" type="textarea" input-align="left"
                        :rules="[{ required: true, message: $t('UAUC.description_of_the_problem_unit_input_tips') }]"
                        label-align="left" />
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <div style="display: flex; align-items: center; position: relative;">
                        <van-field v-model="Picture_problems" :label="$t('UAUC.Picture_of_the_problems')" readonly error
                            input-align="left" label-align="left" disabled />
                        <div class="add_upload">
                            <img :src="upload" alt="" class="icon_">
                            <input id="UploadPictures" type="file" accept="image/*" class="add_upload_file"
                                @change="fileUpload" />
                        </div>
                    </div>
                    <div class="add_upload_imgBox" v-if="fileList.length">
                        <div class="add_upload_imgDiv" v-for="(item, index) in fileList" :key="item.id + index">
                            <section>
                                <img :src="item.base64" @click="fileListShow = true">
                                <van-image-preview v-model:show="fileListShow" :images="imgList"></van-image-preview>
                            </section>
                            <p style="margin-left:5px; margin-bottom: 15px; font-size: 13px; color: #00a0e1;">{{
                                item.formattedName }}
                            </p>
                            <p class="add_upload_close" @click="ProcessDeleteImage(item.id, item.fileId, item.base64)">
                                <van-icon name="cross" style="color:#828282; position: absolute; left: 0; top: 0;" />
                            </p>
                        </div>
                    </div>
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <van-field v-model="recommend" required clearable rows="3" autosize
                        :label="$t('UAUC.Suggest_corrective_measures')" type="textarea" input-align="left"
                        :rules="[{ required: true, message: $t('UAUC.corrective_measures_enter_tips') }]" error
                        label-align="left" />
                </van-cell-group>
                <div class="foot">
                    <van-button class="subbtn-text" color='#00A0E1' block type="primary" native-type="submit"
                        :loading="Loading">
                        {{ $t('CSTC.submit') }}
                    </van-button>
                </div>
            </van-form>
        </div>

        <!-- <van-popup v-model:show="showTimePicker" position="bottom">
            <van-date-picker @confirm="timeConfirm" @cancel="showTimePicker = false" type="date" :min-date="minDate"
                :max-date="maxDate" />
        </van-popup> -->
    </div>
</template>

<script setup>
import Compressor from 'compressorjs';
import YZNav from '@/components/YZNav.vue';
import { onMounted, ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { longitudeProject, UAUC_Submit, OwnerPosition } from '@/service/user'
import { usePostBody, useGetQuery } from '@/utils/request';
import { url } from '@/api/url';
import { useCore } from '@/store/core/index'
import { TodaysDateFormat, formatDate, shortTimezoneName } from "@/utils";
import { HSE } from '@/store/hse'
import { showNotify } from 'vant';
import { useLang } from "@/utils";
import upload from '@/assets/imgs/upload.png'

const core = useCore()
const store = HSE()
const router = useRouter()
const route = useRoute()
const Account = ref('')
const Email = ref('')
const currentTime = ref(new Date())
const timeVal = ref(currentTime.value.toLocaleString().replace(/(\d{4})[^\d](\d{1,2})[^\d](\d{1,2}).*/, '$1-$2-$3').replace(/-(\d)\b/g, '-0$1')) //(new Date().toISOString().split('T')[0])
const formatOffsetToHhMm = (offsetInMins) => {
    let negative = offsetInMins < 0 ? "+" : "-";
    let positiveMins = Math.abs(offsetInMins);
    let hours = Math.floor(positiveMins / 60);
    let mins = Math.floor((positiveMins - ((hours * 3600)) / 60));
    if (hours < 10) { hours = "0" + hours; }
    if (mins < 10) { mins = "0" + mins; }
    return negative + hours + ':' + mins;
}
// const TimezoneOffset = ref(formatOffsetToHhMm(currentTime.value.getTimezoneOffset()))
const TimezoneOffset = ref(shortTimezoneName(currentTime.value)) // GMT+8
const showTimePicker = ref(false)
const minDate = new Date(new Date().setMonth(new Date().getMonth()))
const maxDate = new Date(new Date().setMonth(new Date().getMonth() + 6))
const ProjectName = ref('')
const ProjectNo = ref('')
const WorkArea = ref(store.WorkArea || '')
const SupplierName = ref('')   //责任单位
const SupplierCode = ref('')
const ProblemOwner = ref([])   //问题责任人 email
const ProblemUserName = ref([])//问题责任人
const ResponsiblePersonList = ref([]) // 问题责任人收集
const ProcessingTime = ref('')
const ProcessingTime_name = ref('')
const DueTimeFrameAdditionalInfo = ref('')
const Classification = ref('')
const Classification_name = ref('')
const InformantEmail = ref([]) //告知人 email
const Informant = ref([])      //告知人
const NotifiedParty = ref([])  // 告知人收集
const Description = ref('')    //问题描述
const Picture_problems = ref('')
const recommend = ref('')
const fileList = ref([])
const Loading = ref(false)
const IssueImg = ref()
const MAX_SIZE = ref(2 * 1024 * 1024)  // 最大2MB
const fileIds = ref([])
const FollowupPerson = ref([]) // 审核人 name
const FollowupPersonAccount = ref([])  // 审核人 email
const FollowupPersonList = ref([])  //审核人收集
// const OthInExplanation = ref(false) //other 处理时间说明框是否显示
const fileListShow = ref(false)
const imgList = ref([]) //store.imgList || [])

const uploadFileApi = async (result_img, base64, name, formattedName, id) => {
    let formData = new window.FormData()
    formData.append('fileToUpload', result_img)
    console.log("%c [ 最终 上传的图片 result_img ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result_img)
    await usePostBody(url.form.updateAttach, {}, formData, true)
        .then(result => {
            console.log("%c [upload img result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)
            if (result && result?.length > 0) {
                result_img.status = 'done'
                result_img.message = '上传完成'
                fileList.value.push({ id, base64, formattedName, name, fileId: result[0].fileId })
                imgList.value.push(base64)
                // store.setImgList([...imgList.value]) //^ 更新sotre
                console.log("%c [ --fileList.value-- ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileList.value)
                fileIds.value.push({ id: result[0].fileId })
                IssueImg.value = fileIds.value.map(item => item.id).join(',')
                // console.log("%c [ IssueImg.value 222]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", IssueImg.value)
            } else {
                result_img.status = 'failed';
                result_img.message = '上传失败';
            }
        }).catch(error => {
            result_img.status = 'failed';
            result_img.message = '上传失败';
            console.error('error', error)
        })
}
const CompressPictures = (resultFile, imgName) => {
    console.log("%c [ 压缩前 原图 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", resultFile)
    new Compressor(resultFile, {
        quality: 0.5,
        async success(result_img) {
            console.log("%c [ 压缩后的图片 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result_img)
            console.log("%c [ imgName ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", imgName)
            if (!result_img.name) {
                result_img.name = imgName?.replace(/\.(heic|heif)$/i, '.jpeg')
            }
            uploadFileApi(result_img)
        },
        error(err) {
            console.log('图片压缩失败', err.message); //第一个参数必须是图像 File 或 Blob 对象。
        },
    });
}
function formatFileName(fileName) {
    const [name, ext] = fileName.split(".");
    if (name.length > 4) {
        return `${name.slice(0, 4)}...${name.slice(-3)}.${ext}`;
    } else {
        return fileName; // 文件名小于 4 位时保持原样
    }
}
const fileUpload = async () => {
    let file = document.getElementById('UploadPictures');
    let fileName = file.value;
    let files = file.files;
    if (fileName == null || fileName == "") {
        showNotify({ message: useLang('UAUC.select_file_tips'), type: 'danger' })
    } else {
        let fileType = fileName.substr(fileName.length - 4, fileName.length);
        console.log("%c [ 1 图片类型 ===>>> fileType ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileType)
        let uploadFile = files[0]
        if (fileType) {
            if (uploadFile) {
                const formattedName = formatFileName(uploadFile.name)
                let reader = new FileReader();
                reader.onload = function (e) {
                    uploadFileApi(uploadFile, e.target.result, uploadFile.name, formattedName, new Date().valueOf())
                    // if (uploadFile.size > MAX_SIZE.value) { // & > 2MB的先压缩
                    //     console.log("%c [ 2 jpeg, png 压缩 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
                    //     CompressPictures(uploadFile, uploadFile.name)
                    // } else {                                // & 小于2MB的直接上传 api 接口
                    //     uploadFileApi(uploadFile, e.target.result, uploadFile.name, formattedName, new Date().valueOf())
                    // }
                }
                reader.readAsDataURL(uploadFile);
            } else {
                showNotify({ message: useLang('UAUC.select_file_tips'), type: 'danger' })
            }
        } else {
            showNotify({ message: useLang('UAUC.WrongFileTypeUploaded'), type: 'danger' })
        }
    }
}

const ProcessDeleteImage = (id, fileId, base64) => {
    // console.log("%c [ fileId ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileId)
    for (let i = 0; i < fileList.value.length; i += 1) {
        if (fileList.value[i].id === id) {
            fileList.value.splice(i, 1);
            break;
        }
    }
    // console.log("%c [ fileIds.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileIds.value)
    fileIds.value = fileIds.value.filter(item => item.id !== fileId)
    // console.log("%c [ 222fileIds.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileIds.value)
    IssueImg.value = fileIds.value.map(item => item.id).join(',')
    imgList.value = imgList.value.filter(item => item !== base64)
}

// const timeConfirm = (time) => {
//     timeVal.value = time.selectedValues?.join('/')
//     showTimePicker.value = false
// }

const getLocation = () => {
    console.log("%c [ 获取地理位置 ==>>> ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, green); color:yellow;")
    if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(successCallback, errorCallback);
    } else {
        showNotify({ message: useLang('CSTC.Location_tips'), type: 'danger' })
    }
}
function successCallback(position) {
    var latitude = position.coords.latitude; //纬度
    var longitude = position.coords.longitude; //，经度
    getLongitudeProject(latitude, longitude)
}
function errorCallback(error) {
    console.error("获取位置信息失败: " + error.message);
    showNotify({ message: useLang('CSTC.Location_failed_tips'), type: 'danger' })
}
const getLongitudeProject = async (latitude, longitude) => {
    console.log("%c [ uauc 纬度 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", latitude)
    console.log("%c [ uauc 精度 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", longitude)
    try {
        const params = {
            latitudelongitude: latitude + ',' + longitude
        }
        const res = await longitudeProject(params)
        if (res.data?.success) {
            ProjectName.value = res.data?.data?.ProjectName
            ProjectNo.value = res.data?.data?.ProjectNumber
            SupplierName.value = res.data?.data?.SupplierName
            SupplierCode.value = res.data?.data?.SupplierCode
        }
    }
    catch (error) {
        console.log('res--获取经纬度失败')
    }
}

const Submit = async () => {
    try {
        const params_ = {
            "owner": {},
            "draftId": "-1"
        }
        const { data } = await OwnerPosition(params_)
        const UserType = new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'Linde' : 'Supplier'
        const params = {
            "header": {
                "ouid": data?.providerName + '.' + data?.ouid, //"bpmou.922",  
                "draftid": data?.draftId, //-1
                "ownerPosition": data?.providerName + '.' + data?.memberId, //"bpmou.***************",  
                "actionName": "Submit",
                "comments": "",
                "consignEnabled": false,
                "consignUsers": [],
                "consignRoutingType": "Parallel",
                "consignReturnType": "Return",
                "context": {
                    "Routing": {}
                }
            },
            "formData": {
                "IssuerAccount": Email.value,
                "IssuerDate": formatDate(new Date()), //TodaysDateFormat(),
                "TimezoneOffset": TimezoneOffset.value,
                "IssuerName": Account.value,
                "UserType": UserType,
                "CaseNo": null,
                "IsContinueWork": "Yes",
                // "CurrentState": "Start",
                "ProjectNumber": ProjectNo.value,
                "ProjectName": ProjectName.value,
                "AreaLocation": WorkArea.value,
                "GPSLocation": "",
                "SupplierName": SupplierName.value, //责任单位
                "SupplierCode": SupplierCode.value, //责任单位 --- 供应商代码
                "DueTimeFrame": ProcessingTime.value, //处理时间
                "DueTimeFrameAdditionalInfo": "", //DueTimeFrameAdditionalInfo.value, // 处理时间  other的说明
                "HazardCategory": Classification.value,//危害分类 "Personal Protective Equipment",
                "ResponsiblePersons": ProblemOwner.value.join(), //问题责任人 email
                "ResponsiblePersonNames": ProblemUserName.value, //问题责任人
                // "ResponsiblePersons": "<EMAIL>",
                // "ResponsiblePersonNames": "Mouse CMH",
                "NotifiedPartys": InformantEmail.value.join(), //告知人 email 
                "NotifiedPartyNames": Informant.value, //告知人
                //     "NotifiedPartys": "<EMAIL>,<EMAIL>",
                // "NotifiedPartyNames": "罗志军,Mouse CMH",
                "IssueDesc": Description.value, //问题描述
                "IssueImg": IssueImg.value, //todo cNloceL3.788,GQ9F97Tp.372 || fileId: "T2J25qEo.f57"
                "ProposedCorrectiveAction": recommend.value, //建议整改措施
                "CorrectiveActionTaken": "",
                "CorrectiveActionImg": "",
                "ActionTakenBy": "",
                "ActionTakenByName": "",
                "FollowupPerson": FollowupPerson.value.join(), //审核人 name
                "FollowupPersonAccount": FollowupPersonAccount.value.join(), //审核人 email
                "FollowupResult": null,
                "FollowupComments": "",
                "FollowupAttachments": "",
                "Language": core.$lang.split('-')[0], //提交语言
                "CreateBy": Email.value,
                "UpdateBy": Email.value,
                "CreateTime": null,
                "UpdateTime": null,
                "ResponsiblePersonList": ResponsiblePersonList.value, //问题责任人收集
                //     "ResponsiblePersonList": [
                //     {
                //         "PersonAccount": "<EMAIL>",
                //         "PersonName": "Mouse CMH"
                //     }
                // ]
                "NotifiedParty": NotifiedParty.value, //告知人收集
                //     "NotifiedParty": [
                //     {
                //         "Account": "<EMAIL>",
                //         "Name": "罗志军"
                //     },
                //     {
                //         "Account": "<EMAIL>",
                //         "Name": "Mouse CMH"
                //     }
                // ]
                // "FollowupPersonList": FollowupPersonList.value //审核人收集 --- 不调了
            }
        }
        Loading.value = true
        try {
            const sub_res = await UAUC_Submit(params)
            // {
            //     "PostResult": "HasSentToOtherUsers",
            //     "SN": "UAUC_2024120660",
            //     "TaskID": "***************",
            //     "CustomMessage": "",
            //     "Accounts": [
            //         {
            //             "Account": "<EMAIL>",
            //             "DisplayName": "Chunyao Hou(<EMAIL>)"
            //         }
            //     ],
            //     "Indicators": [],
            //     "Informs": [],
            //     "success": true
            // }
            if (sub_res.data.success) {
                Loading.value = false
                showNotify({ message: useLang('UAUC.SubmissionSuccessful'), type: 'success' })
                router.back()
            } else {
                Loading.value = false
                showNotify({ message: useLang('UAUC.SubmissionFailed'), type: 'danger' })
            }
        } catch (error) {
            Loading.value = false
            showNotify({ message: useLang('UAUC.SubmissionFailed'), type: 'danger' })
            console.log("%c [ submit_UAUC_error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
        }
    } catch (error) {
        console.log("%c [ OwnerPosition error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
    }
}

const getUserInfo = async () => {
    const { success, user } = await useGetQuery(url.login.getUserInfo);
    console.log("%c [ user ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", user)
    if (success) {
        Account.value = user?.DisplayName
        Email.value = user?.EMail
    } else {
        console.log('getUserInfo error')
    }
}
onMounted(() => {
    getUserInfo()
    console.log("%c [--- route.query ---]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route.query)
    if (route.query?.fid) {
        store.projectData.projectName = ''
        store.projectData.projectNo = ''
        store.WorkArea = ''
        store.SupplierParams.SupplierName = ''
        store.SupplierParams.SupplierCode = ''
        store.ProblemOwner = [] //问题责任人收集
        store.Informant = [] //告知人收集
        store.processTime = ''
        store.processTime_name = ''
        store.HazardClassification = ''
        store.HazardClassification_name = ''
        store.FollowupPersonList = [] //审核人清单
    }
    if (!store.projectData?.projectName && !store.SupplierParams?.SupplierName) {
        getLocation()
    }
    if (store.projectData) {     //项目 \ 办公室名称
        console.log("%c [ store.projectData ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", store.projectData)
        ProjectName.value = store.projectData?.projectName
        ProjectNo.value = store.projectData?.projectNo
    }
    if (store.SupplierParams) {  //责任单位
        SupplierName.value = store.SupplierParams?.SupplierName
        SupplierCode.value = store.SupplierParams?.SupplierCode
    }
    if (store.ProblemOwner) {    // 问题责任人
        console.log("%c [ 问题责任人 personSelect store.ProblemOwner ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", store.ProblemOwner)
        const chosenPerson = []
        const chosenUserName = []
        store.ProblemOwner.forEach((item) => {
            chosenPerson.push(item.UserAccount) //email
            chosenUserName.push(item.UserName) //name
        })
        ProblemOwner.value = chosenPerson
        ProblemUserName.value = chosenUserName
        ResponsiblePersonList.value = store.ProblemOwner.map(person => ({
            PersonAccount: person.UserAccount,
            PersonName: person.UserName
        }))
    }
    if (store.Informant) {     // 告知人
        console.log("%c [ 告知人 InformantSelect store.Informant ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", store.Informant)
        const InformantPerson = []
        store.Informant.forEach((item) => {
            InformantPerson.push(item.User_Name) //name
            InformantEmail.value.push(item.User_Account) //email
        })
        Informant.value = InformantPerson
        NotifiedParty.value = store.Informant.map(person => ({
            Account: person.User_Account,
            Name: person.User_Name
        }))
    }
    if (store.processTime) {        //处理时间
        ProcessingTime.value = store.processTime
        ProcessingTime_name.value = store.processTime_name
        // if (store?.processTime === 'Other') {
        //     OthInExplanation.value = true
        // } else {
        //     OthInExplanation.value = false
        // }
    }
    if (store.HazardClassification) {//危险类型
        Classification.value = store.HazardClassification
        Classification_name.value = store.HazardClassification_name
    }

    // if (store.FollowupPersonList) {    // 审核人清单
    //     console.log("%c [ 审核人清单 store.FollowupPersonList ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", store.FollowupPersonList)
    //     const FollowupPersonAccount_ = []
    //     const FollowupPerson_ = []
    //     store.FollowupPersonList.forEach((item) => {
    //         FollowupPersonAccount_.push(item.UserAccount) //email
    //         FollowupPerson_.push(item.UserName) //name
    //     })
    //     FollowupPersonAccount.value = FollowupPersonAccount_
    //     FollowupPerson.value = FollowupPerson_
    //     FollowupPersonList.value = store.FollowupPersonList.map(person => ({
    //         Account: person.UserAccount,
    //         Name: person.UserName
    //     }))
    // }
})

</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #F9F9F9;
    box-sizing: border-box;

    .form {
        margin-top: 10px;
        padding-bottom: 80px;
    }

    .cell {
        margin-bottom: 5px;
    }

    .file {
        padding: 0 16px;

        .li {
            height: 40px;
            font-size: var(--yz-com-14);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9d9d9d;

            span {
                flex: 1;
            }

            i {
                width: 20px;
                display: flex;
                justify-content: flex-end;
            }
        }
    }

    .foot {
        width: 100%;
        box-sizing: border-box;
        padding: 16px;
        position: fixed;
        bottom: 0;
        z-index: 99;
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

::v-deep(.van-cell__title) {
    color: #828282;
}

::v-deep(.van-field__control) {
    color: #00A0E1 !important;
}

::v-deep(.van-field__control:disabled) {
    color: #00a0e1 !important;
    -webkit-text-fill-color: #00a0e1 !important;
}

::v-deep(.van-field__control--error, .van-field__control--error::placeholder) {
    -webkit-text-fill-color: #00a0e1 !important;
}

::v-deep(.van-field__control--error) {
    color: #00A0E1;
}

::v-deep(.van-icon-search) {
    color: #00A0E1;
}

::v-deep(.van-field__label) {
    width: 90px !important;
}

.add_upload {
    .icon_ {
        color: #00a0e1;
        width: 20px;
        height: 20px;
        position: absolute;
        top: 12px;
        right: 15px;
    }
}

.add_upload .add_upload_icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.add_upload .add_upload_file {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    font-size: 0;
}

.add_upload_imgBox {
    padding: 10px 20px;
}

.add_upload_imgBox .add_upload_imgDiv {
    max-width: 250px;
    height: 1.5rem;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.add_upload_imgBox .add_upload_imgDiv section img {
    width: 1.5rem;
    height: 1.5rem;
}

.add_upload_imgBox .add_upload_close {
    position: absolute;
    right: 33px;
    width: 15px;
    height: 15px;
    margin-bottom: 15px;
}

.add_upload_imgBox .add_upload_close img {
    width: 100%;
    height: 100%;
}
</style>