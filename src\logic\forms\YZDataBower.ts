import { UploaderFileListItem } from "vant";
import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON><PERSON>, YZStyle } from "./formModel";
import { useLang } from "@/utils";
export class YZDataBowerConfig extends YZConfig {
    private dataBrowser: any
    private defaultRules: Array<any>;
    private valueConvert: any
    private enableValueConvert: any
    private dlgConfig: any
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZDataBower'
        this.defaultRules = [{ required: true, message: parmas['label'] ? `${useLang('Form.PleaseSelect') + parmas['label']}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.dataBrowser = parmas['dataBrowser']
        this.enableValueConvert = parmas['enableValueConvert']
        this.valueConvert = parmas['valueConvert']
        this.dlgConfig = parmas['extends']['dlgConfig']
    }
    getDlgConfig() {
        return this.dlgConfig
    }
    getDataBoweswer() {
        return this.dataBrowser
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
    getValueConvert() {
        // console.log(this.enableValueConvert,'this.enableValueConvert')
        if (this.enableValueConvert) {
            return this.valueConvert
        }
        return null
    }
}
export class YZDataBowerStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZDataBower extends YZField {
    constructor(params: any) {
        super(params)
        //  this.vModel.value = ''
        this.vModel.optionValue = ''
        this.vModel.value = params['__vModel__'].modelValue ?? ''
    }
    initConfig(config: any): YZConfig {
        return new YZDataBowerConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZDataBowerStyle(style)
    }
}