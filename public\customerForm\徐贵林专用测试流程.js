const funs = {
  test() {

  },
  btnEvents : {
    // 方法名称必须是按钮handler指向的名称
    /**
     * 
     * @param {当前表单实例} $form 
     * @param {当前表单字段控件数组} $fields
     * @param {当前表单控件的实例数组} $control
     */
    fundata($form,$fields,$control) {
       // 提取明细表实例
       const ctx = $control[0]
       // 提取实例方法
       const method =ctx.exposed
       //添加行
       method.addRow() // 或者method.addRow(function(ctx){ })
       // 删除行
       method.deleteRow()
       //赋值
       //从 $fields  找到指定的明细控件，然后找到指定的控件字段赋值/取值即可
  
    }
  }
}