<template>
    <div>
        <van-popup v-model:show="show" position="right" style="heigth: 100vh; width: 100%">
            <div class="service-div">
                <van-nav-bar :title="$t(item.name)" :left-text="$t('Global.Back')"
                    @click-left.stop="item.onBack(item.name)" left-arrow />
                <div class="ver-div">
                    <img src="@/assets/imgs/logo1.png" alt="" />
                    <p>eFlow <span style="color: #a69c9c;font-size: 12px;">1.0.3</span></p>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import { IMyMenu } from '@/logic/my/myModel';
import { computed, PropType } from 'vue';
const props = defineProps({
    isPop: {
        type: Boolean,
        default: false,
        required: false
    },
    item: {
        type: Object as PropType<IMyMenu>,
        required: true
    }
})
const show = computed(() => props.isPop)

</script>
<style lang="scss" scoped>
.service-div {
    width: 100%;
    height: 100vh;

    .ver-div {
        width: 100px;
        margin: 50% auto;
        text-align: center;
    }

    .ver-div img {
        width: 100px;
        height: 100px;
    }

}
</style>