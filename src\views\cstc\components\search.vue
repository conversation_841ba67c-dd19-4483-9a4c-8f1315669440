<template>
    <div class="page">
        <!-- <YZNav isBack>
            <template #title>
                {{ $t('Form.Filter') }}
            </template>
</YZNav> -->
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="iTitle">
                {{ $t('Form.Filter') }}
            </div>
        </div>
        <div class="list">
            <div class="li">
                <div class="li-name">{{ $t('Form.Keywords') }}</div>
                <van-field v-model="keyWords" />
            </div>
            <div class="li">
                <div class="li-name">{{ $t('Form.CheckingDateTime') }}</div>
                <div style="display: flex; align-items: center;">
                    <van-field v-model="minTime" is-link readonly name="datePicker" @click="showPicker = true" />
                    <van-popup v-model:show="showPicker" destroy-on-close position="bottom">
                        <van-date-picker :model-value="minPickerValue" @confirm="minConfirm" :default-date="currentDate"
                            :min-date="minDate" :max-date="maxDate" @cancel="showPicker = false" :disabled="true" />
                    </van-popup>
                    <span style="margin:0 0 4px 12px; color: #dcdfe6;">—</span>
                    <van-field v-model="maxTime" is-link readonly name="datePicker" @click="showPicker_ = true"
                        style="margin-left: 15px;" />
                    <van-popup v-model:show="showPicker_" destroy-on-close position="bottom">
                        <van-date-picker :model-value="maxPickerValue" @confirm="maxConfirm"
                            :default-date="currentDate_" :min-date="minDate_" :max-date="maxDate_"
                            @cancel="showPicker_ = false" />
                    </van-popup>
                </div>
            </div>
        </div>
        <div
            style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
            <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset') }}</van-button>
            <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm">{{
                $t('Global.Confirm')
                }}</van-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import YZNav from '@/components/YZNav.vue';
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const showPicker = ref(false)
const showPicker_ = ref(false)
const keyWords = ref('')

const currentDate = new Date()
const minDate = ref(new Date(1980, 0, 1))
const maxDate = ref(new Date())
const searchType = ref(route.query?.type)

const minPickerValue = ref([
    currentDate.getFullYear(),
    String(currentDate.getMonth() + 1).padStart(2, '0'),
    String(currentDate.getDate()).padStart(2, '0')
])

const currentDate_ = new Date()
const minDate_ = ref(new Date(1980, 0, 1))
const maxDate_ = ref(new Date(2040, 11, 31))
const maxPickerValue = ref([
    currentDate_.getFullYear(),
    String(currentDate_.getMonth() + 1).padStart(2, '0'),
    String(currentDate_.getDate()).padStart(2, '0')
])
const minTime = ref('')
const maxTime = ref('')

const comeBack = () => {
    if (route.query?.type === '1') {
        router.push({
            path: '/trainingQRCode',
            replace: true
        })
    } else {
        router.push({
            path: '/trainingtHistory',
            replace: true
        })
    }
}
const minConfirm = ({ selectedValues }) => {
    minTime.value = selectedValues.join('-');
    minPickerValue.value = selectedValues;
    // 更新 maxDate_ 的最小值为选择的日期
    const selectedDate = new Date(selectedValues[0], selectedValues[1] - 1, selectedValues[2])
    minDate_.value = selectedDate // maxTime 的最小值设为 minTime 的选择值
    // 如果 maxTime 小于新的 minTime,则清空 maxTime
    if (maxTime.value) {
        const maxDate = new Date(maxPickerValue.value[0], maxPickerValue.value[1] - 1, maxPickerValue.value[2])
        if (maxDate < selectedDate) {
            maxTime.value = ''
            maxPickerValue.value = []
        }
    }
    showPicker.value = false;
}

const maxConfirm = ({ selectedValues }) => {
    maxTime.value = selectedValues.join('-');
    maxPickerValue.value = selectedValues;
    showPicker_.value = false;
}

const rest = () => {
    keyWords.value = ''
    minPickerValue.value = [
        currentDate.getFullYear(),
        String(currentDate.getMonth() + 1).padStart(2, '0'),
        String(currentDate.getDate()).padStart(2, '0')
    ]
    maxPickerValue.value = [
        currentDate_.getFullYear(),
        String(currentDate_.getMonth() + 1).padStart(2, '0'),
        String(currentDate_.getDate()).padStart(2, '0')
    ]
    minTime.value = ''
    maxTime.value = ''
}

const confirm = () => {
    console.log("%c [ minTime.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", minTime.value)
    console.log("%c [ maxTime.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", maxTime.value)

    console.log("%c [ keyWords.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", keyWords.value)
    if (route.query?.type === '1') {
        router.push({
            path: '/trainingQRCode',
            query: {
                keyWords: keyWords.value,
                minTime: minTime.value,
                maxTime: maxTime.value
            },
            replace: true
        })
    }
    if (route.query?.type === '2') {
        router.push({
            path: '/trainingtHistory',
            query: {
                keyWords: keyWords.value,
                minTime: minTime.value,
                maxTime: maxTime.value
            },
            replace: true
        })
    }
}
onMounted(() => {

})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;
        position: relative;
        box-shadow: 0 .0625rem .25rem rgba(0, 0, 0, .1);

        .back {
            position: absolute;
            left: 10px;
            top: 50%;
            width: 40px;
            box-sizing: border-box;
            transform: translate(0, -50%);

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .iTitle {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: var(--yz-text-333);
            font-weight: 500;
            text-align: center;
        }
    }

    .list {
        padding: 25px 12px;

        .li {
            font-size: var(--yz-com-14);
            margin-bottom: 16px;

            .li-name {
                color: #999999;
                width: 145px;
                margin-bottom: 10px;
            }

            // .li-info {
            //     color: #25a6d8
            // }

            .van-cell {
                border: 1px solid #dcdfe6;
                border-radius: 5px;
                padding: 3px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

::v-deep(.van-field__control) {
    color: #25a6d8 !important;
}
</style>