const funOptioin = {
    /**
     * 
     * @param { vue 上下文 } param0 
     * @param {当前表单实例} $refs 
     * @param {当前表单所以字段} $fields 
     * @returns 表单提交后事件
     */
  afterSubmit: function({ proxy: $ctx }, $refs, $fields) {
     return new Promise((resolve,reject)=>{
          //do somethings
          resolve()
     }).catch(error=>{
        reject(error)
     })
  },
  /**
   * 表单提交前事件
   * @param {vue 上下文} param0 
   * @param {当前表单实例} $refs 
   * @param {当前表单控件} $fields 
   * @returns true/ false  当返回false时，会阻止表单提交
   */
  beforeSubmit: function({ proxy: $ctx }, $refs, $fields) {


   //  clientHelper.setFieldModelValue($fields,"数字1","100")
     clientHelper.setFieldOptionValue($fields,"数字1","100")
     return new Promise((resolve,reject)=>{
           // 发送异步事件，
          // .then(result=>{    resolve(true)  }).catch(error=>{ reject(false) })


         resolve(true)
     }).catch(error=>{
        reject(error)
     })
  },
  /**
   * 表单验证后事件
   * @param {vue 上下文} param0 
   * @param {当前表单实例} $refs 
   * @param {当前表单控件} $fields 
   * @returns true/ false 当返回false时，会阻止验证后的一切动作
   *   
   */  afterValidate: function({ proxy: $ctx }, $refs, $fields) {
    return new Promise((resolve,reject)=>{
         resolve(true)
     }).catch(error=>{
        reject(error)
     })
  },
  /**
   * 
   * @param {vue 上下文} param0 
   * @param {当前表单控件实例 } $refs 
   * @param {当前表单所有字段} $fields 
   * @returns 任何返回值都可以，不会阻止任何事件继续传递
   */
  beforeValidate: function({ proxy: $ctx }, $refs, $fields) {
    return new Promise((resolve,reject)=>{
        // do something 
         resolve(false)
     }).catch(error=>{
        reject(error)
     })
  },
  /**
   * 
   * @param {vue上下文} param0 
   * @param {当前表单实例} $refs 
   * @param {当前表单所有字段} $fields 
   * @returns 任何返回值都可以，不会阻止任何事件继续传递
   */
  afterFormLoad:function({ proxy: $ctx }, $refs, $fields) {
    return new Promise((resolve,reject)=>{
        resolve()
    }).catch(error=>{
        reject(error)
    })
  }
};