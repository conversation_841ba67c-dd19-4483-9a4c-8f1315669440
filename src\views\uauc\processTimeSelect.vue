<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div class="btn">
                <van-icon name="filter-o" size="20" @click="rightShow" />
                <van-popup v-model:show="isShow" position="right" :style="{ width: '82%', height: '100%' }">
                    <div>
                        <div class="li_">
                            <span class="name">Code</span>
                            <van-field v-model="Code" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">Display Name</span>
                            <van-field v-model="DisplayName" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">Value</span>
                            <van-field v-model="Value" clearable />
                        </div>
                    </div>
                    <div
                        style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
                        <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset')
                            }}</van-button>
                        <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm">{{
                            $t('Global.Confirm')
                        }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="list">
            <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                :loosing-text="$t('Form.Release_to_refresh')" :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loadData.loading" :finished="loadData.finished"
                    :finished-text="$t('Form.NoMore')" :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div v-for="(i, idx) in viewColsData" :key="idx">
                        <div class="li" @click="toProcessTimeDetail(i)">
                            <div class="li-l">
                                <div class="li-n ellipsis"><span class="name">Code</span>{{ i.Code }}</div>
                                <div class="li-m ellipsis"><span class="name">Display Name</span>{{ i.DisplayName }}
                                </div>
                                <div class="li-m ellipsis"><span class="name">Value</span>{{ i.Value }}</div>
                            </div>
                            <div class="li-r">
                                <van-icon name="arrow" size="20" />
                            </div>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useFormData } from '@/utils'
import { usePostBody } from '@/utils/request'
// import { ProcessingTime } from '@/service/user'
import { useCore } from '@/store/core/index'

const router = useRouter()
const core = useCore()
const searchValue = ref(' ')
const isShow = ref(false)
const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    // pageSize: config.getDataBoweswer()?.pageSize || 10
    pageSize: 20
})
const viewColsData = ref([])
const Code = ref('')
const DisplayName = ref('')
const Value = ref('')

const comeBack = () => {
    router.push({
        path: '/uauc',
        replace: true
    })
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const onSearch = () => {
    loadData.pageIndex = 1
    onLoad()
}
const rest = () => {
    Code.value = ''
    DisplayName.value = ''
    Value.value = ''
}
const searchParams = computed(() => {
    return [
        { name: 'Code', op: 'like', value: Code.value, isAll: false },
        { name: 'DisplayName', op: 'like', value: DisplayName.value, isAll: false },
        { name: 'Value', op: 'like', value: Value.value, isAll: false },
    ].filter(param => param.value)
})
const search_ = ref()
const confirm = () => {
    searchValue.value = ''
    search_.value = searchParams.value;
    loadData.pageIndex = 1
    onLoad()
    isShow.value = false
}
const onRefresh = () => {
    searchValue.value = ' ' // 清空搜索框
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    viewColsData.value = []
    rest()
    onLoad()
}

const onLoad = async () => {
    const ff = window.btoa(JSON.stringify({ "DataType": { "op": "=", "value": "UAUC Due Time" }, "Language": { "op": "=", "value": core.$lang.split('-')[0] } }))
    const cc = window.btoa(JSON.stringify(["Code", "Value", "DisplayName"]))
    if (loadData.refreshing) {
        loadData.refreshing = false
        viewColsData.value = []
    }
    const search = [{ "name": "all", "op": "like", "value": searchValue.value !== ' ' ? searchValue.value.trim() : ' ', "isAll": true }]
    const formdata = useFormData({
        o: '',
        // start: (loadData.pageIndex - 1) * loadData.pageSize,
        // page: loadData.pageIndex,
        // limit: loadData.pageSize,
        f: ff,
        c: cc,
        // lc: cc,
        s: btoa(window.unescape(encodeURIComponent(JSON.stringify(searchValue.value ? search : search_.value))))
    })
    console.log("%c [ searchValue.value ? search : search_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value ? search : search_.value)
    if (location.hostname === 'localhost') {
        const localRes = [
            {
                Code: "Option_01",
                DisplayName: "\u7ACB\u5373\u6574\u6539",
                Value: "No work until correction",
                RowNum: 1,
                TotalRows: 3
            },
            {
                Code: "Option_02",
                DisplayName: "\u4E00\u65E5\u5185",
                Value: "Within one day",
                RowNum: 2,
                TotalRows: 3
            },
            {
                Code: "Option_03",
                DisplayName: "\u5176\u5B83",
                Value: "Other",
                RowNum: 3,
                TotalRows: 3
            }
        ]
        viewColsData.value = localRes
    } else {
        try {
            const data = await usePostBody('bpm/datasource/527365695868997/table/MS_Dictionary_I8N', {}, formdata) //处理时间
            console.log("%c [ 处理时间data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
            if (data) {
                viewColsData.value = data || []
                // loadData.pageIndex++
                loadData.loading = false
                if (data.length < loadData.pageSize) {
                    loadData.finished = true
                    console.log("处理时间 --- 停止刷新")
                }
                rest()
            }
        } catch (error) {
            console.log("处理时间---error", error)
        }
    }
}
const toProcessTimeDetail = (val) => {
    router.push({
        path: '/processTimeDetail',
        query: {
            list: JSON.stringify(val)
        },
        replace: true
    })
}
onMounted(() => {
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #f9f9f9;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;

            .li_ {
                padding: 20px 13px 0px 13px;

                .van-field {
                    border-radius: 13px;
                    padding: 3px;
                    margin-top: 10px;
                    background: #f8f8f8;
                }

                .name {
                    color: #999999;

                }
            }
        }
    }

    .list {
        margin-top: 10px;

        .li {
            border-bottom: 1px solid #DCDFE6;
            background-color: var(--yz-div-background);
            padding: 10px 12px;
            display: flex;
            align-items: center;

            .li-l {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #444444;

                .name {
                    color: #999999;
                    margin-right: 10px;
                }

                .li-m {
                    margin-top: 10px;
                }
            }

            .li-r {
                width: 20px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>