<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">SSI</div>
        </div>
        <div class="fave">
            <div class="fave-li" v-for="(item, index) in list" :key="item.id" @click="tabClick(item)"
                :style="{ width: `calc(100% / 4 - 25px)` }">
                <!-- filterList -->
                <!-- width: `calc(100% / ${SupplierP ? 4 : 3} - 16px)` -->
                <img :src="item.icon" class="iconfont" alt="">
                <p class="ellipsis2">{{ $t(item.text) }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import Preliminary3X from './img/Preliminary3X.png'
import Daily3X from './img/Daily3X.png'
import Monthly3X from './img/Monthly3X.png'
import Tool3X from './img/Tool3X.png'
import Quarterly3X from './img/Quarterly3X.png'
import Scan1 from './img/scan1.png'
import { onMounted, ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { SupplierPermissions } from '@/service/user'
import { useWork } from '@/logic/work';


const router = useRouter()
//const SupplierP = ref(location.hostname === 'localhost' ? true : false) //123
const list = [
    { name: 'Tool/Equipment Registration', text: 'SSI.ToolEquipmentRegister', icon: Tool3X, path: '/ssiRegister?fid=646064554467397' },
    { name: 'Preliminary', text: 'SSI.Preliminary', icon: Preliminary3X, path: '', type: 'Preliminary' },
    { name: 'Daily', text: 'SSI.Daily', icon: Daily3X, path: '', type: 'Daily' },
    { name: 'Monthly', text: 'SSI.Monthly', icon: Monthly3X, path: '', type: 'Monthly' },
    { name: 'Quarterly', text: 'SSI.Quarterly', icon: Quarterly3X, path: '', type: 'Quarterly' },
    { name: 'Scan Barcode', text: 'SSI.Barcode', icon: Scan1, path: '/ScanBarcode?qs_title=SSI.ScanBarcode&type=in' }
]
// const filterList = computed(() => {
//     return list.filter(item => {
//         if (item.name === 'Tool/Equipment Registration') {
//             return SupplierP.value
//         }
//         return true
//     })
// })


const tabClick = async (item) => {
    console.log("%c [ tab --- item.type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item.type)
    if (item.type) {
        router.push({
            path: '/layout/work',
            query: {
                type: item.type
            }
        })
    } else {
        router.push(item.path)
    }
}
const comeBack = () => {
    router.push({
        path: '/',
        replace: true
    })
}
// const getSupplierPermissions = async () => {
//     try {
//         const res = await SupplierPermissions()
//         if (res.data.success) {
//             SupplierP.value = res.data?.Data?.Trainer
//         }
//     }
//     catch (error) {
//         console.log(error)
//     }
// }
onMounted(() => {
    // getSupplierPermissions()
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;
        border-bottom: 3px solid #f1f1f1a9;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 0.85;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .fave {
        background-color: var(--yz-div-background);
        display: flex;
        flex-wrap: wrap;
        margin: 8px;
        box-sizing: border-box;

        .fave-li {
            padding: 8px 0;
            box-sizing: border-box;
            margin: 0 11.2px; // 0 12px;
            text-align: center;

            .iconfont {
                font-size: 24px;
                color: var(--yz-text-333);
                width: 24px;
                height: 24px;
                object-fit: contain; // 保持图片比例
            }

            p {
                margin-top: 8px;
                text-align: center;
                color: var(--yz-text-333);
                font-size: var(--yz-com-12);
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ellipsis2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    // -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all; //断词
}
</style>