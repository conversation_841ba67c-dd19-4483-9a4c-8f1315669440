<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ $t('CSTC.TQR') }}
            </template>
        </YZNav>

        <div class="code">
            <van-image class="img" :src="imgSrc">
                <template v-slot:loading="loading">
                    <van-loading type="spinner" size="20" />
                </template>
            </van-image>
            <div class="time" v-show="!loading">{{ new Intl.DateTimeFormat(navigator?.language,
                options_).format(Date.parse(new
                Date())) }}</div>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { QR_code } from '@/service/user'
import { useLoingStore } from '@/store/login';
import { useCore } from '@/store/core';
import store from '@/store';
import axios from 'axios'
const loginStore = useLoingStore(store)
const coreStore = useCore(store)


const router = useRouter()
const route = useRoute()

const loading = ref(false)
const list = ref([])
// const imgSrc = ref('./img/bg.png')
const imgSrc = ref('')
const text_ = ref(route.query?.qrParam || '')
const options_ = ref({
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
})
const currentTime = ref('')

onMounted(async () => {
    loading.value = true
    await axios({
        method: 'GET',
        headers: {
            'Authorization': "Bearer " + loginStore.getToken,
            'Accept-Language': coreStore.$lang == 'en' ? 'en-US' : coreStore.$lang
        },
        url: `/bpm/util/barcode/encode?text=${text_.value}&width=500&height=500&format=QR_CODE&_dc=${Date.now()}`,
        responseType: 'blob'
    }).then(res => {
        imgSrc.value = res?.request?.responseURL
        // currentTime.value = new Intl.DateTimeFormat(navigator?.language, options_).format(Date.parse(new Date())) //new Date().toLocaleString()
        loading.value = false
    }).catch(err => {
        console.log('QR_err', err)
    })
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .code {
        width: 85%;
        margin: 60px auto;
        background-color: #fff;
        box-shadow: 0px 0px 4px 1px #F3F3F3;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;

        .img {
            width: 230px;
            height: 230px;
            background: #8a8888;
        }

        .time {
            margin-top: 30px;
            font-size: var(--yz-com-14);
        }
    }
}
</style>