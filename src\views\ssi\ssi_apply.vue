<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ serialNum }}
            </template>
        </YZNav>
        <div class="fave">
            <van-form>
                <!-- @submit="CheckSubmit" -->
                <p style="font-size:16px; font-weight:700;padding:5px 15px;">{{ $t('SSI.GeneralInformation') }}</p>
                <van-cell-group inset>
                    <van-field v-model="EntryDate_" readonly name="datePicker" :label="$t('SSI.EntryDate')"
                        input-align="left" label-align="left" style="margin-bottom:15px" @click="EntryDatePicker = true"
                        class="GenInfo" disabled />
                    <van-field v-model="ProjectName_" :label="$t('CSTC.ProjectName')" readonly error input-align="left"
                        label-align="left" class="GenInfo" disabled />
                    <van-field v-model="ProjectNo_" :label="$t('CSTC.ProjectNo')" readonly error input-align="left"
                        label-align="left" class="GenInfo" disabled />
                    <van-field v-model="Contractor_" :label="$t('SSI.ContractorName')" error clearable
                        input-align="left" label-align="left" class="GenInfo" disabled />
                    <van-field v-model="TemplateID_" readonly :label="$t('SSI.Equipment')" class="GenInfo" disabled />
                    <van-field v-model="Manufacture_" @update:model-value="SSI_.setManufacture($event)"
                        :label="$t('SSI.Manufacture')" class="GenInfo" disabled />
                    <van-field v-model="SerialNo_" @update:model-value="SSI_.setSerialNo($event)"
                        :label="$t('SSI.SerialNo')" class="GenInfo" disabled />
                    <van-field v-model="DisplayName" error :label="$t('SSI.ContractorHSE')" class="GenInfo" disabled />
                    <van-field v-if="BarCodeShow" v-model="BarCode_" error :label="$t('SSI.BarcodeNo')" class="GenInfo"
                        disabled />
                </van-cell-group>
                <p style="font-size:16px; font-weight:700; padding: 5px 15px;">{{ $t('SSI.TechnicalInformation') }}</p>
                <van-cell-group class="cell" inset style="">
                    <div style="display:flex; align-items: center; padding:0 0 0 14px;">
                        <van-switch v-model="ApplicableCalibrationExpiryDate_" size="16px" disabled />
                        <van-field v-model="CalibrationExpiryDate_" readonly name="datePicker" error
                            :label="$t('SSI.CalibrationExpiryDate')" input-align="left" label-align="left"
                            class="CalibrationExpiryDate" disabled />
                        <!-- @click="showTimePicker = true" -->
                    </div>

                    <div style="display:flex; align-items: center; ">
                        <van-field v-model="CertificateOrOtherAttachment_2"
                            :label="$t('SSI.Certificate_Other_Documents')" readonly error input-align="left"
                            label-align="left" disabled class="line_" />
                    </div>
                    <div class="add_upload_imgBox" v-if="CerList.length">
                        <div class="add_upload_imgDiv" v-for="(item, idx) in CerList" :key="idx">
                            <div style="display:flex;">
                                <div class="imgBox">
                                    <img v-if="item.fileType.includes('image')" :src="item.fileURL"
                                        @click="CerListShow = true">
                                    <img v-else :src="file_" @click="previewFile(1, item)">
                                    <van-image-preview v-model:show="CerListShow"
                                        :images="CerImgList"></van-image-preview>
                                </div>
                                <div style="margin-left:5px; margin-bottom: 15px; font-size: 13px; color: #00a0e1;">{{
                                    item.formattedName }}</div>
                            </div>
                        </div>
                    </div>
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <div style="display:flex; align-items: center;padding:0 0 0 14px;">
                        <van-switch v-model="ApplicableOperatorName_" size="16px" disabled />
                        <van-field v-model="OperatorName_" error readonly :label="$t('SSI.OperatorName')"
                            :rules="[{ required: false, message: $t('SSI.operator_select_tips') }]" class="OperatorName"
                            disabled />
                        <van-icon name="info-o" class="msgIcon1" @click="ShowPrompt_" />
                    </div>
                    <div style="display:flex; align-items: center; ">
                        <van-field v-model="OperatorAttachment_2" error :label="$t('SSI.Competence_Certificate_Card')"
                            disabled class="line_" />
                    </div>
                    <div class="add_upload_imgBox" v-if="ComList.length">
                        <div class="add_upload_imgDiv" v-for="(item, idx) in ComList" :key="idx">
                            <div style="display:flex;">
                                <div class="imgBox">
                                    <img v-if="item.fileType.includes('image')" :src="item.fileURL"
                                        @click="ComImgListShow = true">
                                    <img v-else :src="file_" @click="previewFile(2, item)">
                                    <van-image-preview v-model:show="ComImgListShow"
                                        :images="ComImgList"></van-image-preview>
                                </div>
                                <div style="margin-left:5px; margin-bottom: 15px; font-size: 13px; color: #00a0e1;">{{
                                    item.formattedName }}</div>
                            </div>
                        </div>
                    </div>
                </van-cell-group>
                <van-cell-group inset style=" display: flex; align-items: center; padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableModel_" size="16px" disabled />
                    <van-field v-model="Model_" @update:model-value="SSI_.setModel($event)" error
                        :label="$t('SSI.Model')"
                        :rules="[{ required: false, message: $t('SSI.applicable_model_enter_tips') }]" class="GenInfo"
                        disabled />
                </van-cell-group>
                <van-cell-group inset style="display: flex; align-items: center; padding:0 0 0 14px;">
                    <van-switch v-model="ApplicablePlateNo_" size="16px" disabled />
                    <van-field v-model="PlateNo_" @update:model-value="SSI_.setPlateNo($event)" error
                        :label="$t('SSI.PlateNo')"
                        :rules="[{ required: false, message: $t('SSI.license_plate_number_enter_tips') }]"
                        class="GenInfo" disabled />
                </van-cell-group>
                <van-cell-group inset style="display: flex; align-items: center; padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableSWLOrCapacity_" size="16px" disabled />
                    <van-field v-model="SWLOrCapacity_" @update:model-value="SSI_.setSWLOrCapacity($event)" error
                        :label="$t('SSI.SWL_Capacity_Tonnes')"
                        :rules="[{ required: false, message: $t('SSI.SWL_capacity_enter_tips') }]" class="GenInfo"
                        disabled />
                </van-cell-group>
                <van-cell-group inset style="display: flex; align-items: center; padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableSizeOrDiameter_" size="16px" disabled />
                    <van-field v-model="SizeOrDiameter_" @update:model-value="SSI_.setSizeOrDiameter($event)" error
                        :label="$t('SSI.Size_Diameter_meter')"
                        :rules="[{ required: false, message: $t('SSI.size_diameter_enter_tips') }]" class="GenInfo"
                        disabled />
                </van-cell-group>
                <van-cell-group class="cell" inset style=" display: flex; align-items: center; padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableLength_" size="16px" disabled />
                    <van-field v-model="Length_" @update:model-value="SSI_.setLength($event)" error
                        :label="$t('SSI.Length')" :rules="[{ required: false, message: $t('SSI.length_enter_tips') }]"
                        class="GenInfo" disabled />
                </van-cell-group>
                <van-cell-group inset style="margin-bottom: 5px;">
                    <van-field v-model="Remarks_" @update:model-value="SSI_.setRemarks($event)" clearable rows="3"
                        autosize :label="$t('SSI.Remarks')" type="textarea" input-align="left"
                        :rules="[{ required: false, message: $t('UAUC.description_of_the_problem_unit_input_tips') }]"
                        label-align="left" disabled class="GenInfo" />
                </van-cell-group>
                <p style="font-size:16px; font-weight:700; padding:5px 15px;">{{ $t('SSI.InspectionChecklist') }}</p>
                <!-- //^ 检查清单 start-->
                <van-tabs v-model:active="active1" style="padding: 0 17px 0 17px;" class="swipper_" swipeable>
                    <van-tab v-for="(tab, index) in InChecklist" :key="index" :title="tab.title" class="tabActive">
                        <van-cell-group class="cell" inset style="margin: 5px 0 5px 0; "
                            v-for="(item, idx) in tab.content" :key="idx">
                            <!-- //^ 可编辑 -->
                            <div style="display:flex;align-items: center; position: relative;"
                                v-if='route.query.next === "await" && !route.query?.stepName?.includes("Repair Equipment/Tool")'>
                                <!-- <p>1111</p> -->
                                <van-field v-model="item.text" is-link required readonly
                                    :rules="[{ required: true, message: $t('Form.PleaseSelect') }]"
                                    :label="(idx + 1).toString().padStart(2, '0') + ' ' + item.ItemName"
                                    input-align="left" label-align="left" class="Guard" :id="'item-' + item.ItemId"
                                    @click='openCheckPicker(item.ItemId)'>
                                </van-field>
                                <van-icon name="info-o" class="msgIcon"
                                    @click="ShowPrompt(item.ItemBrief, item.ItemName)" />
                            </div>
                            <!-- //^ 可编辑 -->
                            <!-- v-if='RouteNoAwait' -->
                            <!-- //^ 只读 -->
                            <div style="display:flex;align-items: center; position: relative;" v-else>
                                <!-- <p>2222</p> -->
                                <van-field v-model="item.text" readonly
                                    :label="(idx + 1).toString().padStart(2, '0') + ' ' + item.ItemName"
                                    input-align="left" label-align="left" class="Guard" :id="'item-' + item.ItemId"
                                    disabled>
                                </van-field>
                                <van-icon name="info-o" class="msgIcon"
                                    @click="ShowPrompt(item.ItemBrief, item.ItemName)" />
                            </div>
                            <!-- //^ 只读 -->
                            <!-- remarks 可填 -->
                            <div
                                v-if='route.query.next === "await" && !route.query?.stepName?.includes("Repair Equipment/Tool") && item.Status == 3'>
                                <van-field :label="$t('SSI.Remarks')" required v-model="item.Remarks"
                                    :placeholder="$t('SSI.explain_the_reason_tips')" label-align="top" clearable
                                    class="required_" :rules="[{ required: true }]" rows="2" autosize />
                            </div>
                            <div
                                v-else-if='route.query.next === "await" && !route.query?.stepName?.includes("Repair Equipment/Tool") && item.Status == 2'>
                                <van-field :label="$t('SSI.Remarks')" required v-model="item.Remarks"
                                    :placeholder="$t('SSI.explain_the_reason_tips')" label-align="top" clearable
                                    class="required_" :rules="[{ required: true }]" rows="2" autosize />
                            </div>
                            <!-- remarks 可填 -->

                            <!-- remarks 只读 -->
                            <div v-else-if='route.query?.stepName?.includes("Repair Equipment/Tool")'>
                                <!-- <p>777</p> -->
                                <van-field :label="$t('SSI.Remarks')" v-show="item.Status == 3 || item.Status == 2"
                                    v-model="item.Remarks" label-align="top" class="required_" rows="2" autosize
                                    disabled />
                            </div>
                            <div v-else-if='RouteNoAwait'>
                                <van-field :label="$t('SSI.Remarks')" v-show="item.Status == 3 || item.Status == 2"
                                    v-model="item.Remarks" label-align="top" class="required_" rows="2" autosize
                                    disabled />
                            </div>
                            <!-- remarks 只读 -->
                        </van-cell-group>
                    </van-tab>
                </van-tabs>
                <!-- //^ 检查清单 end-->
                <!-- //^ 评估结果 EvaluationResult start-->
                <van-cell-group inset style=" display: flex; align-items: center;margin-bottom: 5px; ">
                    <van-field v-model='EvaluationResultValue_' error :label="$t('SSI.EvaluationResult')" disabled
                        class="Guard01"
                        :rules="[{ required: !EvaluationResultValue_ ? false : true, message: $t('SSI.Checklist_Assessment_Results_tips') }]" />
                </van-cell-group>
                <!-- //^ 评估结果 EvaluationResult end-->

                <!-- 日期检查 is-link-->
                <van-cell-group inset style=" display: flex; align-items: center; margin-bottom: 5px;">
                    <van-field v-model="DateOfInspection" readonly name="datePicker" error
                        :label="$t('SSI.DateOfInspection')" input-align="left" label-align="left" disabled
                        class="Guard02" />
                </van-cell-group>
                <!-- 日期检查 -->
                <!-- //^ 条形码 Barcode No -->
                <van-cell-group inset style="margin-bottom: 5px;"
                    v-if="route.query?.next === 'await' && EvaluationResultValue !== '' && EvaluationResultValue === 'Pass' && !BarCodeShow">
                    <van-field v-model="BarCode_" error :label="$t('SSI.BarcodeNo')" required readonly
                        :rules="[{ required: true, message: $t('SSI.scan_barcode_tips') }]" right-icon="qr"
                        @click="scan__" class="Guard03" />
                </van-cell-group>
                <!-- <p>symbol_: {{ symbol_ }}</p> -->
                <!-- <section>页面读取值 PageLoadCheckValue === {{ PageLoadCheckValue }}</section> -->
                <!-- <section>页面值(中，英) --- EvaluationResultValue_ === {{ EvaluationResultValue_ }}</section>-->
                <!-- <section>提交值 EvaluationResultValue === {{ EvaluationResultValue }}</section> -->
                <div class="mb"
                    v-if="route.query?.next === 'await' && EvaluationResultValue !== '' && EvaluationResultValue === 'Pass' && !BarCodeShow">
                    <!-- <div id="reader" width="600px"></div> -->
                    <div id="interactive" class="viewport" v-show="symbol_">
                        <canvas class="drawingBuffer"></canvas>
                        <video autoplay="true" preload="auto" src="" muted="true" playsinline="true"></video>
                    </div>
                </div>
                <!-- //^ 条形码 Barcode No -->
                <!-- //^ 上传附件 start -->
                <div style="padding: 0 25px 0 10px; margin-bottom:5px; position:relative;border-radius: 8px; left:7px;"
                    v-if="PageLoadCheckValue && PageLoadCheckValue !== 'Pass' && PageLoadCheckValue === 'Rejected' || PageLoadCheckValue && PageLoadCheckValue !== 'Pass' && PageLoadCheckValue === 'Pending'">
                    <div style="display:flex; align-items: center;"
                        v-if='route.query.next === "await" && !route.query?.stepName?.includes("Repair Equipment/Tool")'>
                        <van-field v-model="attachment_" :label="$t('SSI.InspectionAttachment')" readonly class="line_"
                            input-align="left" label-align="left" disabled required
                            :rules="[{ required: true, message: $t('SSI.upload_attachment_tips') }]" />
                        <div class="add_upload">
                            <img :src="upload" alt="" class="icon_">
                            <!-- accept=".jpg,.jpeg,.png,.svg,.gif,.pdf,.ppt,.pptx,.doc,.docx,.xls,.xlsx,.txt" -->
                            <input id="UploadPictures" type="file" accept="image/*" class="add_upload_file"
                                @change="(e) => fileUpload(e)" />
                        </div>
                    </div>
                    <div style="display:flex; align-items: center;" v-if='RouteNoAwait'>
                        <van-field v-model="attachment_" :label="$t('SSI.InspectionAttachment')" readonly class="line_"
                            input-align="left" label-align="left" disabled :rules='[{ required: false }]' />
                        <div class="add_upload">
                            <img :src="upload" alt="" class="icon_">
                        </div>
                    </div>
                    <div style="display:flex; align-items: center;"
                        v-if='route.query?.stepName?.includes("Repair Equipment/Tool")'>
                        <van-field v-model="attachment_" :label="$t('SSI.InspectionAttachment')" readonly class="line_"
                            input-align="left" label-align="left" disabled :rules='[{ required: false }]' />
                        <div class="add_upload">
                            <img :src="upload" alt="" class="icon_">
                        </div>
                    </div>
                    <!-- 第一次为上传 -->
                    <div class="add_upload_imgBox" v-if="fileList.length">
                        <div class="add_upload_imgDiv" v-for="(item, index) in fileList" :key="item.id + index">
                            <div style="display:flex;">
                                <div class="imgBox">
                                    <img v-if="item.base64.includes('image')" :src="item.base64"
                                        @click="fileListShow = true">
                                    <img v-else :src="file_" @click="previewFile(3, item)">
                                    <van-image-preview v-model:show="fileListShow"
                                        :images="imgList"></van-image-preview>
                                </div>
                                <div style="margin-left:5px; margin-bottom: 15px;font-size: 13px; color: #00a0e1;">{{
                                    item.formattedName }}</div>
                            </div>
                            <div class="add_upload_close" @click="ProcesseDeleteImage(item.id, 1, item.fileName)">
                                <van-icon name="cross" style="color:#828282;" />
                            </div>
                        </div>
                    </div>
                    <!-- 第二次为读取 -->
                    <div class="add_upload_imgBox" v-if="FileList.length">
                        <div class="add_upload_imgDiv" v-for="(item, idx) in FileList" :key="idx">
                            <div style="display:flex;">
                                <div class="imgBox">
                                    <img v-if="item.fileType.includes('image')" :src="item.fileURL"
                                        @click="FileListShow = true">
                                    <img v-else :src="file_" @click="previewFile(4, item)">
                                    <van-image-preview v-model:show="FileListShow"
                                        :images="FileImgList"></van-image-preview>
                                </div>
                                <div style="margin-left:5px; margin-bottom: 15px; font-size: 13px; color: #00a0e1;">{{
                                    item.formattedName }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 第三次读取后可编辑上传 -->
                    <div class="add_upload_imgBox" v-if="FileList2.length">
                        <div class="add_upload_imgDiv" v-for="(item, index) in FileList2" :key="index">
                            <div style="display:flex;">
                                <div class="imgBox">
                                    <img v-if="item.fileType.includes('image')" :src="item.fileURL"
                                        @click="FileList2Show = true">
                                    <img v-else :src="file_" @click="previewFile(3, item)">
                                    <van-image-preview v-model:show="FileList2Show"
                                        :images="FileImgList2"></van-image-preview>
                                </div>
                                <div style="margin-left:5px; margin-bottom: 15px;font-size: 13px; color: #00a0e1;">{{
                                    item.formattedName }}</div>
                            </div>
                            <div class="add_upload_close" @click="ProcesseDeleteImage(item.fileId, 3, item.fileName)">
                                <van-icon name="cross" style="color:#828282;" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- //^ 上传附件 end-->
                <!-- //^ 审批历史 -->
                <van-cell-group inset style="padding: 10px 5px; margin-bottom: 120px;">
                    <p style="margin: 5px 15px 15px 8px; font-size:16px; font-weight: 700; padding: 5px;">
                        {{ $t('Form.HistoryOfApproval') }}
                    </p>
                    <van-steps direction="vertical" :active="-1">
                        <van-step v-for="(item, idx) in historyData" :key="idx">
                            <div
                                style="display: flex; justify-content: space-between; font-size: var(--van-step-font-size); width:100%;">
                                <div style="height: auto; width:85%;">
                                    <p style="font-size: 12px;" v-if="item?.Sign"
                                        :class="[item?.SelAction === 'Running' ? 'blue' : 'pure']">
                                        <span v-if="item?.StepName !== 'End'">[</span>
                                        {{ Array.isArray(item?.Sign) ? item?.Sign.join(', ') :
                                            item?.Sign }}
                                        <span v-if="item?.StepName !== 'End'">]</span> {{
                                            $t('SSI.' + item?.StepName) }}
                                    </p>
                                    <p style="font-size: 12px;">{{ item?.FinishAt ? new
                                        Intl.DateTimeFormat(navigator?.language, options_).format(new
                                            Date(item?.FinishAt + 'Z')) : '' }}</p>
                                </div>
                                <div style="font-size: 14px; margin-top: 7px; text-align:right;"
                                    :class="[item?.SelAction === 'Running' ? 'blue' : 'pure']">
                                    {{ $t('SSI.' + item?.SelAction) }} </div>
                            </div>
                        </van-step>
                    </van-steps>
                </van-cell-group>
                <!-- //^ 审批历史 -->
                <!-- 选择的结果类型 rejected pedding pass not a... -->
                <van-popup v-model:show="checkPicker" destroy-on-close round position="bottom">
                    <van-picker :model-value="CheckPickerValue" :columns="CheckColumns" @cancel="checkPicker = false"
                        @confirm="CheckConfirm" disabled />
                </van-popup>


                <!-- //* 牵引线 ------------------------------------------------------------------------------------------------>
                <van-popup v-model:show="isShowPrompt_" position="bottom" :style="{ height: '30%' }" closeable>
                    <div class="ShowPromptMsg">
                        <div style="text-align:center;margin-bottom: 10px">
                            {{ $t('SSI.OperatorName') }}
                        </div>
                        <div>{{ $t('SSI.Supplier_User_Role_Required_Tips') }}</div>
                    </div>
                </van-popup>
                <van-popup v-model:show="isShowPrompt" position="bottom" :style="{ height: '30%' }" closeable>
                    <div class="ShowPromptMsg2">
                        <div
                            style="text-align: center; margin-top: 10px; margin-bottom: 20px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                            {{ ShowPromptLabel }}
                        </div>
                        {{ ShowPromptMsg }}
                    </div>
                </van-popup>


                <!-- 日历 -->
                <van-popup v-model:show="showTimePicker" position="bottom">
                    <van-date-picker @confirm="timeConfirm" @cancel="showTimePicker = false" type="date"
                        :min-date="minDate2" :max-date="maxDate2" />
                </van-popup>
                <!-- 日历 -->

                <van-popup v-model:show="CommentsShow" position="top" :style="{ height: '20%' }">
                    <div
                        style="display:flex; justify-content: space-between; align-items: center; padding: 9px 7px; border-bottom: 1px solid #e8e8e8;">
                        <van-button size="small" block native-type="submit" @click="CommentsShow = false"
                            style="width:15%">
                            {{ $t('Global.Cancel') }}
                        </van-button>
                        <van-button size="small" block native-type="submit" :loading="Loading" type="primary"
                            @click="CheckSubmit" style="width:15%">
                            {{ $t('Global.Confirm') }}
                        </van-button>
                    </div>
                    <van-cell-group inset>
                        <van-field v-model="Comments" rows="4" autosize type="textarea"
                            :placeholder="$t('SSI.approval_opinion_enter_tips')" clearable />
                    </van-cell-group>
                </van-popup>

                <div class="foot" :style="{ justifyContent: RouteNoAwait ? 'flex-end' : 'space-between' }">
                    <van-button class="subBtn" size="mini" block native-type="submit" type="primary"
                        @click="SubmitTRInspection" v-if='route.query?.stepName?.includes("Repair Equipment/Tool")'>
                        {{ $t('SSI.Submit to Re-inspection') }}
                    </van-button>
                    <van-button class="subBtn" size="mini" block native-type="submit" type="primary"
                        @click="CommentsShow_"
                        v-if='route.query.next === "await" && !route.query?.stepName?.includes("Repair Equipment/Tool")'>
                        {{ $t('Form.Submit') }}
                    </van-button>
                    <van-button size="small" icon="chat-o" type="primary" @click="markRead"
                        v-if="route.query.next === 'notify'" style="margin-right:3px;">
                        {{ $t('Work.Read') }}
                    </van-button>



                    <!-- <van-button class="subbtn-text" size="mini" block native-type="submit"
                        :loading="Loading" disabled>
                        {{ $t('Form.Retrieve') }}
                    </van-button> -->
                    <!-- 撤销 -->

                    <!-- <van-button class="subbtn-text" size="mini" block native-type="submit" icon="revoke"
                        @click="showCancel_" :loading="Cancel_Loading">
                        {{ $t('Global.Cancel') }}
                    </van-button> -->


                    <!-- <van-config-provider> -->
                    <!-- <van-space> -->
                    <van-button class="more" icon="arrow-up" plain size="small" type="primary" @click="More">
                        {{ $t("Form.More") }}
                    </van-button>
                    <van-popup v-model:show="moreShow" position="bottom" :style="{ height: '30%' }" closeable>
                        <div class="NotifyEntrust"
                            :style="{ justifyContent: route.query.next === 'apply' && route.query.next === 'complete' ? 'flex-start' : 'center' }">

                            <van-button size="small" icon="share-o" plain @click="onBatchTransfer" style="width: 28%; "
                                v-if="route.query?.next !== 'complete' && route.query?.next !== 'notify' && route.query?.next !== 'apply'">{{
                                    $t('Form.Entrust') }}</van-button>
                            <van-button size="small" icon="chat-o" plain @click="onBatchInform"
                                style="width: 28%;margin-left: 20px;">{{
                                    $t('Form.Notify')
                                }}</van-button>
                        </div>
                    </van-popup>
                    <!-- </van-space> -->
                    <!-- </van-config-provider> -->
                </div>

                <!-- showMsg submit 弹框，后确认 提交接口 -->
                <van-dialog v-model:show="showCancel" :title="$t('Global.Cancel')" show-cancel-button
                    @confirm="Cancel_(CancelValue)">
                    <van-cell-group inset>
                        <van-field class="subbtn-text" v-model="CancelValue" :label="$t('Form.Reason_for_revocation')"
                            :placeholder="$t('Form.Reason_for_revocation_tips')" />
                    </van-cell-group>
                </van-dialog>

                <!-- 知会 -->
                <van-dialog v-model:show="showInform" :title="$t('Form.Notify')" @confirm="infoConfirm"
                    :before-close="() => false" @cancel="InfoCancel" show-cancel-button>
                    <div class="dialog-div">
                        <van-field v-model="notifyData.users" readonly :label="$t('Form.SelectUser')"
                            :placeholder="$t('Form.Select_Username_tips')" @click="onUserClick" />
                        <van-field v-model="notifyData.message" rows="1" autosize :label="$t('Form.NotifyOpinion')"
                            type="textarea" :placeholder="$t('Form.NotifyOpinion_tips')" />
                    </div>
                </van-dialog>
                <YZUserSelect v-model:show="userShow" :multiple="true" @on-save="onUserSave" />
                <!-- 知会 -->
                <!-- 委托 -->
                <van-dialog v-model:show="weiTuoShow" :title="$t('Form.Task_delegation')" @confirm="EntrustConfirm"
                    :before-close="() => false" @cancel="EntrustCancel" show-cancel-button>
                    <div class="dialog-div">
                        <van-field v-model="wtData.users" readonly :label="$t('Form.EntrustedTo')"
                            :placeholder="$t('Form.Select_Username_tips')" @click="wtUserClick" />
                        <van-field v-model="wtData.message" rows="1" autosize :label="$t('Form.EntrustedOpinion')"
                            type="textarea" :placeholder="$t('Form.Entrusted_opinion_tips')" />
                    </div>
                </van-dialog>
                <YZUserSelect v-model:show="wtUserShow" :multiple="false" @on-save="WtUserSave" />
                <!-- 委托 -->
            </van-form>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import YZUserSelect from '@/components/YZUserSelect.vue';
import { onMounted, ref, reactive, watch, onUnmounted, onBeforeUnmount, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { CheckSubmission, OwnerPosition } from '@/service/user'
import { SSI } from '@/store/ssi'
import { useFormData, useLang, useParamsFormatter } from '@/utils';
import { usePostBody, useGetQuery } from '@/utils/request'
import { showNotify } from 'vant';
import heic2any from 'heic2any'
import Compressor from 'compressorjs';
import { url } from '@/api/url';
import { useForm } from "@/logic/forms";
import upload from '@/assets/imgs/upload.png'
import file_ from '@/assets/imgs/file_.png'
import { Html5QrcodeScanner, Html5Qrcode } from "html5-qrcode";
import Quagga from 'quagga';
import { ProcessService } from '@/logic/forms/processHelper';
import { useLoingStore } from '@/store/login';
import { useCore } from '@/store/core';
import store from '@/store';
import Pdfh5 from 'pdfh5';
import 'pdfh5/css/pdfh5.css';
import axios from 'axios'


const coreStore = useCore(store)
const loginStore = useLoingStore(store)
const state = reactive({
    html5QrCode: null
})
const symbol_ = ref('')
const SSI_ = SSI()
const router = useRouter()
const route = useRoute()
const EntryDate_ = ref()
const EntryDatePicker = ref(false)
const minDate = new Date(new Date().setMonth(new Date().getMonth()))
const maxDate = new Date(new Date().setMonth(new Date().getMonth() + 6))
const minDate2 = new Date(new Date().setMonth(new Date().getMonth()))
const maxDate2 = new Date(new Date().setMonth(new Date().getMonth() + 6))
const ProjectName_ = ref('')
const ProjectNo_ = ref('')
const Contractor_ = ref('')
const ContractorCode_ = ref('')
const columns = ref([])
const CheckColumns = ref([])
const TemplateID_ = ref(SSI_.TemplateID_ || '');
// const TemplateID = ref(SSI_.TemplateID || '');
const pickerValue = ref([]);
const Manufacture_ = ref(SSI_.Manufacture_ || '')
const DisplayName = ref('')
const currentUser = ref('')
const RouteAwait = ref()
const RouteNoAwait = ref()
const CalibrationExpiryDate_ = ref('')
const showTimePicker = ref(false)
const ApplicableCalibrationExpiryDate_ = ref(true);
const ApplicableOperatorName_ = ref(true);
const ApplicableModel_ = ref(true);
const ApplicablePlateNo_ = ref(true);
const ApplicableSWLOrCapacity_ = ref(true);
const ApplicableSizeOrDiameter_ = ref(true);
const ApplicableLength_ = ref(true);
const Loading = ref(false)
const Model_ = ref(SSI_.Model_ || '') //型号
const PlateNo_ = ref(SSI_.PlateNo_ || '') //车牌号
const SWLOrCapacity_ = ref(SSI_.SWLOrCapacity_ || '') //SWL / 容量
const SizeOrDiameter_ = ref(SSI_.SizeOrDiameter_ || '') //尺寸 /直径
const Length_ = ref(SSI_.Length_ || '') //模型
const Remarks_ = ref(SSI_.Remarks_ || '') // 备注
const SerialNo_ = ref(SSI_.SerialNo_ || '') // 序列号
const EquipmentStatus = ref(0)
const EquipmentStatusValue = ref('')
const EvaluationResult = ref('')
const EvaluationResultValue = ref('')
const EvaluationResultValue_ = ref('')
const OperatorName_ = ref('') //操作员
const OperatorAccount_ = ref('') //操作员邮箱
const CertificateOrOtherAttachment_ = ref('') //文件 图片
const CertificateOrOtherAttachment_2 = ref('')
const fileList2 = ref([]) //证书文档
const MAX_SIZE = ref(2 * 1024 * 1024)  // 最大2MB
const fileIds = ref(SSI_.FileIds_ || [])
const OperatorAttachment_ = ref('') //ref(SSI_.OperatorAttachment_ || '') //文件 图片
const OperatorAttachment_2 = ref('')
const fileList3 = ref([]) //证书卡
const fileList = ref(SSI_.FileList || []) //检查附件
const MAX_SIZE_2 = ref(2 * 1024 * 1024)  // 最大2MB
const showCancel = ref(false)
const CancelValue = ref('')
const ComImgListShow = ref(false)
const CerListShow = ref(false)
const fileListShow = ref(false) //上传
const FileListShow = ref(false) //读取
const FileList2Show = ref(false) //读取+编辑上传
const imgList = ref(SSI_.ImgList || [])
const CerList = ref([])
const CerImgList = ref([])
const ComList = ref([])
const ComImgList = ref([])
const FileList = ref([])
const FileImgList = ref([])
const FileList2 = ref([])
const FileImgList2 = ref([])
const acceptImg = ref(['.jpg', '.jpeg', '.png', '.svg', '.gif'])

const options = ref({
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    timeZoneName: "short"
})
const options_ = ref({
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
})
const DateOfInspection = ref(new Intl.DateTimeFormat(navigator?.language, options_.value).format(Date.parse(new Date())))
const attachment = ref('')
const attachment_ = ref('')
const awaitAttachment = ref('')
const FileType = ref('')
const BarCode_ = ref('')
const Comments = ref('')

const focusLoading = ref(false)
const currentStream = ref(null)
const currentTrack = ref(null)


const EntryDateConfirm = (time) => {
    EntryDate_.value = time.selectedValues?.join('/')
    EntryDatePicker.value = false
}

const timeConfirm = (time) => {
    CalibrationExpiryDate_.value = time.selectedValues?.join('/')
    showTimePicker.value = false
}
const getCamerasInfo = () => {
    Html5Qrcode.getCameras()
        .then((devices) => {
            if (devices && devices.length) {
                state.html5QrCode = new Html5Qrcode("reader");
                startScan();
                window.scroll({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                });
            }
        })
        .catch((err) => {
            console.log("%c [ 请设置摄像头访问权限 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
            showNotify({ message: useLang('SSI.Camera_permission_tips'), type: 'danger', duration: 2000 }) //useLang('SSI.Camera_permission_tips')
        });
};
const startScan = () => {
    state.html5QrCode
        .start(
            { facingMode: "environment" },  //environment 后置摄像头 || user前置摄像头
            {
                // focusMode: 'continuous', //设置连续聚焦模式
                fps: 3,  //设置扫码识别速度
                qrbox: { width: 250, height: 150 },
            },
            async (decodedText, decodedResult) => {
                if (decodedText) {
                    symbol_.value = 'in'
                    BarCode_.value = decodedText
                    console.log("%c [ BarCode_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", BarCode_.value)
                    stopScan()
                } else {
                    console.log('no decodedText')
                }
            }
        )
        .catch((err) => {
            symbol_.value = '';
            console.log("扫码失败===>>>>>", err);
        });
};


const stopScan = () => {
    state.html5QrCode
        .stop()
        .then((a) => {
            console.log("已停止扫码", a);
        })
        .catch((err) => {
            console.log("%c [ 停止失败 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
        });
};

//不断检测条形码位置，为图像 / 视频每帧不断进行调用
Quagga.onProcessed(result => {
    // 绘制实际检测的捕获框框
    const drawingCtx = Quagga.canvas.ctx.overlay;
    const drawingCanvas = Quagga.canvas.dom.overlay;
    // console.log("%c [ drawingCanvas ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", drawingCanvas)
    // 下面会实时绘制绿色框、蓝色框、红色线三种图形，你可以对照下方的GIF图进行对比
    if (result) {
        // 绿色框
        if (result.boxes) {
            drawingCtx.clearRect(0, 0, parseInt(drawingCanvas.getAttribute("width")), parseInt(drawingCanvas.getAttribute("height")));
            result.boxes.filter(box => {
                return box !== result.box;
            }).forEach(box => {
                Quagga.ImageDebug.drawPath(box, { x: 0, y: 1 }, drawingCtx, { color: "green", lineWidth: 2 });
            });
        }
        // 蓝色框
        if (result.box) {
            Quagga.ImageDebug.drawPath(result.box, { x: 0, y: 1 }, drawingCtx, { color: "blue", lineWidth: 2 });
        }
        // 红色线
        if (result.codeResult && result.codeResult.code) {
            Quagga.ImageDebug.drawPath(result.line, { x: 'x', y: 'y' }, drawingCtx, { color: 'red', lineWidth: 3 });
        }
    }
});


// 正确定位并解码到条形码时触发
Quagga.onDetected(result => {
    const code = result.codeResult.code;
    console.log("%c [ -- Barcode ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", code)
    if (BarCode_.value !== code) {
        BarCode_.value = code;
        Quagga.pause(); // 暂停扫描
    }
});

const scanner = () => {
    Quagga.init(
        {
            inputStream: {
                type: "LiveStream",
                target: document.querySelector(".viewport"),
                constraints: {
                    facingMode: "environment",
                    focusMode: "continuous",    // 连续自动对焦
                    aspectRatio: { ideal: 16 / 9 },
                    // frame: { ideal: 10 }
                },
                // exposureMode: "continuous", // 持续曝光模式
                // exposureCompensation: 0.5, // 曝光补偿，根据需要调整
                // // 关键参数：降低图像亮度敏感度，减少反光干扰
                // brightness: -5,
                // contrast: 50,
                // saturation: 20,
                // // 降噪处理
                // noiseReduction: "high"
            },
            // 解码器
            decoder: {
                readers: ["code_128_reader", "code_39_vin_reader"], //解码器类型:
                // "code_128_reader",    // Code 128 (常见于物流)
                // "ean_reader",         // EAN-13 (商品条码)
                // "ean_8_reader",       // EAN-8 (短商品条码)
                // "code_39_reader",     // Code 39
                // "code_39_vin_reader", // Code 39 车辆识别码
                // "codabar_reader",     // Codabar
                // "upc_reader",         // UPC-A
                // "upc_e_reader",       // UPC-E
                // "qr_code_reader"      // QR码（需额外配置）
            },
            // frequency: 15,            // 每秒处理10帧（默认值为15）
            // numOfWorkers: 2,           // 根据设备性能调整工作线程数
            // locator: {
            //     patchSize: "medium",    // 可选项：small/medium/large
            //     halfSample: true,       // 半采样提高性能和准确率
            //     debug: {
            //         showCanvas: true,     // 显示定位画布（调试用）
            //         showPatches: true,    // 显示定位斑块
            //         showFoundPatches: true
            //     }
            // },
            // locate: true, // 定位条码位置
        },
        (err) => {
            if (err) {
                console.log("%c [ 请设置摄像头访问权限 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
                showNotify({ message: useLang('SSI.Camera_permission_tips'), type: 'danger', duration: 2000 })
                symbol_.value = '';
                return;
            };
            Quagga.start();
        }
    );
}
const scan__ = () => {
    symbol_.value = 'in';
    scanner();

    // getCamerasInfo();
}

watch(
    () => EvaluationResultValue.value,
    (newValue) => {
        if (newValue !== 'Pass') {
            symbol_.value = '';
        }
    },
    {
        immediate: true,
        deep: true
    }
);


const uploadFileApi = async (result_img, name, formattedName, id, base64) => {
    let formData = new window.FormData()
    formData.append('fileToUpload', result_img)
    console.log("%c [ 最终 上传的文件 result_img ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result_img)
    await usePostBody(url.form.updateAttach, {}, formData, true)
        .then(result => {
            console.log("%c [upload img result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)
            if (result && result?.length > 0) {
                result_img.status = 'done'
                result_img.message = 'Upload completed'
                fileList.value.push({ id, base64, formattedName, fileName: name, fileId: result[0].fileId, ext: result[0].ext })  // 生成图片的 URL 并展示预览            
                SSI_.setFileList_([...fileList.value])
                if (base64.includes('image')) {
                    imgList.value.push(base64)
                    SSI_.setImgList_([...imgList.value])
                }
                console.log("%c [ img preview src地址 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", imgList.value)

                console.log("%c [ fileList --- 检查附件 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileList.value)
                fileIds.value.push({ id: result[0].fileId, name })
                if (attachment.value !== '') {
                    const uniqueArr = [...setFileIds.value, ...fileIds.value]
                    fileIds.value = uniqueArr.reduce((acc, current) => {
                        const isDuplicate = acc.some(item =>
                            item.id === current.id && item.name === current.name
                        );
                        if (!isDuplicate) {
                            acc.push(current);
                        }
                        return acc;
                    }, []);
                }
                SSI_.setFileIds__([...fileIds.value])
                console.log("%c [ fileIds.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileIds.value)
                const newFileIds = fileIds.value.map(item => item.id).join(',')
                attachment.value = newFileIds
                attachment_.value = ' ' //假值
                console.log("%c [ 上传的附件---attachment.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", attachment.value)
            } else {
                result_img.status = 'failed';
                result_img.message = '上传失败';
            }
        }).catch(error => {
            result_img.status = 'failed';
            result_img.message = '上传失败';
            console.error('error', error)
        })
}
const fileUpload = async (event) => {
    let file = document.getElementById('UploadPictures');
    let fileName = file.value;
    let files = file.files;
    if (fileName == null || fileName == "") {
        showNotify({ message: useLang('UAUC.select_file_tips'), type: 'danger' })
    } else {
        let fileType = fileName.substr(fileName.length - 4, fileName.length);
        let uploadFile = files[0]
        if (fileType) {
            if (uploadFile) {
                const name = uploadFile.name
                const prefix = name.substring(0, 7);
                const extension = name.substring(name.lastIndexOf('.'));
                const mainPart = name.substring(name.lastIndexOf('.') - 7, name.lastIndexOf('.'));
                const formattedName = `${prefix}...${mainPart}${extension}`;
                let reader = new FileReader();
                reader.onload = function (e) {
                    uploadFileApi(uploadFile, uploadFile.name, formattedName, new Date().valueOf(), e.target.result)
                }
                reader.readAsDataURL(uploadFile);

            } else {
                showNotify({ message: useLang('UAUC.select_file_tips'), type: 'danger' })
            }
        } else {
            showNotify({ message: useLang('UAUC.WrongFileTypeUploaded'), type: 'danger' })
        }
    }
}
// mergedFiles 
// ext: ".xlsx"
// fileId: "Pf8as6S5.670"
// fileName: "template+(56) (1).xlsx"
// fileType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
// fileURL: "http://localhost:5173/api/bpm/attachment/file/Pf8as6S5.670"
// formattedName: "temp...(1).xlsx"

const previewFile = (n, item) => {
    console.log("%c [ previewFile --- fileName ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item.fileName)
    router.push({
        path: '/fileRead',
        query: { n, type: item.ext?.replace('.', ''), fileId: item.fileId, formattedName: item.formattedName, next: 'ssi_apply', query: JSON.stringify(route.query) },
        replace: true
    })
}

const ProcesseDeleteImage = (id, n, name) => {
    console.log("%c [ name ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", name)
    if (n === 1) {
        for (let i = 0; i < fileList.value.length; i += 1) {
            if (fileList.value[i].id === id) {
                fileList.value.splice(i, 1);
                break;
            }
        }
        imgList.value.forEach((item, index) => {
            imgList.value.splice(index, 1)
        })
        SSI_.setFileList_([...fileList.value])
        SSI_.setImgList_([...imgList.value])
    } else {
        for (let i = 0; i < FileList2.value.length; i += 1) {
            if (FileList2.value[i].fileId === id) {
                FileList2.value.splice(i, 1);
                break;
            }
        }
        console.log("%c [ FileList2.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", FileList2.value)
        FileImgList2.value.forEach((item, index) => {
            FileImgList2.value.splice(index, 1)
        })
        SSI_.setFileList_2_([...FileList2.value])
        SSI_.setImgList_2_([...FileImgList2.value])
    }
    console.log("%c [ fileIds.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileIds.value)

    fileIds.value = fileIds.value?.filter(item => item.name !== name)
    SSI_.setFileIds_([...fileIds.value])
    const newFileIds = fileIds.value?.map(item => item.id).join(',')
    attachment.value = newFileIds
    attachment_.value = fileIds.value.length ? ' ' : ''
    console.log("%c [ attachment.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", attachment.value)
}

const InChecklist = ref([])
const active1 = ref(0)
const AllNot = ref(false)
const checkEvaluationResult = () => {
    const allNot = InChecklist.value.every(group => group.content.every(item => item.Status == 0))
    console.log("%c [ allNot ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", allNot)
    console.log("%c [ EvaluationResultValue_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", EvaluationResultValue_.value)
    const allPassORNot = InChecklist.value.every(group => group.content.every(item => item.Status === 1 || item.Status == 0))
    if (allNot) {
        console.log('1', 'allNot')
        EquipmentStatus.value = 0;  //^ 新增
        EvaluationResult.value = 0; //^ 新增
        EvaluationResultValue_.value = '';
        EvaluationResultValue.value = ''; //^ 新增
        PageLoadCheckValue.value = ''     //^ 新增
        AllNot.value = true
    } else if (allPassORNot) {
        console.log('2', 'allPassORNot')
        EquipmentStatus.value = 1; // 设备的状态(1, 3, 2)
        EquipmentStatusValue.value = 'Pass' //给接口的值 设备的状态值(Pass, Rejected, Pending)
        console.log("%c [ 1 EquipmentStatus.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", EquipmentStatus.value)
        EvaluationResult.value = 1; //评估结果(1, 3, 2)
        EvaluationResultValue.value = 'Pass'; //评估状态值(Pass, Rejected, Pending)
        EvaluationResultValue_.value = useLang("SSI.Pass");
        PageLoadCheckValue.value = 'Pass'
        AllNot.value = false
    } else {
        console.log('3', 'oth_check--->')
        // 初检/月检/季度检查：
        // 1.所有的检查项结果不能都是Not Applicable，如果用户全选Not Applicable，无法提交，提交时提示Not applicable cannot be selected for all Item Results.
        // 2.检查项结果里除去Not Applicable 只要有1个是Rejected，Evaluation Result就是Rejected
        // 3.检查项结果里除去Not Applicable 只要有1个是Pending，Evaluation Result就是Pending
        // 4.检查项结果里除去Not Applicable ，同时有Rejected和Pending，Evaluation Result就是Rejected。优先级：Rejected>Pending>Pass
        // 5.检查项结果里除去Not Applicable ，其余所有的是Pass，Evaluation Result就是Pass
        // 综上：Not Applicable 不参与计算，但不可以全部都是Not Applicable 
        // 日检里面没有Pending，只有Rejected/Pass/Not Applicable ,计算逻辑同上1，2，5.
        const hasRejected = InChecklist.value.some(group => group.content.some(item => item.Status == 3))
        const hasPending = InChecklist.value.some(group => group.content.some(item => item.Status == 2))
        if (hasRejected) {
            EquipmentStatus.value = 3;
            EquipmentStatusValue.value = 'Rejected';
            EvaluationResult.value = 3;
            EvaluationResultValue.value = 'Rejected';
            EvaluationResultValue_.value = useLang("SSI.Rejected");
            PageLoadCheckValue.value = 'Rejected'
            console.log("%c [ 3 EquipmentStatus.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", EquipmentStatus.value)
        } else if (hasPending) {
            // 只有在没有Rejected的情况下，才设置为Pending
            EquipmentStatus.value = 2;
            EquipmentStatusValue.value = 'Pending';
            EvaluationResult.value = 2;
            EvaluationResultValue.value = 'Pending';
            EvaluationResultValue_.value = useLang("SSI.Pending");
            PageLoadCheckValue.value = 'Pending'
            console.log("%c [ 2 EquipmentStatus.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", EquipmentStatus.value)
        }
        AllNot.value = false
    }
}
const checkRemarks = () => {
    console.log("%c [ InChecklist.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", InChecklist.value)
    const ie = InChecklist.value.every(group =>
        group.content?.every(item =>
            item.Status == 1 || item.Status == 0 || (item.Status != 1 && item.Remarks)
        )
    );
    const AllChecksHaveValues = InChecklist.value.every(item => item.content?.every(item_ => item_.StatusValue !== ""))
    return ie && AllChecksHaveValues
}

const CommentsShow = ref(false)
const AllChecksHaveValues = ref(false)
const CommentsShow_ = () => {
    console.log("%c [ CommentsShow_ --- EvaluationResult.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", EvaluationResult.value)
    console.log("%c [ CommentsShow_ --- attachment.value  ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", attachment.value)
    console.log("%c [ CommentsShow_ --- BarCode_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", BarCode_.value)
    const isCheckRemarks = checkRemarks()
    console.log("%c [ 1111 isCheckRemarks ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", isCheckRemarks)
    active1.value = active1.value + 1
    if (EquipmentStatus.value === 1 && BarCode_.value !== '' && isCheckRemarks) {
        FileList2.value = []
        FileImgList2.value = []
        attachment_.value = ''
        attachment.value = ''
        CommentsShow.value = true
    } else if (EquipmentStatus.value !== 1 && attachment.value !== '' && isCheckRemarks) {
        BarCode_.value = ''
        CommentsShow.value = true
    } else if (AllNot.value) {
        showNotify({ message: useLang("SSI.no_applicable_Tips"), type: 'danger', duration: 1500, })
    } else {
        console.log("%c [ 检查InChecklist Remarks未通过 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", InChecklist.value)
        return false
    }
}
const isShowPrompt_ = ref(false)
const ShowPrompt_ = () => {
    isShowPrompt_.value = true
}
const PlanId_ = ref('')
const DetectionType = ref('')
const CheckSubmit = async () => {
    try {
        const params = {
            TaskID: viewModel_Data.value.TaskID,// (string) - TaskID
            ItemId: viewModel_Data.value.ItemId, // (string) - ItemId
            PlanId: DetectionType.value === 'preliminary' ? '' : PlanId_.value,
            FormNo: viewModel_Data.value.FormNo, //(string) - FormNo
            EquipmentStatus: EquipmentStatus.value,  //(string) - 设备的状态(1, 3, 2)
            EquipmentStatusValue: EquipmentStatusValue.value,  //(string) - 设备的状态(Pass, Rejected, Pending)
            EvaluationResult: EvaluationResult.value,  //(string) - 评估结果(当检查项都是Pass，结果是Pass;有一个是pending，结果就是pending，有一个是Rejected，结果就是Rejected),(1, 3, 2)
            EvaluationResultValue: EvaluationResultValue.value,  //(string) - 评估结果(Pass, Rejected, Pending)
            InspectionStatus: EvaluationResultValue.value === 'Pass' || EvaluationResultValue.value === 'Rejected' ? 'Closed' : 'Pending Inspection',  //(string) - Inspection form的状态,检查结果是Pass或Rejected填Closed,检查结果是Pending填Pending Inspection
            InspectionAttachment: attachment.value, //附件 图片
            BarCode: BarCode_.value,  //(string) - 条形码编号
            // Group: [ //(array)
            //     {
            //         Name: viewModel_Data.value[0].Name,  //(string) - Group Name
            //         TemplateGroupId: viewModel_Data.value[0].TemplateGroupId,  //(string) - 模版的GroupID
            //         TaskID: viewModel_Data.value[0].TaskID,  //(string) - TaskID
            //         ItemId: viewModel_Data.value[0].ItemId,  //(string) - ItemId
            //         ParentItemId: viewModel_Data.value[0].ParentItemId,  //(string) - 父级ItemId
            //         Item: [
            //             {
            //                 ItemName: "Overall body cleanliness",
            //                 ItemBrief: "General cleanliness, no soil or dirt sticked to the body.",
            //                 StatusValue: "-1", //Pass, Rejected, Pending
            //                 Status: "",  //1，3，2
            //                 Remarks: "",
            //                 TaskID: "652979612786757",
            //                 ItemId: "646985155559495",
            //                 ParentItemId: "652979611476038"
            //             }
            //         ]
            //     }            
            // ],    
            Group: InChecklist.value?.map(group => {
                return {
                    Name: group.title,
                    TemplateGroupId: group.TemplateGroupId,
                    TaskID: group.TaskID,
                    ItemId: group.ItemId,
                    ParentItemId: group.ParentItemId,
                    Item: group.content?.map(item => {
                        return {
                            ItemName: item.ItemName,
                            ItemBrief: item.ItemBrief,
                            StatusValue: item.StatusValue || "",
                            Status: item.Status || "",
                            Remarks: item.Remarks || "",
                            TaskID: item.TaskID,
                            ItemId: item.ItemId,
                            ParentItemId: item.ParentItemId
                        }
                    })
                }
            }),
            Comments: Comments.value,  //(string) - 签核意见
        }
        console.log("%c [ params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        Loading.value = true
        const stepId = route.query?.stepId
        // try {
        //     if (route.query?.processName.includes('SSI-Tool&Equipment Registration')) { // SSI-Equipment Register
        //         DetectionType.value = 'preliminary'
        //         // Reflect.deleteProperty(params, "BarCode");
        //     } else if (route.query?.processName.includes('Daily')) {
        //         DetectionType.value = 'daily'
        //     } else if (route.query?.processName.includes('Monthly')) {
        //         DetectionType.value = 'monthly'
        //     } else if (route.query?.processName.includes('Quarterly')) {
        //         DetectionType.value = 'quarterly'
        //     }
        //     console.log("%c [ DetectionType.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", DetectionType.value)

        //     const subRes = await CheckSubmission(params, DetectionType.value, stepId)
        //     console.log("%c [ subRes ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", subRes)
        //     if (subRes.success || subRes.data.success) {
        //         Loading.value = false
        //         CommentsShow.value = false
        //         showNotify({ message: useLang('UAUC.SubmissionSuccessful'), type: 'success' })
        //         router.back()
        //     } else {
        //         Loading.value = false
        //         CommentsShow.value = false
        //         showNotify({ message: useLang('UAUC.SubmissionFailed'), type: 'danger' })
        //     }
        // } catch (error) {
        //     Loading.value = false
        //     CommentsShow.value = false
        //     showNotify({ message: useLang('UAUC.SubmissionFailed'), type: 'danger' })
        // }
    } catch (error) {
        Loading.value = false
    }
}
const SubmitTRInspection = async () => {
    const postData = {
        "header": {
            "actionName": "Submit to Re-inspection",
            "comments": "",
            "consignEnabled": false,
            "consignUsers": [],
            "consignRoutingType": "Parallel",
            "consignReturnType": "Return",
            "context": {
                "Routing": {}
            }
        },
        "formData": {
            "FormNo": "****************",
            "CreateBy": "<EMAIL>",
            "UpdateTime": "2025-05-26 14:48:52",
            "ProjectNo": "test0925xw",
            "ProjectName": "Office:LED-浦东金桥1",
            "Contractor": "Linde group",
            "ContractorCode": "LD01",
            "TemplateID": "***************",
            "TemplateName": "",
            "EntryDate": "2025-05-26 00:00:00",
            "Manufacture": "11",
            "SerialNo": "222",
            "ContractorHSEAccount": "<EMAIL>",
            "ContractorHSE": "Chunyao Hou",
            "ContractorHSEName": "Chunyao Hou",
            "InspectionRequired": true,
            "EquipmentStatus": "2",
            "EquipmentStatusValue": "Pending",
            "ApplicableCalibrationExpiryDate": true,
            "CertificateOrOtherAttachment": "",
            "ApplicableOperatorName": true,
            "OperatorName": "",
            "OperatorAccount": "",
            "OperatorAttachment": "",
            "ApplicableModel": true,
            "Model": "",
            "ApplicablePlateNo": true,
            "PlateNo": "",
            "ApplicableSWLOrCapacity": true,
            "SWLOrCapacity": "",
            "ApplicableSizeOrDiameter": true,
            "SizeOrDiameter": "",
            "ApplicableLength": true,
            "Length": "",
            "Remarks": "33",
            "EvaluationResult": "2",
            "EvaluationResultValue": "Pending",
            "ItemCheckResult": "2",
            "InspectionType": "Preliminary",
            "InspectionPlanDate": "2025-05-26 00:00:00",
            "InspectionDate": "2025-05-26 00:00:00",
            "Inspector": "<EMAIL>",
            "InspectionStatus": "Pending Inspection",
            "InspectionAttachment": "XgnolH5F.dc7",
            "BarCode": "",
            "ContractorHSERole": "Contractor Site HSE",
            "LEHSEAccountRole": "Linde Engineering Site HSE",
            "IsMobile": true,
            "HasDaily": false,
            "LEHSEAccount": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
            "LEHSEName": "Chunyao Hou,Shasha Meng,Shasha Meng,Lihua Wu,Qiyang Chen",
            "ClientTimeZone": "Asia/Singapore",
            "CreateTime": "2025-05-26 14:48:52",
            "Timezone": "",
            "UpdateBy": "<EMAIL>",
            "TaskID": "***************",
            "ItemId": "***************",
            "$$$var467": "<div style=\"text-align: center;\"><b style=\"color: #00A0E1; font-size: large;\"> Preliminary Inspection Checklist</b></div>",
            "$$$var468": "Apply Date<br/>05/26/2025, 15:53 GMT+8",
            "$$$var469": "Register Date<br/>05/26/2025, GMT+8",
            "CalibrationExpiryDate": null,
            "$$$var470": "Calibration Expiry Date<br/>",
            "$$$var471": "Inspection Date<br/>05/26/2025, 15:53 GMT+8",
            "Group": [
                {
                    "Name": "General Appearance",
                    "TemplateGroupId": "***************",
                    "TaskID": "***************",
                    "ItemId": "680251106697286",
                    "ParentItemId": "***************",
                    "Item": [
                        {
                            "ItemName": "Handle",
                            "ItemBrief": "Check For Cracks. Rubber Grips Should Be Installed",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729286",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "Nuts And Bolts",
                            "ItemBrief": "All Properly Tightened. No Loose Or Missing Items",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729287",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "General Cleanliness",
                            "ItemBrief": "The Wheelbarrow Is Cleaned After Use",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729288",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "Tire",
                            "ItemBrief": "Use The Correct Size Tire. The Tire Should Be In Good Condition And Air Filled To The Correct PRejection_of_success_tipsre",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729289",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "Wheel And Bearing",
                            "ItemBrief": "Ensure They Are In A Straight Position With Smooth Rotation. Bearings Should Be Well Lubricated",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729290",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "Bracing",
                            "ItemBrief": "In Good Condition; No Bend, Crack, Or Holes",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729291",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "Frames",
                            "ItemBrief": "In Good Condition; No Bend, Crack, Or Holes",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729292",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "Bucket",
                            "ItemBrief": "In Good Condition; No Bend, Crack, Or Holes",
                            "Status": "0",
                            "StatusValue": "Not Applicable",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729293",
                            "ParentItemId": "680251106697286"
                        },
                        {
                            "ItemName": "Stands",
                            "ItemBrief": "In Good Condition; No Bend, Crack, Or Holes",
                            "Status": "1",
                            "StatusValue": "Pass",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729294",
                            "ParentItemId": "680251106697286"
                        }
                    ]
                },
                {
                    "Name": "Operator",
                    "TemplateGroupId": "***************",
                    "TaskID": "***************",
                    "ItemId": "680251106697287",
                    "ParentItemId": "***************",
                    "Item": [
                        {
                            "ItemName": "Operator ",
                            "ItemBrief": "Operator Has The Knowledge/Training To Operate The Equipment",
                            "Status": "0",
                            "StatusValue": "Not Applicable",
                            "Remarks": "",
                            "TaskID": "***************",
                            "ItemId": "679172430729296",
                            "ParentItemId": "680251106697287"
                        },
                        {
                            "ItemName": "PPE",
                            "ItemBrief": "Correct PPE Should Be Used When Operating The Equipment (E.g., Hand Protection)",
                            "Status": "2",
                            "StatusValue": "Pending",
                            "Remarks": "1111",
                            "TaskID": "***************",
                            "ItemId": "679172430729297",
                            "ParentItemId": "680251106697287"
                        }
                    ]
                }
            ]
        }
    }
    const newUrl = useParamsFormatter(url.process.processAudit, {
        params1: route.query?.stepId
    });
    console.log("%c [ SubmitTRInspection --- newUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)
    const result = await usePostBody(newUrl, {}, {
        formData: viewModel_Data.value,
        header: {
            actionName: "Submit to Re-inspection",
            comments: "",
            consignEnabled: false,
            consignUsers: [],
            consignRoutingType: "Parallel",
            consignReturnType: "Return",
            context: {
                Routing: {}
            }
        }
    }, true);
    if (result.success || result.data.success) {
        showNotify({ message: useLang('UAUC.SubmissionSuccessful'), type: 'success' })
        router.back()
    } else {
        console.log("%c [ SubmitTRInspection --- result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)
    }
}
const POST_EquipmentClassification = async () => {
    const formdata = useFormData({
        f: 'e30=',
        c: 'WyJJdGVtSWQiLCJOYW1lIl0=', //["ItemId","Name"]
        o: ''
    })
    try {
        const data = await usePostBody('bpm/datasource/***************/table/BDS_EquipmentTemplate', {}, formdata)
        columns.value = data?.map(item => ({
            text: item.Name,
            value: item.ItemId
        })) || []
    } catch (error) {
        console.log("设备工具分类---error", error)
    }
}
const applyData = ref()
const serialNum = ref('')
const historyData = ref()
const viewModel_Data = ref()
const BarCodeShow = ref(false)
const PageLoadCheckValue = ref('')
const setFileIds = ref([])
const LoadData = async (taskId) => {
    try {
        const newUrl = useParamsFormatter(url.process.getMyRequestInfo, {
            params1: taskId,
        });
        const data = await useGetQuery(newUrl, {}, false);
        serialNum.value = data?.processInstance?.serialNum
        viewModel_Data.value = data?.viewModel?.data

        const { EntryDate, ProjectNo, ProjectName, TemplateID, TemplateName, Contractor, ContractorHSE, ContractorHSEName, InspectionDate, ContractorCode, Manufacture, SerialNo, ApplicableCalibrationExpiryDate, CalibrationExpiryDate,
            CertificateOrOtherAttachment, ApplicableOperatorName, OperatorAccount, OperatorName, OperatorAttachment, ApplicableModel, Model, ApplicablePlateNo, PlateNo,
            ApplicableSWLOrCapacity, SWLOrCapacity, ApplicableSizeOrDiameter, SizeOrDiameter, ApplicableLength, Length, Remarks, Group, BarCode, PlanId, EvaluationResultValue, CreateBy, ContractorHSEAccount, InspectionAttachment } = data?.viewModel?.data
        console.log("%c [ data?.viewModel?.data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data?.viewModel?.data)
        History_data(CreateBy)
        // EntryDate_.value = EntryDate ? new Intl.DateTimeFormat(navigator?.language, options_.value).format(new Date(EntryDate)) : '' //EntryDate?.split(' ')[0]
        EntryDate_.value = EntryDate ? new Intl.DateTimeFormat(navigator?.language, options.value).format(new Date(EntryDate + 'Z')) : ''
        ProjectNo_.value = ProjectNo
        ProjectName_.value = ProjectName
        // const found = columns.value?.find(item => item.value === TemplateID)
        // console.log("%c [ Equipment/Tool type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", found?.text)
        TemplateID_.value = TemplateName //found?.text //找到对应的 TemplateID_ 
        Contractor_.value = Contractor
        ContractorCode_.value = ContractorCode
        Manufacture_.value = Manufacture
        SerialNo_.value = SerialNo
        DisplayName.value = ContractorHSEName //ContractorHSE 
        ApplicableCalibrationExpiryDate_.value = ApplicableCalibrationExpiryDate
        // CalibrationExpiryDate_.value = CalibrationExpiryDate?.split(' ')[0]
        console.log("%c [ CalibrationExpiryDate ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", CalibrationExpiryDate)
        CalibrationExpiryDate_.value = CalibrationExpiryDate ? new Intl.DateTimeFormat(navigator?.language, options.value).format(new Date(CalibrationExpiryDate + 'Z')) : '' //new Intl.DateTimeFormat(navigator?.language, options_.value).format(new Date(CalibrationExpiryDate)) : '' //CalibrationExpiryDate?.split(' ')[0] 
        CertificateOrOtherAttachment_.value = CertificateOrOtherAttachment
        ApplicableOperatorName_.value = ApplicableOperatorName
        OperatorAccount_.value = OperatorAccount
        OperatorName_.value = OperatorName
        OperatorAttachment_.value = OperatorAttachment
        ApplicableModel_.value = ApplicableModel
        Model_.value = Model
        ApplicablePlateNo_.value = ApplicablePlateNo
        PlateNo_.value = PlateNo
        ApplicableSWLOrCapacity_.value = ApplicableSWLOrCapacity
        SWLOrCapacity_.value = SWLOrCapacity
        ApplicableSizeOrDiameter_.value = ApplicableSizeOrDiameter
        SizeOrDiameter_.value = SizeOrDiameter
        ApplicableLength_.value = ApplicableLength
        Length_.value = Length
        Remarks_.value = Remarks
        PlanId_.value = PlanId
        if (EvaluationResultValue === "Pass") {
            EvaluationResultValue_.value = useLang("SSI.Pass")
        } else if (EvaluationResultValue === "Rejected") {
            EvaluationResultValue_.value = useLang("SSI.Rejected")
        } else if (EvaluationResultValue === "Pending") {
            EvaluationResultValue_.value = useLang("SSI.Pending")
        } else {
            EvaluationResultValue_.value = EvaluationResultValue
        }
        PageLoadCheckValue.value = EvaluationResultValue
        RouteNoAwait.value = route.query.next === "complete" || route.query.next === "notify" || route.query.next == "apply"
        console.log("%c [ --页面读取值-- PageLoadCheckValue ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", PageLoadCheckValue.value)
        DateOfInspection.value = InspectionDate ? new Intl.DateTimeFormat(navigator?.language, options_.value).format(new Date(InspectionDate + 'Z')) : new Intl.DateTimeFormat(navigator?.language, options_.value).format(Date.parse(new Date()))
        if (CertificateOrOtherAttachment) {
            loadFile_Img(1, CertificateOrOtherAttachment)
        }
        if (OperatorAttachment) {
            loadFile_Img(2, OperatorAttachment)
        }
        if (InspectionAttachment) {
            setFileIds.value = []
            loadFile_Img(3, InspectionAttachment)
            attachment_.value = ' ' //假值
        }
        if (BarCode) { // 日检 月检 季检 的时候 From 表里才有 Barcode，初检没有 Barcode
            console.log("%c [ --BarCode-- ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", BarCode)
            BarCodeShow.value = true
            BarCode_.value = BarCode
        }
        if (route.query?.processName?.includes('Daily')) { // 1. 日检：Pass，Rejected，Not Applicable
            CheckColumns.value = [
                {
                    "text": useLang('SSI.Pass'),//useLang('SSI.Pass'),
                    "CurrentVal": 'Pass',
                    "value": 1
                },
                {
                    "text": useLang('SSI.Rejected'),
                    "CurrentVal": 'Rejected',
                    "value": 3
                },
                {
                    "text": useLang('SSI.NotApplicable'),
                    "CurrentVal": 'Not Applicable',
                    "value": 0
                }
            ]
        } else { // 2. 初检，月检，季检：Pass, Rejected, Pending, Not Applicable 都有
            CheckColumns.value = [
                {
                    "text": useLang('SSI.Pass'),
                    "CurrentVal": 'Pass',
                    "value": 1
                },
                {
                    "text": useLang('SSI.Rejected'),
                    "CurrentVal": 'Rejected',
                    "value": 3
                },
                {
                    "text": useLang('SSI.Pending'),
                    "CurrentVal": 'Pending',
                    "value": 2
                },
                {
                    "text": useLang('SSI.NotApplicable'),
                    "CurrentVal": 'Not Applicable',
                    "value": 0
                }
            ]
        }
        const Group1 = [
            {
                "Name": "GENERAL APPEARANCE",
                "TemplateGroupId": "646985155559493",
                "TaskID": "652979612786757",
                "ItemId": "652979611476038",
                "ParentItemId": "652979611476037",
                "Item": [
                    {
                        "ItemName": "Overall body cleanliness",
                        "ItemBrief": "General cleanliness, no soil or dirt sticked to the body.",
                        "StatusValue": "-1", //Pass, Rejected, Pending
                        "Status": "",  //1，3，2
                        "Remarks": "",
                        "TaskID": "652979612786757",
                        "ItemId": "646985155559495",
                        "ParentItemId": "652979611476038"
                    },
                    {
                        "ItemName": "Structure, chassis",
                        "ItemBrief": "Rusty condition, any visible welding joint repair ",
                        "StatusValue": "-1",
                        "Status": "",
                        "Remarks": "",
                        "TaskID": "652979612786757",
                        "ItemId": "646985155559496",
                        "ParentItemId": "652979611476038"
                    }
                ]
            },
            {
                "Name": "TLED",
                "TemplateGroupId": "646985155559493",
                "TaskID": "652979612786757",
                "ItemId": "652979611480134",
                "ParentItemId": "652979611476037",
                "Item": [
                    {
                        "ItemName": "tt",
                        "ItemBrief": "tt",
                        "StatusValue": "-1",
                        "Status": "",
                        "Remarks": "",
                        "TaskID": "652979612786757",
                        "ItemId": "649052185292870",
                        "ParentItemId": "652979611480134"
                    },
                    // {
                    //     "ItemName": "tt",
                    //     "ItemBrief": "t",
                    //     "StatusValue": "-1",
                    //     "Status": "",
                    //     "Remarks": "",
                    //     "TaskID": "652979612786757",
                    //     "ItemId": "649052185292871",
                    //     "ParentItemId": "652979611480134"
                    // }
                ]
            }
        ]
        InChecklist.value = Group?.map((group) => ({
            title: group.Name,
            ItemId: group.ItemId,
            ParentItemId: group.ParentItemId,
            TaskID: group.TaskID,
            TemplateGroupId: group.TemplateGroupId,
            content: group.Item.map(item => ({
                ...item,
                StatusValue: item.StatusValue === '-1' ? '' : item.StatusValue,
                text: item.StatusValue === 'Pass' ? useLang("SSI.Pass") : item.StatusValue === 'Rejected' ? useLang("SSI.Rejected") : item.StatusValue === 'Pending' ? useLang("SSI.Pending") : item.StatusValue === 'Not Applicable' ? useLang("SSI.NotApplicable") : ''
            }))
        }))

        console.log("%c [ InChecklist.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", InChecklist.value)
    } catch (error) {
        console.log("%c [ LoadData error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
    }
}


function formatFileName(fileName) {
    const [name, ext] = fileName.split(".");
    if (name.length > 4) {
        return `${name.slice(0, 4)}...${name.slice(-3)}.${ext}`;
    } else {
        return fileName; // 文件名小于 4 位时保持原样
    }
}
const getFileDetailInfo = async (fileId) => {
    try {
        const res = await axios({
            method: 'GET',
            headers: {
                'Authorization': "Bearer " + loginStore.getToken,
                'Accept-Language': coreStore.$lang == 'en' ? 'en-US' : coreStore.$lang
            },
            url: `/bpm/attachment/file/${fileId}`,
            responseType: 'blob'
        });
        if (res.status === 200) {
            return {
                fileId,
                fileType: res?.request.response.type,
                fileURL: res?.request?.responseURL
            };
        } else {
            showNotify({ message: 'File does not exist.' });
            return null;
        }
    } catch (err) {
        console.log('获取文件详细信息失败:', err);
        return null;
    }
};
const processFileList = async (n, fileIds) => {
    const formdata = new FormData();
    const fileDetailInfos = [];
    for (const fileId of fileIds) {
        const detailInfo = await getFileDetailInfo(fileId);
        if (detailInfo) {
            fileDetailInfos.push(detailInfo);
        }
        formdata.append('fileIds', fileId);
    }
    const data = await usePostBody(url.form.getAttachmentInfo, {}, formdata, false);
    const formattedFiles = data.map(file => ({
        ...file,
        formattedName: formatFileName(file.fileName)
    }));
    const mergedFiles = formattedFiles.map(file => {
        const detailInfo_ = fileDetailInfos.find(info => info.fileId === file.fileId);
        return {
            ...file,
            ...detailInfo_
        };
    });
    console.log("%c [ mergedFiles ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", mergedFiles)
    if (n === 1) {
        CerList.value = mergedFiles
        CerImgList.value = mergedFiles.filter(item => acceptImg.value.includes(item.ext)).map(item => item.fileURL)
        console.log("%c [ CerImgList.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", CerImgList.value)
    } else if (n === 2) {
        ComList.value = mergedFiles
        ComImgList.value = mergedFiles.filter(item => acceptImg.value.includes(item.ext)).map(item => item.fileURL)
    } else {      //只读取
        if (route.query.next === "await" && route.query?.stepName?.includes("Repair Equipment/Tool") || RouteNoAwait.value) {
            FileList.value = mergedFiles
            FileImgList.value = mergedFiles.filter(item => acceptImg.value.includes(item.ext)).map(item => item.fileURL)
            console.log("%c [ 111 --- FileImgList.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", FileImgList.value)
        } else {  // 读取 + 编辑
            FileList2.value = mergedFiles
            FileImgList2.value = mergedFiles.filter(item => acceptImg.value.includes(item.ext)).map(item => item.fileURL)
            console.log("%c [ 222 --- FileImgList2.value  ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", FileImgList2.value)
            fileIds.value = mergedFiles.map(item => {
                return {
                    id: item.fileId,
                    name: item.fileName
                }
            })
            setFileIds.value = [...fileIds.value]
            const newFileIds = fileIds.value.map(item => item.id).join(',')
            attachment.value = newFileIds
            console.log("%c [ --- attachment.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", attachment.value)
            attachment_.value = ' ' //假值
        }
    }
};
const loadFile_Img = async (n, FileId) => {
    console.log("%c [ FileId -- n --> ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", n, '--->>>', FileId)
    const files = FileId.split(',')
    await processFileList(n, files)
}
const History_data = async (CreateBy) => {
    try {
        const formdata = useFormData({
            f: window.btoa(JSON.stringify({ "SerialNum": { "op": "=", "value": route.query.serialNum }, "TaskOwner": { "op": "=", "value": CreateBy } })),
            o: '',
            lc: 'WyJTdGVwTmFtZSIsIlNpZ24iLCJGaW5pc2hBdCIsIlNlbEFjdGlvbiIsIkNvbW1lbnRzIl0=', //["StepName","Sign","FinishAt","SelAction","Comments"],
            page: 1,
            start: 0,
            limit: 10,
        })
        const res = await usePostBody(`bpm/datasource/***************/table/VW_TaskHistory?_dc=${Date.now()}`, {}, formdata)
        if (res || res.success) {
            const mergedData = [];
            const stepActionMap = new Map();
            res.forEach(item => {
                // 使用 StepName、SelAction 和 FinishAt 作为唯一键
                const key = `${item.StepName}-${item.SelAction}-${item.FinishAt}`;

                if (!stepActionMap.has(key)) {
                    const newItem = { ...item };
                    newItem.Sign = [item.Sign];
                    stepActionMap.set(key, newItem);
                    mergedData.push(newItem);
                } else {
                    const existingItem = stepActionMap.get(key);
                    // 只有在相同的 StepName、SelAction 和 FinishAt 下才合并 Sign
                    if (!existingItem.Sign.includes(item.Sign)) {
                        existingItem.Sign.push(item.Sign);
                    }
                }
            });
            historyData.value = mergedData.reverse();
        }
    } catch (error) {
        console.log("设备工具分类---error", error)
    }
}
const isShowPrompt = ref(false)
const ShowPromptMsg = ref('')
const ShowPromptLabel = ref('')
const ShowPrompt = (val, label) => {
    console.log("%c [ label ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", label)
    isShowPrompt.value = true
    ShowPromptMsg.value = val
    ShowPromptLabel.value = label
}
const checkPicker = ref(false)
const CheckPickerValue = ref([])
const StatusValue = ref('')
const currentInputId = ref('')
const openCheckPicker = (id) => {
    if (id !== '') {
        checkPicker.value = true
        currentInputId.value = id
    }
}
const CheckConfirm = ({ selectedValues, selectedOptions }) => {
    console.log("%c [ selectedValues ??? ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedValues)
    console.log("%c [ selectedOptions ???  ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedOptions)
    checkPicker.value = false;
    CheckPickerValue.value = selectedValues;
    const currentInput = document.querySelector(`#item-${currentInputId.value}`)
    if (currentInput) {
        currentInput.value = selectedOptions[0].text
        InChecklist.value.forEach(group => {
            group.content.forEach(item => {
                if (item.ItemId === currentInputId.value) {
                    item.text = selectedOptions[0].text
                    item.StatusValue = selectedOptions[0].CurrentVal
                    item.Status = selectedOptions[0].value
                }
            })
        })
        checkEvaluationResult()
    }
};


const Cancel_Loading = ref(false)
const showCancel_ = () => { //撤销
    CancelValue.value = ''
    showCancel.value = true
}
const Cancel_ = async (val) => { //撤销
    Cancel_Loading.value = true
    console.log("%c [ CancelValue ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", val)
    const data = await usePostBody(url.process.cancleTask, {}, {
        comments: val,
        items: [{
            ID: route.query?.taskId,
            TaskID: route.query?.taskId
        }]
    })
    if (data.success) {
        Cancel_Loading.value = false
        showNotify({ message: useLang('Form.Cancellation_successful'), type: 'success' })
        router.back()
    } else {
        Cancel_Loading.value = false
        showNotify({ message: data.errorMessage, type: 'danger' })
    }
}

// 知会 start
const moreShow = ref(false)
const showInform = ref(false)
const More = () => {
    moreShow.value = true
}
const notifyData = reactive({
    message: '',
    users: ''
})
const userShow = ref(false)
const userIds = ref([])
const AwaitList = ref([])
const infoConfirm = async () => { //知会 
    if (!notifyData.users) {
        showNotify({ message: useLang('Form.Select_Username_tips'), type: 'danger' })
        return
    }
    if (!notifyData.message) {
        showNotify({ message: useLang('Form.NotifyOpinion_tips'), type: 'danger' })
        return
    }
    const dataArr = {
        ID: route.query?.stepId,
        TaskID: route.query?.taskId
    }
    const postJson = {
        accounts: userIds.value,
        comments: notifyData.message,
        items: [dataArr]
    }
    console.log("%c [ 知会 params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", postJson)
    const data = await usePostBody(url.process.postNotify, {}, postJson, true)
    if (data.success === false) {
        showNotify({ type: 'danger', message: data.message ?? data.errorMessage })
    } else {
        showNotify({ type: 'success', message: useLang('Form.NotifySuccess') })
        showInform.value = false
        notifyData.message = ''
        notifyData.users = ''
        router.back()
    }
}
const InfoCancel = () => {
    showInform.value = false
    notifyData.message = ''
    notifyData.users = ''
}
const onUserClick = () => {
    userShow.value = true
}
const onUserSave = (users) => {
    userShow.value = false
    userIds.value = users.map((x) => x.account)
    notifyData.users = users.map((x) => x.name).join(',')
}
const onBatchInform = () => {
    showInform.value = true
}
let service = new ProcessService()
const markRead = async () => {
    const newUrl = useParamsFormatter(url.process.notifyRead, {
        params1: route.query?.stepId
    })
    const data = await usePostBody(newUrl, {}, { comments: '' })
    if (!data.success) {
        showNotify({ type: 'danger', message: data.errorMessage })
        console.log('error-7')
    } else {
        await service.getWorkCount()
        console.log('markRead')
        showNotify({ message: useLang('Global.ProcessingCompleted'), type: 'success' })
        router.back()
    }
}
// 知会 end

// 委托 start
const wtData = reactive({
    message: '',
    users: ''
})
const weiTuoShow = ref(false)
const wtUserShow = ref(false)
const onBatchTransfer = () => {
    weiTuoShow.value = true
}
const wtUserClick = () => {
    wtUserShow.value = true
}
const WtUserSave = (users) => {
    wtUserShow.value = false
    userIds.value = users.map((x) => x.account)
    wtData.users = users.map((x) => x.name).join(',')
}
const EntrustCancel = () => {
    weiTuoShow.value = false
    wtData.message = ''
    wtData.users = ''
}
const EntrustConfirm = async () => {
    if (!wtData.users) {
        showNotify({ message: useLang('Form.select_EntrustedPersonnel_tips'), type: 'danger' })
        return
    }
    if (!wtData.message) {
        showNotify({ message: useLang('Form.Enter_Entrusted_opinion_tips'), type: 'danger' })
        return
    }
    const dataArr = {
        ID: route.query?.stepId,
        TaskID: route.query?.taskId,
        StepId: route.query?.stepId,
    }
    const postJson = {
        accounts: userIds.value,
        comments: wtData.message,
        items: [dataArr]
    }
    console.log("%c [ 委托 params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", postJson)
    let newUrl = useParamsFormatter(url.process.assigneUserBatch, {})
    const data = await usePostBody(newUrl, {}, postJson, true)
    if (!data.success) {
        showNotify({ type: 'danger', message: data.message || data.errorMessage })
    } else {
        showNotify({ type: 'success', message: useLang('Form.EntrustSuccess') })
        weiTuoShow.value = false
        wtData.message = ''
        wtData.users = ''
        router.back()
    }
}
// 委托 end

onMounted(async () => {
    POST_EquipmentClassification()
    if (route.query?.taskId) {
        const { user } = await useGetQuery(url.login.getUserInfo)
        currentUser.value = user?.DisplayName
        LoadData(route.query?.taskId)
        fileList.value = []
        imgList.value = []
    }
    console.log("%c [ --route.query-- ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route.query)
})
onUnmounted(() => {
    // if (state.html5QrCode?.isScanning) {
    //     stopScan();
    // }
    if (document.querySelector(".viewport")) {
        Quagga.stop();
    }
});
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #f9f9f9;
    box-sizing: border-box;

    .fave {
        .mb {
            #interactive.viewport {
                position: relative;
                width: 100%;
                height: 250px;

                video {
                    max-width: 100%;
                    width: 100%;
                    height: 250px;
                    object-fit: cover;
                }

                .drawingBuffer {
                    width: 100%;
                    height: 250px;
                    position: absolute;
                    left: 0;
                    top: 0;
                }
            }
        }
    }

    .cell {
        margin-bottom: 3px;
    }

    .foot {
        border-top: 1px solid var(--van-cell-border-color);
        width: 100%;
        box-sizing: border-box;
        padding: 12px;
        position: fixed;
        bottom: 0;
        z-index: 99;
        display: flex;
        background: var(--yz-div-background);
        align-items: center;


        .subBtn {
            width: 50%;
        }

        .NotifyEntrust {
            padding: 60px 38px;
            display: flex;
            align-items: center;
        }
    }
}

::v-deep(.van-button--small) {
    height: 26px;
    width: 175px;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ellipsis2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all; //断词
}


::v-deep(.van-cell__title) {
    color: #828282;
}

::v-deep(.van-field) {
    border-radius: 5px;
}

::v-deep(.van-field__control) {
    color: #00A0E1 !important;
}

::v-deep(.van-field__control:disabled) {
    color: #00a0e1 !important;
    -webkit-text-fill-color: #00a0e1 !important;
}

::v-deep(.van-field__control--error, .van-field__control--error::placeholder) {
    -webkit-text-fill-color: #00a0e1 !important;
}

::v-deep(.van-field__control--error) {
    color: #00A0E1;
}

::v-deep(.van-icon-search) {
    color: #00A0E1;
}

::v-deep(.van-field__label) {
    //Operator name
    width: 90px !important;
}

::v-deep(.line_ .van-field__label) {
    width: 250px !important;
}

// OperatorName value width
::v-deep(.OperatorName .van-field__value) {
    margin-left: 20px;
}

::v-deep(.GenInfo .van-field__label) {
    //Register Date start --- Remarks end
    width: 110px !important;
}

::v-deep(.CalibrationExpiryDate .van-field__label) {
    width: 85px !important;
    margin-left: -8px;
}


::v-deep(.Guard .van-field__label) {
    //各个检查项
    width: 210px !important;
}

::v-deep(.Guard01 .van-field__label) {
    //评估结果
    width: 210px !important;
}

::v-deep(.Guard02 .van-field__label) {
    width: 125px !important;
}

::v-deep(.Guard03 .van-field__label) {
    width: 115px !important;
}

::v-deep(.Guard02 .van-field__value .van-field__body input) {
    margin-left: 12px;
}

::v-deep(.Guard .van-field__value .van-field__body input) {
    //检查项
    margin-left: 20px;
    text-align: center;
}

::v-deep(.Guard01 .van-field__value .van-field__body input) {
    //评估结果
    margin-left: 10px;
    text-align: center;
}

.msgIcon {
    position: absolute;
    left: 215px;
    top: 13px;
}

.msgIcon1 {
    position: absolute;
    left: 155px;
}

.Guard {
    padding: 8px 10px;
}

::v-deep(.van-field__body input) {
    margin-left: 20px;
}

::v-deep(.van-field__body textarea) {
    margin-left: 20px;
}

::v-deep(.van-cell__right-icon) {
    margin-right: -11px;
}

::v-deep(.van-field__error-message) {
    margin-left: 31px;
}

::v-deep(.van-tab--line) {
    flex: 1 0 auto;
    padding: 0 8px 0 0; //0 var(--van-padding-sm);
}

::v-deep(.van-tab--active) {
    color: #00A0E1;
}

::v-deep(.van-step__circle) {
    background: #1989fa;
}

::v-deep(.van-step__line) {
    background: #1989fa;
}

.add_upload {
    .icon_ {
        color: #00a0e1;
        width: 20px;
        height: 20px;
        position: absolute;
        top: 12px;
        right: 35px;
    }
}


.add_upload .add_upload_icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.add_upload .add_upload_file {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    //height: 100%;
    height: 48px;
    opacity: 0;
    font-size: 0;
}

.add_upload_imgBox {
    padding: 10px 15px;
    background: #ffffff;
    margin-top: 3px;
    border-radius: 6px;
}

.add_upload_imgBox .add_upload_imgDiv {
    height: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.add_upload_imgBox .add_upload_imgDiv div img {
    width: 1.5rem;
    height: 1.5rem;
}

.add_upload_imgBox .add_upload_close {
    width: 15px;
    height: 15px;
    margin-bottom: 15px;
}

.add_upload_imgBox .add_upload_close img {
    width: 100%;
    height: 100%;
}

.swipper_ .van-tabs__wrap {
    border-radius: 10px;
}

.ShowPromptMsg {
    padding: 60px 25px;
}

.ShowPromptMsg2 {
    padding: 30px 25px;
}

.pure {
    color: #444444;
}

.blue {
    color: #00a0e1;
}
</style>