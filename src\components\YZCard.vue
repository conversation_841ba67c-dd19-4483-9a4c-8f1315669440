<template>
    <div class="work-card">
        <!-- <van-row> -->
        <!-- <van-col span="18"> -->
        <!-- <div class="left-div-wrap"> -->
        <slot name="checkbox" />
        <div class="left-div" :class="queryType ? 'padding01' : 'padding02'">
            <!-- icon 不展示 -->
            <!-- <div class="left-main">
                                <div class="left-icon">
                                    <i v-if="process.processIcon" :class="process.processIcon"></i>
                                    <i v-else class="fa fa-paper-plane-o"></i>
                                </div>
                                <div class="left-title">
                                    {{ process.processInstanceOwner + $t('Work.is') }}{{ process.processName }}
                                </div>
                            </div> -->
            <div class="left-title">
                <div v-show="!queryType" style="width: 185px;">{{ process.processName }}:</div>
                <div class="serialNum" :style="{ marginLeft: queryType ? '-12px' : '0px' }">
                    {{ process.serialNum }}
                </div>
            </div>
            <div class="left-desc">
                <slot />
                <div class="left-time" :class="!queryType ? '' : 'active_'">
                    {{ currentTime }}
                </div>
            </div>


            <!-- 描述 不展示 -->
            <!-- <div class="left-desc" v-if="process.description">
                                <span v-html="process.description"></span>
                            </div> -->
        </div>
        <!-- </div> -->
        <!-- </van-col> -->
        <!-- <van-col span="6">
                    <div class="right-main">
                        <div class="right-content">
                            <slot />
                        </div>
                    </div>
                </van-col> -->
        <!-- </van-row> -->
    </div>
</template>

<script setup lang="ts">
import { BaseTaskInfo } from '@/logic/forms/processHelper'
import { PropType, computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const queryType: any = ref(route?.query.type && route?.query.type !== 'ApplyWork')
// console.log("%c [ queryType ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", queryType.value)
const props = defineProps({
    process: {
        type: Object as PropType<BaseTaskInfo>,
        requeired: true,
        default: {}
    }
})
const options: any = {
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
}
const currentTime = ref(new Intl.DateTimeFormat(navigator?.language, options).format(new Date(props.process.processTime + 'Z')))

// const dynamicLeftDiv = computed(() => {
//     return queryType.value ? { marginBottom: '14px' } : { marginBottom: '30px' }
// })
</script>
<style lang="scss" scoped>
.work-card {
    width: 100%;
    background: var(--yz-div-background);
    border-bottom: 0.5px solid rgba(238, 238, 238, 1);
    display: flex;
    align-items: baseline;

    // .left-div-wrap {
    //     display: flex;
    //     align-items: baseline;
    // }

    .left-div {
        width: 100%;
        position: relative;

        // .left-main {
        //     width: 100%;
        //     display: inline-block;

        //     .left-icon {
        //         width: 16px;
        //         height: 16px;
        //         float: left;
        //         margin-top: 2px;
        //         margin-left: 15px;
        //         text-align: center;
        //         line-height: 16px;
        //         color: var(--yz-text-333);
        //     }

        //     .left-title {
        //         font-family: PingFangSC-Medium;
        //         font-size: var(--yz-card-15);
        //         color: var(--yz-text-333);
        //         margin-left: 7px;
        //         font-weight: 500;
        //         float: left;
        //     }
        // }

        .left-title {
            font-family: PingFangSC-Regular;
            font-size: var(--yz-card-14);
            color: var(--yz-text-333);
            margin-top: 2px;
            margin-left: 15px;
            font-weight: 700;
            display: flex;
            align-items: start;
        }

        .left-desc {
            margin-left: 15px;
            font-family: PingFangSC-Regular;
            font-size: var(--yz-card-13);
            color: var(--yz-text-333);
            font-weight: 400;
            position: relative;

            .left-time {
                font-family: PingFangSC-Regular;
                font-size: var(--yz-card-13);
                color: var(--yz-text-333);
                font-weight: 400;
                position: absolute;
                top: 7px;
                right: 36px;
            }
        }
    }

    .right-main {
        height: 100%;

        .right-content {
            width: 80%;
            text-align: center;
            flex-direction: column;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
    }
}

.padding01 {
    padding: 9px 9px 37px 13px;
}

.padding02 {
    padding: 6px 9px 25px;
}

.active_ {
    left: -10px !important;
}
</style>