import { CheckerDirection } from "vant/lib/checkbox/Checker";
import { Y<PERSON><PERSON>on<PERSON>g, YZField, YZOption, Y<PERSON><PERSON>tyle } from "./formModel";
import { useLang } from "@/utils";
export class Y<PERSON><PERSON>heckBoxConfig extends Y<PERSON>Config {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZCheckBox'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class Y<PERSON><PERSON>heckBoxStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class Y<PERSON><PERSON><PERSON><PERSON>Box extends Y<PERSON><PERSON>ield {
    public checkBoxOption: Y<PERSON><PERSON>heckBoxOption
    public postion: CheckerDirection
    constructor(params: any) {
        super(params)
        this.vModel.value = []
        this.checkBoxOption = new YZCheckBoxOption(params['__slot__'].options)
        var types = params['__config__']['extends'].columns
        this.postion = types === 'auto' ? 'horizontal' : 'vertical'
        // this.disabled =params.disabled
        const values = params['__vModel__'].modelValue
        if (values && typeof values === 'string') {
            this.vModel.value = values.split(',')
        } else {
            const checked = this.checkBoxOption.options.filter(x => x.checked)
            if (checked && checked.length > 0) {
                this.vModel.value = checked.map(x => x.value)
                this.vModel.optionValue = checked.map(x => x.value).join(',')
            }
        }

    }
    initConfig(config: any): YZConfig {
        return new YZCheckBoxConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZCheckBoxStyle(style)
    }
    getOptions(): ICheckboxOption[] {
        return this.checkBoxOption.options
    }
    getPosition(): CheckerDirection {
        return this.postion
    }
}
export interface ICheckboxOption {
    label: string;
    value: number;
    checked: boolean;
}
export class YZCheckBoxOption extends YZOption<Array<ICheckboxOption>> {
    constructor(options: any) {
        super(options)
    }
    initOption(array: Array<any>): ICheckboxOption[] {
        const options: ICheckboxOption[] = []
        if (array) {
            array.forEach(({ label, value, checked }) => {
                options.push({
                    label,
                    value,
                    checked
                })
            });
        }
        return options
    }
}