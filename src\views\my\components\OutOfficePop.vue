<template>
    <div>
        <van-popup v-model:show="show" position="right" style="height: 100vh; width: 100%">
            <div class="service-div">
                <van-nav-bar :title="$t(item.name)" :left-text="$t('Global.Back')"
                    @click-left.stop="item.onBack(item.name)" left-arrow :right-text="$t('Global.Save')"
                    @click-right.stop="saveOutOffice" />
                <div>
                    <van-cell-group>
                        <van-cell v-for="item in outMenus" @click="handlerClick(item)" :key="item.name"
                            :title="$t(item.name)">
                            <van-icon slot="right-icon" name="success" v-if="item.value === outSetting?.state"
                                color="#4caf50" class="van-cell__right-icon"></van-icon>
                        </van-cell>
                        <van-field :name="$t('my.OutBegin')" :label="$t('my.OutBegin')" placeholder=""
                            v-if="outSetting.state === 'Period'" v-model="outSetting.from" @click="handlerShow(1)"
                            readonly />
                        <van-field v-if="outSetting.state === 'Period'" readonly :name="$t('my.OutEnd')"
                            :label="$t('my.OutEnd')" placeholder="" v-model="outSetting.to" @click="handlerShow(2)" />
                    </van-cell-group>
                </div>
            </div>
        </van-popup>
        <!-- 日期选择 -->
        <van-popup v-model:show="pickerShow" position="bottom">
            <van-picker-group :tabs="[$t('my.SelectDate'), $t('my.SelectTime')]" @confirm="onConfirm"
                @cancel="pickerShow = false">
                <van-date-picker v-model="currentDate" />
                <van-time-picker v-model="currentTime" />
            </van-picker-group>
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { url } from '@/api/url';
import { IMyMenu, IOutMenu, OutOffice } from '@/logic/my/myModel';
import { useShortTimeArrys, useTimeDiff } from '@/utils';
import { useGetQuery, usePostBody } from '@/utils/request';
import { NotifyType, showNotify } from 'vant';
import { computed, onMounted, PropType, ref } from 'vue';
const props = defineProps({
    isPop: {
        type: Boolean,
        default: false,
        required: false
    },
    item: {
        type: Object as PropType<IMyMenu>,
        required: true
    }
})

const show = computed(() => props.isPop)
const selectId = ref<number>(0)
const outSetting = ref<OutOffice>({
    from: '',
    to: '',
    state: 'InOffice'
})
const time = useShortTimeArrys(new Date()) as Array<string>
const currentDate = ref<Array<string>>([])
const currentTime = ref<Array<string>>(['00', '00'])
onMounted(async () => {
    currentDate.value = time
    await initOffice()
})
const initOffice = async () => {
    const data = await useGetQuery(url.my.getOutSetting)
    if (data) {
        outSetting.value = new OutOffice(data)
    }
}
const outMenus = ref<Array<IOutMenu>>([
    { name: 'my.InOffice', value: 'InOffice' },
    { name: 'my.OutOffice', value: 'Out' },
    { name: 'my.OutTime', value: 'Period' }
])
const saveOutOffice = async () => {
    const postData: Record<string, string> = {}
    if (outSetting.value.state === 'Period') {
        if (!outSetting.value.from) {
            showNotify({ type: 'danger', message: '请选择外出的开始日期' })
            return
        }
        if (!outSetting.value.to) {
            showNotify({ type: 'danger', message: '请选择外出的结束日期' })
            return
        }
        if (useTimeDiff(outSetting.value.from, outSetting.value.to) < 0) {
            showNotify({ type: 'danger', message: '结束日期不能小于开始日期' })
            return
        }
        postData['OutOfOfficeFrom'] = outSetting.value.from
        postData['OutOfOfficeState'] = outSetting.value.state
        postData['OutOfOfficeTo'] = outSetting.value.to
    }
    postData['OutOfOfficeState'] = outSetting.value.state
    const data = await usePostBody(url.my.saveOutSetting, {}, postData)
    if (data.code === 1) {
        showNotify({ type: 'danger', message: data.errorMessage })
    } else {
        showNotify({ type: 'success', message: '操作成功' })
    }
}
const handlerClick = (item: IOutMenu) => {
    outSetting.value.state = item.value
}
const pickerShow = ref<boolean>(false)
const handlerShow = (val: number) => {
    selectId.value = val
    pickerShow.value = true
}
const onConfirm = (val: any) => {
    const day = val[0].selectedValues as Array<any>
    const date = val[1].selectedValues as Array<any>
    if (selectId.value === 1) {
        //  开始
        outSetting.value.from = day.map(x => x).join('-') + ' ' + date.map(x => x).join(':')
    } else {
        // 结束
        outSetting.value.to = day.map(x => x).join('-') + ' ' + date.map(x => x).join(':')
    }
    pickerShow.value = false
}
</script>
<style lang="scss" scoped>
.service-div {
    width: 100%;
    height: 100vh;

    ::v-deep(.van-cell__title) {
        font-size: var(--yz-com-14);
    }

}
</style>