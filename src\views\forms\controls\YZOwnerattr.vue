<template>
    <div>
        <van-field v-if="!field.hidden" v-model="vModel" v-show="!field.hidden" :name="field.vModel.model"
            :label="field.config.label" :required="field.config.required" :key="field.vModel.model" disabled
            :rules="field.config.rules" :placeholder="field.placeholder" />
    </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/users';
import { computed, PropType, ref ,watch } from 'vue';
import { onMounted } from 'vue';
import { YZOwner } from '@/logic/forms/YZOwner';
import { eventBus } from '@/utils/eventBus';
import { typeOf } from 'mathjs';
import { onUnmounted } from 'vue';
const userStore = useUserStore()
const props = defineProps({
    field: {
        type: Object as PropType<YZOwner>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue'])
const setModelValue =ref<string>('')
eventBus.on('setOwerProty', {
    cb: function (params) {
        if (props.field.uuid === params.uuid) {
            if (params.ouInfo) {
                let newProp = params.ouInfo
                const name = params.propName
                if (name.indexOf('.') > 0) {
                    const propList = name.split('.')
                    for (let i = 0; i < propList.length; i++) {
                        const v = propList[i]
                        const vitem = buildPropTypeValue(newProp, v)
                        if (typeof vitem === 'object') {
                            newProp = vitem
                        } else {
                            setModelValue.value =  vitem 
                    
                        }
                    }
                } else {
                    setModelValue.value = buildPropTypeValue(newProp, name)
                }
            }
           
        }

    }
})
const buildPropTypeValue = (info: any, propName: string) => {
    let item: any = info[propName]
    return item
}
const isFirstLoad = ref<boolean>(true)
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
onMounted(() => {
    isFirstLoad.value = false
})
watch(setModelValue,(n,v)=>{
    emit('update:modelValue',n)
    setFun()
    setTimeout(()=>{
        console.log('v',vModel.value)
    },1000)

})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
onUnmounted(()=>{
    eventBus.off('setOwerProty',true)
})
</script>
<style  scoped></style>