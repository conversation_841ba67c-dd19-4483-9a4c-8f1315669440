<!DOCTYPE html>
<html lang="en" data-theme="">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
  <meta name="format-detection" content="telephone=yes" />
  <meta http-equiv="Expires" content="0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Cache" content="no-cache">
  <title>BPM8</title>
  <link rel="stylesheet" href="icon/iconfont/iconfont.css" />
  <link rel="stylesheet" href="icon/font-awesome/css/font-awesome.min.css" />
  <!-- <link rel="stylesheet" href="icon/layui/css/layui.css" /> -->
  <link rel="stylesheet" href="/src/assets/css/yzLightTheam.css" />
  <link rel="stylesheet" href="/src/assets/css/yzDarkTheam.css" />
  <link rel="stylesheet" href="/src/assets/css/yzTheamSize.css" />
  <script src="./vue3-sfc-loader.js"></script>
  <!-- <script src="/tinymce/js/tinymce/tinymce.min.js"></script> -->
  <script>
    window.onload = function () {
      adapter();
    };
    function _isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    }
    function adapter() {
      const baseSize = 16;
      const scale = window.screen.width / (_isMobile() ? 375 : 1920); //document.documentElement.clientWidth / (_isMobile() ? 375 : 1920)
      let fontSize = baseSize * Math.min(scale, 2); //(baseSize * Math.min(scale, 2))>12 ? (baseSize * Math.min(scale, 2)): 12
      document.documentElement.style.fontSize = fontSize + 'px';
      document.documentElement.style.translate =
        'scale(' + scale + ',' + scale + ')';
    }

    var isUniApp = false;
    var pushuuid = '';
    const agent = window.navigator.userAgent.toLocaleLowerCase();
    // 判断是否是uni-app
    if (agent.indexOf('uni-app') > -1) {
      document.addEventListener('UniAppJSBridgeReady', function () {
        const lcid = localStorage.getItem('yz-lcid');
        uni.getEnv(function (res) {
          if (res.plus) {
            isUniApp = true;
            window.uni = uni;
            pushuuid = plus.push.getClientInfo().clientid;
            //如果是UniApp 进入页面则清除角标数字  如果客户需要保留则注释该代码
            plus.runtime.setBadgeNumber(0);
            //设置语言
            // if (lcid == null) {
            //   const locale = plus.os.language;
            //   if (locale == 'en-CN') {
            //     localStorage.setItem('yz-lcid', '1033');
            //     window.i18n.locale = 'en';
            //   } else {
            //     localStorage.setItem('yz-lcid', '2052');
            //     window.i18n.locale = 'zh';
            //   }
            // }
            if (lcid == null) {
              const locale = plus.os.language;
              if (locale == 'zh-CN') {
                localStorage.setItem('yz-lcid', '2052');
                window.i18n.locale = 'zh';
              } else if (locale == 'ko-KR') {
                localStorage.setItem('yz-lcid', '1042');
                window.i18n.locale = 'kr';
              } else if (locale == 'de-DE') {
                localStorage.setItem('yz-lcid', '1031');
                window.i18n.locale = 'de';
              } else {
                localStorage.setItem('yz-lcid', '1033');
                window.i18n.locale = 'en';
              }
            }
          }
        });
      });
      window.logOutApp = function () {
        // 退出
        uni.reLaunch({
          url: '/pages/index/login?action=logout',
        });
      };
    }
    window.onresize = adapter;
  </script>
  <!-- <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: 'fefafa029f41532e634a6d278473110b',
    };
  </script>
  <script type="text/javascript"
    src="https://webapi.amap.com/maps?v=1.4.15&key=2104374e4a3706a0406680e72b3a7e10&plugin=AMap.Geocoder"></script> -->
</head>

<body>
  <div id="app"></div>
  <script src="./web.js?v=8888888"></script>
  <script src="./jsencrypt.js"></script>
  <script src="./clientHelper.js"></script>
  <!-- <script src="./dymics/component.js"></script> -->

  <!-- <script type="text/javascript" src="https://gitcode.net/dcloud/uni-app/-/raw/dev/dist/uni.webview.1.5.6.js"></script>
  <script>
    document.addEventListener('UniAppJSBridgeReady', function () {
      console.log('发送的网址: === >>> ', window.location.href)
      uni.postMessage({
        data: {
          action: 'setURL',
          env_url: window.location.href
        }
      })
    });
  </script> -->

  <!-- 如使用微信认证，请注册此方法 -->
  <!-- <script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script> -->
  <!-- 如果使用 uniapp，请注册此文件 -->
  <!-- <script src="https://2059152915.ietheivaicai.com:22443/qn-eZhCu9HCXKSZLNhoiFj4uMVYtWjsUJSk2XG0aLw4.js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script> -->
  <!-- <script type="text/javascript" src="./uni.webview.1.5.5.js"></script> -->
  <!-- 如果使用飞书认证，请注册此方法 -->
  <!-- <script type="text/javascript" src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.23.js"></script> -->

  <!-- <script type="module" src="/src/main.ts?v=<%= version %>"></script> -->
  <script type="module" src="/src/main.ts"></script>

  <!-- <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: window.webConfig.securityJsCode,
    };
    // 在外部定义 key 的值
    const apiKey = window.webConfig.mapsKey
    // 动态创建 <script> 标签
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://webapi.amap.com/maps?v=1.4.15&key=${apiKey}&plugin=AMap.Geocoder`;
    // 将 <script> 标签插入到文档头部中
    document.head.appendChild(script);
  </script> -->

  <!-- <script src="https://cdn.jsdelivr.net/npm/ext@1.7.0/global-this/index.min.js"></script> -->
  <style>
    /* .iconfont {
      font-size: 25px !important;
    }
    span {
      font-size: var(--yz-font-size) !important;
    }

    div {
      font-size: var(--yz-font-size) !important;
    } */
  </style>
  <script>
    this.globalThis || (this.globalThis = this);
  </script>
</body>

</html>