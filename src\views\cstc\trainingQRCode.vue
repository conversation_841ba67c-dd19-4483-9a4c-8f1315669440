<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ $t('CSTC.TQR') }}
                <div class="navbtn" @click="router.push({ path: '/search', query: { type: '1' }, replace: true })">
                    <van-icon name="filter-o" size="20" />
                </div>
            </template>
        </YZNav>
        <div class="main">
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :loosing-text="$t('Form.Release_to_refresh')"
                :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loading" :finished="finished" :finished-text="$t('Form.NoMore')"
                    :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div v-for="(item, index) in listData" :key="index" class="list">
                        <div class="list-li ellipsis" @click="runGenerateQR(item)">
                            <div class="head">ST</div>
                            <p class="ellipsis">
                                {{ item.TrainingCategory }}
                            </p>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
        <!-- <div class="bg">
            <van-empty :image="emptyimg" image-size="100" v-if="!listData.length">
                <template #description>
                    <span class="desc-title">
                        {{ $t('home.Empty_tips') }}
                    </span>
                </template>
            </van-empty>
        </div> -->
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import emptyimg from '@/assets/imgs/empty.jpg'
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import { useFormData } from '@/utils'
import { TrainingQRCodeApi } from '@/service/user'
import emitter from '@/utils/emitter'
import axios from 'axios';

const route = useRoute()
const router = useRouter()
const listData = ref([])
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false); // 下拉刷新
const pageParams = ref({
    page: 1, // 当前页数, 从 1 开始
    start: 0,
    limit: 20 // 每页条数
});
const keyWords = ref(route.query?.keyWords || "")
const minTime = ref(route.query?.minTime || "1990-01-01")
const maxTime = ref(route.query?.maxTime || new Date().toISOString().split('T')[0])
const onRefresh = () => { //下拉刷新
    loading.value = true;
    pageParams.value.page = 1;
    finished.value = false;
    // refreshing.value = false; // 重置下拉刷新状态 ?
    listData.value = [];
    onLoad();
};

const onLoad = async () => {// 上拉加载 分页
    if (refreshing.value) {
        refreshing.value = false;
        listData.value = [];
    }
    const formdata = useFormData({
        s: btoa(JSON.stringify([{ "name": "all", "op": "like", "value": keyWords.value, "isAll": true }, { "name": "IssuerDate", "op": ">=", "value": minTime.value }, { "name": "IssuerDate", "op": "<=", "value": maxTime.value }])),
        f: '',
        o: '',
        lc: 'WyJUcmFpbmluZ0NhdGVnb3J5Il0=', //btoa(JSON.stringify(["TrainingCategory"]))
        page: pageParams.value.page,
        start: (pageParams.value.page - 1) * pageParams.value.limit,
        limit: pageParams.value.limit
    })
    console.log("%c [ 培训类型 ---  formdata ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formdata)

    try {
        const res = await TrainingQRCodeApi(formdata)
        if (res.data.success) {
            console.log("%c [ res.data?.children ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res.data?.children)
            listData.value = res.data?.children || []
            pageParams.value.page++
            loading.value = false
            if (res.data.children.length < pageParams.value.limit) {
                finished.value = true
                console.log("停止刷新")
            }
        }
    } catch (error) {
        console.log("trainingQRCode---error", error)
    }

}
const runGenerateQR = (i) => {
    const manyParams = {
        IssuerAccount: i.IssuerAccount, // "<EMAIL>"
        CourseID: i.ItemId, //*************** || ItemId == CourseID
        TrainingCategory: i.TrainingCategory, //"Permit to Work "
        UserType: i.UserType, //Linde
        Issuer: i.Issuer, // "Qiyang Chen"
        // CaseNo: i.CaseNo, //"DTC2024100002"
        IssuerDate: i.IssuerDate, // "2024-10-25 00:00:00"
        CreateBy: i.CreateBy, // "<EMAIL>"
        // CreateTime: i.CreateTime, // "2024-10-25 07:59:47"
        UpdateBy: i.UpdateBy, // "<EMAIL>"
    }
    localStorage.setItem('manyParams', JSON.stringify(manyParams))

    router.push({
        path: '/generateQRCode',
        replace: true
    })
}
onMounted(() => {
    const searchVal = route.query
    console.log("%c [ 来自search --- searchVal ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchVal)
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .main {
        width: 100%;
        height: 100vh;
    }

    .list {
        margin-top: 10px;
        width: 100%;

        .list-li {
            width: 100%;
            box-sizing: border-box;
            height: 44px;
            padding: 0 12px;
            display: flex;
            align-items: center;
            background-color: var(--van-background-2);
            margin-bottom: 5px;

            .head {
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 24px;
                width: 24px;
                height: 24px;
                box-sizing: border-box;
                background-color: #00C777;
                color: #fff;
                margin-right: 8px;
            }

            p {
                flex: 1;
                color: #444444;
                font-size: var(--yz-com-14);
            }
        }
    }

    .navbtn {
        position: absolute;
        top: 0;
        right: 0;
        width: 44px;
        margin-right: 10px;
        height: 100%;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.bg {
    background-color: var(--yz-div-background);
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yz-text-333) !important;
}

.ellipsis2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--yz-text-333) !important;
}
</style>