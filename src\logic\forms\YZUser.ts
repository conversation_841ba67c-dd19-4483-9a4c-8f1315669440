import { UploaderFileListItem } from "vant";
import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON><PERSON>, YZStyle } from "./formModel";
import { usePostBody } from "@/utils/request";
import { url } from "@/api/url";
import { eventBus } from "@/utils/eventBus";
import { useLang } from "@/utils";
export class Y<PERSON>UserConfig extends YZConfig {
    // 文件大小
    private fileSize: number;
    private sizeUnit: string;
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZUser'
        this.defaultRules = [{ required: true, message: useLang('Form.SelectUser') + '< 1' }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.fileSize = parmas['fileSize']
        this.sizeUnit = parmas['sizeUnit']
    }
    getSize(): number {
        return this.fileSize
    }
    getUnit(): string {
        return this.sizeUnit
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class Y<PERSON><PERSON><PERSON>Style extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZUser extends YZField {
    constructor(params: any) {
        super(params)
        const modelValue = params['__vModel__'].modelValue
        this.vModel.value = modelValue
        if (modelValue) {
            usePostBody(url.process.getSimpleUser, {}, {
                accounts: [modelValue]
            }).then(result => {
                if (result && result.length > 0) {
                    const user = result[0]
                    eventBus.emit('setSigleUser', false, {
                        uuid: params['uuid'],
                        user
                    })
                }
            }).catch(error => {
                console.error('获取单个用户失败', error)
            })
        }
    }
    initConfig(config: any): YZConfig {
        return new YZUserConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZUserStyle(style)
    }
}