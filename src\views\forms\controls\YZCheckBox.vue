<template>
    <div>
        <van-field v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required"
            :rules="field.config.rules" :readonly="field.readonly" :disabled="field.disabled"
            :label="field.config.label">
            <template #input>
                <van-checkbox-group v-model="vModel" :disabled="field.disabled" :direction="field.getPosition()"
                    @change="onCheckChange">
                    <van-checkbox :name="item.value" v-for="(item, index) in field.checkBoxOption.options" :key="index"
                        shape="square">{{
                            item.label
                        }}</van-checkbox>
                </van-checkbox-group>
            </template>
            <!-- <template v-else #input>
                <van-checkbox-group v-model="vModel" direction="vertical" :disabled="field.disabled">
                    <van-checkbox :name="item.value" v-for="(item, index) in field.checkBoxOption.options" :key="index"
                        shape="square">{{
                        item.label
                        }}</van-checkbox>
                </van-checkbox-group>
            </template> -->
        </van-field>
    </div>
</template>
<script lang="ts" setup>
import { YZCheckBox, YZCheckBoxConfig } from '@/logic/forms/YZCheckBox';
import { eventBus } from '@/utils/eventBus';
import { onMounted, ref } from 'vue';
import { computed, PropType } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZCheckBox>,
        required: true
    },
    modelValue: {
        type: [Array, String],
        default: []
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
onMounted(() => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value) //YZ +
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value) //YZ +

    // if(vModel.value){  //YZ -
    //     setFun()
    // }
})
const emit = defineEmits(['update:modelValue', 'update:optionValue'])
const config = props.field.config as YZCheckBoxConfig
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        return typeof props.modelValue == 'string' ? props.modelValue == '' ? [] : props.modelValue.split(',') : props.modelValue
    },
    set(val) {
        emit('update:optionValue', val.join(','))
        emit('update:modelValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const onCheckChange = () => {
    setFun()
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            const vpush = params.value.split(',')
            vModel.value = vpush
            setFun()
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
</script>
<style scoped>
.van-checkbox--horizontal {
    margin-right: 10px;
    margin-bottom: 4px;
}
</style>