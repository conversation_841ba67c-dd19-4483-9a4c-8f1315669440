
<template>
  <div>
    <van-field
      v-if="!field.hidden"
      :name="field.vModel.model"
      :required="field.config.required"
      :readonly="field.readonly"
      :disabled="field.disabled"
      :rules="field.config.rules"
      :label="field.config.label"
    >
      <template #label>
        <div v-if="field.config.showLabel">{{ field.config.label }}</div>
      </template>
      <template #input>
        <div
          class="code"
          :style="{
            width: config.getWidth() + 'px',
            height: config.getHeight() + 'px',
          }"
        >
          <img :src="config.getImgSrc()" alt="" />
        </div>
      </template>
    </van-field>
  </div>
</template>

<script lang="ts" setup>
import { YZStaticImg, YZStaticImgConfig } from "@/logic/forms/YZStaticImg";
import { eventBus } from "@/utils/eventBus";
import { computed, onMounted, PropType, ref, watch } from "vue";
const props = defineProps({
  field: {
    type: Object as PropType<YZStaticImg>,
    required: true,
  },
  modelValue: {
    type: [String, Number],
    default: 0,
  },
  index: {
    type: Number,
    defualt: -1,
  },
});
// const emit = defineEmits(['update:modelValue'])
// const isFirstLoad = ref<boolean>(true)
const config = props.field.config as YZStaticImgConfig;
//PC 未设置禁用和隐藏配置 先不做处理
// eventBus.on("setFieldDisable", {
//   cb: function (params) {
//     if (params.uuid === props.field.uuid) {
//       props.field.disabled = params.value;
//     }
//   },
// });
// eventBus.on("setFieldHidden", {
//   cb: function (params) {
//     if (params.uuid === props.field.uuid) {
//       props.field.hidden = params.value;
//     }
//   },
// });
</script>
<style lang="scss" scoped>
.code {
  border: 1px dashed #ddd7d7;
  max-width: 300px;
  max-height: 300px;
  img {
    // width: 100%;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}
</style>