<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="router.back()">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search"></div>
            <div class="btn" @click="Select">
                {{ $t('Global.Select') }}
            </div>
        </div>

        <div class="grap"></div>

        <div class="list">
            <div class="li">
                <div class="li-name">{{ $t('CSTC.ProjectNo') }}</div>
                <div class="li-info">{{ num }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.ProjectName') }}</div>
                <div class="li-info">{{ name }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.GPS') }}</div>
                <div class="li-info">{{ region }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const Select = () => {
    router.push({
        path: '/generateQRCode',
        query: {
            num_: num.value,
            name_: name.value,
            region: region.value,
            SupplierCode: route.query.SupplierCode,
            SupplierName: route.query.SupplierName
        },
        replace: true
    })
}
const num = ref(route.query.ProjectNumber || '')
const name = ref(route.query.ProjectName || '')
const region = ref(route.query.region || '')
onMounted(() => {
    // console.log("%c [ name ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", name.value)

})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;
            color: #00A0E1;
        }
    }

    .grap {
        width: 100%;
        height: 10px;
        background-color: #f9f9f9;
    }

    .list {
        padding: 10px 12px;

        .li {
            font-size: var(--yz-com-14);
            margin-bottom: 16px;

            .li-name {
                color: #999999;
            }

            .li-info {
                color: #444444;
                margin-top: 8px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>