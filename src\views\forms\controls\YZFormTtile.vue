<template>
    <div class="clearAfter" v-if="!field.hidden">
        <div class="title-desc">
         {{ field.getTitle() }}
        </div>
    </div>
</template>

<script lang="ts" setup>
import {ref,onMounted} from 'vue'
import { YZFormTtile } from '@/logic/forms/YZFormTtile';
import { eventBus } from '@/utils/eventBus';
import { PropType } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZFormTtile>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    isFirstLoad.value = false
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
</script>
<style  lang="scss" scoped>
.clearAfter::after {
    display: none;

}

.title-desc {
    text-align: center;
    font-weight: 400;
    color: var(--yz-icon-text-color);
    background: var(--yz-title-desc);
    padding: 10px 0px;
}
</style>