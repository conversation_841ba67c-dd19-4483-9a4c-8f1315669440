import { useLang } from "@/utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>g, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZStyle } from "./formModel";
export class YZCascaderConfig extends YZConfig {
    private caseShow: boolean;
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZCascader'
        this.caseShow = false

        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getShow() {
        return this.caseShow
    }
    setShow(show: boolean) {
        this.caseShow = show
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZCascaderStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export interface ICaseOption {
    id: number;
    text: string;
    value: any;
    children?: Array<ICaseOption>
}
export interface ICaseProps {
    children: string;
    text: string;
    value: string;
}
export class YZCascader extends YZField {
    private selectOption: YZCascaderOption
    private caseProp: ICaseProps;
    constructor(params: any) {
        super(params)
        this.vModel.value = ''
        this.vModel.optionValue = ''
        this.selectOption = new YZCascaderOption(params.options)
        this.caseProp = {
            text: params['props'].props.label,
            value: params['props'].props.value,
            children: params['props'].props.children
        }
    }
    initConfig(config: any): YZConfig {
        return new YZCascaderConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZCascaderStyle(style)
    }
    getOptions(): ICaseOption[] {
        return this.selectOption.options
    }
    getProps(): ICaseProps {
        return this.caseProp
    }
}
export class YZCascaderOption extends YZOption<Array<ICaseOption>> {
    initOption(options: any): ICaseOption[] {
        return options
    }
}