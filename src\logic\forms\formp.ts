import { isArray, isDefined } from "@/utils";
import { useGlobalEvents, useMobileEvents } from "./formsutil";

export interface IStaticItem {
    ItemType_None: number;
    ItemType_Value: number;
    ItemType_Operator: number;
    ItemType_Function: number
    PRI_OPENBRACKET: number,
    PRI_LOWEST: number,
    PRI_BELOWADD: number,
    PRI_ADD: number,
    PRI_MULT: number,
    PRI_EXP: number,
    PRI_UNARY: number,
    PRI_FCT: number,
}
const staticItem: IStaticItem = {
    ItemType_None: 0,
    ItemType_Value: 1,
    ItemType_Operator: 2,
    ItemType_Function: 3,
    PRI_OPENBRACKET: -1,
    PRI_LOWEST: 0,
    PRI_BELOWADD: 1,
    PRI_ADD: 2,
    PRI_MULT: 3,
    PRI_EXP: 4,
    PRI_UNARY: 5,
    PRI_FCT: 6,
}
const me: any = {
    $digit: function (a: any) {
        if (!a)
            return 0;

        var ar = a.toString().split('.');
        if (ar.length <= 1)
            return 0;

        return ar[1].length;
    },

    addOp: function (a: any, b: any) {
   
        var d1 = me.$digit(a),
            d2 = me.$digit(b),
            d = Math.max(d1, d2),
            m = Math.pow(10, d);

        return (Math.round(a * m) + Math.round(b * m)) / m;
    },

    subOp: function (a: any, b: any) {
      
        var d1 = me.$digit(a)

        var d2 = me.$digit(b)
        var d = Math.max(d1, d2);
        var m = Math.pow(10, d);
        return ((Math.round(a * m) - Math.round(b * m)) / m).toFixed(d);
    },

    multOp: function (a: any, b: any) {
  
        var d1 = me.$digit(a),
            d2 = me.$digit(b),
            v1 = Number(a.toString().replace('.', '')),
            v2 = Number(b.toString().replace('.', ''));

        return Number(v1) * Number(v2) / Math.pow(10, d1 + d2)
    },

    divOp: function (a: any, b: any) {
        var d1 = me.$digit(a),
            d2 = me.$digit(b),
            v1 = Number(a.toString().replace('.', '')),
            v2 = Number(b.toString().replace('.', ''));

        if (v2 == 0)
            return NaN;

        return (v1 / v2) * Math.pow(10, d2 - d1);
    },

    minusOp: function (a: any) {
        return -a;
    },

    modOp: function (a: any, b: any) {
        return a % b;
    },

    greaterThanOp: function (a: any, b: any) {
        return a > b;
    },

    lesserThanOp: function (a: any, b: any) {
        return a < b;
    },

    greaterEquOp: function (a: any, b: any) {
        return a >= b;
    },

    lesserEquOp: function (a: any, b: any) {
        return a <= b;
    },

    notEquOp: function (a: any, b: any) {
        return a != b;
    },

    andOp: function (a: any, b: any) {
        return a && b;
    },

    orOp: function (a: any, b: any) {
        return a || b;
    },

    notOp: function (a: any) {
        return !a;
    },

    equOp: function (a: any, b: any) {
        return a == b;
    },

    ifFn: function (c: any, a: any, b: any) {
        return c ? a : b;
    },

    minFn: function (vs: any) {
        if (vs.length == 0)
            vs.push(0);
        return Math.min.apply(undefined, vs);
    },

    maxFn: function (vs: any) {
        if (vs.length == 0)
            vs.push(0);
        return Math.max.apply(undefined, vs);
    },

    sumFn: function (vs: any) {

        var rv = 0;
        for (var i = 0; i < vs.length; i++)
            rv = me.addOp(rv, Number(vs[i]));

        return rv;
    },

    avgFn: function (vs: any) {
        var rv = 0;
        for (var i = 0; i < vs.length; i++)
            rv = me.addOp(rv, Number(vs[i]));
        return vs.length ? me.divOp(rv, vs.length) : 0;
    }
}
const ops = [
    { fn: me.addOp, pri: staticItem.PRI_ADD, symbol: '+', unary: false },
    { fn: me.subOp, pri: staticItem.PRI_ADD, symbol: '-', unary: false },
    { fn: me.multOp, pri: staticItem.PRI_MULT, symbol: '*', unary: false },
    { fn: me.divOp, pri: staticItem.PRI_MULT, symbol: '/', unary: false },
    { fn: Math.pow, pri: staticItem.PRI_EXP, symbol: '^', unary: false },
    { fn: me.minusOp, pri: staticItem.PRI_UNARY, symbol: '-', unary: true },
    { fn: me.modOp, pri: staticItem.PRI_MULT, symbol: '%', unary: false },
    { fn: me.greaterEquOp, pri: staticItem.PRI_BELOWADD, symbol: '>=', unary: false },
    { fn: me.lesserEquOp, pri: staticItem.PRI_BELOWADD, symbol: '<=', unary: false },
    { fn: me.greaterThanOp, pri: staticItem.PRI_BELOWADD, symbol: '>', unary: false },
    { fn: me.lesserThanOp, pri: staticItem.PRI_BELOWADD, symbol: '<', unary: false },
    { fn: me.notEquOp, pri: staticItem.PRI_BELOWADD, symbol: '!=', unary: false },
    { fn: me.andOp, pri: staticItem.PRI_LOWEST, symbol: '&&', unary: false },
    { fn: me.orOp, pri: staticItem.PRI_LOWEST, symbol: '||', unary: false },
    { fn: me.notOp, pri: staticItem.PRI_MULT, symbol: '!', unary: true },
    { fn: me.equOp, pri: staticItem.PRI_BELOWADD, symbol: '==', unary: false }
];
const functions = {
    "sin": { fn: Math.sin, nbArgs: 1, symbol: 'sin', rpt: false, isSysFunc: true },
    "asin": { fn: Math.asin, nbArgs: 1, symbol: 'asin', rpt: false, isSysFunc: true },
    "cos": { fn: Math.cos, nbArgs: 1, symbol: 'cos', rpt: false, isSysFunc: true },
    "acos": { fn: Math.acos, nbArgs: 1, symbol: 'acos', rpt: false, isSysFunc: true },
    "tan": { fn: Math.tan, nbArgs: 1, symbol: 'tan', rpt: false, isSysFunc: true },
    "atan": { fn: Math.atan, nbArgs: 1, symbol: 'atan', rpt: false, isSysFunc: true },
    "atan2": { fn: Math.atan2, nbArgs: 1, symbol: 'atan2', rpt: false, isSysFunc: true },
    "sqrt": { fn: Math.sqrt, nbArgs: 1, symbol: 'sqrt', rpt: false, isSysFunc: true },
    "abs": { fn: Math.abs, nbArgs: 1, symbol: 'abs', rpt: false, isSysFunc: true },
    "ceil": { fn: Math.ceil, nbArgs: 1, symbol: 'ceil', rpt: false, isSysFunc: true },
    "floor": { fn: Math.floor, nbArgs: 1, symbol: 'floor', rpt: false, isSysFunc: true },
    "log": { fn: Math.log, nbArgs: 1, symbol: 'log', rpt: false, isSysFunc: true },
    "pow": { fn: Math.pow, nbArgs: 2, symbol: 'pow', rpt: false, isSysFunc: true },
    "round": { fn: Math.round, nbArgs: 1, symbol: 'round', rpt: false, isSysFunc: true },
    "exp": { fn: Math.exp, nbArgs: 1, symbol: 'exp', rpt: false, isSysFunc: true },
    "if": { fn: me.ifFn, nbArgs: 3, symbol: 'if', rpt: false, isSysFunc: true },
    "min": { fn: me.minFn, nbArgs: 1, symbol: 'min', rpt: true, isSysFunc: true },
    "max": { fn: me.maxFn, nbArgs: 1, symbol: 'max', rpt: true, isSysFunc: true },
    "sum": { fn: me.sumFn, nbArgs: 1, symbol: 'sum', rpt: true, isSysFunc: true },
    "avg": { fn: me.avgFn, nbArgs: 1, symbol: 'avg', rpt: true, isSysFunc: true }
}
const consts: Record<string, any> = {
    "E": Math.E,
    "PI": Math.PI
};
export class formpt {
    formFuns: Record<string, any>;
    formOps: Array<any>
    curItemTypeStack: Array<any>;
    itemStack: any[]
    opStack: any[]
    vars: any[]
    exp: string
    nbPops: number;
    FirstCharStr: string;
    popPtr: number;
    isUsed: boolean;
    lastWord: string;
    curWord: string;
    allConstants: boolean;
    objItem: any
    constructor(express: string) {
        this.curItemTypeStack = []
        this.itemStack = []
        this.opStack = []
        this.vars = []
        this.exp = ''
        this.nbPops = 0
        this.popPtr = 0
        this.isUsed = false
        this.FirstCharStr = ''
        // 初始化系统方法
        this.formFuns = functions
        // 初始化内置方法
        this.formOps = ops
        this.curWord = ''
        this.lastWord = ''
        this.allConstants = false
        // 自定义全局方法
        const events = useGlobalEvents()
        // 移动端重写方法
        const mobileEvents = useMobileEvents()
        for (let i = 0; i < events.length; i++) {
            const key = events[i].name
            let fn = events[i].fun
            const mobile=mobileEvents.find(x=>x.name === key)
            if (mobile) 
                 fn = mobile.fun
            if (this.formFuns[key] && this.formFuns[key].isSysFunc)
                return;
            this.formFuns[key] = {
                fn: fn,
                nbArgs: -1,
                symbol: key,
                rpt: false
            }
        }
        this.objItem = this.parse(express)
    }
    /**
     *  移除表达式多余空白
     * @param express 表达式
     * @returns 返回表达式
     */
    removeSpace(express: string) {
        if (!express)
            return '';
        var l = express.length;
        var v = [];
        var f = false;
        for (var i = 0; i < l; i++) {
            var c = express.charAt(i);
            if (c == '\'' || c == '\"')
                f = !f;
            if ((c != ' ' && c != '\r' && c != '\n') || f)
                v.push(c);
        }
        return v.join('');
    }
    parse(exp: string) {
        exp = this.removeSpace(exp);
        if (!exp)
            return null;
        var sl = exp.length;
        let sc: string | number = 0;
        var wd = '';
        this.curItemTypeStack = [];
        this.itemStack = [];
        this.opStack = [];
        this.vars = [];
        this.exp = exp;
        this.pushCurItemType()

        for (var i = 0; i < sl; i++) {
            var c = exp.charAt(i);
            if (sc === 0) { if (c == '\'' || c == '\"') { sc = c; wd += c; continue; } }
            else { if (c === sc) { sc = 0; } else { wd += c; continue; } }

            var op = this.areNextCharsOpString(exp, i);
            if (op) {
                i += op.symbol.length - 1;
                this.onOp(i, wd, op);
                wd = '';
            }
            else {
                if (c == ',') {
                    this.onArgSeparator(i, wd);
                    wd = '';
                }
                else if (c == '(') {
                    this.onOpenBracket(i, wd);
                    wd = '';
                }
                else if (c == ')') {
                    this.onCloseBracket(i, wd);
                    wd = '';
                }
                else
                    wd += c;
            }
        }
        this.onExpEnd(sl - 1, wd);
    }
    evaluate(values: any) {
        var its = this.itemStack;
        if (its.length == 1 && !its[0].args)
            return its[0].isVar ? values[its[0].val] : its[0].val;

        var idx;
        for (var i = 0; i < its.length; i++) {
            var it = its[i];

            if (idx && idx.itm == it) {
                if (idx.i < idx.len) {
                    i = idx.start - 1;
                    continue;
                }
                else
                    idx = null;
            }

            if (it.beginrpt) {
                idx = { start: i + 1, len: 0, i: 0, itm: it.beginrpt };
                for (var j = 0; j < it.vars.length; j++) {
                    var vr = it.vars[j];
                    if (vr in values) {
                        var v = values[it.vars[j]];
                        v = isArray(v) ? v : [v];
                        idx.len = Math.max(idx.len, v.length);
                    }
                }
                continue;
            }

            if ((idx || { len: 1 }).len != 0) {
                var vs = [];
                for (var j = 0; j < it.args.length; j++) {
                    var arg = it.args[j];
                    var v = arg.isVar ? values[arg.val] : arg.val;
                    if (idx) {
                        v = isArray(v) ? v : [v];
                        v = v.length >= idx.len ? v[idx.i] : v[0];
                    }
                    if (it.eval.rpt) {
                        v = isDefined(v) ? v : [];
                        v = isArray(v) ? v : [v];
                    }
                    vs.push(v);
                }

                var rv = it.eval.fn.apply(me, vs);

                var parg = it.ParentArg;
                if (idx && parg.item == idx.itm) {
                    parg.val = idx.i == 0 ? [] : parg.val;
                    parg.val.push(rv);
                    idx.i++;
                }
                else
                    parg.val = rv;
            }
            else {
                it.ParentArg.val = [];
            }
        }
        return its[its.length - 1].ParentArg.val;
    }
    areNextCharsOpString(exp: string, pos: number) {
        if (!this.FirstCharStr) {
            var fcs = [];
            for (var i = 0; i < this.formOps.length; i++)
                fcs.push(this.formOps[i].symbol.charAt(0));
            this.FirstCharStr = fcs.join('');
        }

        var c = exp.charAt(pos);
        if (this.FirstCharStr.indexOf(c) == -1)
            return false;

        for (var i = 0; i < this.formOps.length; i++) {
            var op = this.formOps[i];
            var s = op.symbol;
            var l = op.symbolLength = op.symbolLength || s.length;
            var f = op.firstChar = op.firstChar || s.charAt(0);
            if (c != f)
                continue;

            var m = true;
            for (var j = 1; j < l; j++) {
                if (s.charAt(j) != exp.charAt(pos + j)) {
                    m = false;
                    break;
                }
            }

            if (m) return op;
        }

        return false;
    }
    pushCurItemType() {
        this.curItemTypeStack.push({ current: staticItem.ItemType_None, previous: staticItem.ItemType_None });
    }

    onOpenBracket(pos: any, wd: any) {
        if (!this.parseWord(pos, wd))
            return;

        this.setArgValueFlag(false);

        var opi = {
            pri: staticItem.PRI_OPENBRACKET,
            endWithABlock: false,
            itemType: staticItem.ItemType_None
        };
        this.opStack.push(opi);

        if (this.getCurItemType() == staticItem.ItemType_Function)
            this.itemStack.push({ isUsed: true, isABlock: true });

        this.pushCurItemType();
    }
    parseWord(p: any, w: any) {
        var rv = true;
        if (!w)
            return true;

        var citp = this.getCurItemType();
        if (citp != staticItem.ItemType_Operator && citp != staticItem.ItemType_None) {
            console.log('发生错误')
        }

        if (this.exp.charAt(p) == '(') {
            var f = this.formFuns[w];
            if (f) {
                this.setArgValueFlag(true);
                this.setCurItemType(staticItem.ItemType_Function);
                this.opStack.push({
                    pri: staticItem.PRI_FCT,
                    endWithABlock: true,
                    nbArgs: 0,
                    eval: f,
                    argValue: false,
                    itemType: staticItem.ItemType_Function
                });
            } else {
                console.log('发生错误')
            }

        }
        else if (this.isNumber(w)) {
            this.setCurItemType(staticItem.ItemType_Value);
            this.itemStack.push({ isEvaluated: true, val: parseFloat(w) });
            this.setArgValueFlag(true);
        }
        else if (this.isString(w)) {
            this.setCurItemType(staticItem.ItemType_Value);
            w = w.substring(1, w.length - 1);
            this.itemStack.push({ isEvaluated: true, val: w });
            this.setArgValueFlag(true);
        }
        else {
            var itm = { isEvaluated: true, isVar: false, val: '' };
            if (w in consts) {
                itm.val = consts[w];
            }
            else {
                itm.isVar = true;
                itm.val = w;
                if (!(w in this.vars))
                    this.vars.push(w);
            }

            this.setCurItemType(staticItem.ItemType_Value);
            this.itemStack.push(itm);
            this.setArgValueFlag(true);
        }

        this.lastWord = w;
        this.curWord = '';
        return rv;
    }
    getCurItemType() {
        var its = this.curItemTypeStack;
        return its[its.length - 1].current;
    }
    setArgValueFlag(b: any) {
        if (this.opStack.length > 0) {
            var t = this.opStack.length - 1;
            if (this.opStack[t].pri == staticItem.PRI_OPENBRACKET)
                t--;

            if (t >= 0)
                this.opStack[t].argValue = b;
        }
    }
    setCurItemType(type: any) {
        var its = this.curItemTypeStack;
        var last = its[its.length - 1];
        last.previous = last.current;
        last.current = type;
    }
    onCloseBracket(pos: any, wd: any) {
        this.onCloseArgSeparator(pos, wd, true);
    }

    onCloseArgSeparator(pos: any, wd: any, close: any) {
        if (!this.parseWord(pos, wd))
            return;
        if (this.getCurItemType() == staticItem.ItemType_Operator) {
            console.log('发生错误')
        }
        this.curItemTypeStack.pop();

        var nc = this.getNextChar(pos);
        if (!close) { //arg
            this.pushCurItemType();
            if (nc == ')') {
                console.log('发生错误')
            }
            // ZHYSoft.Error.raise(RS.$('YZStrings.Form_Express_SynErr003'), this.exp);
        }

        var cpri, popeditems = 0;
        var ops = this.opStack;
        do {
            if (ops.length == 0) {
                if (close) {
                    console.log('发生错误')
                }
                // ZHYSoft.Error.raise(RS.$('YZStrings.Form_Express_SynErr004'), this.exp);
                else {
                    console.log('发生错误')
                }
                // ZHYSoft.Error.raise(RS.$('YZStrings.Form_Express_SynErr003'), this.exp);
            }

            cpri = ops[ops.length - 1].pri;
            if (cpri != staticItem.PRI_OPENBRACKET) {
                popeditems++;
                var opi = ops.pop();
                this.addAndValidateToExprStack(opi);
            }
        } while (cpri != staticItem.PRI_OPENBRACKET);

        var isInc = this.incArgCountIfArgValue(ops);
        if (close) {
            ops.pop();
            if (this.getCurItemType() != staticItem.ItemType_Function)
                this.setCurItemType(staticItem.ItemType_Value);
        }

        if (popeditems == 0) {
            if (!close && !isInc) {
                console.log('发生错误')
            }
            //ZHYSoft.Error.raise(RS.$('YZStrings.Form_Express_SynErr003'), this.exp);
        }

        this.setArgValueFlag((close && (nc == ')' || nc == ',')) ? isInc : false);
    }
    getNextChar(pos: any) {
        return this.exp.charAt(pos + 1) || '';
    }
    incArgCountIfArgValue(ops: any) {
        if (ops.length > 0) {
            var idx = ops.length - 1;
            if (ops[idx].pri == staticItem.PRI_OPENBRACKET)
                idx--;

            if (idx >= 0) {
                if (ops[idx].argValue) {
                    if (ops[idx].itemType == staticItem.ItemType_Function)
                        ops[idx].nbArgs++;
                    return true;
                }
            }
        }
        return false;
    }
    addAndValidateToExprStack(opi: any) {
        if (opi.itemType == staticItem.ItemType_Function) {
            if (opi.eval.nbArgs != -1 && opi.eval.nbArgs != opi.nbArgs) {
                console.log('发生错误')
            }
        }

        var info = {
            eval: opi.eval,
            nbArgs: opi.nbArgs,
            args: [],
            endWithABlock: opi.endWithABlock
        };
        this.itemStack.push(info);

        try { this.evaluateValidate(); }
        catch (e) { console.log('发生错误') }
    }
    evaluateValidate() {
        this.allConstants = true;
        this.nbPops = 0;
        var its = this.itemStack;
        if (its.length == 0)
            return 0;

        var val = this.evaluateValidateItem(its.length - 1);
        its[its.length - 1].isUsed = true;

        for (var t = 0; t < its.length; t++)
            its[t].isUsed = false;

        return val;
    }
    evaluateValidateItem(idx: any) {
        var its = this.itemStack;
        var itm = its[idx];

        if (itm.isUsed) {
            console.log('发生错误')
        }
        if (itm.isEvaluated) {
            itm.isUsed = true;
            return itm.val;
        }
        this.popPtr = idx - 1;
        for (var i = itm.nbArgs - 1; i >= 0; i--) {
            var arg = itm.args[i] = itm.args[i] || { item: itm };
            var popv = this.popValidate(arg);
            arg.isVar = popv.isVar || false;
            const a = popv.val
            if (typeof a !== 'undefined') {
                arg.val = a
            } else {
                arg.val = null
            }
        }

        var val = { val: 0 };
        if (itm.endWithABlock) {
            if (!its[this.popPtr].isABlock)
                console.log('发生错误')
            else
                this.popPtr--;
        }
        return val;
    }
    popValidate(parentArg: any): Record<string, any> {
        this.nbPops++;
        if (this.nbPops < 0)
            console.log('发生错误')
        var val = {
            isVar: '',
            val: ''
        };
        var itm = this.itemStack[this.popPtr];
        if (itm.isABlock) {
            console.log('发生错误')
        }
        if (itm.isUsed)
            console.log('发生错误')

        itm.ParentArg = parentArg;
        if (itm.isEvaluated) {
            val.isVar = itm.isVar;
            val.val = itm.val;
            itm.isUsed = true;
            this.popPtr--;
        }
        else {
            val = this.evaluateValidateItem(this.popPtr);
            this.isUsed = true;
        }

        return val;
    }
    onExpEnd(pos: any, wd: any) {
        this.parseWord(pos, wd);
        var ops = this.opStack;
        var its = this.itemStack;
        var nbOps = ops.length;
        var invT = nbOps - 1;

        for (var i = ops.length - 1; i >= 0; i--) {
            var opi = ops[i];
            if (opi.pri == staticItem.PRI_OPENBRACKET) {
                console.log('发生错误')
            }
            this.addAndValidateToExprStack(opi);
        }

        var l = its.length;
        for (var i = l - 1; i >= 0; i--) {
            var itm = its[i];
            if (itm.isABlock || (itm.isEvaluated && i != l - 1)) {
                its.splice(i, 1);
            }
        }

        var l = its.length;
        for (var i = 0; i < l; i++) {
            var itm = its[i];
            if (itm.eval && itm.eval.rpt) {
                var j;
                var vs = [];
                for (j = i - 1; j >= 0; j--) {
                    var itm1 = its[j];
                    if (!this.isParent(itm, itm1))
                        break;
                    var args = itm1.args;
                    if (itm1.eval.rpt) {
                        console.log('发生错误');

                    }
                    for (var k = 0; k < args.length; k++) {
                        var arg = args[k];
                        if (arg.isVar)
                            vs.push(arg.val);
                    }
                }
                its.splice(j + 1, 0, { beginrpt: itm, vars: vs });
                l++;
                i++;
            }
        }
        var lit = its[its.length - 1];
        lit.ParentArg = lit.val || {};
    }
    isParent(itm: any, itm1: any) {
        while (itm1 && itm1.ParentArg) {
            itm1 = itm1.ParentArg.item;
            if (itm1 == itm)
                return true;
        }
        return false;
    }

    onOp(pos: any, wd: any, op: any) {
        if (!this.parseWord(pos, wd))
            return;

        this.setArgValueFlag(true);
        this.setCurItemType(staticItem.ItemType_Operator);

        var pitp = this.getPrevItemType();
        var unary = (pitp == staticItem.ItemType_None || pitp == staticItem.ItemType_Operator);

        if (op.unary != unary) {
            var s = op.symbol;
            for (var i = 0; i < this.formOps.length; i++) {
                var op1 = this.formOps[i];
                if (op1.symbol == s && op1.unary == unary) {
                    op = op1;
                    break;
                }
            }
            if (op.unary != unary)
                console.log("发生错误");
        }

        if (pos + op.symbol.length > this.exp.length)

            console.log("发生错误");


        var ops = this.opStack;
        if (ops.length > 0) {
            var ppri = ops[ops.length - 1].pri;
            var cpri;
            if (ppri != staticItem.PRI_OPENBRACKET)
                cpri = op.pri;

            if (cpri == ppri && ops[ops.length - 1].itemType == staticItem.ItemType_Operator && ops[ops.length - 1].eval.unary)
                cpri = ppri + 1; // skip the while

            while (cpri <= ppri) {
                var opsitm = ops.pop();
                this.addAndValidateToExprStack(opsitm);

                if (this.opStack.length == 0)
                    break;

                ppri = ops[ops.length - 1].pri;
                if (ppri == staticItem.PRI_OPENBRACKET)
                    break;
            }
        }

        var opi = {
            nbArgs: unary ? 1 : 2,
            pri: op.pri,
            endWithABlock: false,
            eval: op,
            itemType: staticItem.ItemType_Operator
        };
        ops.push(opi);
    }
    getPrevItemType() {
        var its = this.curItemTypeStack;
        return its[its.length - 1].previous;
    }
    onArgSeparator(pos: any, wd: any) {
        this.onCloseArgSeparator(pos, wd, false);
    }
    isNumber(w: any) {
        if (typeof w === 'number') {
            return true
        }
        if (typeof w !== 'string') {
            false
        }
        var l = w.length, d;

        for (var i = 0; i < l; i++) {
            if (!d && i >= 15) //超过16位
                return false;

            var c = w.charCodeAt(i);

            if (c == 46) {
                if (d) { return false; } else { d = true; }
            }
            else if (c < 48 || c > 57)
                return false;
        }
        return !(l == 0 || (!d && l != 1 && w.charCodeAt(0) == 48));
    }
    isString(w: any) {
        if (typeof w !== 'string') {
            return false
        }
        const n = /^[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u2028\u2029\u202f\u205f\u3000]+|[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u2028\u2029\u202f\u205f\u3000]+$/g
        if (w) {
            if (w) {
                w = w.replace(n, "")
            } else {
                w = w || ''
            }

        }
        var l = w.length;
        if (l < 2)
            return false;

        var s = w.charAt(0),
            e = w.charAt(l - 1);

        return (s == e && (s == '\'' || s == '"'));
    }
}