
import { use<PERSON>ost<PERSON><PERSON> } from "@/utils/request";
import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
import { url } from "@/api/url";
import { eventBus } from "@/utils/eventBus";
import { useLang } from "@/utils";
export class YZUsersConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZUsers'
        this.defaultRules = [{ required: true, message: useLang('Form.SelectUser') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZUsersStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class Y<PERSON><PERSON>sers extends Y<PERSON><PERSON><PERSON> {
    constructor(params: any) {
        super(params)
        const modelValue = params['__vModel__'].modelValue
        this.vModel.value = modelValue
        if (modelValue) {
            usePostBody(url.process.getSimpleUser, {}, {
                accounts: modelValue.split(',')
            }).then(result => {
                if (result && result.length > 0) {
                    eventBus.emit('setMultipleUser', false, {
                        uuid: params['uuid'],
                        data: result
                    })
                }

            }).catch(error => {
                console.error('获取多个用户失败', error)
            })
        }
        //this.vModel.value = ''
        //this.vModel.optionValue=''
    }
    initConfig(config: any): YZConfig {
        return new YZUsersConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZUsersStyle(style)
    }
}