import { url } from "@/api/url"
import { useHomeStore } from "@/store/home"
import { useGetQuery } from "@/utils/request"
import { onMounted, ref, reactive, computed } from "vue"
import { useRouter } from "vue-router"
import { IColor, ITabModel } from "./layoutModel"
import { IUserInfo } from "../login/loginModel"
import { useUserStore } from "@/store/users"
function useLayout() {
    const homeStore = useHomeStore()
    const userStore = useUserStore()
    const router = useRouter()
    const activeName = computed(
        {
            get: () => homeStore.getActivePath,
            set: (val) => homeStore.setActivePath(val)
        }
    )
    const colorInfo = reactive<IColor>({
        BGCOLOR: '',
        SELECTCOLOR: '',
        USELECTCOLOR: ''
    })
    const tabBarData = ref<Array<ITabModel>>([])
    const tabShow = computed(()=>homeStore.getTab)
    const countNum =computed(()=>homeStore.getAllCount)
    onMounted(async () => {
        await initTab()
        await initUserInfo()
    })
    const initTab = async () => {
        tabBarData.value.push({
            CODE:'home',
            TITLECN:'Home',
            TITLEEN:'home',
            ICON:'fa fa-desktop',
            ID:1,
            TOURL:'/layout/home'
        },
        {
            CODE:'work',
            TITLECN:'Work',
            TITLEEN:'work',
            ICON:'fa fa-list-alt',
            ID:1,
            TOURL:'/layout/work'
        },
        {
            CODE:'my',
            TITLECN:'My',
            TITLEEN:'my',
            ICON:'fa fa-user',
            ID:3,
            TOURL:'/layout/my'
        }
        )
    }
    const onChange = (index: string) => {
        router.push({ path: index })
    }
    const initUserInfo = async ( ) => {
        const { success, user } = await useGetQuery(url.login.getUserInfo, false)
        if (success) {
          userStore.setUserInfo(user as IUserInfo)
        }
      }
      
    return {
        activeName,
        onChange,
        colorInfo,
        countNum,
        tabBarData,
        tabShow
    }
}
export {
    useLayout
}