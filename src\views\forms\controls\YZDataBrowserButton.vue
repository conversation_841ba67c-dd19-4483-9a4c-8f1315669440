<template>
    <div class="clearAfter">
        <van-button v-if="!field.hidden" style="width: 100%;" size="small" type="primary" :disabled="field.disabled"
            v-show="!field.hidden" icon="search" @click="openSource">{{ field.config.label }}</van-button>
        <DataSourceBtn v-model:is-show="sourceShow" :field="field" @onDataSave="onDataSave" />
    </div>
</template>

<script lang="ts" setup>
import DataSourceBtn from '@/components/DataSourceBtn.vue';
import { YZDataBrowserButton, YZDataBrowserButtonConfig } from '@/logic/forms/YZDataBrowserButton';
import { parseExpress } from '@/logic/forms/express';
import { onMounted, PropType, ref, computed } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { deepCopy } from "@/utils";
import { YZField } from '@/logic/forms/formModel';
import { useProcessStore } from "@/store/process";
import { formBuilder } from '@/logic/forms/formBuilder';
import { showNotify } from 'vant';

const processStore = useProcessStore();
const getProcLoad: any = computed(() => processStore.getProcLoad)
let taskIns = sessionStorage.getItem('taskInstance') || "{}"
let taskInstance = JSON.parse(taskIns)
const props = defineProps({
    field: {
        type: Object as PropType<YZDataBrowserButton>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    },
    // isMainBtn: {
    //     type: Boolean,
    //     default: true
    // }
})
const sourceShow = ref<boolean>(false)
const config = props.field.config as YZDataBrowserButtonConfig
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    isFirstLoad.value = false
})
const openSource = () => {
    if (!config.getDataBoweswer()) {
        showNotify({ type: 'danger', message: '未配置任何数据源' })
        return
    }
    if (!props.field.disabled) {
        sourceShow.value = true
    }
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
// 在YZTable组件里面触发onDetailMap事件
const onDataSave = (data: any) => {
    let list = deepCopy(data).map((item: { onlyId: any; }) => {
        delete item.onlyId
        return item
    })
    const ds = config.getDataBoweswer()
    // 填充明细
    eventBus.emit("onDetailMap", true, {
        list,
        map: ds,
        targetFormId: props.field.targetFormId,
        tableName: props.field.tableName
    })
    if (data && data.length > 0)
        onToMainOrTableField(data)

}
const onToMainOrTableField = (ndata: any) => {
    const data = ndata[0]
    // 提取map信息
    const { $map } = config.getDataBoweswer()

    if ($map) {
        const mapFields: YZField[] = []
        Object.keys($map).forEach(item => {
            const name = item
            const value = $map[name] as string
            let mapKey = value ?? name
            // 获取指定的控件
            let formId = props.field.targetFormId
            let field = parseExpress.getFieldByFid(formId, value)
            // let field = parseExpress.getField(value)
            if (value.indexOf(".") > -1 || value.indexOf("子表") > -1 || value.split('.').length === 2) {
                let fex = value.split('.')[0]
                let fieldModel = value.split('.')[1]
                field = parseExpress.getFieldByFid(formId, fex) // parseExpress.getField(fex)
                const fieldValue = field?.vModel.value
                if (fieldValue && fieldValue.length > 0) {
                    let newIndex = props.index
                    if (newIndex === -1) {
                        // 表示当前操作的控件是主表，但是要给字表赋值
                        fieldValue.forEach((f: any, index: number) => {
                            const items = fieldValue[index].colums as Array<any>
                            const xfield = items.find(x => x.field.vModel.model === fieldModel)
                            if (xfield) {
                                sendField(data, name, xfield.field)
                                // mapFields.push(xfield.field)
                            }
                        })
                    } else {
                        const items = fieldValue[newIndex].colums as Array<any>
                        const xfield = items.find(x => x.field.vModel.model === fieldModel)
                        if (xfield)
                            sendField(data, name, xfield.field)
                        // mapFields.push(xfield.field)
                    }
                    // for(let i =0; i<fieldValue.length;i++) {

                    // }
                    // const newIndex = props.index
                    // const items = fieldValue[newIndex].colums as Array<any>
                    // const xfield = items.find(x => x.field.vModel.model === fieldModel)
                    // if (xfield)
                    //     field = xfield.field
                }
            } else {
                let fields = parseExpress.getFiledsByFid(formId,)
                let field = fields.find(item => item.vModel && item.vModel.model === value)
                if (field) {
                    // field.vModel.value = data[name] //YZ -
                    eventBus.emit('onMap', true, {  //YZ +
                        value: data[name],
                        model: field.vModel.model,
                        uuid: field.uuid,
                        index: props.index
                    })
                }
            }
            // if(field) 
            //    mapFields.push(field)
            //   mapFields.forEach(field=>{
            //     let newValue = data[name]
            //     if (!newValue) {
            //         const vName = name.toLocaleUpperCase()
            //         Object.keys(data).forEach(item => {
            //             const vItem = item.toUpperCase()
            //             if (vItem === vName)
            //                 newValue = data[item]
            //         })
            //     }
            //     console.log('明细复制',field,field.uuid,field.vModel.value)
            //     eventBus.emit('setFieldValue', true, {
            //         value: newValue,
            //         model: field.vModel.model,
            //         uuid: field.uuid,
            //         index: props.index
            //     })
            // })

        })
    }
}

const sendField = (data: any, name: string, field: YZField) => {
    let newValue = data[name]
    if (!newValue) {
        const vName = name.toLocaleUpperCase()
        Object.keys(data).forEach(item => {
            const vItem = item.toUpperCase()
            if (vItem === vName)
                newValue = data[item]
        })
    }
    if (field.vModel.model === '附件属性') {
        eventBus.emit('setFieldValue', true, {
            value: newValue,
            model: field.vModel.model,
            uuid: field.uuid,
            index: props.index
        })
    }


}
</script>
<style lang="scss" scoped>
.clearAfter::after {
    display: none;

}



// .right {
//     text-align: right;
//     float: right;
//     margin: 5px;
//     z-index: 0;
// }</style>