<template>
    <div class="html-editorarea">
        <van-field v-if="!field.hidden" v-model="vModel" :name="field.vModel.model" :label="field.config.label"
            :required="field.config.required" :disabled="field.disabled" :rules="field.config.rules"
            :placeholder="field.placeholder">
            <template #input>
                <Editor class="full-editor" v-model="vModel" api-key="no-api-key" :init="init"
                    :disabled="field.disabled" @change="onChange" />
            </template>
        </van-field>




    </div>
</template>

<script lang="ts" setup>
import { url } from '@/api/url';
import { usePostBody } from '@/utils/request';
import { YZHtmleDitorArea, YZHtmleDitorAreaConfig } from '@/logic/forms/YZHtmleDitorArea';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, onUnmounted, PropType, ref, watch, reactive } from 'vue';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
// import { DecoupledEditor } from 'ckeditor5'
import 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'


const props = defineProps({
    field: {
        type: Object as PropType<YZHtmleDitorArea>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
const config = props.field.config as YZHtmleDitorAreaConfig
const readOnly = computed(() => props.field.config.extends.readOnly ?? props.field.readonly)

const imageUpload = (blobInfo) => new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('fileToUpload', blobInfo.blob(), blobInfo.filename())
    usePostBody(url.form.updateAttach, {}, formData).then(result => {
        if (result && result.length > 0) {
            result.forEach((item: any) => {
                let url = `/bpm/attachment/file/${item.fileId}?access_token=ZHYSoftToken`
                resolve(url)
            })
        } else {
            reject('上传错误！')
        }
    }).catch(() => {
        reject('上传错误！')
    })

})
const init = ref({
    font_size_formats: '10px 12px 14px 16px 18px 20px 22px', //YZ +
    language: 'zh_CN', // 设置语言为简体中文
    language_url: 'tinymce/js/tinymce/langs/zh_CN.js',
    toolbar_mode: 'sliding',
    plugins: 'link image table charmap ',
    toolbar: ' undo redo bold italic link bullist numlist indent outdent image blockquote table backcolor forecolor fontsize underline removeformat charmap   ', //strikethrough |    mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | emoticons  |
    tinycomments_mode: 'embedded',
    tinycomments_author: 'Author name',
    mergetags_list: [
        { value: 'First.Name', title: 'First Name' },
        { value: 'Email', title: 'Email' },
    ],
    convert_urls: false,
    images_upload_handler: imageUpload,
    height: 100, // 设置编辑器的高度
    min_height: props.field.getMaxHeight()?.minHeight, // 设置最小高度
    max_height: props.field.getMaxHeight()?.maxHeight,  // 设置最大高度
    content_style: "p{margin-block-start: 2px; margin-block-end: 2px;}body {min-height: auto;}"
})
const onChange = (e) => { //YZ +
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
onMounted(() => {
    isFirstLoad.value = false
    window.addEventListener('touchmove', stopTextarea, true)
})
onUnmounted(() => {
    window.removeEventListener('touchmove', stopTextarea)
})
const stopTextarea = (e: any) => {
    let target = e.target
    if (target && target.tagName === 'TEXTAREA') {
        e.stopPropagation();
    }
}
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.vModel.value = String(params.value)
        }
    }
})

eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
// 监听数据MAP
eventBus.on('onMap', {
    cb: function (params) {
        console.log("%c [ params 4]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
            props.field.vModel.value = params.value
        }
    }
})
</script>
<style scoped>
.html-editorarea {
    .van-field {
        display: flex;
        flex-direction: column;
    }

    :deep() .van-cell__value {
        width: 100%;
    }

    :deep() .van-cell__value .van-field__body .van-field__control.van-field__control--custom {
        width: 100%;
        display: block;
    }

    .full-editor :deep p {
        margin-block-start: 2px;
        margin-block-end: 2px;
    }
}
</style>
<style>
.tox-tinymce-aux {
    z-index: 1000000000 !important;
}
</style>