<template>
    <div class="box">
        <div class="search-div">
            <div class="back" @click="router.back()">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="btn" @click="confirm">
                {{ $t(qs_title) }}
            </div>
        </div>
        <div class="mb" v-if="!symbol_">
            <div id="reader" width="600px"></div>
        </div>
        <div class="mb_" v-if="symbol_">
            <div class="tit">
                <div class="t">{{ $t('CSTC.CheckInTime') }}：</div>
                <div style="margin-left: 50px;" class="t_">{{ currentTime }}</div>
            </div>
            <div class="tit">
                <div class="t">{{ $t('CSTC.Sign_in_name') }}：</div>
                <div style="margin-left: 50px;" class="t_">{{ Account }}</div>
            </div>
            <div class="tit">
                <div class="t">{{ $t('CSTC.TrainingCategory') }}：</div>
                <div style="margin-left: 50px;" class="t_">{{ sign_arr[0]?.TrainingCategory }}</div>
            </div>
            <div class="tit">
                <div class="t">{{ $t('CSTC.ProjectLocation') }}：</div>
                <div style="margin-left: 28px;" class="t_">{{ sign_arr[0]?.ProjectNo }}</div>
            </div>
            <div class="tit">
                <div class="t">{{ $t('CSTC.ProjectName') }}：</div>
                <div style="margin-left: 50px;" class="t_">{{ sign_arr[0]?.ProjectName }}</div>
            </div>
            <div class="tit">
                <div class="t">{{ $t('CSTC.SupplierCode') }}：</div>
                <div style="margin-left: 35px;" class="t_">{{ sign_arr[0]?.SupplierCode }}</div>
            </div>
            <div class="tit">
                <div class="t">{{ $t('CSTC.SupplierName') }}：</div>
                <div style="margin-left: 35px;" class="t_">{{ sign_arr[0]?.SupplierName }}</div>
            </div>

            <div class="foot">
                <van-button @click="Sign_in_out" class="subbtn-text" color='#00A0E1' block type="primary"
                    native-type="submit" :loading="Loading">
                    {{ $t(qs_title) }}
                </van-button>
            </div>

        </div>
    </div>
</template>

<script setup>
import { onMounted, computed, ref, reactive, onUnmounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Html5QrcodeScanner, Html5Qrcode } from "html5-qrcode";
import { Sign_in_and_out, SignIn, SignOut } from '@/service/user'
import { useFormData, useLang, TodaysDateFormat, formatDate, shortTimezoneName } from '@/utils'
import { showNotify } from 'vant';
import { usePostBody, useGetQuery } from '@/utils/request';
import { url } from '@/api/url';

const router = useRouter()
const route = useRoute()
const Account = ref()
const qs_title = ref('')
const symbol_ = ref('')
const Sign = ref('')
const scanDT = ref('')
const Loading = ref(false)
const sign_arr = ref([])
const latitude = ref()
const longitude = ref()
const currentTime = ref(new Date().toLocaleString().replace(/(\d{4})[^\d](\d{1,2})[^\d](\d{1,2}).*/, '$1-$2-$3').replace(/-(\d)\b/g, '-0$1'))
const currentTime_ = ref(new Date())
// const timeVal = ref(currentTime_.value.toLocaleString().replace(/(\d{4})[^\d](\d{1,2})[^\d](\d{1,2}).*/, '$1-$2-$3').replace(/-(\d)\b/g, '-0$1')) //(new Date().toISOString().split('T')[0])
const formatOffsetToHhMm = (offsetInMins) => {
    let negative = offsetInMins < 0 ? "+" : "-";
    let positiveMins = Math.abs(offsetInMins);
    let hours = Math.floor(positiveMins / 60);
    let mins = Math.floor((positiveMins - ((hours * 3600)) / 60));
    if (hours < 10) { hours = "0" + hours; }
    if (mins < 10) { mins = "0" + mins; }
    return negative + hours + ':' + mins;
}
// const TimezoneOffset = ref(formatOffsetToHhMm(currentTime_.value.getTimezoneOffset()))

const TimezoneOffset = ref(shortTimezoneName(new Date()))
const state = reactive({
    html5QrCode: null
});
const getCamerasInfo = () => {
    Html5Qrcode.getCameras()
        .then((devices) => {
            if (devices && devices.length) {
                state.html5QrCode = new Html5Qrcode("reader");
                startScan();
            }
        })
        .catch((err) => {
            console.log("%c [ 请设置摄像头访问权限 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
            showNotify({ message: useLang('SSI.Camera_permission_tips'), type: 'danger', duration: 2000 })
            // uni.postMessage({
            //     data: {
            //         type: 'scan',
            //         data: 'error'
            //     }
            // });
        });
};
const startScan = () => {
    state.html5QrCode
        .start(
            { facingMode: "environment" },  //environment 后置摄像头 || user前置摄像头
            {
                fps: 1,
                qrbox: { width: 250, height: 250 },
            },
            async (decodedText, decodedResult) => {
                console.log("%c [ decodedText ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", decodedText)
                if (decodedText) {
                    symbol_.value = 'in' //展示签到后的页面
                    scanDT.value = decodedText
                    try {
                        const formdata = useFormData({
                            f: btoa(JSON.stringify({ "CaseNo": { "op": "=", "value": decodedText } })),
                            c: 'WyJQcm9qZWN0Tm8iLCJQcm9qZWN0TmFtZSIsIlN1cHBsaWVyQ29kZSIsIlN1cHBsaWVyTmFtZSIsIlRyYWluaW5nQ2F0ZWdvcnkiXQ==', //["ProjectNo","ProjectName","SupplierCode","SupplierName","TrainingCategory"]
                            o: ''
                        })
                        const get_Sign_res = await Sign_in_and_out(formdata)
                        console.log("%c [ get_Sign_res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", get_Sign_res?.data)
                        if (get_Sign_res.status === 200) {
                            sign_arr.value = get_Sign_res?.data || []//[{ "ProjectNo": "123","ProjectName": "234","SupplierCode": null,"SupplierName":null,"TrainingCategory":"Working at heights"}] 
                            if (sign_arr.value) {
                                stopScan()
                            }
                        }
                    } catch (error) {
                        console.log("%c [ submit_QR_error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
                    }
                } else {
                    console.log('such as ==>> DT2024110017, CaseNo is no data')
                }
            }
        )
        .catch((err) => {
            console.log("扫码失败===>>>>>", err);
        });
};
const stopScan = () => {
    state.html5QrCode
        .stop()
        .then((a) => {
            console.log("已停止扫码", a);
        })
        .catch((err) => {
            console.log("%c [ 停止失败 err ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", err)
        });
};

const getLocation = () => {
    try {
        if ("geolocation" in navigator) {
            navigator.geolocation.getCurrentPosition(successCallback, errorCallback);
        } else {
            showNotify({ message: useLang('CSTC.Location_tips'), type: 'danger' })
        }
    } catch (error) {
        showNotify({ message: useLang('CSTC.enable_location_information_tips'), type: 'danger' })
    }
}
function successCallback(position) {
    latitude.value = position.coords.latitude; //纬度
    longitude.value = position.coords.longitude; //，经度
}
function errorCallback(error) {
    console.error("获取位置信息失败: " + error.message);
    showNotify({ message: useLang('CSTC.Location_failed_tips'), type: 'danger' })
}

const Sign_in_out = async () => {
    if (latitude.value && longitude.value) {
        getAddress(latitude.value, longitude.value)
    } else {
        showNotify({ message: useLang('CSTC.enable_location_information_tips'), type: 'danger' })
    }
}
const getAddress = async (latitude, longitude) => {
    console.log("%c [ 经度 --- longitude ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", longitude)
    console.log("%c [ 纬度 --- latitude ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", latitude)

    Loading.value = true
    const params = {
        "header": {
        },
        "formData": {
            "account": Account.value,
            "type": new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'Linde' : 'Supplier',
            "location": longitude ? `${String(longitude) + ',' + String(latitude)}` : null,
            "address": longitude ? `${String(longitude) + ',' + String(latitude)}` : null,
        }
    }
    if (Sign.value === 'in') { //Sign.value === in 签到  || out 签退
        try {
            params.formData.checkinDatetime = formatDate(new Date()) //TodaysDateFormat()
            params.formData.timezoneOffset = TimezoneOffset.value
            const Sign_in_res = await SignIn(params, scanDT.value)
            console.log("%c [ 签到_res_params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
            if (Sign_in_res.data.success) {
                Loading.value = false
                showNotify({ message: useLang('CSTC.SignInSuccessfully'), type: 'success' })
                router.back()
            } else {
                Loading.value = false
                showNotify({ message: Sign_in_res.data.errorMessage, type: 'danger' })
            }
        } catch (error) {
            Loading.value = false
            showNotify({ message: useLang('CSTC.SignInFailed'), type: 'danger' })
            console.log("%c [ SignIn---error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
        }
    } else {
        try {
            params.formData.checkoutDatetime = formatDate(new Date()) //TodaysDateFormat()
            console.log("%c [ 签退_res_params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
            const Sign_out_res = await SignOut(params, scanDT.value)
            // console.log("%c [ 签退_res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", Sign_out_res)
            if (Sign_out_res.data.success) {
                Loading.value = false
                showNotify({ message: useLang('CSTC.SignOutSuccessfully'), type: 'success' })
                router.back()
            } else {
                Loading.value = false
                showNotify({ message: useLang('CSTC.SignOutFailed'), type: 'danger' })
            }
        } catch (error) {
            Loading.value = false
            showNotify({ message: useLang('CSTC.SignOutFailed'), type: 'danger' })
            console.log("%c [ SignOut---error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
        }
    }
}
const getUserInfo = async () => {
    const { success, user } = await useGetQuery(url.login.getUserInfo);
    console.log("%c [ getUserInfo ===>>> ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #0286a8); color:yellow;", user)
    if (success) {
        Account.value = user?.DisplayName
    } else {
        console.log('scan getUserInfo error')
    }
}
onMounted(() => {
    getUserInfo()
    qs_title.value = route?.query?.qs_title
    Sign.value = route?.query?.type
    console.log("%c [scan ---Sign--- route?.query ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route?.query)
    // console.log("%c [ selectedConstraints ===>>> ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #0286a8); color:yellow;", selectedConstraints.value.facingMode)
    getCamerasInfo();
    getLocation();
})
onUnmounted(() => {
    if (state.html5QrCode?.isScanning) {
        stopScan();
    }
});

</script>

<style scoped lang="scss">
.box {
    background-color: var(--van-background-2);
    position: relative;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;
        box-shadow: 0 1px 4px rgba(0, 0, 0, .1);

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            position: absolute;
            top: 50%;
            left: 43%;
            transform: translateY(-50%);
        }
    }

    .mb {
        // margin-top: 15%;
    }

    .mb_ {
        margin-top: 5%;

        .foot {
            width: 100%;
            box-sizing: border-box;
            padding: 16px;
            position: fixed;
            bottom: 0;
            z-index: 99;
        }
    }

    .tit {
        display: flex;
        align-items: center;
        margin-bottom: 25px;
        margin-left: 16px;
        // border-bottom: 1px solid #ccc;

        .t {
            color: #828282;
        }

        .t_ {
            color: #00a0e1;
        }
    }
}
</style>