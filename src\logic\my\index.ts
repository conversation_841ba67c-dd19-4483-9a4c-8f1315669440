import { useUserStore } from "@/store/users"
import { computed, ref } from 'vue'
import { IMyMenu } from "./myModel"
import { useEnviroMent } from "@/utils"
import { useRouter } from 'vue-router'
import { useGetQuery } from "@/utils/request"
import { url } from "@/api/url"
import { useLoingStore } from "@/store/login"
import Cookies from 'js-cookie'

function useMy() {
    const user = useUserStore()
    const loginStore = useLoingStore()
    const userInfo = computed(() => user.getUserInfo)
    const env = useEnviroMent()
    const router = useRouter()
    console.log('当前用户===>>>>', userInfo.value)
    let host = process.env.NODE_ENV === 'development' ? window.webConfig.devhost : window.webConfig.host //window.webConfig.host = ./   ||    window.webConfig.devhost = /api/
    let portraitUrl = userInfo.value?.PortraitUrl
    if (portraitUrl && portraitUrl.charAt(0) === '/') {
        portraitUrl = portraitUrl.slice(1)
    }
    const userImage = computed(() => host + portraitUrl)
    const menu = ref<Array<IMyMenu>>([
        {
            name: 'my.Out_of_office_settings', isPop: false, component: 'OutOfficePop', iconName: 'comment-circle',
            handler: (index: number): void => {
                menu.value[index].isPop = true
            },
            onBack(name): void {
                const findex = menu.value.findIndex(x => x.name === name)
                menu.value[findex].isPop = false
            },
            color: '#54d1ec'
        },
        {
            name: 'my.SwitchEnvironment', isPop: false, component: 'Environment', iconName: 'records',
            handler: (index: number): void => {
                menu.value[index].isPop = true
            },
            onBack(name): void {
                const findex = menu.value.findIndex(x => x.name === name)
                menu.value[findex].isPop = false
            },
            color: '#55aed8'
        },
        // Linde 语言设置
        // {
        //     name: 'my.LanguageSettings', isPop: false, component: 'SetLangPop', iconName: 'records',
        //     handler: (index: number): void => {
        //         menu.value[index].isPop = true
        //     },
        //     onBack(name): void {
        //         const findex = menu.value.findIndex(x => x.name === name)
        //         menu.value[findex].isPop = false
        //     },
        //     color: '#d822a0'
        // },
        {
            name: 'my.system', isPop: false, component: 'SystemPop', iconName: 'tosend',
            handler: (index: number): void => {
                menu.value[index].isPop = true
            },
            onBack(name): void {
                const findex = menu.value.findIndex(x => x.name === name)
                menu.value[findex].isPop = false
            },
            color: '#55aed8'

        },
        // {
        //     name: 'my.version', isPop: false, component: 'VersionPop', iconName: 'tosend',
        //     handler: (index: number): void => {
        //         menu.value[index].isPop = true
        //     },
        //     onBack(name): void {
        //         const findex = menu.value.findIndex(x => x.name === name)
        //         menu.value[findex].isPop = false
        //     },
        //     color: '#55aed8'

        // },

        // {
        //     name: 'my.about', isPop: false, iconName: 'eye-o', component: 'AboutPop',
        //     handler: (index: number): void => {
        //         menu.value[index].isPop = true
        //     },
        //     onBack(name): void {
        //         const findex = menu.value.findIndex(x => x.name === name)
        //         menu.value[findex].isPop = false
        //     },
        //     color: '#a00a41'

        // }
    ])
    const handleCallBack = () => {

    }
    const href_ = window.location.origin //ppa.dev.lidnele.cn
    const handlerOut = async () => {
        Cookies.remove('PPA.Auth.Cookies')   //cf modify
        loginStore.restart()
        sessionStorage.clear()
        localStorage.setItem('Account', '')
        localStorage.setItem('SubjectType', '')
        localStorage.setItem('langInfo', '')

        if (!localStorage.getItem('Account')) {
            const data = await useGetQuery(url.login.loginOut)
            console.log("%c [ data?.logoutPath ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data?.logoutPath)
            if (data?.success) {
                if (localStorage.getItem('url_appid') === 'com.yzsoft.lindeapp' || localStorage.getItem('url_appid') === 'com.yzsoft.com' || localStorage.getItem('url_appid') === 'ppa.lindele.cn') {
                    // alert('logout 两个入口')
                    // window.location.href = href_  //两个入口的登录页   ppa谷歌测试包 --- com.yzsoft.com || ppa谷歌 ppa.lindele.cn ---上下登录入口  || ppa系统应用宝 --- 上下登录入口 
                    window.location.href = data?.logoutPath + '?uri=/Home/Portal'
                } else {
                    // alert('logout 1个入口')
                    window.location.href = data?.logoutPath
                }
            } else {
                console.log('登出失败')
            }
        }
    }
    return {
        userInfo,
        menu,
        handleCallBack,
        userImage,
        handlerOut
    }
}
export {
    useMy
}
