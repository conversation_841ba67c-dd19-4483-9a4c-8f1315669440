<template>
    <div>
        <van-popup v-model:show="show" position="right" style="height: 100vh; width: 100%">
            <div class="service-div">
                <van-nav-bar :title="$t(item.name)" :left-text="$t('Global.Back')" left-arrow
                    @click-left.stop="item.onBack(item.name)">
                    <template #right>
                        <div style="color:#1989fa" @click="saveEnvironments">{{ $t('Global.Save') }}</div>
                    </template>
                </van-nav-bar>
                <div>
                    <van-cell-group>
                        <van-cell v-for="(it, i) in Environments" @click="handlerClick(it)" :key="i"
                            :title="$t(it.name)">
                            <van-icon slot="right-icon" name="success" v-if="it.value === curEnvironments"
                                color="#4caf50" class="van-cell__right-icon"></van-icon>
                        </van-cell>
                    </van-cell-group>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { IMyMenu } from '@/logic/my/myModel';
import { useLoingStore } from "@/store/login"
import { computed, onMounted, PropType, ref } from 'vue';
import Cookies from 'js-cookie'

const props = defineProps({
    isPop: {
        type: Boolean,
        default: false,
        required: false
    },
    item: {
        type: Object as PropType<IMyMenu>,
        required: true
    }
})

const show = computed(() => props.isPop)
const curEnvironments = ref()
const loginStore = useLoingStore()

onMounted(() => {
    iniEnvironment()
})
const iniEnvironment = () => {
    // 8.0生产环境 （https://ccm.ppa.linde-le.cn）
    // 8.0测试环境（https://test.ppa80.linde-le.cn || https://test.ppa80.linde-le.cn）
    // 8.0开发环境（https://ppa80-dev.linde-le.cn）
    // 6.7生产环境（https://ppa.linde-le.cn）
    // 6.7测试环境（https://ppa-test.linde-le.cn）
    // 6.7开发环境（https://ppa-dev.linde-le.cn）
    // Global环境（https://ppa.linde-le.com）
    if (location.href?.includes('ppa-dev')) {
        curEnvironments.value = 'dev80'
    } else if (location.href?.includes('ppa-test')) {
        curEnvironments.value = 'test80'
    } else if (location.href?.includes('ppa.linde-le.cn')) {
        curEnvironments.value = 'prod80'
    } else if (location.href?.includes('ppa.linde-le.com')) {
        curEnvironments.value = 'GlobalENV'
    } else {
        curEnvironments.value = 'dev80' // local默认 dev80
    }
}
const Environments = ref([
    { name: 'my.GlobalENV', value: 'GlobalENV' },
    { name: 'my.prod80', value: 'prod80' },
    { name: 'my.test80', value: 'test80' },
    { name: 'my.dev80', value: 'dev80' },
    // { name: 'my.newProd', value: '67prod' },
    // { name: 'my.test_', value: '67test' },
    // { name: 'my.dev_', value: '67dev' },
    // { name: 'my.newProd', value: 'newProd' },
    // { name: 'my.identity', value: 'identity' },
])
const handlerClick = (item: any) => {
    curEnvironments.value = item.value
}

const saveEnvironments = () => {
    console.log("%c [ curEnvironments.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", curEnvironments.value)
    switch (curEnvironments.value) {
        case 'GlobalENV':
            sessionStorage.clear()
            Cookies.remove('.AspNetCore.Cookies')
            location.href = 'https://ppa.linde-le.com'
            break
        case 'dev80':
            sessionStorage.clear()
            Cookies.remove('.AspNetCore.Cookies')
            location.href = 'https://ppa-dev.linde-le.cn'
            break
        case 'test80':
            sessionStorage.clear()
            Cookies.remove('.AspNetCore.Cookies')
            location.href = 'https://ppa-test.linde-le.cn'
            break
        case 'prod80':
            sessionStorage.clear()
            Cookies.remove('.AspNetCore.Cookies')
            location.href = 'https://ppa.linde-le.cn'
            break

        // case 'identity':
        //     sessionStorage.clear()
        //     Cookies.remove('.AspNetCore.Cookies')
        //     location.href = 'https://ppa.linde-le.cn/identity'
        //     break

        // case '67prod':
        //     sessionStorage.clear()
        //     Cookies.remove('.AspNetCore.Cookies')
        //     location.href = 'https://ppa.linde-le.cn'
        //     break
        // case '67test':
        //     sessionStorage.clear()
        //     Cookies.remove('.AspNetCore.Cookies')
        //     location.href = 'https://ppa-test.linde-le.cn'
        //     break
        // case '67dev':
        //     sessionStorage.clear()
        //     Cookies.remove('.AspNetCore.Cookies')
        //     location.href = 'https://ppa-dev.linde-le.cn'
        //     break

        default:
            sessionStorage.clear()
            Cookies.remove('.AspNetCore.Cookies')
            location.href = 'https://ppa.linde-le.cn'
    }
}
</script>
<style lang="scss" scoped>
.service-div {
    width: 100%;
    height: 100vh;

    ::v-deep(.van-cell__title) {
        font-size: var(--yz-com-14);
    }

}
</style>