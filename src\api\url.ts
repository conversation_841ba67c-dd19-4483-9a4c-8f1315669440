import { IUrl } from './models';
// 对接BPM客户端
const url: IUrl = {
  login: {
    userLogin: `auth/signin`, // 用户登录
    getPublickey: `auth/signin/publickey`,
    getUserInfo: `portal/get-user-info`, //  获取用户基本信息
    thirdAuth: `sso/login/token`,
    getWeChatUser: `wechat/@params1`,
    getWeChatJssdk: `wechat/getJssdk`,
    getDingTalkUser: `dingtalk/@params1`,
    getDingTalkSdk: `dingtalk/getJssdk`,
    SsoLogin: `sso/login`, //sso login
    loginOut: `auth/signout`,
    getFeiShuUser: `feishu/@params1`,
    getFeiShuSdk: `feishu/getJssdk`,
    getThirdUser: `third/checkUser/@params1`,
    submitauth: `auth/account/submitauth`,
    checklogin: `sso/checklogin`, //check login
  },
  home: {
    getMyCollection: `portal/process/mycollection?_dc=${new Date().getTime()}`, // 获取我的收藏
    getlibiaryProcess: `portal/process/all`, // 获取流程库
    cancleCollection: `portal/process/mycollection/@params1/cancel`, //取消收藏
    addCollection: `portal/process/mycollection/@params1/add`, //增加收藏
    getProcessForLib: `post/folder/@params1/processes`, // 获取指定流程库下的流程
    getTopList: `portal/process/commonuse?v=${new Date().getTime()}`, //最近流程
    getTreeFloder: `bpm/process/folder/tree`, //根目录 "HSE", "Construction Change Management", "Procurement"
    getFolderProcess: `bpm/process/folder/@params1/processes`, //HSE---Children
    getLindeProcess: `bpm/lindeprocess/folder/@params1/processes`, //新 HSE---Children
  },
  process: {
    getSubmitProcessInfoInfo: `bpm/workflow/process/@params1/postinfo`, // 流程发起时，获取流程信息 || 这里可进入uauc页面
    getMyRequestInfo: `bpm/workflow/task/@params1/readinfo/myrequest`, // 我的申请打开时，解析表单定义
    getMyWorkInfo: `bpm/workflow/step/@params1/processinfo`, // 我的待办打开时，获取流程信息
    getCompletePostInfo: `bpm/workflow/task/@params1/readinfo/myprocessed`, //获取已办流程/我的知会显示的信息
    notifyRead: `bpm/workflow/step/@params1/informsubmit`, //知会已阅
    submitProcess: `bpm/workflow/process/@params1/version/@params2/start`, //发起流程
    processAudit: `bpm/workflow/step/@params1/process`, // 流程审批
    getforecastProcess: `forecast/process/@params1/version/@params2`, // 表单打开时获取流程的审批预测
    getRequestForecast: `forecast/processinstance/@params1`, // 我的申请打开时候，获取审批预测
    getWorkForecast: `forecast/task/@params1`, // 我的待办打开时，获取审批预测
    postNotify: `bpm/taskopt/task/batch/inform`, // 知会
    remandToUser: `bpm/taskopt/task/@params1/remind`, //催办指定用户
    remandTask: `bpm/taskopt/task/@params1/taskinstances/remindable`, // 获取催办信息
    getTaskTacke: `bpm/tasktrace/@params1/timeline`, // 获取审批记录
    backToUser: `bpm/taskopt/task/@params1/step/@params2/pickback`, // 任务取回
    backToNode: `bpm/taskopt/task/@params1/pickbackableSteps`, //任务取回到某部
    cancleTask: `bpm/taskopt/task/batch/abort`, //任务撤销
    getReturnSteps: `bpm/taskopt/step/@params1/recedebacksteps`, // 获取退回步骤
    returnToStep: `bpm/taskopt/task/@params1/step/@params2/returnto`, // 退回到指定的步骤
    returnToUser: `bpm/taskopt/task/@params1/step/@params2/returntoinitiator`, // 退回到申请人
    assigneUserBatch: `bpm/taskopt/task/batch/transfer`, // 委托给指定用户
    assigneUser: 'bpm/taskopt/step/@params1/transfer',
    rejectTask: `bpm/taskopt/task/@params1/reject`, // 任务拒绝
    getOwnerUser: `bpm/user/account/@params1`, // 初始化获取创建人
    getInitUser: `bpm/user/account/@params1`, // 初始化获取拥有人
    getMemberDetail: `bpm/member/qualified/@params1/detail?_dc=${new Date().getTime()}`, // 获取拥有者的扩展属性
    getInitDepart: `bpm/ou/qualified/@params1`, // 初始化获取所属部门
    getSimpleUser: `bpm/user/accounts/info/simple`, // 除发起之外，所有情况打开表单获取组织用户
    getSimpleDept: `bpm/ou/qualifiedids/info/simple`, // 除发起之外，所有情况打开表单获取组织部门
    getTaskPermission: `permision`, //检查任务权限
    dingTalkUpload: `dingtalk/dingTalkUpload`,
    createQrCode: `bpm/util/barcode/encode`, // 创建二维码
    historyFormInfo: `bpm/workflow/task/@params1/info`, //获取历史表单组件数据
    batchApprove: `bpm/taskopt/task/batch/approve`, //批量审批
  },
  my: {
    saveOutSetting: `bpm/user/leavingsetting/save?_dc=${new Date().getTime()}`, //保存外出设置
    getOutSetting: `bpm/user/leavingSetting?_dc=${new Date().getTime()}`, //获取外出设置
    saveLange: `portal/@params1/language?_dc=${new Date().getTime()}`,
  },
  form: {
    getFormUf: `bpm/form/@params1/definition?_dc=${new Date().getTime()}`, // 解析表单定义
    getFatureProcess: `forecast/process/@params1/version/@params2`,
    updateAttach: `bpm/attachment/upload`, // 附件上传
    getAttachmentInfo: `bpm/attachment/attachments/info`, // 获取指定的附件
    updateBase64: `bpm/attachment/base64/upload`,
    getChildFormData: `app/instance/@params1/record/@params2/appinfo/@params3?_dc=${new Date().getTime()}`,
    childFormSubmit: `app/instance/@params1/rec/@params2/appinfo/@params3/submit`,
  },
  org: {
    searchUser: `bpm/user/search?_dc=${new Date().getTime()}`,
    getOrgs: `bpm/org/rootnodes?perm=Read&_dc=${new Date().getTime()}`, // 部门初始化
    getOrgChildren: `bpm/org/provider/@params1/ou/@params2/childnodes?_dc=${new Date().getTime()}&perm=Read`, //根据部门获取子部门
    getOrgUsers: `bpm/org/provider/@params1/ou/@params2/users?_dc=${new Date().getTime()}`, // 获取指定部门下的用户
    getUserMapData: `bpm/user/account/@params1/map?_dc=${new Date().getTime()}`, // 获取人员映射的数据
    getOrgMapData: `bpm/ou/provider/bpmou/ou/@params1/detail?_dc=${new Date().getTime()}`, // 获取人员映射的数据
  },
  work: {
    getMyRequest: `bpm/tasklist/historylist/paging?_dc=${new Date().getTime()}&historyTaskType=MyRequest`, //获取我的申请
    getMyWorks: `bpm/tasklist/worklist/paging?_dc=${new Date().getTime()}`, // 待审批 hou
    getMyProcessed: `bpm/tasklist/historylist/paging?_dc=${new Date().getTime()}&historyTaskType=MyProcessed`, // 获取我的已办
    getMyNotifys: `bpm/tasklist/informlist/paging?_dc=${new Date().getTime()}`, // 待阅 data[] 数据 hou 正式的
    getMyRequestForm: `workflow/processinstance/@params1/read/formdata`,
    getProcessPostInfo: `workflow/process/@params1/postinfo`,
    getWorkListCount: `portal/get-index-worklist?page=1&limit=1`, // 获取待办数量   XXX
    getWorkReadCount: `bpm/tasklist/informlist/paging?page=1&limit=1`, // 待阅数量
  },
  dataSource: {
    getTableData: `bpm/datasource/@params1/@params2/@params3`, // 加载数据源
    getTableParams: `bpm/datasource/@params1/@params2/@params3/params?_dc=` + new Date().getTime(),
    getDataSource: `bpm/datasource/@params1/@params2/@params3/paging`, //开窗数据选择 // 项目号 bpm/datasource/527365695868997/table/VW_UAUC_ProjectSupplier/paging
    getDataESB: `bpm/datasource/@params1/@params2/paging`,
    getFormPage: `bpm/datasource/form/@params1/table/@params2/paging?_dc=` + new Date().getTime(),// 表单数据源分页接口
    getFormNoPage: `bpm/datasource/form/@params1/table/@params2`,// 表单数据源全部数据接口
    getFormParams: `bpm/datasource/form/@params1/table/@params2/params?_dc=` + new Date().getTime(),// 表单数据源全部数据接口
    getEsbDataSource: `esb/ds/@params1/info?_dc=` + new Date().getTime(), //加载ESB数据源
    getEsbDataNoPage: `bpm/datasource/esb/@params1/nopaging`,
    getEsbDataPaging: `bpm/datasource/esb/@params1/paging`,
    getEsbParams: `bpm/datasource/esb/@params1/params?_dc=` + new Date().getTime(),
  },
};
export { url };
