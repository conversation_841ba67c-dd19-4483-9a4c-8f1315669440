<template>
    <div>
        <van-field 
        v-if="!field.hidden"
         v-model="vModel"
        :name="field.vModel.model"
         :label="field.config.label"
        :required="field.config.required"
        :readonly="field.disabled"
        :rules="field.config.rules"
        @input="onInput"
        :type=" showPass ? 'text':'password'"
         :right-icon="field.getDefaultIcon()"
         :maxlength="field.getMaxLength()"
         @click-right-icon="setShowPassWord"
         :show-word-limit="field.getShowlimit()"
         :placeholder="field.placeholder" />
    </div>
</template>

<script lang="ts" setup>
import { YZPassword } from '@/logic/forms/YZPassword';
import { eventBus } from '@/utils/eventBus';
import { debounce } from 'lodash';
import { computed, onMounted, PropType,ref } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZPassword>,
        required: true
    },
    modelValue:{
        type:String,
        default:''
    },
    index:{
      type:Number,
      default:-1
    }
})
const emit = defineEmits(['update:modelValue'])
const onInput = debounce(() => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
},300)
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue',val)   
    }
})
const showPass = ref<boolean>(props.field.getShowPass())
const setShowPassWord =(e:MouseEvent):void=>{
   showPass.value = !showPass.value
}
eventBus.on('setFieldDisable',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.disabled = params.value 
       }
    }
})
eventBus.on('setFieldHidden',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.hidden = params.value 
       }
    }
})
</script>
<style  scoped>

</style>