<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ $t('CSTC.TrainingRecords') }}
                <div class="navbtn">
                    <van-icon name="filter-o" size="20"
                        @click="router.push({ path: '/search', query: { type: '2' }, replace: true })" />
                </div>
            </template>
        </YZNav>

        <div class="list">
            <div class="li">
                <div class="head">
                    <img src="./img/avat.png" alt="">
                </div>
                <div class="info">
                    <div class="name">{{ Account }}</div>
                    <div class="key_">
                        <div style="width: 90px;">{{ $t('CSTC.Company') }}：</div><span>{{ list[0]?.SupplierName
                        }}</span>
                    </div>
                    <div class="key_">
                        <div style="width: 90px;">{{ $t('CSTC.EMail') }}：</div><span>{{ EMail }}</span>
                    </div>
                    <div class="key_">
                        <div style="width: 90px;">{{ $t('CSTC.JobTitle') }}：</div><span></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="main">
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :loosing-text="$t('Form.Release_to_refresh')"
                :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loading" :finished="finished" :finished-text="$t('Form.NoMore')"
                    :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div class="list_" v-for="(item, idx) in list" :key="idx" @click="historyDetail(item)">
                        <div class="key_">
                            <div>{{ $t('CSTC.ProjectName') }}：</div><span>{{ item?.ProjectName
                            }}</span>
                        </div>
                        <div class="key_">
                            <div>{{ $t('CSTC.TrainingCategory') }}：</div><span>{{
                                item?.TrainingCategory
                            }}</span>
                        </div>
                        <div class="key_" style="position: relative;">
                            <div>{{ $t('CSTC.CheckInDateTime') }}：</div>
                            <span>{{
                                item?.CheckInDateTime ? new Intl.DateTimeFormat(navigator?.language,
                                    options_).format(Date.parse(item.CheckInDateTime)) : ''
                            }}</span>
                            <div v-if="!item?.CheckOutDateTime" style="position: absolute;right: 15px;">
                                <div style="font-weight:600; font-size: 12px; color: green;">{{ $t('CSTC.NotCheckedOut')
                                }}
                                </div>
                            </div>
                        </div>
                        <div class="key_">
                            <div>{{ $t('CSTC.CheckInValidationDuration') }}：</div><span>{{
                                !item?.CheckOutAccount && !item?.CheckOutDateTime ? '' : new
                                    Intl.DateTimeFormat(navigator?.language, options).format(new
                                        Date(item?.CheckInValidationDuration + 'Z'))
                            }}</span>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import emptyimg from '@/assets/imgs/empty.jpg'
import { onMounted, ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Sign_in_out_record } from '@/service/user'
import { useUserStore } from "@/store/users"
import { useFormData } from '@/utils'
import { useGetQuery } from '@/utils/request';
import { url } from '@/api/url';

const user = useUserStore()
const router = useRouter()
const route = useRoute()
const userInfo = computed(() => user.getUserInfo)
const Account = ref('')
const EMail = ref('')

const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false); // 下拉刷新
const keyWords = ref(route.query?.keyWords || "")
const minTime = ref(route.query?.minTime || "1990-01-01")
const now = new Date()
// const dateTimeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`
// const maxTime = ref(route.query?.maxTime || dateTimeStr)
const maxTime = ref(
    route.query?.maxTime
        ? new Date((new Date(route.query.maxTime) / 1000 + 86400) * 1000).toISOString().split('T')[0]
        : new Date((new Date() / 1000 + 86400) * 1000).toISOString().split('T')[0]
)

const options = ref({
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    timeZoneName: "short"
})

const options_ = ref({
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
})
const pageParams = ref({
    page: 1, // 当前页数, 从 1 开始
    start: 0,
    limit: 20 // 每页条数
});

const list = ref(location.hostname === 'localhost' ? [
    {
        "ItemId": "***************",
        "TaskID": "***************",
        "TrainingInformationId": "***************",
        "CreateBy": "<EMAIL>",
        "CreateTime": "2024-11-27 02:02:22",
        "UpdateBy": "<EMAIL>",
        "UpdateTime": "2024-11-27 07:59:00",
        "CheckInAccount": "<EMAIL>", //签到人
        "CheckInName": "Qiyang Chen",             //签到人
        "CheckInDateTime": "2024-11-27", //签到时间
        "CheckInLocation": "金桥现代产业服务园区-上海市浦东新区明月路1147号",//签到地点
        "CheckOutAccount": "<EMAIL>", //签退人
        "CheckOutName": "Qiyang Chen",//签退人
        "CheckOutDateTime": "2024-11-27 07:59:00",//签退时间
        "CheckOutLocation": "金桥现代产业服务园区-上海市浦东新区明月路1147号",//签退地点
        "UserType": "Linde", //用户类型
        "ProjectNo": "test0925xw",//项目编号
        "SupplierCode": "LD01",//供应商编号
        "ProjectName": "Office:LED-浦东金桥1",//项目编号
        "SupplierName": "Linde group",//供应商名称
        "TrainingCategory": "Working at heights",//培训类型
        "TrainingInformationNo": "DT2024110017", //培训编号
        "CheckInValidationDuration": "2025-12-04"
    },
    {
        "ItemId": "***************",
        "TaskID": "***************",
        "TrainingInformationId": "***************",
        "CreateBy": "<EMAIL>",
        "CreateTime": "2024-12-04 00:58:04",
        "UpdateBy": "<EMAIL>",
        "UpdateTime": "2024-12-04 00:58:04",
        "CheckInAccount": "<EMAIL>",
        "CheckInName": "Chunyao Hou",
        "CheckInDateTime": "2024-12-04 00:58:04",
        "CheckInLocation": "121.591491 + ' ' + 31.243752",
        "CheckOutAccount": null,
        "CheckOutName": null,
        "CheckOutDateTime": null,
        "CheckOutLocation": null,
        "UserType": "Linde",
        "ProjectNo": "LE-CNDL HuangHaiYuYe",
        "SupplierCode": "LD01",
        "ProjectName": "Office:LED-黄海渔业厂区",
        "SupplierName": "Linde Group",
        "TrainingCategory": "Permit to Work ",
        "TrainingInformationNo": "DT2024120019",
        "CheckInValidationDuration": "2025-12-04"
    }] : [])

const onRefresh = () => { //下拉刷新
    loading.value = true;
    pageParams.value.page = 1;
    finished.value = false;
    // refreshing.value = false; // 重置下拉刷新状态 ?
    list.value = [];
    onLoad();
};
const onLoad = async () => {
    if (refreshing.value) {
        refreshing.value = false
        list.value = []
    }
    // const cc = btoa(JSON.stringify(["Region", "TrainingCategory", "ProjectName", "ProjectNo", "SupplierCode", "SupplierName", "CheckInAccount", "CheckInName", "CheckInDateTime", "CheckInLocation", "CheckOutAccount", "CheckOutName", "CheckOutDateTime", "CheckOutLocation", "TrainingInformationNo", "ValidationDuration", "CheckInValidationDuration"]))
    // const formdata = useFormData({
    //     f: '',
    //     o: '',
    //     s: btoa(JSON.stringify([{ "name": "all", "op": "like", "value": keyWords.value, "isAll": true }, { "name": "CheckInDateTime", "op": ">=", "value": minTime.value }, { "name": "CheckInDateTime", "op": "<=", "value": maxTime.value }])),
    //     c: cc,
    //     lc: cc,
    //     page: pageParams.value.page,
    //     start: (pageParams.value.page - 1) * pageParams.value.limit,
    //     limit: pageParams.value.limit
    // })
    const formdata = useFormData({
        kwd: keyWords.value,
        start_time: minTime.value,
        end_time: maxTime.value,
        page: pageParams.value.page,
        limit: pageParams.value.limit
    })
    try {
        const res = await Sign_in_out_record(formdata)
        console.log("%c [ trainingHistory 2 --- res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res)
        if (res?.data?.success) {
            list.value = res?.data?.children || []
            pageParams.value.page++
            loading.value = false
            if (res?.data?.children.length < pageParams.value.limit) {
                finished.value = true
            }
        }
    }
    catch (error) {
        console.log('培训记录 error', error)
    }
}

const historyDetail = (i) => {
    router.push({
        path: '/trainingtHistoryDetail',
        query: {
            i: JSON.stringify(i)
        }
    })
}


onMounted(async () => {
    const { success, user } = await useGetQuery(url.login.getUserInfo);
    if (success) {
        Account.value = user?.Account
        EMail.value = user?.EMail
    } else {
        console.log('getUserInfo error')
    }
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .list {
        border-bottom: 1px solid aliceblue;
        width: 100%;

        .li {
            background-color: var(--yz-div-background);
            display: flex;
            padding: 12px;

            .head {
                width: 40px;
                margin-right: 8px;
                height: 40px;
                border: 1px solid #ddd;
                overflow: hidden;
                border-radius: 50%;
                background-color: #ddd;
                display: flex;
                justify-content: center;
                align-content: center;
            }

            .info {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #666666;

                .name {
                    color: #444444;
                    font-weight: 600;
                }

                .name~div {
                    margin-top: 6px;
                }

                .key_ {
                    display: flex;
                    align-items: center;
                }
            }
        }
    }

    .main {
        width: 100%;
        height: 100vh;
    }

    .list_ {
        padding: 10px 13px;
        font-size: var(--yz-com-14);
        color: #666666;

        .key_ {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
    }

    .navbtn {
        position: absolute;
        top: 0;
        right: 0;
        width: 44px;
        margin-right: 10px;
        height: 100%;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.bg {
    background-color: var(--yz-div-background);
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yz-text-333) !important;
}

.ellipsis2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--yz-text-333) !important;
}
</style>