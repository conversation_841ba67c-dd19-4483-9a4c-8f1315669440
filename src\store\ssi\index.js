import { defineStore } from 'pinia'

export const SSI = defineStore('ssi', {
    state: () => {
        return {
            projectData: {
                projectName: '',
                projectCode: '',
                supplierName: '',
                supplierCode: '',
            },
            Manufacture: '',
            SerialNo: '',
            TemplateID_: '', //text
            TemplateID: '',  //value
            OperatorName: '',
            OperatorAccount: '',
            Model: '',
            PlateNo: '',
            SWLOrCapacity: '',
            SizeOrDiameter: '',
            Length: '',
            Remarks: '',
            CalibrationExpiryDate: '',
            CertificateOrOtherAttachment: '',
            OperatorAttachment: '',
            ApplicableCalibrationExpiryDate: true,
            ApplicableOperatorName: true,
            ApplicableModel: true,
            ApplicablePlateNo: true,
            ApplicableSWLOrCapacity: true,
            ApplicableSizeOrDiameter: true,
            ApplicableLength: true,
            fileList: [],
            fileList_2: [],
            imgList: [],
            imgList_2: [],
            fileIds_: [],
            fileIds_2: [],
            FileList: [],
            FileList_2: [],
            ImgList: [],
            ImgList_2: [],
            FileIds_: [],
            FileIds_2: [],
        }
    },
    actions: {
        setManufacture(value) {
            this.Manufacture = value
        },
        setSerialNo(value) {
            this.SerialNo = value
        },
        setApplicableCalibrationExpiryDate(value) {
            this.ApplicableCalibrationExpiryDate = value
        },
        setApplicableOperatorName(value) {
            this.ApplicableOperatorName = value
        },
        setApplicableModel(value) {
            this.ApplicableModel = value
        },
        setApplicablePlateNo(value) {
            this.ApplicablePlateNo = value
        },
        setApplicableSWLOrCapacity(value) {
            this.ApplicableSWLOrCapacity = value
        },
        setApplicableSizeOrDiameter(value) {
            this.ApplicableSizeOrDiameter = value
        },
        setApplicableLength(value) {
            this.ApplicableLength = value
        },
        setModel(value) {
            this.Model = value
        },
        setPlateNo(value) {
            this.PlateNo = value
        },
        setSWLOrCapacity(value) {
            this.SWLOrCapacity = value
        },
        setSizeOrDiameter(value) {
            this.SizeOrDiameter = value
        },
        setLength(value) {
            this.Length = value
        },
        setRemarks(value) {
            this.Remarks = value
        },
        setFileList(value) {
            this.fileList = value
        },
        setFileList_2(value) {
            this.fileList_2 = value
        },
        setImgList(value) {
            this.imgList = value
        },
        setImgList_2(value) {
            this.imgList_2 = value
        },
        setFileIds_(value) {
            this.fileIds_ = value
        },
        setFileIds_2(value) {
            this.fileIds_2 = value
        },
        //SSI_APPLY
        setFileList_(value) {
            this.FileList = value
        },
        setFileList_2_(value) {
            this.FileList_2 = value
        },
        setImgList_(value) {
            this.ImgList = value
        },
        setImgList_2_(value) {
            this.ImgList_2 = value
        },
        setFileIds__(value) {
            this.FileIds_ = value
        },
        setFileIds_2_(value) {
            this.FileIds_2 = value
        },
    }
})