<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                CSTC
            </template>
        </YZNav>
        <div class="fave">
            <div class="fave-li" v-for="item in filterList" :key="item.id" @click="tabClick(item)" :style="{
                width: `calc(100% / ${SupplierP ? 4 : 3} - 16px)`
            }">
                <img :src="item.icon" class="iconfont" alt="">
                <p class="ellipsis2">{{ $t(item.text) }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import { onMounted, ref, reactive, onUnmounted, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import { SupplierPermissions } from '@/service/user'
import TQC from './img/qr2.png'
import Scan1 from './img/scan1.png'
import Scan2 from './img/scan2.png'
import QR from './img/qr.png'
const router = useRouter()


const SupplierP = ref(location.hostname === 'localhost' ? true : false)
const list = [
    { name: 'Training QR Code', text: 'CSTC.TQR', icon: TQC, path: '/trainingQRCode' },
    { name: 'Sign in', text: 'CSTC.SignIn', icon: Scan1, path: '', type: 'in' },
    { name: 'Sign out', text: 'CSTC.SignOut', icon: Scan2, path: '', type: 'out' },
    { name: 'Training History', text: 'CSTC.TrainingRecords', icon: QR, path: 'trainingtHistory' }
]
const filterList = computed(() => {
    return list.filter(item => {
        if (item.name === 'Training QR Code') {
            return SupplierP.value
        }
        return true
    })
})

const type = ref('in')
const state = reactive({
    data: null
})
// uni.webView.getEnv((res) => {
//     console.log('当前环境：' + JSON.stringify(res));
// })
// 点击tab
const tabClick = async (item) => {
    console.log("%c [ scan -- path ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item.path)
    if (item.path) router.push(item.path)
    else {
        router.push({
            path: 'scan',
            query: {
                qs_title: item.text,
                type: item.type
            }
        })
        // uni.webView.postMessage({
        //     data: {
        //         action: 'scanCode',
        //         value: {
        //             type: item.type,
        //         }
        //     }
        // })

        // uni.postMessage({
        //     data: {
        //         action: 'scanCode'
        //     }
        // })


    }
}
const getSupplierPermissions = async () => {
    try {
        const res = await SupplierPermissions()
        if (res.data.success) {
            SupplierP.value = res.data?.Data?.Trainer
        }
    }
    catch (error) {
        console.log(error)
    }
}
onMounted(() => {
    if (location.hostname !== 'localhost') {
        getSupplierPermissions()
    }
})
onUnmounted(() => {
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .fave {
        background-color: var(--yz-div-background);
        display: flex;
        flex-wrap: wrap;
        margin: 8px;
        box-sizing: border-box;

        .fave-li {
            padding: 8px 0;
            box-sizing: border-box;
            margin: 0 8px;
            text-align: center;
            flex: 1;

            .iconfont {
                font-size: 24px;
                color: var(--yz-text-333);
                width: 24px;
                height: 24px;
                object-fit: contain; // 保持图片比例
            }

            p {
                margin-top: 8px;
                text-align: center;
                color: var(--yz-text-333);
                font-size: var(--yz-com-12);
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ellipsis2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all; //断词
}
</style>