import { Y<PERSON><PERSON><PERSON><PERSON>g, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./formModel";
import { useDefaultValueSetting } from "./DefaultFieldSet";
import { FieldType } from "vant";
import { useLang } from "@/utils";

export class YZTextBoxConfig extends YZConfig {
    private vtype: string;
    private defaultRules: Array<any>
    private maxLength: number
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZTextBox'
        this.vtype = parmas['extends'].vtype
        this.maxLength = parmas['extends'].maxLength ?? 50
        if (this.required && this.writable || this.forceValidation && !this.validators || this.forceValidation && this.validators && this.validators.find(item => parmas.groupIdArr.includes(item.validationGroup))) {
            this.required = true
            this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }, { validator: (val: any) => { return val.length < this.maxLength }, message: `${useLang('Form.FieldLength_tips')}${this.maxLength}` }]
        } else {
            this.defaultRules = [{ validator: (val: any) => { return val.length < this.maxLength }, message: `${useLang('Form.FieldLength_tips')}${this.maxLength}` }]
        }
        this.rules = this.defaultRules
    }
    getMaxLength(): number {
        return this.maxLength
    }
    getVType(): string {
        return this.vtype
    }
    showTelBtn(): boolean {
        let isShow = false
        switch (this.vtype) {
            case 'mobile':
            case 'phone':
            case 'globalphone':
            case 'tel':
                isShow = true
                break;
            default:
                isShow = false
        }
        return isShow
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZTextBoxStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZTextBox extends YZField {
    textType: FieldType;
    constructor(params: any) {
        super(params)
        this.textType = 'text'
        this.readonly = params['__config__'].extends.readOnly ?? params.readonly
        const config = params['__config__']
        if (config && config['defualtValue']) {
            const code = config['defualtValue'].code
            if (code) {
                this.vModel.value = useDefaultValueSetting(code)
            }
        }
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZTextBoxConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {
        return new YZTextBoxStyle(style)
    }
    onChange() {
    }
}