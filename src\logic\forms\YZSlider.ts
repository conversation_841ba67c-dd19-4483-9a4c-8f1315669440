import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
export class YZ<PERSON>liderConfig extends YZConfig {
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZSlider'
        if (this.required && !this.rules) {
            this.rules = [{ required: true, message: this.label? `${this.label}值不能小于或等于0`:'请输入必填项',validator: this.sliderValidator }]
        }
    }
    sliderValidator(a:number,b:any):boolean {
        if (a<=0) {
            return false 
        }
        return true 
    }
}
export class YZSliderStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZ<PERSON>lider extends YZField {
    private min:number;
    private max:number;
    private range:boolean;
    private step:number;
    constructor(params: any) {
        super(params)
        this.vModel.value = params['__config__'].defaultValue
        this.min = params['min']
        this.max = params['max']
        this.range = params['range']
        this.step = params['step']
        
    }
    initConfig(config: any): YZConfig {
        return new YZSliderConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZSliderStyle(style)
    }
    getMin():number {
        return this.min
    }
    getMax():number {
        return this.max
    }
    getRange():boolean {
        return this.range
    }
    getStep():number {
        return this.step
    }


}
