
import { useProcessStore } from "@/store/process";
import { formBuilder } from "./formBuilder";
import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON>tyle } from "./formModel";
import { useUUID } from "@/utils";
export interface IYZColumn {
    columnName: string;
    columnCode: string;
    renderKey: number;
    value?: any;
    field: any;

}
export class YZTableConfig extends YZConfig {
    private column: Array<YZField>;
    public colunms:Array<any>;
    private defaultRules:Array<any>;
    private map:any;
    private ds:any;
    public tools:Array<any>;
    public emptyGrid:string
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZTable'
        this.column = []
        this.colunms = parmas['column'] 
        this.map = parmas["map"]
        this.ds = parmas["ds"]
        // console.log('ds',this.ds)
        this.defaultRules =[{ required: true, message: `明细表[${this.label}]不能为空` }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules  
        }
        this.tools =  parmas["tools"]
        this.emptyGrid = parmas.emptyGrid
    }
    getCols() {
        return this.column
    }
    setCols(field:YZField) {
        const item = this.column.find(x=>x.vModel.model === field.vModel.model)
        if (!item)
           this.column.push(field)
    }
    getDefaultRule():Array<any> {
        return this.defaultRules
    }
    getMap():any {
        return this.map
    }
    getDs():any {
        return this.ds
    }
    getTools():Array<any> {
        return this.tools
    }
    getIsAddRow(){
        return this.emptyGrid != 'KeepEmpty'
    }
}
export class YZTableStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZTable extends YZField {
    private allowAddRow: boolean;
    private enablePaging: boolean;
    private pageSize: number
    constructor(params: any) {
        super(params)
        const values = params['__vModel__'].modelValue as Array<any>
        const newModelValue:Array<any> = []
        const cols = params['__config__'].column as Array<any>
        const tableName = params['__vModel__'].model 
        if(values && values.length>0) {     
            for(let i=0;i<values.length;i++) {
                const columns:Array<any> = []
                const item = values[i]
                Object.keys(item).forEach(col=>{
                    const value = item[col]
                     const fieldItem = cols.find(x=>x['__vModel__'].model === col)
                     if (fieldItem) {
                         //创建控件以及创建每个控件的实例
                         fieldItem['uuid'] =  useUUID()
                         fieldItem['__vModel__'].modelValue =  value 
                         //table
                         fieldItem['table'] = `${tableName}.${fieldItem['__vModel__'].model}`
                         fieldItem['tableName'] = tableName
                          fieldItem['pindex'] = i
                         const ctype = fieldItem['__config__'].ctype
                         
                         const newField  =  formBuilder.builderField(ctype, fieldItem)    
                         if(newField) {
                            const item: IYZColumn = {
                                columnCode: newField.vModel.model,
                                columnName: newField.vModel.model,
                                renderKey: new Date().getTime()*3+1,
                                value: '',
                                field: newField,
                            }
                            columns.push(item)
                         }           
                     }
                })
                 const table: any = {
                    colums: columns
                }
                newModelValue.push(table)
            }
        }
        this.vModel.value = newModelValue
        this.allowAddRow = params['__config__'].allowAddRecord
        this.enablePaging = params['__config__'].enablePaging
        this.pageSize = params['__config__'].pageSize
    }
    initConfig(config: any): YZConfig {
        return new YZTableConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZTableStyle(style)
    }
    getAllAddRow() {
        return this.allowAddRow
    }
    getEnablePaging(){
        return this.enablePaging
    }
    getPageSieze(){
        return this.pageSize
    }
}