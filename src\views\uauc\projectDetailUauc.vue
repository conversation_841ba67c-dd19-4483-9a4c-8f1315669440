<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search"></div>
            <div class="btn" @click="confirm">
                {{ $t('Global.Selected') }}
            </div>
        </div>
        <div class="grap"></div>
        <div class="list">
            <div class="li">
                <div class="row_">
                    <div class="li-name">
                        {{ $t('CSTC.ProjectLocation') }}
                    </div>
                    <div class="li-info">{{ projectDetail.ProjectNumber }}</div>
                </div>
                <div class="row_">
                    <div class="li-name">
                        {{ $t('UAUC.ProjectOfficeName') }}</div>
                    <div class="li-info">{{ projectDetail.ProjectName }}</div>
                </div>
                <div class="row_">
                    <div class="li-name">
                        {{ $t('CSTC.SupplierCode') }}</div>
                    <div class="li-info">{{ projectDetail.SupplierCode }}</div>
                </div>
                <div class="row_">
                    <div class="li-name">
                        {{ $t('CSTC.SupplierName') }}</div>
                    <div class="li-info">{{ projectDetail.SupplierName }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import emitter from '@/utils/emitter'
import { HSE } from '@/store/hse'
import { useFormData } from '@/utils'
import { usePostBody } from '@/utils/request';

const store = HSE()
const route = useRoute()
const router = useRouter()

const projectDetail = ref([])
const FollowupPersonList = ref([])

const comeBack = () => {
    router.push({
        path: '/projectSelectUauc',
        replace: true
    })
}
const FollowupPerson = async (value) => {
    const ff = window.btoa(JSON.stringify({ "ProjectNumber": { "op": "=", "value": value }, "SubjectType": { "op": "=", "value": "FollowupPerson" } }))
    const cc = window.btoa(JSON.stringify(["UserAccount", "UserName"]))
    const formdata = useFormData({
        o: '',
        f: ff,
        c: cc,
    })
    console.log("%c [ ff ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", ff)
    if (location.hostname === 'localhost') {
        const localRes = [
            {
                "UserAccount": "<EMAIL>",
                "UserName": "Zhijun Wang"
            },
            {
                "UserAccount": "<EMAIL>",
                "UserName": "Corrine Xie"
            },
            {
                "UserAccount": "<EMAIL>",
                "UserName": "Lihua Wu"
            },
            {
                "UserAccount": "<EMAIL>",
                "UserName": "Zoe Fan"
            }
        ]
        FollowupPersonList.value = localRes
    } else {
        try {
            const data = await usePostBody('bpm/datasource/***************/table/VW_UAUC_ProjectSupplier', {}, formdata) //审核人清单的接口
            console.log("%c [ 审核人清单的接口 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
            if (data) {
                FollowupPersonList.value = data || []
            }
        } catch (error) {
            console.log("projectDetailUAUC---error", error)
        }
    }
}

const confirm = () => {
    const data = {
        projectNo: projectDetail.value?.ProjectNumber,
        projectName: projectDetail.value?.ProjectName,
        // supplierName: projectDetail.value?.SupplierName,
    }
    store.projectData = data
    // store.FollowupPersonList = FollowupPersonList.value //不调了
    router.push({
        path: '/uauc',
        replace: true
    })
    // router.push({ path: '/uauc', query: { projectData: JSON.stringify(data) } });
}

onMounted(() => {
    projectDetail.value = JSON.parse(route?.query?.ProjectSUAUC)
    // if (projectDetail.value) { //不调了
    //     FollowupPerson(projectDetail.value?.ProjectNumber)
    // }
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;
            color: #00A0E1;
        }
    }

    .grap {
        width: 100%;
        height: 10px;
        background-color: #f9f9f9;
    }

    .list {
        padding: 10px 12px;

        .li {
            font-size: var(--yz-com-14);
            margin-bottom: 16px;

            .row_ {
                margin-bottom: 30px;

                .li-name {
                    color: #999999;

                }

                .li-info {
                    color: #444444;
                    margin-top: 8px;
                }
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>