import { Ilanguages, YZField } from "@/logic/forms/formModel";
import { useLocalStorage, useSessionStorage } from "@/utils";
import { ConstConfig } from "@/utils/localConst";
import { defineStore } from "pinia";

const useCore = defineStore('coreId', {
  state: () => {
    return {
      $ref: null,
      lang: 'en',
      // lang: 'zh-cn',
      // lang: 'zh-CN',//正确的，暴露错误 'zh-cn' ??
      formLang: {},
      fields: [],
      controlsInstance: [],
      // fieldsRecord:{} as Record<string,Array<YZField>>
      fieldsRecordArr: [] as any,
      selectDs: {} as any
    }
  },
  getters: {
    $form: (state) => state.$ref,
    $update: (state) => state.$ref?.['$'],
    $fields: (state) => state.fields,
    $lang(state) {
      const value = useLocalStorage.get(ConstConfig.core.langInfo) //值==>>>langInfo
      console.log("%c  [langInfo 原始1 value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", value)
      if (value) {
        state.lang = value
        return state.lang
      }
      return state.lang
    }
  },
  actions: {
    setRefs(ref: any) {
      this.$state.$ref = ref
      window.$ref = ref
    },
    setFieldRefs(ref: any) {
      this.$state.fields = ref
      window.$fieldsRef = ref
    },
    setControlInstance(instance: any) {
      const arrys = this.$state.controlsInstance as any
      arrys.push(instance)
      this.$state.controlsInstance = arrys
    },
    setLang(lang: string) {
      this.$state.lang = lang
      useLocalStorage.set(ConstConfig.core.langInfo, lang)
    },
    setFormLang(lang: Ilanguages) {
      console.log("%c [ lang 2]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", lang)
      this.formLang = lang;
      useLocalStorage.setSecretObject<Ilanguages>(ConstConfig.core.FormLang, lang)
    },
    getFormLang(): any {
      const data = useLocalStorage.getSecretObject<Ilanguages>(ConstConfig.core.FormLang)
      // console.log("%c [ formLang 语言的表 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
      // 只有ZhCn 和 EnUs 两种语言👆
      if (data) {
        this.formLang = data as any
        return this.formLang
      } else {
        return this.formLang
      }
    },
    // 存储打开当前页面的
    setFieldsRecord(fields: Array<YZField>) {
      this.$state.fieldsRecordArr.push(fields)
      // console.log(this.$state.fieldsRecordArr,'this.$state.fieldsRecord')
    },
    delFieldsRecordArrByLast() {
      if (this.$state.fieldsRecordArr.length >= 0) {
        this.$state.fieldsRecordArr.pop()
      }
    },
    getFieldsRecord() {
      let currentFormFields = this.fieldsRecordArr[this.fieldsRecordArr.length - 1]
      const newAllFields: YZField[] = []
      if (currentFormFields && currentFormFields.length > 0) {
        for (let i = 0; i < currentFormFields.length; i++) {
          const item = currentFormFields[i]
          if (item.config.ctype === 'YZTable') {
            // 如果当前控件是明细表，那么提取明细表的字段信息
            // 如果当前明细有行数据，才进行提取，否则默认明细无任何字段加载
            if (item.vModel.value && item.vModel.value.length > 0) {
              const rows = item.vModel.value as Array<any>
              rows.forEach(item => {
                const cols = item.colums
                if (cols && cols.length > 0) {
                  for (let j = 0; j < cols.length; j++) {
                    const field = cols[j].field as YZField
                    if (field) {
                      const addItem = newAllFields.find(x => x.uuid === field.uuid)
                      if (!addItem)
                        newAllFields.push(field)
                    }
                  }
                }
              })
            }
            // 提取当前明细表本身，因为在某些情况下明细表也需要配置表达式
            newAllFields.push(item)
          } else {
            // 如果当前控件是普通字段，那么直接提取
            //field
            const addItem = newAllFields.find(x => x.uuid === item.uuid)
            if (!addItem)
              newAllFields.push(item)
          }
        }
      }
      return newAllFields
    },
    // setFieldsRecord(formId:string,fields:Array<YZField>){
    //   this.$state.fieldsRecord[formId] = fields
    //   // console.log(this.$state.fieldsRecord,'this.$state.fieldsRecord')
    // },
    // getFieldsRecord(formId:string){
    //   let currentFormFields = this.fieldsRecord[formId]
    //   const newAllFields: YZField[] = []
    //   if (currentFormFields && currentFormFields.length > 0) {
    //     for (let i = 0; i < currentFormFields.length; i++) {
    //       const item = currentFormFields[i]
    //       if (item.config.ctype === 'YZTable') {
    //         // 如果当前控件是明细表，那么提取明细表的字段信息
    //         // 如果当前明细有行数据，才进行提取，否则默认明细无任何字段加载
    //         if (item.vModel.value && item.vModel.value.length > 0) {
    //           const rows = item.vModel.value as Array<any>
    //           rows.forEach(item => {
    //             const cols = item.colums
    //             if (cols && cols.length > 0) {
    //               for (let j = 0; j < cols.length; j++) {
    //                 const field = cols[j].field as YZField
    //                 if (field) {
    //                   const addItem = newAllFields.find(x => x.uuid === field.uuid)
    //                   if (!addItem)
    //                     newAllFields.push(field)
    //                 }
    //               }
    //             }
    //           })
    //         }
    //         // 提取当前明细表本身，因为在某些情况下明细表也需要配置表达式
    //         newAllFields.push(item)
    //       } else {
    //         // 如果当前控件是普通字段，那么直接提取
    //         //field
    //         const addItem = newAllFields.find(x => x.uuid === item.uuid)
    //         if (!addItem)
    //           newAllFields.push(item)
    //       }
    //     }
    //   }
    //   return newAllFields
    // },
    // delFieldsRecordByTid(id:number|string){
    //   let tid:string = id.toString()
    //   let fieldsRecordKeys = Object.keys(this.$state.fieldsRecord) 
    //   if(fieldsRecordKeys.length>0 && fieldsRecordKeys.includes(tid)){
    //     delete this.$state.fieldsRecord[tid]
    //   }
    // }
    setCachedData(key: string, data: any) {
      this.$state.selectDs[key] = data
    },
    getCachedData(key: string) {
      return this.$state.selectDs[key]
    }

  }
})
export {
  useCore
}