import { CheckerDirection } from "vant/lib/checkbox/Checker";
import { <PERSON><PERSON><PERSON>on<PERSON>g, Y<PERSON>Field, YZO<PERSON>, YZ<PERSON>tyle } from "./formModel";
import { useLang } from "@/utils";
export class YZRadioConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZRadio'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }

    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }

}
export class YZRadioStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class Y<PERSON>Radio extends <PERSON><PERSON><PERSON><PERSON> {
    private radioOption: YZRadioOption
    private positon: CheckerDirection
    constructor(params: any) {
        super(params)
        const optionsItems = params['__slot__'].options
        this.vModel.value = params['__vModel__'].modelValue
        this.radioOption = new YZRadioOption(params['__slot__'].options)
        if (optionsItems && optionsItems.length > 0 && !params['__vModel__'].modelValue) {
            const checked = this.radioOption.options.find(x => x.checked)
            if (checked) {
                this.vModel.value = String(checked.value)
            } else {
                this.vModel.value = ''
            }
        }

        // this.disabled =params.disabled
        var types = params['__config__']['extends'].columns
        this.positon = types === 'auto' ? 'horizontal' : 'vertical'
    }
    initConfig(config: any): YZConfig {
        return new YZRadioConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZRadioStyle(style)
    }
    getOptions(): IRadioOption[] {
        return this.radioOption.options
    }
    getPosition(): CheckerDirection {
        return this.positon
    }
}
export interface IRadioOption {
    label: string;
    value: number;
    checked: boolean;
}
export class YZRadioOption extends YZOption<Array<IRadioOption>> {
    constructor(options: any) {
        super(options)
    }
    initOption(array: Array<any>): IRadioOption[] {
        const options: IRadioOption[] = []
        if (array) {
            array.forEach(({ label, value, checked }) => {
                options.push({
                    label,
                    value,
                    checked
                })
            });
        }
        return options
    }
}