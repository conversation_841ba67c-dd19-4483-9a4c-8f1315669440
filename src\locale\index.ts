// import { Locale } from 'vant';
import { createI18n } from "vue-i18n";
import zhCNMessage from "./lang/zh";
import enUSMessage from "./lang/en";
import koKRMessage from "./lang/ko";
import deDEMessage from "./lang/de";
// import enUS from 'vant/es/locale/lang/en-US'; //vant 组件语言包 * 
// import zhCn from 'vant/es/locale/lang/zh-CN';
// import koKR from 'vant/es/locale/lang/ko-KR';
// import deDE from 'vant/es/locale/lang/de-DE-formal';
// Locale.use('en-US', enUS);

// const message = { //YZ need hide 
//   en: { ...enUSMessage, ...enUS },
//   "zh-cn": { ...zhCNMessage, ...zhCn },
// };

// const i18n = createI18n({  //YZ need hide 
//   locale: "zh-cn",
//   messages: message,
//   fallbackLocale: "zh-cn",
//   globalInjection: true,
//   allowComposition: true,
// });

const message = {  //Linde cf modify 
  'zh-CN': zhCNMessage,
  'en': enUSMessage,
  'ko-KR': koKRMessage,
  'de-DE': deDEMessage
};
let browserLanguage;
let lan = (navigator.browserLanguage || navigator.language).toLowerCase()
console.log("%c [ 浏览器语言 --- lan ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", lan)
if (lan.indexOf('zh') > -1) {
  browserLanguage = 'zh-CN'
} else if (lan.indexOf('en') > -1) {
  browserLanguage = 'en'
} else if (lan.indexOf('de') > -1) {
  browserLanguage = 'de-DE'
} else if (lan.indexOf('ko') > -1) {
  browserLanguage = 'ko-KR'
}
const i18n = createI18n({  //Linde cf modify 
  locale: browserLanguage, // "zh-CN",
  messages: message,
  fallbackLocale: "en", //所有语言无法展示时的备用语言
  globalInjection: true,
  allowComposition: true,
});

export default i18n;
