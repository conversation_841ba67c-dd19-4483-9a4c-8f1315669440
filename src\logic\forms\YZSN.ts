import { useProcessStore } from "@/store/process";
import { YZConfig, YZField, YZ<PERSON>tyle } from "./formModel";
import { useLang } from "@/utils";
const store = useProcessStore()
export class YZSNConfig extends YZConfig {
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZSN'
        if (this.required && !this.rules) {
            this.rules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        }
    }
}
export class YZSNStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)

        //
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZSN extends YZField {
    constructor(params: any) {
        super(params)
        const info = store.getPostInfoData
        if (info && info.processInstance) {
            this.vModel.value = info.processInstance.serialNum
        }
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZSNConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZSNStyle(style)
    }
}