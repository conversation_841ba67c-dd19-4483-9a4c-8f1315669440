@charset "utf-8";

/* 禁用iPhone中Safari的字号自动调整 */

html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    /* 解决IOS默认滑动很卡的情况 */
    -webkit-overflow-scrolling: touch;
}

html,body{
    width: 100%;
    /* height: calc(100% + constant(safe-area-inset-bottom));
    height: calc(100% + env(safe-area-inset-bottom)); */
    height: 100%;
    overflow: hidden;
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
}
.main{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    background: #f8f7f7;
}
.van-popup--bottom{
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
}
.cont{
    overflow: auto;
}

.cont>.van-cell-group {
    padding-bottom: 100px;
}


/* 禁止缩放表单 */

input[type="submit"],
input[type="reset"],
input[type="button"],
input {
    resize: none;
    border: none;
}


/* 取消链接高亮  */

body,
div,
ul,
li,
ol,
h1,
h2,
h3,
h4,
h5,
h6,
input,
textarea,
select,
p,
dl,
dt,
dd,
a,
img,
button,
form,
table,
th,
tr,
td,
tbody,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}


/* 设置HTML5元素为块 */

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}


/* 图片自适应 */

img {
    width: auto;
    height: auto;
    width: auto \9;
    /* ie8 */
    display: block;
    -ms-interpolation-mode: bicubic;
    /*为了照顾ie图片缩放失真*/
}


/* 初始化 */

body,
div,
ul,
li,
ol,
h1,
h2,
h3,
h4,
h5,
h6,
input,
textarea,
select,
p,
dl,
dt,
dd,
a,
img,
button,
form,
table,
th,
tr,
td,
tbody,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    margin: 0;
    padding: 0;
}

em,
i {
    font-style: normal;
}

ul,
li {
    list-style-type: none;
}

strong {
    font-weight: normal;
}

ul,
ol {
    list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
    font-family: -apple-system,BlinkMacSystemFont,'Helvetica Neue',Helvetica,Segoe UI,Arial,Roboto,'PingFang SC',miui,'Hiragino Sans GB','Microsoft Yahei',sans-serif;
}

img {
    border: none;
}

input {
    font-family: -apple-system,BlinkMacSystemFont,'Helvetica Neue',Helvetica,Segoe UI,Arial,Roboto,'PingFang SC',miui,'Hiragino Sans GB','Microsoft Yahei',sans-serif;
}

* {
    margin: 0;
    padding: 0;
}

::-webkit-scrollbar {
    width: 0;
    height: 0;
}

::-webkit-scrollbar-track {
    background:var(--yz-div-background);
}

::-webkit-scrollbar-thumb {
    -webkit-border-radius: 10px;
    border-radius: 10px;
    background: #999;
}

::-webkit-scrollbar-thumb:hover {
    background: #666;
}

::-webkit-scrollbar-thumb:window-inactive {
    background: #ddd;
}


/*单行省略号*/

.one-txt-cut {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}


/*多行省略号*/

.txt-cut-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.txt-cut-3 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}


/* 移动端点击a链接出现蓝色背景问题解决 */

a:link,
a:active,
a:visited,
a:hover {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}


/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    visibility: hidden;
    height: 0;
    clear: both;
}

.clearfix {
    zoom: 1;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.db {
    display: block !important;
}

.dn {
    display: none !important;
}

.dib {
    display: inline-block !important;
}


/*字体图标*/

.icon {
    font-family: "iconfont", serif !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}


/*修改了vant样式*/

.h-grid .van-grid-item__content {
    background-color: transparent;
}

.TimeHour .van-picker-column:nth-child(5) {
    display: none;
}


/*filed 图标间距*/

.icon-box {
    display: inline-block;
    white-space: nowrap;
}

.icon-box i {
    display: inline-block;
    min-width: 1em;
    line-height: inherit;
    margin-left: 8px;
}


/*将滚动区域跟视口隔开*/

.mainCont {
    position: fixed;
    width: 100%;
    top: 46px;
    bottom: 46px;
    overflow: auto;
}

.mainCont .van-field__label {
    word-break: break-all;
    padding-right: 10px;
}

.mainCont .van-field__body {
    height: 100%;
}


/* .van-cell-group{
    margin-bottom: 8px;
} */

.van-pull-refresh__track {
    min-height: calc(100vh - 140px);
}

.van-grid-item__icon-wrapper i{
  font-size: 28px;
}