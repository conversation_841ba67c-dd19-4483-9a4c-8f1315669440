import { i, number, string } from "mathjs";

export interface IWorkBar {
    name: string;
    text: string
}
export interface ISearchPanel {
    pageIndex: number;
    pageSize: number;
    account: string;




}

export class SearchPanel {
    HistoryTaskType: string;
    PostDate1?: string;
    PostDate2?: string;
    PostDateType: string;
    ProcessName?: string;
    SerialNum?: string;
    TaskID?: string;
    Year: number;
    byYear: number;
    kwd?: string;
    limit: number;
    page: number;
    searchType?: string;
    start: number;
    PostUserAccount: string; //+
    // pn: string; //新增 1
    // contractor: string; //新增 2
    // equipment: string; //新增 3
    constructor(params: any) {
        this.start = params['start']
        this.limit = params['limit']
        this.page = params['page']
        this.HistoryTaskType = params['HistoryTaskType']
        this.PostDate1 = params['PostDate1']
        this.PostDate2 = params['PostDate2']
        this.PostDateType = params['PostDateType']
        this.ProcessName = params['ProcessName']
        this.SerialNum = params['SerialNum']
        this.TaskID = params['TaskID']
        this.Year = params['Year']
        this.byYear = params['byYear']
        this.kwd = params['kwd']
        this.searchType = params['searchType']
        // this.pn = params['pn'] //1
        // this.contractor = params['contractor'] //2
        // this.equipment = params['equipment'] //3
        this.PostUserAccount = params['PostUserAccount'] //+
    }
}
export interface IPanelData {
    year: number;
    month: number;
    quto: number;
    day: string;
    processname: string;
    taskid: string;
    sn: string;
    kwd: string;
    postUser: string; //+
    // pn: string; //新增 1
    // contractor: string; //新增 2
    // equipment: string; //新增 3
}