<template>
    <div>
        <van-popup v-model:show="show" position="right" style="heigth: 100vh; width: 100%">
            <div class="service-div">
                <van-nav-bar :title="$t(item.name)" :left-text="$t('Global.Back')"
                    @click-left.stop="item.onBack(item.name)" left-arrow />
                <div>
                    <van-cell-group>
                        <van-cell v-for="(item, index) in setting" :key="item.name" :title="item.name">
                            <template #right-icon>
                                <van-switch :model-value="item.model" size="24"
                                    @update:model-value="onUpdateValue($event, index)" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { IMyMenu, INewSetting } from '@/logic/my/myModel';
import { url } from '@/api/url'
import { useGetQuery } from '@/utils/request';
import { computed, onMounted, PropType, ref } from 'vue';
import { showConfirmDialog, showNotify } from 'vant';
const props = defineProps({
    isPop: {
        type: Boolean,
        default: false,
        required: false
    },
    item: {
        type: Object as PropType<IMyMenu>,
        required: true
    }
})
const show = computed(() => props.isPop)
const setting = ref<Array<INewSetting>>([])
onMounted(async () => {
//await initSetting()
})
const initSetting = async () => {
    // const { success, providers } = await useGetQuery(url.my.getNewSetting)
    // if (success && providers.length > 0) {
    //     providers.forEach((element: any) => {
    //         const item: INewSetting = {
    //             name: element.ProviderName,
    //             model: element.Enabled
    //         }
    //         setting.value.push(item)
    //     });
    // }
}
const onUpdateValue = (value: boolean, index: number) => {
    // showConfirmDialog({
    //     title: '提醒',
    //     message: '确定打开此通知？'
    // }).then(async (result:any) => {
    //     const name = setting.value[index].name
    //     const { success } = await useGetQuery(url.my.saveNewSetting, {
    //         rejectedNotifys: name
    //     })
    //     if (success) {
    //         showNotify({ type: 'success', message: '操作成功' })
    //         setting.value[index].model = value
    //     } else {
    //         showNotify({ type: 'danger', message: '操作失败' })
    //     }
    // }).catch(error => {
    //     console.error('error', error)
    // })
}

</script>
<style  scoped>
.service-div {
    width: 100%;
    height: 100vh;
    ::v-deep(.van-cell__title) {
      font-size: var(--yz-com-14);
    }
    
}
</style>