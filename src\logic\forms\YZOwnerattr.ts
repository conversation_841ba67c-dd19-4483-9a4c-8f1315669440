import { useProcessStore } from "@/store/process";
import { Y<PERSON><PERSON>onfig, Y<PERSON>Field, YZStyle } from "./formModel";
import { useParamsFormatter } from "@/utils";
import { useGetQuery } from "@/utils/request";
import { eventBus } from "@/utils/eventBus";
import { url } from "@/api/url";
import { useLang } from "@/utils";
const store = useProcessStore()
export class YZOwnerattrConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZOwnerattr'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZOwn<PERSON>ttrStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZOwnerattr extends YZField {
    constructor(params: any) {
        super(params)
        this.vModel.value = params['__vModel__'].modelValue
        const info = store.getOuMemberId
        if (info) {
            store.LoadOWerPropty()
                .then(result => {
                    const propName = params['__config__'].extends.attrName
                    const uuid = params.uuid
                    const postData = {
                        propName,
                        uuid,
                        ouInfo: result
                    }
                    eventBus.emit('setOwerProty', true, postData)
                }).catch(error => {
                    // console.log('error',error)
                })
        }
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZOwnerattrConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZOwnerattrStyle(style)
    }
}