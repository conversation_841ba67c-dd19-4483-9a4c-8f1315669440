import { useProcessStore } from "@/store/process";
import { YZConfig, Y<PERSON><PERSON><PERSON>, YZStyle } from "./formModel";
import { useGetQuery } from "@/utils/request";
import { url } from "@/api/url";
import { useParamsFormatter } from "@/utils";
import { eventBus } from "@/utils/eventBus";
import { useCore } from "@/store/core";
import { effect, ref, shallowRef, triggerRef } from "vue";
import { useLang } from "@/utils";
const store = useProcessStore()
export class YZInitiatorConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZInitiator'


        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZInitiatorStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZInitiator extends YZField {
    constructor(params: any) {
        super(params)
        this.vModel.value = params['__vModel__'].modelValue
        //    this.disabled = params['disabled']
        const info = store.getPostInfoData
        if (info) {
            const newUrl = useParamsFormatter(url.process.getInitUser, {
                params1: info.initiatorAccount ?? info.processInstance.initiatorAccount
            })
            useGetQuery(newUrl)
                .then(result => {
                    this.vModel.value = result.shortName ?? result.name
                    this.vModel.optionValue = result.account
                    const core = useCore()
                    const intance = core.$update as any
                    if (intance)
                        intance.update()
                }).catch(error => {
                    console.error('初始化用户失败', error)
                })
        }
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZInitiatorConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZInitiatorStyle(style)
    }
}