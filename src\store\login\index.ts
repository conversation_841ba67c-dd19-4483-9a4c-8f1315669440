import { useSessionStorage } from '@/utils'
import { ConstConfig } from '@/utils/localConst'
import { defineStore } from 'pinia'
import { ILoginState } from '../storeModel'
import dayjs from 'dayjs'
import { url } from '@/api/url'
import dd from 'dingtalk-jsapi'
const storeId = 'login'
const useLoingStore = defineStore(storeId, {
    getters: {
        getPublicKey: (state) => {
            if (state.publicKey) {
                return state.publicKey
            } else {
                return useSessionStorage.getSecret(ConstConfig.login.getPublicKey)
            }
        },
        getKeyStore: (state) => {
            if (state.keyStore) {
                return state.keyStore
            } else {
                return useSessionStorage.getSecret(ConstConfig.login.getKeyStore)
            }
        },
        getToken: (state) => {
            if (state.accessToken) {
                return state.accessToken
            } else {
                state.accessToken = useSessionStorage.getSecretObject<string>(ConstConfig.login.getToken) as string
                return state.accessToken
            }
            console.log("%c [ state.accessToken ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", state.accessToken)

        },
        getTokenTime: (state) => {
            const value = useSessionStorage.getSecretObject<string>(ConstConfig.login.getTokenTime)
            if (value) {
                state.tokenValidate = JSON.parse(value)
                return state.tokenValidate
            } else {
                return state.tokenValidate
            }
        },
        getAppName: (state) => {
            const name = useSessionStorage.getSecret(ConstConfig.login.getAppName)
            if (name) {
                state.appName = name
                return state.appName
            } else {
                return state.appName
            }
        },
    },
    state: (): ILoginState => {
        return {
            publicKey: undefined,
            keyStore: undefined,
            accessToken: '',
            tokenValidate: {
                start: '',
                end: ''
            },
            appName: '',
            applicant: ''
        }
    },
    actions: {
        setPublicKey(publicKey: string): void {
            this.$state.publicKey = publicKey
            useSessionStorage.setSecret(ConstConfig.login.setPublicKey, publicKey)
        },
        setKeyStore(keystore: string): void {
            this.$state.keyStore = keystore
            useSessionStorage.setSecret(ConstConfig.login.setKeyStore, keystore)
        },
        setToken(token: string) {
            this.$state.accessToken = token
            const start = dayjs()

            const end = start.add(30, 'minute')
            const tokenconfig = {
                start: start,
                end: end
            }
            useSessionStorage.setSecretObject<string>(ConstConfig.login.setToken, token)
            // console.log("%c [ ConstConfig.login.setToken ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", ConstConfig.login.setToken)
            const timejson = JSON.stringify(tokenconfig)
            useSessionStorage.setSecretObject(ConstConfig.login.setTokenTime, timejson)
        },
        restart() {
            this.$reset()
        },
        ssoLogin({ AccessToken, Account, AppkeyName }: any, type: string = "wechat") {
            const newUrl = `${url.login.SsoLogin}?appKeyName=${AppkeyName}&AccessToken=${AccessToken}&Account=${Account}&RedirectUrl=${window.location.href}`
            console.log("%c [ newUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)
            this.setToken(AccessToken)
            this.$state.appName = AppkeyName
            this.$state.applicant = Account
            useSessionStorage.setSecret(ConstConfig.login.setAppName, AppkeyName)
            if (type === 'dd') {
                window.location.href = newUrl
                setTimeout(() => {
                    window.location.reload()
                }, 1000)
            } else {
                window.location.href = newUrl
            }
        },
        clearToken() {
            this.$state.accessToken = ''
            this.$reset()
        }

    }
})
export { useLoingStore }