import { UploaderFileListItem } from "vant";
import { Y<PERSON><PERSON>onfig, Y<PERSON><PERSON><PERSON>, Y<PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
import { useGetBody, usePostBody } from "@/utils/request";
import { url } from "@/api/url";
import { eventBus } from "@/utils/eventBus";
import { useLang } from "@/utils";
export class YZImageUploadConfig extends YZConfig {
    // 文件大小
    private fileSize: number;
    private sizeUnit: string;
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZImageUpload'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.upload')} [${this.label}]${useLang('Form.file')}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }

        this.fileSize = parmas['fileSize']
        this.sizeUnit = parmas['sizeUnit']
    }
    getSize(): number {
        return this.fileSize
    }
    getUnit(): string {
        return this.sizeUnit
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZImageUploadStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZImageUpload extends YZField {
    private autoUpload: boolean;
    private listType: string;
    private multipe: boolean;
    private action: string;
    private accpt: string;
    private readImages: string[]
    private isLoading: boolean
    constructor(params: any) {
        super(params)
        this.vModel.value = []
        this.vModel.optionValue = params['__vModel__'].modelValue
        this.autoUpload = params['auto-upload']
        this.listType = params['list-type']
        this.multipe = params['multiple']
        this.action = params['action']
        this.accpt = params['accept']
        this.readImages = []
        this.isLoading = false

    }
    initConfig(config: any): YZConfig {
        return new YZImageUploadConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZImageUploadStyle(style)
    }
    getMultipe(): boolean {
        return this.multipe
    }
    getTypes(): string {
        return this.listType
    }
    getAction(): string {
        return this.action
    }
    getAccept(): string {
        return this.accpt
    }
    setImages(urls: []): void {
        this.readImages = urls
    }
    getImages(): string[] {
        return this.readImages
    }
    setIsUpload(value: boolean) {
        this.isLoading = value
    }
    getIsUpload() {
        return this.isLoading
    }
}