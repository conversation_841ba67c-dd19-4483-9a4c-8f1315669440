import { useProcessStore } from "@/store/process";
import { YZConfig, Y<PERSON><PERSON>ield, YZStyle } from "./formModel";
import { eventBus } from "@/utils/eventBus";
import { useLang, useShortTime } from "@/utils";
const store = useProcessStore()
export class YZCreateTimeConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {

        super(parmas)
        this.ctype = 'YZCreateTime'

        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZCreateTimeStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZ<PERSON>reateTime extends YZField {
    constructor(params: any) {
        super(params)
        const info = store.getPostInfoData
        if (info && info.processInstance) {
            const time = useShortTime(info.processInstance.startTime)
            this.vModel.value = time
        }
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZCreateTimeConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZCreateTimeStyle(style)
    }
}