<template>
    <div>
        <van-field v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required"
            :readonly="field.readonly" :disabled="field.disabled" :rules="field.config.rules"
            :label="field.config.label">
            <template #input>
                <van-radio-group :disabled="field.disabled" v-model="vModel" :direction="field.getPosition()"
                    @change="onRadioChange">
                    <van-radio :name="item.value" v-for="(item, index) in optionsData" :key="index">{{
                        item.label
                    }}</van-radio>
                </van-radio-group>
            </template>
        </van-field>
    </div>
</template>

<script lang="ts" setup>
import { IRadioOption, YZRadio, YZRadioConfig } from '@/logic/forms/YZRadio';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, PropType, ref, nextTick } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZRadio>,
        required: true
    },
    modelValue: {
        type: [String, Number],
        default: 0
    },
    index: {
        type: Number,
        defualt: -1
    }
})
const emit = defineEmits(['update:modelValue'])
const config = props.field.config as YZRadioConfig
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
    nextTick(() => { //YZ +
        setFun()
    })

    //   if(vModel.value){ //YZ -
    //     setFun()
    //   }
})
const optionsData = ref<IRadioOption[]>(props.field.getOptions())
const vModel = computed({
    get() {
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const onRadioChange = () => {
    setFun()
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            const item = optionsData.value.find(x => x.value === params.value)
            if (item) {
                vModel.value = params.value
                item.checked = true
            } else if (params.value == '') {
                vModel.value = ''
            }
            setFun()
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid && params.index == props.field.pindex) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
</script>
<style scoped>
.van-radio--horizontal {
    margin-right: 10px;
    margin-bottom: 4px;
}
</style>