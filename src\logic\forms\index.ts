import { url } from "@/api/url";
import { useUserStore } from "@/store/users";
import {
  isArray,
  useBase64,
  useCallBackClose,
  useEnviroMent,
  useLang,
  useParamsFormatter,
  usePlatForm,
  useSessionStorage,
  useTimeZHcn,
} from "@/utils";
import { useGetQuery, usePostBody } from "@/utils/request";
import { buttonProps, contactCardProps, passwordInputProps, rowProps, showConfirmDialog, showDialog, showNotify } from "vant";
import { getCurrentInstance, nextTick, onMounted, onUnmounted, onUpdated, reactive, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { formBuilder } from "./formBuilder";
import { useFormData } from '@/utils'

//formtitle1
import {
  FormTitle,
  YZField,
  FormConfig,
  IBtnModel,
  IGridModel,
  IFatureModel,
  IPostionData,
  IHeaderData,
  IFormValidator,
} from "./formModel";
import {
  ProcessService,
} from "./processHelper";
import { useProcessStore } from "@/store/process";
import {
  clearFormValidator,
  resetEvents,
  setFormMobileEvents,
  setFormValidator,
  useFormValidator,
  useMemberEvents,
  useResolveFields,
  useResolveFormStr,
  useResolveFuns,
  useResolveLang,
  useTaskProcess,
  useValidateHight,
  useGetFormFields
} from "./formsutil";
import { eventBus } from "@/utils/eventBus";
import { useHomeStore } from "@/store/home";
import { computed } from "vue";
import { init, loadMobileCustomerFun, loadValidator } from "@/init";
import { useCore } from "@/store/core";
import { formpt } from "./formp";
import store from "@/store";
export interface IDetailForm {
  childControl: IGridModel[];
  filterChild: any[];
}

function useForm(instances: any, props: any) {
  const userStore = useUserStore();
  const homeStore = useHomeStore();
  const processStore = useProcessStore();
  const processVersion = ref<string>("");
  const processDefineId = ref<string>("");
  const memberOUID = ref<string>("");
  const memberID = ref<string>("");
  const providerName = ref<string>("");
  const processInstanceId = ref<string>("");
  const processTaskId = ref<string>("");
  const processStepId = ref<string>('')
  const title = ref<FormTitle>();
  const formProcessTitle = ref<string>("");
  const fieldsRefs = ref<YZField[]>([]);
  const submitShow = ref<boolean>(false);
  const activeTab = ref<number>(0);
  const activeStep = ref<number>(-1);
  const fatureData = ref<Array<IFatureModel>>([]);
  const opinion = ref<string>("");
  const currentAction = ref<string>("");
  const btns = ref<IBtnModel[]>([]);
  const postType = ref<number>(0); // 记录提交行为 1 发起 2 审核
  const tabTitle = ref<string>("流程发起");
  const userShow = ref<boolean>(false);
  const openType = ref<number>(0);
  const recordStep = ref<number>(-1);
  const reordData = ref<Array<any>>([]);
  const viewModel = ref<any>(null); // 存放填充
  const processNameValue = ref<string>("");
  const eventFire = ref<any>({});
  const validatorFuns = ref<any>({})
  const headerShow = computed(() => homeStore.headshow);
  const showLoading = ref<boolean>(false)
  const planForm = usePlatForm()
  const isThird = window.webConfig.openForm ?? false
  const wxFormId = ref<string>('')
  const moreBtns = ref<IBtnModel[]>([])
  const moreShow = ref<boolean>(false)
  const postioinData = ref<Array<IPostionData>>([])
  const changePostion = ref<string>('')
  const changePositonId = ref<string>('')
  const pistionShow = ref<boolean>(false)
  const positionColumns = ref<Array<any>>([])
  const consignEnable = ref<boolean>(false)
  const tabHeaderShow = computed(() => processStore.getHideTabHeader)
  const historyData = reactive<Array<any>>([])
  const activeId = ref<number>(0)
  const showPass = ref<boolean>(false)
  const passBind = ref<string>('')
  const isPassTrue = ref<boolean>(true)
  const submitParams = ref<any>(null)
  const groupIdArr = ref<any>([])
  const viewColsData = ref<any>()
  const TopTitle = ref<string>('')

  const headData: IHeaderData = reactive({
    Name: '暂无',
    IdCard: '暂无',
    CustomerId: '',
    HeaderUrl: '',
    RedirctUrl: '',
    TaskID: ''
  })
  const openSign = ref<boolean>(false)

  const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    pageSize: 50
  })

  onMounted(async () => {
    homeStore.setTab(false)
    showLoading.value = true

    try {
      await openTaskForm()
    } catch (error) {
      showLoading.value = false
    }
  });
  // 解析表单
  const renderForm = async (formId: string): Promise<any> => {
    // 解析表单对象
    const result = await useResolveFormStr(formId);
    // 设置表头信息
    const titleInfo: any = {
      value: result.name,
      fontSize: 17,
      color: "",
      fontWeight: "bold",
      paddingBottom: 0,
      paddingTop: 0,
      position: "",
      processName: "a",
    };
    const newTitle = new FormTitle(titleInfo);
    title.value = newTitle;
    // 清空历史绑定方法
    resetEvents()
    // 解析表单控件
    const constrolData = useResolveFields(result);

    // console.log('表单控件', constrolData)
    // 解析表单多语言
    const langs = useResolveLang(result)
    console.log("%c [ langs --- 语言列表 --- 1 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", langs)
    if (langs) {
      if (langs.enable) {
        const core = useCore()
        core.setFormLang(langs.language)
        // languages：👇
        // "en-US",
        // "zh-Hans",
        // "ko-KR",
        // "de-DE"
      }
    }
    // 解析自定义方法
    const constrolEvent = useResolveFuns(result)
    // 存储全局方法
    useMemberEvents(constrolEvent)
    // 布局隐藏
    setHboxHidden(constrolData)
    // 主表与明细表判断
    if (checkHaveDetail(constrolData)) {
      const { components } = constrolData;
      const allfieldData: any = []
      const model = buildeDetail(components);
      for (var key in components) {
        const item = components[key]
        if (item.ctype === 'grid') {
          const $thisGrid = model.childControl.filter(x => x.gridName === key)
          const fields: any[] = getDetailFields($thisGrid, formId);
          allfieldData.push(...fields)
        } else {
          if (
            !key.startsWith("grid") &&
            !key.startsWith("table") &&
            !key.startsWith("hbox") &&
            !checkIsDetailControl(model.filterChild, components[key])
          ) {
            const fieldRender: any = { components: {} };
            Object.defineProperty(fieldRender.components, key, {
              value: components[key],
              writable: true,
              enumerable: true,
              configurable: true,
            });
            const fd = formBuilder.builderExtField(fieldRender, formId, groupIdArr.value, viewModel.value);
            allfieldData.push(...fd)
          }
        }
      }
      const newJson = {
        fields: allfieldData
      };
      return newJson;
    } else {
      const newJson = {
        fields: formBuilder.builderExtField(constrolData, formId, groupIdArr.value, viewModel.value),
      };
      useSessionStorage.setSecretObject<any>(formId, newJson);
      return newJson;
    }
  };
  const planObj = () => {
    return { height: 'calc(100vh - 230px - constant(safe-area-inset-bottom));' }
    // if (planForm === 'Mac') {
    //    return  { height: 'calc(100vh - 230px - constant(safe-area-inset-bottom));'}
    // } else {
    //   return   { height: 'calc(100vh - 150px - env(safe-area-inset-bottom));' } 
    // }
  }
  /**
   * 
   * 重构流程打开表单按钮权限控制
   * 以及其他操作入口打开表单后的操作
   */
  const openTaskForm = async () => {
    const loadInfo = processStore.getProcLoad
    if (loadInfo) {
      // formService表单服务对象 用于子表单
      if (Object.keys(props.formService).length !== 0) {
        const { formId, viewModel: formServiceData, showSaveButton } = props.formService
        viewModel.value = formServiceData;
        buildForm(formId)
        console.log("%c [1 formId ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formId)
        if (showSaveButton) {
          btns.value.push({
            text: useLang('Global.Save'),
            component: "YZFormServiceSave",
            enable: true,
            formService: props.formService
          });
        } else {
          btns.value.push({
            enable: true,
            text: useLang('Global.Cancel'),
            component: "YZFormServiceClose",
          });
        }
        childFormByEvent()
      } else if (Object.keys(props.reformRead).length !== 0) {
        //用于历史表单信息
        const data = await useTaskProcess.myProcessed.getCompletePostInfo(props.reformRead.taskId)
        if (data) {
          processStore.setPostInfo(data);
          processVersion.value = data.processVersion;
          processTaskId.value = data.taskId
          const formId = data.form.formId;
          const permisions = data.permisions;
          tabTitle.value = data.processInstance.serialNum;
          formProcessTitle.value = tabTitle.value
          viewModel.value = data.viewModel;
          memberID.value = data.processInstance.ouid;
          memberOUID.value = data.processInstance.ownerPosition;
          processStore.setTaskId(data.taskId)
          if (permisions) {
            // btns.value.push({
            //   text: useLang('Form.Retrieve'),
            //   enable: permisions.PickBack,
            //   component: "YZBack",
            // });
            btns.value.push({
              text: useLang('Form.Notify'),
              enable: permisions.Inform ?? true,
              component: "YZNotify",
            });
          }
          buildForm(formId)
          console.log("%c [2 formId ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formId)

        } else {
          showNotify({
            type: 'danger',
            message: JSON.stringify(data)
          })
        }
      } else if (loadInfo.processId) {
        // 如果有流程实例id表示发起 
        postType.value = 1
        const data = await useTaskProcess.myRequest.getProcessPostInfo(loadInfo.processId);
        if (data) {
          processStore.setPostInfo(data);
          processNameValue.value = data.prcessName ?? data.processName
          formProcessTitle.value = processNameValue.value
          postioinData.value = data.positions
          positionColumns.value = []
          if (postioinData.value && postioinData.value.length > 0) {
            const memberItem = postioinData.value.find(x => x.memberId === data.memberId)
            if (memberItem) {
              changePostion.value = memberItem.text
              console.log("%c [ changePostion.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", changePostion.value)
              changePositonId.value = memberItem.qualifiedId
            }
            postioinData.value.forEach(v => {
              positionColumns.value.push({
                text: v.text,
                value: v.qualifiedId
              })
            })
          }

          const formId = data.form.formId;
          const actions = data.actions as Array<any>;
          processVersion.value = data.processVersion;
          processDefineId.value = data.processId;
          memberOUID.value = data.ouid;
          memberID.value = data.memberId;
          viewModel.value = data.viewModel;
          providerName.value = data.providerName;
          processStore.setMemberOuId(`${providerName.value}.${memberID.value}`)
          groupIdArr.value = []
          if (actions && actions.length > 0) {
            actions.forEach((item) => {
              btns.value.push({
                text: item.actionName,
                component: "YZSubmit",
                enable: true,
                groupId: item.validationGroup,
                submitConfirm: item.submitConfirm,
                promptMessage: item.promptMessage
              });
              groupIdArr.value.push(item.validationGroup)
            });
          }
          consignEnable.value = data.permisions.Consign
          buildForm(formId)
          console.log("%c [3 formId ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formId)

        } else {
          showNotify({
            type: 'danger',
            message: '无法解析表单，请检查流程实例id是否正确'
          })
        }
      } else if (loadInfo.stepId) {
        // 含有步骤id
        const data = await useTaskProcess.myWorks.getWorkPostInfo(loadInfo.stepId);
        if (data) {
          postType.value = 2
          processStore.setPostInfo(data);
          processVersion.value = data.processVersion;
          processStepId.value = loadInfo.stepId
          processTaskId.value = data.taskId
          processStore.setTaskId(data.taskId)
          processStore.setStepId(loadInfo.stepId)
          processNameValue.value = data.prcessName ?? data.processName
          tabTitle.value = data.processInstance.serialNum;
          formProcessTitle.value = tabTitle.value
          viewModel.value = data.viewModel;
          console.log("%c [ 3，/bpm/workflow/step/628674524479557/processinfo --- data.viewModel ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data.viewModel)
          // ResponsiblePersonNames
          memberID.value = data.processInstance.ouid;
          memberOUID.value = data.processInstance.ownerPosition;
          const permisions = data.permisions;
          const formId = data.form.formId;
          const actions = data.actions as Array<any>;
          const taskInstance = data.taskInstance
          sessionStorage.setItem('taskInstance', JSON.stringify(taskInstance))
          if (actions && actions.length > 0) {
            actions.forEach((item) => {
              btns.value.push({
                text: item.actionName,
                component: "YZAgreen",
                enable: true,
                groupId: item.validationGroup,
                submitConfirm: item.submitConfirm,
                promptMessage: item.promptMessage
              });
              groupIdArr.value.push(item.validationGroup)
            });
          }
          if (taskInstance) {
            if (taskInstance.taskInstanceType === 'Process') {
              // 流程完毕知会
              if (taskInstance.stepFinished && taskInstance.stepFinished != undefined) {
                btns.value = []
                processStore.setLoadType('read')
                btns.value.push(
                  {
                    text: useLang('Form.Notify'),
                    component: "YZNotify",
                    enable: true,
                  }
                );
              } else {
                btns.value.push(
                  // 退回申请人
                  {
                    text: useLang('Form.Return_applicant'),
                    enable: permisions.RecedeRestart,
                    component: "YZReturnStart",
                  },
                  // 拒绝
                  {
                    text: useLang('Form.Refuse'),
                    enable: permisions.Reject,
                    component: "YZReject",
                  },
                  // {
                  //   text: useLang('Form.Return'),
                  //   enable: permisions.RecedeBack,
                  //   component: "YZReturn",
                  // },
                  {
                    text: useLang('Form.Entrust'),
                    component: "YZAssign",
                    enable: permisions.Transfer,
                  }
                );
                if (permisions) {
                  btns.value.push(
                    {
                      text: useLang('Form.Notify'),
                      enable: permisions.Inform,
                      component: "YZNotify",
                    },
                    {
                      text: useLang('Global.Cancel'),
                      enable: permisions.Abort,
                      component: "YZCancle",
                    },
                    {
                      text: useLang('Form.Back_a_step'),
                      enable: permisions.RecedeBack,
                      component: "YZReturn",
                    }
                  )
                  // moreBtns.value.push(
                  //   {
                  //     text: useLang('Form.Notify'),
                  //     enable: permisions.Inform,
                  //     component: "YZNotify",
                  //   },
                  //   {
                  //     text: useLang('Global.Cancel'),
                  //     enable: permisions.Abort,
                  //     component: "YZCancle",
                  //   }
                  // )
                }
              }

            } else if (taskInstance.taskInstanceType === 'Inform') {
              btns.value.push(
                {
                  text: useLang('Form.Read'),
                  component: "YZRead",
                  enable: true,
                },
                {
                  text: useLang('Form.Notify'),
                  component: "YZNotify",
                  enable: true,
                }
              );
            }
            consignEnable.value = permisions.Consign
          }
          buildForm(formId)
          // console.log("%c [4 formId ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formId)

        } else {
          showNotify({
            type: 'danger',
            message: JSON.stringify(data)
          })
        }
      } else if (loadInfo.taskId) {
        if (loadInfo.loadType === 'read') {
          //已办
          const data = await useTaskProcess.myProcessed.getCompletePostInfo(loadInfo.taskId)
          if (data) {
            processStore.setPostInfo(data);
            processVersion.value = data.processVersion;
            processTaskId.value = data.taskId
            const formId = data.form.formId;
            const permisions = data.permisions;
            tabTitle.value = data.processInstance.serialNum;
            formProcessTitle.value = tabTitle.value
            viewModel.value = data.viewModel;
            memberID.value = data.processInstance.ouid;
            memberOUID.value = data.processInstance.ownerPosition;
            processStore.setTaskId(data.taskId)
            if (permisions) {
              btns.value.push({
                text: useLang('Form.Retrieve'),
                enable: permisions.PickBack,
                component: "YZBack",
              });
              btns.value.push({
                text: useLang('Form.Notify'),
                enable: permisions.Inform ?? true,
                component: "YZNotify",
              });
            }
            buildForm(formId)
            console.log("%c [5 formId ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formId)

          } else {
            showNotify({
              type: 'danger',
              message: JSON.stringify(data)
            })
          }
        } else {
          // 申请
          const data = await useTaskProcess.myRequest.getMyRequestInfo(loadInfo.taskId);
          if (data) {
            processStore.setPostInfo(data);
            processVersion.value = data.processVersion;
            processNameValue.value = data.processName ?? data.prcessName;
            processTaskId.value = data.taskId
            const formId = data.form.formId;
            tabTitle.value = data.processInstance.serialNum;
            formProcessTitle.value = tabTitle.value
            const permisions = data.permisions
            viewModel.value = data.viewModel;
            memberID.value = data.processInstance.ouid;
            memberOUID.value = data.processInstance.ownerPosition;
            if (permisions) {
              btns.value.push(
                {
                  text: useLang('Form.Retrieve'),
                  enable: permisions.PickBackRestart,
                  component: "YZBack",
                },
                {
                  text: useLang('Global.Cancel'),
                  enable: permisions.Abort,
                  component: "YZCancle",
                },
                {
                  text: useLang('Form.Notify'),
                  enable: permisions.Inform,
                  component: "YZNotify",
                },
                {
                  text: useLang('Form.Urging'),
                  enable: permisions.Reminder,
                  component: "YZRemind",
                }
              );
            }
            buildForm(formId)
            console.log("%c [6 formId ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formId)

          } else {
            showNotify({
              type: 'danger',
              message: JSON.stringify(data)
            })
          }

        }
      }
    } else {
      showNotify({
        type: 'danger',
        message: useLang('Form.ProcessInformation_tips')
      })
    }
  }
  /**
   *  根据表单id构建渲染表单
   * @param formId 表单id
   */
  const buildForm = async (formId: string, isTrue: boolean = false) => {
    // 构建按钮
    const allBtns = btns.value
    btns.value = []
    for (let i = 0; i < allBtns.length; i++) {
      const btn = allBtns[i]
      if (i <= 2) {
        btns.value.push(btn)
      } else {
        moreBtns.value.push(btn)
      }
    }

    if (formId) {
      // 解析表单
      const jsonConfig = await renderForm(formId);
      reanderFields(jsonConfig, formId);
    }
  }
  // 1、解析每个控件的信息
  const reanderFields = async (
    newJson: any,
    formId: string
  ): Promise<void> => {
    const formconfig = new FormConfig(newJson);
    const allFields = newJson.fields as Array<any>;
    let newfieldsData: any = [];
    if (viewModel.value && viewModel.value.data) {
      const rowData = viewModel.value.data;
      if (rowData['TaskID']) {
        //表示审批，创建TaskID控件
        const taskField = formBuilder.buildSystemField('taskid', rowData['TaskID'])
        allFields.push(taskField)
      }
      if (rowData['ItemId']) {
        const taskField = formBuilder.buildSystemField('itemid', rowData['ItemId'])
        allFields.push(taskField)
      }
      const sechemData = viewModel.value.schema;
      if (allFields && allFields.length > 0) {
        for (let i = 0; i < allFields.length; i++) {
          const field = allFields[i];
          if (field['__vModel__']) {
            const model = field["__vModel__"].model;
            let fModel = model
            if (field["__config__"].type === "grid") {
              if (!rowData[fModel]) {
                //自动生成的表名称
                const newT = allFields.find(x => x['__config__'].ctype === 'formtitle')
                if (newT) {
                  fModel = "BDS_" + newT['__config__'].label + "_" + model
                }
              }
              const rowValues = rowData[fModel]
              field["__vModel__"].modelValue = rowData[fModel];
              const colmuns = sechemData[fModel].columns;
              const vColums = field['__config__'].column
              if (rowValues.length > 0 && rowValues[0]['ParentItemId']) {
                // 增加特殊字段
                const taskField = formBuilder.buildSystemField('taskid', rowData['TaskID'])
                vColums.push(taskField)
                const itemId = formBuilder.buildSystemField('itemid', rowData['ItemId'])
                vColums.push(itemId)
                const parentid = formBuilder.buildSystemField('parentid', '')
                vColums.push(parentid)
              }
              vColums.forEach((vm: any) => {
                const keyModel = vm['__vModel__'].model
                if (keyModel !== 'TaskID' && keyModel !== 'ItemId' && keyModel !== 'ParentItemId') {
                  if (colmuns[keyModel]) {
                    vm.disabled = !colmuns[keyModel].writeable;
                    vm.readonly = !colmuns[keyModel].writeable;
                    vm.writeable = colmuns[keyModel].writeable;
                    field['__config__'].writeable = colmuns[keyModel].writeable;
                    vm.hidden = !colmuns[keyModel].readable;
                    vm['__config__'].defualtValue = colmuns[keyModel].defaultValue
                  }
                }

              })
              // 获取子表writeable 作为明细的可读 添加编辑删除修改等操作 一真即真
              let gridWriteable = vColums.some((vm: any) => {
                const keyModel = vm['__vModel__'].model
                if (keyModel !== 'TaskID' && keyModel !== 'ItemId' && keyModel !== 'ParentItemId') {
                  return vm.disabled
                }
              })
              field['__config__'].column = vColums
              field['__config__'].allowAddRecord = sechemData[fModel].allowAddRecord
              field.disabled = gridWriteable;
              field.readonly = gridWriteable;
              newfieldsData.push(field);
            } else {
              if (rowData[model] !== null && rowData[model] !== '' && rowData[model] !== undefined) {
                field["__vModel__"].modelValue = rowData[model];
              } else {
                field["__vModel__"].modelValue = ''
              }
              const colmuns = sechemData["$$root"].columns;
              if (colmuns[model]) {
                field.disabled = !colmuns[model].writeable;
                field.readonly = !colmuns[model].writeable;
                field.writable = colmuns[model].writeable;
                field.hidden = !colmuns[model].readable;
                field['__config__'].writable = colmuns[model].writeable;
                newfieldsData.push(field);
              } else {
                newfieldsData.push(field);
              }
            }
          }

        }
      }
    } else {
      newfieldsData = allFields;
    }
    const newfields = buildedFormField({
      fields: newfieldsData,
    });
    renderEnd(newfields, formconfig, formId);
  };
  // 最后的控件填充
  const renderEnd = async (newfields: YZField[], formconfig: FormConfig, formId: string) => {
    formconfig.setFields(newfields);
    formconfig.setFieldsRecord(newfields);
    fieldsRefs.value = formconfig.getFields();
    formBuilder.setFormConfig(formconfig);
    formBuilder.setCopyField(newfields);
    // 加载外部表单拦截函数
    eventFire.value = await init(processNameValue.value);
    // 加载外部自定义高级验证
    validatorFuns.value = await loadValidator(processNameValue.value)
    if (eventFire.value && eventFire.value.afterFormLoad) {
      await eventFire.value.afterFormLoad(instances, formRef.value, fieldsRefs.value)
    }
    // 加载外部移动端重写方法
    const mobileEvents = await loadMobileCustomerFun(processNameValue.value)
    if (mobileEvents) {
      setFormMobileEvents(mobileEvents)
    }
    clearFormValidator();
    // 存储高级验证
    hightValidator();
    const core = useCore()
    core.setRefs(formRef.value)
    core.setFieldRefs(fieldsRefs.value)
    setTimeout(() => {
      showLoading.value = false
    }, 1000);
  };
  // 加载高级验证
  const customreValidate = () => {
    const filedValues = fieldsRefs.value
    //执行自定义验证且是高级函数验证
    const funs = validatorFuns.value
    let isValidate = true
    for (let i = 0; i < filedValues.length; i++) {
      const element = filedValues[i]
      if (element.config.ctype === 'YZTable') {
        const values = element.vModel.value
        if (values && values.length > 0) {
          for (let j = 0; j < values.length; j++) {
            const item = values[j]
            for (let k = 0; k < item.colums.length; k++) {
              const colm = item.colums[k]
              if (colm) {
                const validatetors = colm.field.config.validators
                if (validatetors.length > 0) {
                  // 注册事件
                  for (let j = 0; j < validatetors.length; j++) {
                    const validate = validatetors[j]
                    const funName = validate.validator
                    if (funName) {
                      const fv = funs[funName]
                      if (fv) {
                        const flag = fv(colm.field.vModel, colm.field, filedValues, item)
                        if (flag !== true) {
                          showNotify({ type: 'danger', message: flag })
                          isValidate = false
                          colm.field.config.required = true
                          return false
                        }
                      }
                    }
                  }
                }
              }

            }
          }
        }
      } else {
        const validatetors = element.config.validators
        if (validatetors.length > 0) {
          // 注册事件
          for (let j = 0; j < validatetors.length; j++) {
            const validate = validatetors[j]
            const funName = validate.validator
            if (funName) {
              const fv = funs[funName]
              if (fv) {
                const flag = fv(element.vModel, element, filedValues, null)
                if (flag !== true) {
                  showNotify({ type: 'danger', message: flag })
                  isValidate = false
                  element.config.required = true
                  return false
                }
              }

            }
          }
        }
      }

      if (!isValidate)
        break;
    }
    return isValidate
  }
  // 加载高级验证部分自定义验证
  const hightValidator = () => {
    const filedValues = fieldsRefs.value
    //执行自定义验证且是高级函数验证
    for (let i = 0; i < filedValues.length; i++) {
      const element = filedValues[i]
      const validatetors: any = element.config.validators
      if (validatetors && validatetors.length > 0) {
        validatetors.forEach((item: IFormValidator) => {
          let newItem = {
            ...item,
            field: element
          }
          setFormValidator(newItem)
        })
      }
    }
  }

  /* 流程按钮操作开始 */
  // 流程提交 submit
  eventBus.on("onSubmit", {
    cb: async function (params) {
      submitParams.value = params
      /*业务规则拦截验证开始 */
      const validateData = await useValidateHight(params.groupId)
      console.log('onSubmit --- >>> ', validateData)
      if (validateData && validateData.length > 0) {
        const validateBool = validateData.find(x => x.validate === false)
        if (validateBool) {
          // showNotify({
          //   type: 'danger',
          //   message: validateBool.message // messageHtmls.join(',')
          // })
          console.log('validateBool.message', validateBool.message)
          return;
        }
      }

      let uploadCom = useGetFormFields().filter(item => (item.config.ctype == 'YZUploader' || item.config.ctype == 'YZImageUpload'))
      // console.log(uploadCom)
      if (uploadCom && uploadCom.length > 0) {
        let uploadFlag = uploadCom.some((item: any) => item.getIsUpload())
        // console.log(uploadFlag)
        if (uploadFlag) {
          showNotify({
            type: 'danger',
            message: '文件上传中，请稍后...' // messageHtmls.join(',')
          })
          return;
        }
      }


      /*业务规则拦截验证结束 */

      /* 提交弹出验证 */
      if (params.submitConfirm && params.submitConfirm === 'Prompt') {
        const dialog = await showConfirmDialog({
          title: useLang('Form.SubmitConfirmation_tips'),
          message: params.promptMessage,
          cancelButtonText: useLang('Global.Cancel'),
          confirmButtonText: useLang('Global.Confirm'),
        })
        if (dialog !== 'confirm') {
          submitParams.value = null
          return
        }

      }
      if (params.submitConfirm && params.submitConfirm === 'EnterPassword') {
        showPass.value = true
        isPassTrue.value = false
      }
      if (isPassTrue.value)
        await onSubmitProcess(params)
      /*提交弹出验证 */
      // if (eventFire.value && eventFire.value.beforeSubmit) {
      //   // 执行提交前事件
      //   const result = await eventFire.value.beforeSubmit(
      //     instances,
      //     params.refs,
      //     fieldsRefs.value
      //   );
      //   if (result) {
      //     // 执行验证前事件
      //     await eventFire.value.beforeValidate(
      //       instances,
      //       params.refs,
      //       fieldsRefs.value
      //     );
      //     // 执行默认验证
      //     params.refs.validate().then(async () => {
      //       if (customreValidate()) {
      //         // 执行验证通过后的事件
      //         const valdiateResult = await eventFire.value.afterValidate(
      //           instances,
      //           params.refs,
      //           fieldsRefs.value
      //         );
      //         if (valdiateResult) {
      //           submitShow.value = true;
      //           currentAction.value = params.text;
      //         }
      //       }
      //     });
      //   }
      // } else {
      //   if (customreValidate()) {
      //     params.refs.validate().then(async () => {
      //       submitShow.value = true;
      //       currentAction.value = params.text;
      //     }).catch((error: any) => {
      //       if (error && error.length > 0) {
      //         const pop = error.pop()
      //         showNotify({ message: `${pop.message}`, type: 'danger' })
      //       }
      //     })
      //   }

      // }
    },
  });
  const onSubmitProcess = async (params: any) => {
    if (eventFire.value && eventFire.value.beforeSubmit) {
      // 执行提交前事件
      const result = await eventFire.value.beforeSubmit(
        instances,
        params.refs,
        fieldsRefs.value
      );
      if (result) {
        // 执行验证前事件
        await eventFire.value.beforeValidate(
          instances,
          params.refs,
          fieldsRefs.value
        );
        // 执行默认验证
        params.refs.validate().then(async () => {
          if (customreValidate()) {
            // 执行验证通过后的事件
            const valdiateResult = await eventFire.value.afterValidate(
              instances,
              params.refs,
              fieldsRefs.value
            );
            if (valdiateResult) {
              submitShow.value = true;
              currentAction.value = params.text;
            }
          }
        });
      }
    } else {
      if (customreValidate()) {
        params.refs.validate().then(async () => {
          submitShow.value = true;
          currentAction.value = params.text;
        }).catch((error: any) => {
          if (error && error.length > 0) {
            const pop = error.pop()
            showNotify({ message: `${pop.message}`, type: 'danger' })
          }
        })
      }

    }
  }
  const onAuditProcess = async (params: any) => {
    if (eventFire.value && eventFire.value.beforeSubmit) {
      const result = await eventFire.value.beforeSubmit(
        instances,
        params.refs,
        fieldsRefs.value
      );
      if (result) {
        await eventFire.value.beforeValidate(
          instances,
          params.refs,
          fieldsRefs.value
        );
        params.refs.validate().then(async () => {
          if (customreValidate()) {
            submitShow.value = true;
            currentAction.value = params.text;
          }
        })
          .catch((error: any) => {
            if (error && error.length > 0) {
              const pop = error.pop()
              showNotify({ message: `${pop.message}`, type: 'danger' })
            }
          })
      }
    } else {
      params.refs.validate().then(async () => {
        if (customreValidate()) {
          submitShow.value = true;
          currentAction.value = params.text;
        }
      }).catch((error: any) => {
        if (error && error.length > 0) {
          const pop = error.pop()
          showNotify({ message: `${pop.message}`, type: 'danger' })
        }
      })
    }
  }
  const onPassConfirm = async () => {
    if (!passBind.value) {
      showNotify({ type: 'danger', message: '请输入密码' })
      showPass.value = true
      return false
    }
    const { pass } = await usePostBody(url.login.submitauth, {},
      {
        p: btoa(passBind.value),
      })
    if (pass) {
      showPass.value = false
      await onSubmitProcess(submitParams.value)
      submitParams.value = null
    } else {
      showNotify({ type: 'danger', message: '密码错误' })
    }

  }
  const onPassCancel = () => {
    passBind.value = ''
    showPass.value = false
    submitParams.value = null
  }
  onMounted(() => {
    // console.log('onous')
    // console.log(eventBus,'eventBus')
  })
  //知会
  eventBus.on("onNotitfy", {
    cb: function (params) {
      userShow.value = true;
    },
  });
  // 审批
  eventBus.on("onProcess", {
    cb: async function (params: any) {
      submitParams.value = params;
      /*业务规则拦截验证开始 */
      const validateData = await useValidateHight(params.groupId)
      if (validateData && validateData.length > 0) {
        const validateBool = validateData.find(x => x.validate === false)
        if (validateBool) {
          showNotify({
            type: 'danger',
            message: validateBool.message // messageHtmls.join(',')
          })
          return;
        }
      }
      /*业务规则拦截验证结束 */
      if (params.submitConfirm && params.submitConfirm === 'Prompt') {
        const dialog = await showConfirmDialog({
          title: useLang('Form.SubmitConfirmation_tips'),
          message: params.promptMessage,
          cancelButtonText: useLang('Global.Cancel'),
          confirmButtonText: useLang('Global.Confirm'),
        })
        if (dialog !== 'confirm') {
          submitParams.value = null
          return
        }

      }
      if (params.submitConfirm && params.submitConfirm === 'EnterPassword') {
        showPass.value = true
        isPassTrue.value = false
      }
      if (isPassTrue.value)
        await onAuditProcess(params)

    },
  });
  const childFormByEvent = () => {
    eventBus.on(`childFormSave${props.formService.uuid}`, {
      cb: async function (params) {
        submitParams.value = params
        if (eventFire.value && eventFire.value.beforeSubmit) {
          // 执行提交前事件
          const result = await eventFire.value.beforeSubmit(
            instances,
            params.refs,
            fieldsRefs.value
          );
          if (result) {
            // 执行验证前事件
            await eventFire.value.beforeValidate(
              instances,
              params.refs,
              fieldsRefs.value
            );
            // 执行默认验证
            params.refs.validate().then(async () => {
              if (customreValidate()) {
                // 执行验证通过后的事件
                const valdiateResult = await eventFire.value.afterValidate(
                  instances,
                  params.refs,
                  fieldsRefs.value
                );
                if (valdiateResult) {
                  // submitShow.value = true;
                  await childFormSubmit(params)
                  currentAction.value = params.text;
                }
              }
            });
          }
        } else {
          if (customreValidate()) {
            params.refs.validate().then(async () => {
              await childFormSubmit(params)

              currentAction.value = params.text;
            }).catch((error: any) => {
              if (error && error.length > 0) {
                const pop = error.pop()
                showNotify({ message: `${pop.message}`, type: 'danger' })
              }
            })
          }

        }
      },
    });
  }

  const childFormSubmit = async (params: any) => {
    const { appId, formId, formState, formKey } = params.formService
    const service = new ProcessService();
    // 构建表单字段
    const buildTable = service.buildSumbitTable(fieldsRefs.value);
    const newUrl = useParamsFormatter(url.form.childFormSubmit, {
      params1: appId,
      params2: formKey,
      params3: encodeURI(formState)
    });
    const body = {
      formData: buildTable,
      header: {
        appId,
        formId,
        formState
      }
    }
    const result = await usePostBody(newUrl, {}, body);
    let cb = { ...result, uuid: params.formService.uuid }
    useCallBackClose(function () {
      eventBus.emit(`onBackChildForm`, true, cb)
    }, tabHeaderShow.value)
  }
  const onSubmit = async (val: any) => {
    submitShow.value = true;
  };
  const formRef = ref();
  const onClickLeft = () => {
    const core = useCore()
    core.delFieldsRecordArrByLast()
    // 表单关闭，执行表单内事件销毁
    eventBus.off('setFieldValue', true)
    eventBus.off('setFieldDisable', true)
    eventBus.off('setFieldHidden', true)
    eventBus.emit('onBack', true)
    eventBus.off('onDetailMap', true)
    homeStore.setTab(true)
    processStore.clearFormStorage()
    sessionStorage.removeItem('taskInstance')
  };
  /**
   *
   * @param type 类型 1 取消 2 完成
   * @returns
   */
  const hadlerPop = async (type: number) => {
    console.log("%c [ 审批 type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", type)
    if (postType.value === 1) {
      submitShow.value = false;
      if (type === 2) {
        await postProcess();
      }
    } else if (postType.value === 2) {
      // 流程审批
      submitShow.value = false;
      if (type === 2) {
        await processTask();
      }
    }
  };
  // 流程提交==调用API
  const postProcess = async () => {
    const service = new ProcessService();
    // 构建表单字段
    const buildTable = service.buildSumbitTable(fieldsRefs.value);
    // const a = false 
    // if (!a)
    //   return 
    // // 构建头部
    let ouidVale = `${providerName.value}.${memberOUID.value}`
    let owernValue = `${providerName.value}.${memberID.value}`
    // 获取切换后的信息
    const changeItem = postioinData.value.find(x => x.qualifiedId === changePositonId.value)
    if (changeItem) {
      const noouidValue = `${changeItem.providerName}.${changeItem.ouid}`
      const nnowerValue = `${changeItem.providerName}.${changeItem.memberId}`
      if (ouidVale !== noouidValue)
        ouidVale = noouidValue
      if (owernValue !== nnowerValue)
        owernValue = nnowerValue
    }
    // 流程提交
    const consign = processStore.getConsign
    if (openSign.value && consign.userArrys.length <= 0) {
      showNotify({
        type: 'danger',
        message: useLang('Form.Select_Signatory_tips')
      })
      return
    }
    const result = await service.SubmitProcess({
      processId: processDefineId.value,
      version: processVersion.value,
      comments: opinion.value,
      formData: buildTable,
      actionName: currentAction.value,
      ouid: ouidVale,
      ownerPosition: owernValue,
      consignEnabled: openSign.value,
      consignRoutingType: consign.jxsx,
      consignReturnType: consign.jqh,
      consignUsers: consign.userArrys
    });
    if (result.success) {
      processStore.clearFormStorage()
      if (eventFire.value && eventFire.value.afterSubmit) {
        showNotify({
          message: "流程提交成功",
          type: "success",
        });

        eventFire.value
          .afterSubmit(instances, formRef.value, fieldsRefs.value)
          .then((result: any) => {
            useCallBackClose(function () {
              eventBus.emit('onBack', true)
            }, tabHeaderShow.value)
          });
      } else {
        showNotify({
          message: "流程提交成功",
          type: "success",
        });
        useCallBackClose(function () {
          eventBus.emit('onBack', true)
        }, tabHeaderShow.value)
        //eventBus.emit('onBack', true)
      }
    } else {
      console.log("%c [ errorMessage - 2 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result.errorMessage)
    }
  };
  // 流程审核
  const processTask = async () => {
    const service = new ProcessService();
    // 构建表单字段
    const buildTable = service.buildSumbitTable(fieldsRefs.value) as any;
    //   [
    //     {
    //         "Account": "", //2 问题在这里
    //         "Name": "Chunyao Hou",
    //         "TaskID": "***************",
    //         "ItemId": "***************",
    //         "ParentItemId": "***************"
    //     },
    //     {
    //         "Account": "",
    //         "Name": "Lihua Wu",
    //         "TaskID": "***************",
    //         "ItemId": "***************",
    //         "ParentItemId": "***************"
    //     },
    //     {
    //         "Account": "",
    //         "Name": "Qiyang Chen",
    //         "TaskID": "***************",
    //         "ItemId": "***************",
    //         "ParentItemId": "***************"
    //     }
    // ]
    // const emails = buildTable?.FollowupPersonAccount?.split(',') || [];
    // buildTable.FollowupPersonList?.forEach((person: any, index: number) => {  //1 感觉这里有问题
    //   if (index < emails.length) {
    //     if (person.Acount === '') {
    //       person.Acount = emails[index].trim()
    //     }
    //   }
    // })
    if (buildTable?.ResponsiblePersonNames === '' && buildTable?.ResponsiblePersons === '') {
      for (let i = 0; i < buildTable.ResponsiblePersonList.length; i++) {
        buildTable.ResponsiblePersons += buildTable.ResponsiblePersonList[i].PersonAccount;
        buildTable.ResponsiblePersonNames += buildTable.ResponsiblePersonList[i].PersonName;
        if (i < buildTable.ResponsiblePersonList.length - 1) {
          buildTable.ResponsiblePersons += ", ";
          buildTable.ResponsiblePersonNames += ", ";
        }
      }
    }
    // NotifiedParty: [{Account
    // :
    //   "<EMAIL>"
    //   ItemId
    //   :
    //   "***************"
    //   Name
    //   :
    //   "Chunyao Hou"
    //   ParentItemId
    //   :
    //   "***************"
    //   RoleName
    //   :
    //   ""
    //   TaskID
    //   :
    //   "***************"
    // }]
    // NotifiedPartyNames: "Chunyao Hou"
    // NotifiedPartys: "<EMAIL>"
    if (buildTable?.NotifiedPartyNames === '' && buildTable?.NotifiedPartys === '') {
      for (let i = 0; i < buildTable.NotifiedParty.length; i++) {
        buildTable.NotifiedPartys += buildTable.NotifiedParty[i].Account;
        buildTable.NotifiedPartyNames += buildTable.NotifiedParty[i].Name;
        if (i < buildTable.NotifiedParty.length - 1) {
          buildTable.NotifiedPartys += ", ";
          buildTable.NotifiedPartyNames += ", ";
        }
      }
    }
    console.log("%c [ buildTable 2]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", buildTable)
    buildTable['TaskID'] = processTaskId.value
    const consign = processStore.getConsign
    if (openSign.value && consign.userArrys.length <= 0) {
      showNotify({
        type: 'danger',
        message: useLang('Form.Select_Signatory_tips')
      })
      return
    }
    const result = await service.ProcessTask({
      taskId: processTaskId.value,
      stepId: processStepId.value,
      uid: userStore.getAccount as string,
      actionName: currentAction.value,
      formData: buildTable,
      comments: opinion.value,
      consignEnabled: openSign.value,
      consignReturnType: consign.jqh,
      consignRoutingType: consign.jxsx,
      consignUsers: consign.userArrys
    });
    console.log("%c [ 待审批 submit --- result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)
    if (result.success === false && result.errorMessage) {
      showNotify({ message: result.errorMessage, type: "danger" });
      console.log("%c [ errorMessage - 1]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result.errorMessage)
    } else {
      processStore.clearFormStorage()
      showNotify({ message: useLang('UAUC.AuditReminder'), type: "success" }); //填写复合意见，然后跳转
      await service.getWorkCount()
      useCallBackClose(function () {
        eventBus.emit('onBack', true)
      }, tabHeaderShow.value)
    }
  };
  // 检查是否包含明细表
  const checkHaveDetail = (params: any): boolean => {
    let isHave = false;
    const components = params.components;
    if (components) {
      const keys = Object.keys(components);
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        if (key.startsWith("grid")) {
          isHave = true;
          break;
        }
      }
    }
    return isHave;
  };
  //检查控件是否在明细表里 true 是 false  否
  const checkIsDetailControl = (params: any[], control: any): boolean => {
    const item = params.find((x) => x.uniqueId === control.uniqueId);
    if (item) {
      return true;
    } else {
      return false;
    }
  };
  // 构建每个控件字段
  const buildedFormField = (newJson: any): YZField[] => {
    const fields: YZField[] = [];
    newJson.fields.forEach((item: any) => {
      if (item["__config__"]) {
        // 构建每个控件之前触发
        let newRenderItem = item
        const newItem = formBuilder.builderField(
          item["__config__"].ctype,
          newRenderItem
        );
        if (newItem) fields.push(newItem);
      }
    });
    return fields;
  };
  //整理--解析包含明细表的表单
  const buildeDetail = (components: any): IDetailForm => {
    const childControl: IGridModel[] = [];
    const filterChild: any[] = [];
    // 处理明细表
    Object.keys(components).forEach((key) => {
      if (key.startsWith("grid")) {
        const json: IGridModel = {};
        const gridName = key;
        json.gridName = gridName;
        json.ctype = "table";
        const item = components[gridName];
        json.title = item.title;
        json.bind = item["$bind"];
        json.uniqueId = item["uniqueId"];
        json.$hidden = item['$hidden']
        json.$disable = item['$disable']
        json.$exress = item['$express']
        json.ds = item["ds"]
        json.map = item['$map']
        json.enablePaging = item['enablePaging']
        json.pageSize = item['pageSize']
        json.emptyGrid = item['emptyGrid']
        const colmuns: string[] = item.$items;
        const tools: string[] = item.$tools
        if (colmuns && colmuns.length > 0) {
          json.colums = [];
          colmuns.forEach((col: string) => {
            const control = components[col];
            control["fieldName"] = col;
            if (control) {
              json.colums?.push(control);
              filterChild.push(control);
            }
          });
        }
        if (tools && tools.length > 0) {
          json.tools = []
          tools.forEach((col: string) => {
            const control = components[col];
            control["fieldName"] = col;
            if (control) {
              json.tools?.push(control);
              filterChild.push(control);
            }
          });
        }
        childControl.push(json);
      }
    });
    return {
      childControl: childControl,
      filterChild: filterChild,
    };
  };
  const setHboxHidden = (constrolData: any) => {
    const { components } = constrolData;
    Object.keys(components).forEach((key: any) => {
      if (components[key].ctype == 'hbox' || components[key].ctype == 'table') {
        // 布局需要的隐藏条件
        let $hidden = components[key]?.$hidden
        // HboxCell的控件名
        let $items = components[key]?.$items
        // 存在隐藏条件进行HboxCell遍历 找到那个控件
        if ($hidden && $hidden != '') {
          // 递归
          findHboxCellRecursively(components, $items, $hidden)
        }
      }
    })
  }
  // 做递归的进行hbox控件加入到控件内部
  const findHboxCellRecursively = (components: any, items: any[], hiddenStr: string) => {
    if (hiddenStr == '' || hiddenStr === undefined) return
    for (let i = 0; i < items.length; i++) {
      const el = items[i];
      let HboxCellobj = components[el];
      if (components[el].ctype == 'hbox' || components[el].ctype == 'table') {
        if (HboxCellobj.$hidden) {
          let sonHiddenStr = hiddenStr + `|| ${HboxCellobj.$hidden}`
          findHboxCellRecursively(components, HboxCellobj.$items, sonHiddenStr)
        }
        else {
          findHboxCellRecursively(components, HboxCellobj.$items, hiddenStr)
        }
      } else if (components[el].ctype == 'hboxcell' || el.startsWith('hboxcell') || components[el].ctype == 'tablecell' || el.startsWith('tablecell') || components[el].ctype == 'tab' || el.startsWith('tabpanel')) {
        findHboxCellRecursively(components, HboxCellobj.$items, hiddenStr)
      } else {
        // console.log(HboxCellobj)
        HboxCellobj.$items?.forEach((item: any) => {
          if (components[item].ctype == 'hbox' || components[item].ctype == 'table') {
            if (HboxCellobj.$hidden) {

              let sonHiddenStr = hiddenStr + `|| ${HboxCellobj.$hidden}`
              findHboxCellRecursively(components, HboxCellobj.$items, sonHiddenStr)
            }
            else {
              findHboxCellRecursively(components, HboxCellobj.$items, hiddenStr)
            }
          } else {
            let field = components[item]
            if (field && hiddenStr) {
              field.$hidden = field.$hidden ? field.$hidden + `|| ${hiddenStr}` : hiddenStr
            }
          }
        })
        HboxCellobj.$hidden = HboxCellobj.$hidden ? HboxCellobj.$hidden + `|| ${hiddenStr}` : hiddenStr
      }
    }
  }
  // 获取解析后的明细表字段
  const getDetailFields = (childControl: IGridModel[], formId: string): any[] => {
    const fields: any[] = [];
    childControl.forEach((grid) => {
      const item = formBuilder.builderExtDetial(grid, formId, viewModel.value);
      const colmuns = grid.colums;
      const tools = grid.tools
      if (colmuns && colmuns.length > 0) {
        const fieldRender: any = { components: {} };
        colmuns.forEach((item) => {
          //构建明细字段
          item["from"] = "detail";
          item["tablefield"] = grid.title + "." + item["$bind"];
          Object.defineProperty(fieldRender.components, item.fieldName, {
            value: item,
            writable: true,
            enumerable: true,
            configurable: true,
          });
        });
        const buildField = formBuilder.builderExtField(fieldRender, formId, groupIdArr.value, viewModel.value);
        item["__config__"]["column"] = [];
        item["__config__"]["column"].push(...buildField);
        if (tools && tools.length > 0) {
          //构建工具条控件
          const toolRender: any = { components: {} };
          tools.forEach(tol => {
            tol["tablefield"] = grid.title + "." + tol["fieldName"];
            Object.defineProperty(toolRender.components, tol.fieldName, {
              value: tol,
              writable: true,
              enumerable: true,
              configurable: true,
            });
          })
          const buildField = formBuilder.builderExtField(toolRender, formId, groupIdArr.value, viewModel.value);
          item['__config__']['tools'] = []
          item['__config__']['tools'].push(...buildField)
        }
        fields.push(item);
      }

    });
    return fields;
  };
  // 审批记录
  const initHistory = async () => {
    const taskId = processStore.getProcLoad.taskId
    const newUrl = useParamsFormatter(url.process.getTaskTacke, {
      params1: taskId
    })
    console.log("%c [ 3 newUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)

    const { children } = await useGetQuery(newUrl, {
      Method: 'GetTimellineSteps',
      TaskID: taskId
    })
    console.log("%c [ 4 children ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", children)

    children.forEach((item: any) => {
      let nextData: any = {}
      if (!item.Comments)
        item.Comments = useLang("Global.No_fill_tips")
      if (item.Finished) {
        if (item.NodeName === "时钟触发器") {
          item.handlerText = useLang("Global.SignerGenerate") //`签核人：系统自动启动`
          item.handlerColor = "color:#000000"
        } else if (item.isEndStep) {
          item.handlerText = `${item.NodeDisplayName}`
          item.handlerColor = "color:red"
        } else {
          item.handlerText = `${useLang("Global.Signer")}：${item.HandlerDisplayName}`
          item.handlerColor = "color:#000000"
        }
      } else {
        item.handlerText = `${item.RecipientDisplayName}${useLang("Work.running")}...`
        item.handlerColor = "color:red"
      }
      switch (item.SelAction) {
        case 'sysInform':
          item.NodeDisplayName = item.RecipientDisplayName
          item.handlerText = `${useLang("Work.Initiator")}：${item.HandlerDisplayName}`
          if (item.fllowSteps && item.fllowSteps.length > 0) {
            item.otherRedText = `${useLang('Form.NotifyTo')} ${getUsers(item.fllowSteps)}`
          }
          break;
        case 'sysTransfer':
          if (item.nextSteps && item.nextSteps.length > 0) {
            item.otherRedText = `${useLang('Form.EntrustTo')} ${getUsers(item.nextSteps)}`
          }
          break;
        case "sysRecedeBack":
          if (item.nextSteps && item.nextSteps.length > 0) {
            nextData = getNextUserAndNode(item.nextSteps)
            item.otherRedText = `${useLang('Form.Return')}-->${nextData.nodes.join(',')}(${nextData.users.join(',')})`
          }
          break;
        case "sysRecedeRestart":
          item.otherRedText = useLang('Work.WithdrawToRefill') //`撤回重填`
          break;
        case "sysReject":
          item.otherRedText = useLang('Work.Rejected') //已拒绝
          break;
        case "sysPickBackRestart":
          if (item.nextSteps && item.nextSteps.length > 0) {
            nextData = getNextUserAndNode(item.nextSteps)
            // 取回重填
            item.otherRedText = `${useLang('Work.RetrieveToRefill')}-->${nextData.nodes.join(',')}(${nextData.users.join(',')})`
          }

          break;
        case "sysPickBack":
          if (item.nextSteps && item.nextSteps.length > 0) {
            nextData = getNextUserAndNode(item.nextSteps)
            // 取回审批
            item.otherRedText = `${useLang('Work.RetrieveToApproval')}-->${nextData.nodes.join(',')}(${nextData.users.join(',')})`
          }
          break;
        case "sysAbort":
          item.otherRedText = useLang('Work.WithdrawApplication') //"撤销申请"
          item.NodeDisplayName = useLang('Work.WithdrawApplication') //"撤销申请"
          break;

          console.log("%c [2  item.SelAction ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item.SelAction)

      }
    })
    historyData.length = 0
    historyData.push(...children)
    console.log("%c [ 6 historyData ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", historyData)
    const findex = historyData.findIndex(x => !x.Finished)
    activeId.value = 100
    if (findex > -1) {
      activeId.value = findex
    } else {
      const endIndex = historyData.findIndex(x => x.isEndStep)
      if (endIndex > -1)
        activeId.value = endIndex
    }
  }
  const getUsers = (steps: Array<any>): string => {
    const users: string[] = []
    steps.forEach((step: any) => {
      users.push(step.RecipientDisplayName)
    })
    return users.join(',')
  }
  const getNextUserAndNode = (steps: Array<any>) => {
    const users: string[] = []
    const nodes: string[] = []
    steps.forEach((step: any) => {
      users.push(step.RecipientDisplayName)
      nodes.push(step.NodeDisplayName)
    })
    return {
      users,
      nodes
    }
  }
  // 审批预测
  const onTabChange = async (val: number) => {
    console.log("%c [ 5 val ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", val)
    if (val === 1) {
      // 获取审批记录
      await initHistory()
    }
    // if (val === 1 && fatureData.value.length <= 0) {
    //   const process = new ProcessService();
    //   // 获取表单数据
    //   const postForm = {
    //     formData: process.buildSumbitTable(fieldsRefs.value),
    //     header: {
    //       comments: "",
    //       ouid: `${providerName.value}.${memberOUID.value}`,
    //       ownerPosition: `${providerName.value}.${memberID.value}`,
    //     },
    //   };
    //   if (openType.value === 2) {
    //     // 我的申请打开，获取审批预测
    //     postForm.header.ouid = memberOUID.value;
    //     postForm.header.ownerPosition = memberOUID.value;
    //     const { forecastResult } = await process.myRequestFature(
    //       processInstanceId.value,
    //       postForm
    //     );
    //     if (forecastResult) {
    //       const { taskInstances } = forecastResult;
    //       fatureData.value = taskInstances;
    //     } else {
    //       showNotify({ message: "获取审批预测失败" });
    //     }
    //   } else if (openType.value === 3) {
    //     postForm.header.ouid = memberOUID.value;
    //     postForm.header.ownerPosition = memberOUID.value;
    //     const { forecastResult } = await process.myWorkFature(
    //       processTaskId.value,
    //       postForm
    //     );
    //     if (forecastResult) {
    //       const { taskInstances } = forecastResult;
    //       fatureData.value = taskInstances;
    //     } else {
    //       showNotify({ message: "获取审批预测失败" });
    //     }
    //   } else if (openType.value === 1) {
    //     const { forecastResult } = await process.initFature(
    //       processVersion.value,
    //       processDefineId.value,
    //       postForm
    //     );
    //     if (forecastResult) {
    //       const { taskInstances } = forecastResult;
    //       fatureData.value = taskInstances;
    //     } else {
    //       showNotify({ message: "获取审批预测失败" });
    //     }
    //   }
    // }
  };
  const onMoreClick = () => {
    moreShow.value = true
  }
  const onPostionChange = (val: any) => {
    console.log("%c [ val ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", val)
    if (val && val.selectedOptions.length > 0) {
      const item = val.selectedOptions[0]
      changePostion.value = item.text
      console.log("%c [ changePostion.value  ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", changePostion.value)
      changePositonId.value = item.value
      processStore.setMemberOuId(changePositonId.value)
      eventBus.emit('setInitDept', true, item.text)
    }

    pistionShow.value = false
  }
  const onRedirectUser = () => {
    if (headData.RedirctUrl) {
      window.open(headData.RedirctUrl, '_self')
    }
  }
  onUnmounted(() => {
    // console.log('表单关闭，销毁')
    eventBus.off('setFieldValue', true)
    eventBus.off('setFieldDisable', true)
    eventBus.off('setFieldHidden', true)
  })
  return {
    title,
    btns,
    onClickLeft,
    submitShow,
    hadlerPop,
    fieldsRefs,
    onSubmit,
    formRef,
    activeTab,
    onTabChange,
    activeStep,
    fatureData,
    opinion,
    tabTitle,
    userShow,
    headerShow,
    openType,
    recordStep,
    reordData,
    formProcessTitle,
    showLoading,
    planForm,
    planObj,
    isThird,
    moreBtns,
    moreShow,
    onMoreClick,
    postioinData,
    changePostion,
    pistionShow,
    positionColumns,
    onPostionChange,
    consignEnable,
    headData,
    onRedirectUser,
    tabHeaderShow,
    openSign,
    postType,
    historyData,
    activeId,
    showPass,
    passBind,
    onPassConfirm,
    onPassCancel,
    openTaskForm,
    groupIdArr,
    viewColsData
  };
}
export { useForm };
