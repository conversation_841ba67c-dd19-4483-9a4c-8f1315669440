//formtitle
import { Y<PERSON><PERSON>onfig, Y<PERSON><PERSON><PERSON>, Y<PERSON><PERSON>ty<PERSON> } from "./formModel";
import { useLang } from "@/utils";
export class YZFormTtileConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZFormTtile'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZFormTtileStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZFormTtile extends Y<PERSON><PERSON>ield {
    private title: string;
    constructor(params: any) {
        super(params)
        this.title = params['__config__'].extends['title']
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZFormTtileConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZFormTtileStyle(style)
    }
    getTitle() {
        return this.title
    }
}