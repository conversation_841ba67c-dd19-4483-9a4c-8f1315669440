<template>
    <div v-if="!field.hidden">
        <div class="address-card">
            <van-field :name="field.vModel.model" :required="field.config.required" :disabled="field.disabled" is-link
                v-model="vModel" :rules="field.config.rules" :placeholder="field.placeholder" @click="onAreaShow"
                :readonly="true" :label="field.config.label">
            </van-field>
            <van-field v-if="config.getShowDetail()" name="details" v-model="vOptionModel" :readonly="field.disabled"
                :disabled="field.disabled" type="textarea" :placeholder="useLang('Form.SelectAddress_tips')"
                :label="useLang('Form.DetailAddress')">
            </van-field>
            <van-popup v-model:show="areaShow" position="bottom">
                <van-area v-model="selectValue" :columns-num="config.getLevel()" title="选择地址" @cancel="onCancle"
                    @confirm="onConfirm" :area-list="areaList" />
            </van-popup>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, PropType, ref } from 'vue';
import { YZAddress, YZAddressConfig } from '@/logic/forms/YZAddress';
import { useGetBody } from '@/utils/request';
import { eventBus } from '@/utils/eventBus';
import { useLang } from '@/utils';
const props = defineProps({
    field: {
        type: Object as PropType<YZAddress>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue', 'update:optionValue'])
const config = props.field.config as YZAddressConfig
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const vOptionModel = computed({
    get() {
        return props.optionValue
    },
    set(val) {
        emit('update:optionValue', val)
    }
})
const areaList = ref()
const areaShow = ref<boolean>(false)
const selectValue = ref<string>('')
const onAreaShow = () => {
    if (!props.field.disabled) {
        areaShow.value = true
    }
}
const onCancle = () => {
    areaShow.value = false
}
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const onConfirm = (e: any) => {
    vModel.value = e.selectedOptions.map((x: any) => x.text).join('/')
    setFun()
    areaShow.value = false
}
onMounted(async () => {
    const data = await useGetBody('customer/area.json', {}, {})
    areaList.value = data
})

eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
eventBus.on('onMap', {
    cb: async function (params) {
        console.log("%c [ params 2]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
            vModel.value = params.value
            setFun()

        }
    }
})
</script>