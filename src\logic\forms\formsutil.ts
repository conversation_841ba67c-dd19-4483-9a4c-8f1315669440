import { url } from "@/api/url";
import { useFormD<PERSON>, useParamsFormatter } from "@/utils";
import {
  useAxios,
  useGetQuery,
  usePostBody,
  usePostQuery,
  useRequest,
} from "@/utils/request";
import { showNotify } from "vant";
import { FormTitle, IFormEvent, IFormValidator, ILocalizationSetting, YZField } from "./formModel";
import { e } from "mathjs";
import { formpt } from "./formp";
import { formBuilder } from "./formBuilder";
import { parseExpress } from "./express";
import { useProcessStore } from "@/store/process";
import { YZDatePickerConfig } from "./YZDatePicker";
import { YZSelect } from "./YZSelect";
import { YZRadio } from "./YZRadio";
import { YZCheckBox } from "./YZCheckBox";

/**
 *  解析指定的表单
 * @param filepath 指定表单ID
 * @returns 返回解析后的表单对象
 */
export const useResolveFormStr = async (filepath: string): Promise<any> => {
  const newUrl = useParamsFormatter(url.form.getFormUf, {
    params1: filepath,
  });
  // https://ppa80-dev.linde-le.cn/mobile/bpm/form/616916772278341/definition?_dc=1735122444533
  const data = await useGetQuery(newUrl, {}, false); //解析表单，中英文
  // console.log("%c [ data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
  if (data.code === 1) {
    showNotify({ message: data.message });
    return;
  }
  return data;
};
export const useResovleObj = (result: any) => {
  let data = result.src
  const startindex = data.indexOf('{')
  const lastIndex = data.lastIndexOf(')')
  const value = data.substring(startindex, lastIndex)
  const obj = eval('(' + value + ')')
  return obj
}
/**
 *  解析表单控件
 * @param result 表单对象
 * @returns  返回表单控件对象
 */
export const useResolveFields = (result: any) => {
  const obj = useResovleObj(result)
  return obj['definition']
};
/**
 *  解析表单多语言
 */
export const useResolveLang = (result: any) => {
  const propty = result['property']
  const settting = propty.localizationSetting
  // console.log("%c [ settting 4]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", settting)
  if (settting.enable) {
    const data: ILocalizationSetting = {
      enable: settting.enable,
      cultures: settting.cultures,
      language: {
        ZhCn: settting.languages['zh-Hans'],
        EnUs: settting.languages['en-US'],
      }
    }
    return data
  }

}
/**
 *  解析表单自定方法
 */
export const useResolveFuns = (result: any) => {
  const obj = useResovleObj(result)
  return obj['funcs']
}
/**
 *  获取发起流程时的流程定义
 * @param processId 流程定义ID
 * @param userAccount 当前登录用户工号
 * @returns  返回流程定义信息
 */
export const usePostDefined = async (
  processId: string,
  userAccount: string
): Promise<any> => {
  // 获取流程定义
  const newUrl = useParamsFormatter(url.process.getSubmitProcessInfoInfo, {
    params1: processId,
  });
  console.log("%c [ 进入uauc页面 1 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)

  const data = await useGetQuery(newUrl, {
    uid: userAccount,
  });
  if (data.code === 1 || data.success === false) {
    showNotify({ message: data.message || data.errorMessage });
    console.log('error - 8', data.errorMessage)
    return;
  }
  return data.postInfo;
};

/**
 *  从我的申请打开时，获取当前申请的流程定义
 * @param processId  当前流程数据实例ID
 * @param userAccount  当前登录账号
 * @returns
 */
export const useMyRequestDefined = async (
  processId: string,
  userAccount: string
): Promise<any> => {
  // 获取流程定义
  const newUrl = useParamsFormatter(url.work.getMyRequestForm, {
    params1: processId,
  });
  const data = await useGetQuery(newUrl, {
    uid: userAccount,
  });
  if (data.code === 1) {
    showNotify({ message: data.message });
    return;
  }
  return data.rootTable;
};
/**
 * 流程任务的操作
 * 包含
 * 我的申请
 * 我的待办
 * 我的已办
 * 我的知会
 */
export const useTaskProcess = {
  myRequest: {
    /**
     *  获取我的申请时显示的信息
     * @param processInstanceId 流程实例
     * @param userAccount  当前用户工号
     */
    async getMyRequestInfo(processInstanceId: string): Promise<any> {
      const newUrl = useParamsFormatter(url.process.getMyRequestInfo, {
        params1: processInstanceId,
      });
      const data = await useGetQuery(newUrl, {}, false);
      if (data.code === 1) {
        showNotify({ message: data.message, type: "danger" });
        return null;
      } else {
        return data;
      }
    },
    /**
     *  获取流程发起时显示的信息
     * @param processId  流程定义id
     * @param userAccount 当前登录用户
     */
    async getProcessPostInfo(processId: string): Promise<any> {
      const newUrl = useParamsFormatter(url.process.getSubmitProcessInfoInfo, {
        params1: processId,
      });
      console.log("%c [ 进入uauc页面 2 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)

      const data = await usePostBody(newUrl, {}, {}, false);
      if (data.code === 1) {
        showNotify({ message: data.message, type: "danger" });
        return null;
      } else {
        return data;
      }
    },
  },
  myWorks: {
    /**
     *   获取任务审批、知会 显示的信息
     * @param stepId 步骤id
     * @param userAccount  当前登录用户
     * @returns
     */
    async getWorkPostInfo(stepId: string): Promise<any> {
      const newUrl = useParamsFormatter(url.process.getMyWorkInfo, {
        params1: stepId,
      });
      // console.log('请求地址', newUrl)
      const data = await useGetQuery(newUrl, {}, false);
      console.log("%c [ 1, bpm/workflow/step/***************/processinfo ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
      // console.log('接口返回数据', data)
      if (data.code === 1) {
        showNotify({ message: data.message, type: "danger" });
        return null;
      } else {
        return data;
      }
    },
  },
  myProcessed: {
    /**
     * 获取我的已办时显示的信息
     * @param instanceId  流程实例ID
     * @param userAccount  当前登录用户
     * @returns
     */
    async getCompletePostInfo(
      instanceId: string
    ): Promise<any> {
      const newUrl = useParamsFormatter(url.process.getCompletePostInfo, {
        params1: instanceId,
      });
      const data = await useGetQuery(newUrl, {}, false);
      if (data.code === 1) {
        showNotify({ message: data.message, type: "danger" });
        return null;
      } else {
        return data;
      }
    },
  },
  myNotify: {
    /**
     *   获取知会已阅时显示的信息
     * @param taskId 任务ID
     * @returns
     */
    async getNotifyPostInfo(taskId: string): Promise<any> {
      const newUrl = useParamsFormatter(url.process.getMyWorkInfo, {
        params1: taskId,
      });
      const data = await useGetQuery(newUrl, {}, false);
      console.log("%c [ 2, bpm/workflow/step/***************/processinfo ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
      if (data.code === 1) {
        showNotify({ message: data.message, type: "danger" });
        return null;
      } else {
        return data;
      }
    },
  },
  common: {
    /**
     * 获取审批中的表单数据
     * @param processInstanceId 流程实例
     * @param userAccount  当前登录用用户
     * @param requestModel true/false
     */
    async getProcessForm(
      processInstanceId: string,
      userAccount: string,
      requestModel: boolean
    ): Promise<any> {
      const newUrl = useParamsFormatter(url.work.getMyRequestForm, {
        params1: processInstanceId,
      });
      const data = await useGetQuery(newUrl, {
        uid: userAccount,
        myRequestModel: requestModel,
      }, false);
      if (data.code === 1) {
        showNotify({ message: data.message, type: "danger" });
        return [];
      }
      return data.rootTable;
    },
    /**
     *  根据附件ID获取附件信息
     * @param field 附件ID
     * @returns 返回附件信息
     */
    async getAttmentFile(field: string): Promise<any> {
      if (field) {
        const files = field.split(",");
        const formdata = new FormData();
        files.forEach((file) => {
          formdata.append("fileIds", file);
        });
        const data = await usePostBody(
          url.form.getAttachmentInfo,
          {},
          formdata
          , false);
        return data;
      }
    },
    /**
     *  获取当前流程实例的审批记录
     * @param instanceId 流程实例ID
     * @param userAccount 当前登录用户
     * @returns 当前流程的审批记录
     */
    async getTaskTrace(instanceId: string): Promise<any> {
      const newUrl = useParamsFormatter(url.process.getTaskTacke, {
        params1: instanceId,
      });
      const data = await useGetQuery(newUrl);
      if (data.code === 1 && data.message) {
        showNotify({ message: data.message, type: "danger" });
        return null;
      } else {
        return data;
      }
    },
    /** 
     * 根据任务ID获取当前任务的权限
     * @param taskid 任务ID
     */
    async getTaskPermission(taskid: string): Promise<any> {
      const postData = {
        Method: 'GetTaskPermision',
        perms: 'PickBackExt,Inform,Abort',
        ids: taskid
      }
      const data = await usePostBody(url.process.getTaskPermission, {}, useFormData(postData));
      if (!data.success) {
        showNotify({ message: '权限获取失败', type: "danger" });
        return null;
      }
      return data.perms[taskid]
    }
  },
};
let formEvents: Array<IFormEvent> = []
export function useMemberEvents(events: any) {
  if (events) {
    Object.keys(events).forEach(fun => {
      formEvents.push({
        name: fun,
        fun: events[fun] as Function
      })
    })
  }
}
export function useGlobalEvents(): Array<IFormEvent> {
  return formEvents
}
export function resetEvents() {
  formEvents = []
}
const formValidatorGroup: Array<IFormValidator> = []
export function useFormValidator() {
  return formValidatorGroup;
}
export function clearFormValidator() {
  formValidatorGroup.length = 0
}
export const setFormValidator = (validator: IFormValidator) => {
  formValidatorGroup.push(validator)
}
export const useGetFormFields = () => {
  const allFields = formBuilder.formConfig?.getFields()
  const newAllFields: YZField[] = []
  if (allFields && allFields.length > 0) {
    for (let i = 0; i < allFields.length; i++) {
      const item = allFields[i]
      if (item.config.ctype === 'YZTable') {
        // 如果当前控件是明细表，那么提取明细表的字段信息
        // 如果当前明细有行数据，才进行提取，否则默认明细无任何字段加载
        if (item.vModel.value && item.vModel.value.length > 0) {
          const rows = item.vModel.value as Array<any>
          rows.forEach(item => {
            const cols = item.colums
            if (cols && cols.length > 0) {
              for (let j = 0; j < cols.length; j++) {
                const field = cols[j].field as YZField
                if (field) {
                  const addItem = newAllFields.find(x => x.uuid === field.uuid)
                  if (!addItem)
                    newAllFields.push(field)
                }
              }
            }
          })
        }
        // 如果明细表中存在tools 提取tools中的字段
        let tools = item.config.tools
        if (tools && tools.length > 0) {
          for (let ti = 0; ti < tools.length; ti++) {
            const tl = tools[ti];
            // 转化成字段
            const newItem = formBuilder.builderField(
              tl["__config__"].ctype,
              tl
            ) as YZField;
            newAllFields.push(newItem)
          }
        }
        // 提取当前明细表本身，因为在某些情况下明细表也需要配置表达式
        newAllFields.push(item)
      } else {
        // 如果当前控件是普通字段，那么直接提取
        //field
        const addItem = newAllFields.find(x => x.uuid === item.uuid)
        if (!addItem)
          newAllFields.push(item)
      }
    }
  }
  return newAllFields
}
// 验证流程变量
const processExp = (str: string) => {
  const regex = /\$activityId|\$activityName|\$isPost|\$isProcess|\$isRead|\$isInform/;
  return regex.test(str)
}

const getFieldValue = (fieldModel: YZField) => {
  const $varValues: any = []
  if (fieldModel.config.ctype === 'YZStepper') {
    const nValue = Number(fieldModel.vModel.value)
    if (isNaN(nValue)) {
      // 这边注释是未识别为数字不要反悔nan，返回''
      // $varValues.push(fieldModel.vModel.optionValue)
      $varValues.push(fieldModel.vModel.optionValue)
    } else {
      $varValues.push(nValue)
    }
  } else if (fieldModel.config.ctype === 'YZDatePicker') {
    if (fieldModel.vModel.value) {
      const dcofig = fieldModel.config as YZDatePickerConfig
      if (dcofig.getFormatterType() === 'HH:mm') {
        $varValues.push(fieldModel.vModel.value)
      } else {
        $varValues.push(new Date(fieldModel.vModel.value))
      }

    }
  } else if (fieldModel.config.ctype === 'YZSelect') {
    if (fieldModel.vModel.optionValue) {
      $varValues.push(fieldModel.vModel.optionValue)
    } else {
      const yzSelect = fieldModel as YZSelect
      if (yzSelect) {
        const defaultOptions = yzSelect.getOptions();
        if (defaultOptions && defaultOptions.length > 0) {
          const option = defaultOptions.find(x => x.checked)
          if (option)
            $varValues.push(option.value)
        }
      }
    }
  } else if (fieldModel.config.ctype === 'YZRadio') {
    if (fieldModel.vModel.value) {
      $varValues.push(fieldModel.vModel.value)
    } else {
      const yzRadio = fieldModel as YZRadio
      if (yzRadio) {
        const defaultOptions = yzRadio.getOptions();
        const checkOption = defaultOptions.find(x => x.checked)
        if (checkOption) {
          $varValues.push(checkOption.value)
        }
      }
    }
  } else if (fieldModel.config.ctype === 'YZCheckBox') {
    if (fieldModel.vModel.value) {
      $varValues.push(fieldModel.vModel.optionValue)
    } else {
      const yzCheckBox = fieldModel as YZCheckBox
      if (yzCheckBox) {
        const defaultOptions = yzCheckBox.getOptions();
        const checkOption = defaultOptions.find(x => x.checked)
        if (checkOption) {
          $varValues.push(checkOption.value)
        }
      }
    }
  } else {
    $varValues.push(fieldModel.vModel.value)
  }
  return $varValues
}
const useValidateHightDisabled = (item: IFormValidator) => {
  // 获取当前表单中所有的控件
  let $varValues: Array<any> = []
  const newAllFields = useGetFormFields()
  // 提取当前有关控件的表达式
  const exp = item['$disable']
  if (exp) {
    //解析当前表达式
    const vp = new formpt(exp)
    if (vp.vars && vp.vars.length > 0) {
      vp.vars.forEach(item => {
        //检查当前变量是否跟明细有关
        const vitem = item
        if (vitem.indexOf('.') > -1) {
          //当前变量与明细表有关，提取字段名称
          const fieldName = vitem.split('.')[1]
          //提取当前变量的值
          var fieldModel = newAllFields.find(x => x.vModel.model === fieldName)
          if (fieldModel) {
            $varValues = getFieldValue(fieldModel)
          } else if (processExp(vitem)) {
            let processStr = vitem.replace('$', '')
            const processStore = useProcessStore();
            const postInfoData = processStore.getPostInfoData
            $varValues.push(postInfoData[processStr])
          }
        } else {
          //当前变量与明细表无关，直接提取值
          const fieldModel = newAllFields.find(x => x.vModel.model === vitem)
          if (fieldModel) {
            $varValues = getFieldValue(fieldModel)
            // if (fieldModel.config.ctype === 'YZStepper') {
            //   const nvalue = Number(fieldModel.vModel.value)
            //   if (isNaN(nvalue)) {
            //     $varValues.push(fieldModel.vModel.optionValue)
            //   } else {
            //     $varValues.push(nvalue)
            //   }
            // }else if(fieldModel.config.ctype === 'YZDatePicker') {
            //    if (fieldModel.vModel.value) {
            //     $varValues.push(new Date(fieldModel.vModel.value))
            //    } 
            // } else {
            //   $varValues.push(fieldModel.vModel.value)
            // }
          } else if (processExp(vitem)) {
            let processStr = vitem.replace('$', '')
            const processStore = useProcessStore();
            const postInfoData = processStore.getPostInfoData
            $varValues.push(postInfoData[processStr])
          }
        }
      })
    }
    // 计算表达式
    const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
    const sendValue = Number(value) > 0 ? true : false
    return sendValue
  }
  return false
}
/* 高级验证--express验证 */
export const useValidateHight = (groupId: string): Promise<Array<any>> => {
  return new Promise<any[]>((resolve, reject) => {
    const fields = useGetFormFields()
    const events = useFormValidator();
    const noCostomer = events.filter(x => x.type === 'express' && !x.field.disabled)
    let $varValues: any[] = []
    const resultData = []
    if (noCostomer && noCostomer.length > 0) {
      for (let i = 0; i < noCostomer.length; i++) {
        const noItem = noCostomer[i]
        const json = {
          message: noItem.message,
          validate: true
        }
        if (noItem.validationGroup) {
          if (hasValidationGroup(noItem.validationGroup, groupId) && noItem.field.writable) {
            const vp = new formpt(noItem.$express)
            // 检查变量是否为表单字段
            const { vars } = vp
            if (vars && vars.length > 0 && !useValidateHightDisabled(noItem)) {
              vars.forEach((v: any) => {
                const fieldModel = fields.find(x => {
                  if (v.indexOf(".") > -1 && x.table && x.tableName) {
                    return x.table == v
                  } else {
                    return x.vModel.model === v && !x.table && !x.tableName
                  }
                })
                if (fieldModel) {
                  if (fieldModel.config.ctype === 'YZStepper') {
                    const nValue = Number(fieldModel.vModel.value)
                    if (isNaN(nValue)) {
                      $varValues.push(fieldModel.vModel.optionValue)
                    } else {
                      $varValues.push(nValue)
                    }
                  } else {
                    $varValues.push(fieldModel.vModel.value)
                  }
                }
              })
              // 执行表达式计算
              let endValues = parseExpress.ExecuteExpress(noItem.$express, $varValues, vp.vars)
              json.validate = endValues
              resultData.push(json)
              $varValues = []
            }
          }
        }
        // 执行一次disabled 字段关联 返回false
        else if (noItem.$disable) {
          if (!useValidateHightDisabled(noItem)) {
            const vp = new formpt(noItem.$express)
            // 检查变量是否为表单字段
            const { vars } = vp
            if (vars && vars.length > 0) {
              vars.forEach((v: any) => {
                const fieldModel = fields.find(x => {
                  if (v.indexOf(".") > -1 && x.table && x.tableName) {
                    return x.table == v
                  } else {
                    return x.vModel.model === v && !x.table && !x.tableName
                  }
                })
                if (fieldModel) {
                  if (fieldModel.config.ctype === 'YZStepper') {
                    const nValue = Number(fieldModel.vModel.value)
                    if (isNaN(nValue)) {
                      $varValues.push(fieldModel.vModel.optionValue)
                    } else {
                      $varValues.push(nValue)
                    }
                  } else {
                    $varValues.push(fieldModel.vModel.value)
                  }
                }
              })
              // 执行表达式计算
              let endValues = parseExpress.ExecuteExpress(noItem.$express, $varValues, vp.vars)
              json.validate = endValues
              resultData.push(json)
              $varValues = []
            }
          } else {
            json.validate = useValidateHightDisabled(noItem)
            resultData.push(json)
          }
        } else {
          const vp = new formpt(noItem.$express)
          // 检查变量是否为表单字段
          const { vars } = vp
          if (vars && vars.length > 0) {
            vars.forEach((v: any) => {
              const fieldModel = fields.find(x => {
                if (v.indexOf(".") > -1 && x.table && x.tableName) {
                  return x.table == v
                } else {
                  return x.vModel.model === v
                }
              })
              if (fieldModel) {
                if (fieldModel.config.ctype === 'YZStepper') {
                  const nValue = Number(fieldModel.vModel.value)
                  if (isNaN(nValue)) {
                    $varValues.push(fieldModel.vModel.optionValue)
                  } else {
                    $varValues.push(nValue)
                  }
                } else {
                  $varValues.push(fieldModel.vModel.value)
                }
              }
            })
            // 执行表达式计算
            let endValues = parseExpress.ExecuteExpress(noItem.$express, $varValues, vp.vars)
            json.validate = endValues
            resultData.push(json)
            $varValues = []
          }
        }
      }
    }
    // 过滤必填验证
    fields.forEach(item => {

      if (item.config.validators && item.config.validators.length > 0) {
        // var validateor = item.config.validators.filter(x => x.type === 'required' && hasValidationGroup(x.validationGroup,groupId) && !item.disabled || x.type === 'required' && x.validationGroup == undefined && groupId == undefined && !item.disabled)
        let validateor = []
        // 过滤掉验证组存在的情况
        if (groupId) {
          // 存在可能是一种有validationGroup的时候验证验证组，没有直接取required
          validateor = item.config.validators.filter(x => (x.type === 'required' && hasValidationGroup(x.validationGroup, groupId) && !item.disabled) || x.type === 'required' && !x.validationGroup && !item.disabled)
        } else {
          // 没有验证组的时候直接取出来
          validateor = item.config.validators.filter(x => x.type === 'required' && !item.disabled)
        }
        if (validateor && validateor.length > 0 && (item.config.rules && item.config.rules.length > 0) && item.writable) {
          if (item.tableName) {
            let tableObj = fields.find(el => el.vModel.model == item.tableName && el.config.ctype === 'YZTable')
            if (tableObj?.hidden) {
              return false
            }
          }
          const json = {
            message: `${item.vModel.model}必填`,
            validate: true
          }
          if (item.config.ctype === 'YZStepper') {
            if (!item.vModel.optionValue)
              json.validate = false
          } else if (item.config.ctype === 'YZUploader') {
            if (!item.vModel.optionValue)
              json.validate = false
          } else {
            if (!item.vModel.value)
              json.validate = false
          }
          resultData.push(json)
        }
      }
    })
    resolve(resultData)
  })
}
let formMobileEvents: Array<IFormEvent> = []
// 设置移动端重写自定义方法
export function setFormMobileEvents(events: any) {
  Object.keys(events).forEach(key => {
    const event: IFormEvent = {
      name: key,
      fun: events[key]
    }
    formMobileEvents.push(event)
  })
}
export function useMobileEvents() {
  return formMobileEvents;
}
export function hasValidationGroup(groupId: string, groupIdArr: any = []) {

  if (typeof groupIdArr == 'string') groupIdArr = groupIdArr.split(',')
  if (groupId) {
    return groupIdArr.some((el: any) => {
      if (el && el.indexOf(',') !== -1) {
        let arr = el.split(',')
        let flag = arr.some((ag: string) => ag == groupId)
        if (flag) return flag
      } else {
        return el == groupId
      }
    })
  }
}