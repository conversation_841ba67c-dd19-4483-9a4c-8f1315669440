<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ $t('SSI.EquipmentTool') }}
            </template>
        </YZNav>
        <div class="fave">
            <van-form @submit="onSubmit">
                <!-- -->
                <p style="font-size:16px; font-weight:700;padding:15px;">{{ $t('SSI.GeneralInformation') }}</p>
                <van-cell-group inset>
                    <van-field v-model="EntryDate" readonly name="datePicker" :label="$t('SSI.EntryDate')"
                        input-align="left" label-align="left" style="margin-bottom:15px" class="GenInfo" />
                    <!-- @click="EntryDatePicker = true"  -->
                    <van-field v-model="ProjectName" @click="router.push({ path: '/projectSSI', replace: true })"
                        required rightIcon="search" :label="$t('CSTC.ProjectName')" readonly error
                        :rules="[{ required: true, message: $t('Form.PleaseSelect') }]" input-align="left"
                        label-align="left" class="GenInfo" />
                    <van-field v-model="ProjectNo" :label="$t('CSTC.ProjectNo')" readonly error input-align="left"
                        label-align="left" class="GenInfo" />
                    <van-field v-model="Contractor" :label="$t('SSI.ContractorName')" error clearable input-align="left"
                        label-align="left" class="GenInfo" disabled />
                    <van-field v-model="TemplateID_" is-link required readonly :label="$t('SSI.Equipment')"
                        :rules="[{ required: true, message: $t('Form.PleaseSelect') }]" @click="showEquipmentType"
                        class="GenInfo" />
                    <van-field v-model="Manufacture" @update:model-value="store.setManufacture($event)" required error
                        :label="$t('SSI.Manufacture')"
                        :rules="[{ required: true, message: $t('SSI.manufacturers_name_enter_tips') }]" class="GenInfo"
                        clearable />
                    <van-field v-model="SerialNo" @update:model-value="store.setSerialNo($event)" required error
                        :label="$t('SSI.SerialNo')"
                        :rules="[{ required: true, message: $t('SSI.serial_number_enter_tips') }]" class="GenInfo"
                        clearable />
                    <van-field v-model="DisplayName" :label="$t('SSI.ContractorHSE')" class="GenInfo" readonly />
                </van-cell-group>
                <p style="font-size:16px; font-weight:700; padding: 15px;">{{ $t('SSI.TechnicalInformation') }}</p>
                <van-cell-group class="cell" inset>
                    <div style="display:flex; align-items: center; padding:0 0 0 14px;">
                        <van-switch v-model="ApplicableCalibrationExpiryDate"
                            @update:model-value="store.setApplicableCalibrationExpiryDate($event)" size="16px" />
                        <!-- Calibration Expiry Date 校准到期日 1-->
                        <van-field v-model="CalibrationExpiryDate" is-link readonly name="datePicker" error
                            :label="$t('SSI.CalibrationExpiryDate')" input-align="left" label-align="left"
                            @click="showTimePicker = true" class="CalibrationExpiryDate"
                            :required="ApplicableCalibrationExpiryDate ? true : false"
                            :rules="[{ required: ApplicableCalibrationExpiryDate ? true : false, message: $t('Form.PleaseSelect') }]" />
                        <!-- Calibration Expiry Date 校准到期日 1-->
                    </div>
                    <!-- 上传附件 1  -->
                    <div style="display:flex; align-items: center; position: relative;">
                        <van-field v-model="CertificateOrOtherAttachment_"
                            :label="$t('SSI.Certificate_Other_Documents')" readonly input-align="left"
                            label-align="left" disabled class="line_"
                            :required="ApplicableCalibrationExpiryDate ? true : false"
                            :rules="[{ required: ApplicableCalibrationExpiryDate ? true : false, message: $t('Form.upload') }]" />
                        <div class="add_upload">
                            <img :src="upload" alt="" class="icon_">
                            <input id="UploadPictures" type="file" accept="image/*" class="add_upload_file"
                                @change="(e) => fileUpload(e, 1)" />
                        </div>
                    </div>
                    <div class="add_upload_imgBox" v-if="fileList.length">
                        <div class="add_upload_imgDiv" v-for="(item, index) in fileList" :key="item.id + index">
                            <section>
                                <img v-if="item.base64.includes('image')" :src="item.base64"
                                    @click="fileListShow = true">
                                <img v-else :src="file_" @click="previewFile(1, item)">
                                <van-image-preview v-model:show="fileListShow" :images="imgList"></van-image-preview>
                            </section>
                            <p style="margin-left:5px; margin-bottom: 15px; font-size: 13px; color: #00a0e1;">{{
                                item.formattedName }}
                            </p>
                            <p class="add_upload_close"
                                @click="ProcessedeleteImage(item.id, 1, item.base64, item.fileName)">
                                <van-icon name="cross" style="color:#828282; position: absolute; left: 0; top: 0;" />
                            </p>
                        </div>
                    </div>
                    <!-- 上传附件 1  -->
                </van-cell-group>
                <van-cell-group class="cell" inset>
                    <div style="display:flex; align-items: center;position:relative; padding:0 0 0 14px;">
                        <van-switch v-model="ApplicableOperatorName" size="16px"
                            @update:model-value="store.setApplicableOperatorName($event)" />
                        <!-- Operator Name 操作员名称 2-->
                        <van-field v-model="OperatorName" rightIcon="search"
                            @click="router.push({ path: '/operatorSelect', query: { ProjectNo: ProjectNo, ContractorCode: ContractorCode, ApplicableOperatorName: ApplicableOperatorName }, replace: true })"
                            error readonly :label="$t('SSI.OperatorName')"
                            :required="ApplicableOperatorName ? true : false"
                            :rules="[{ required: ApplicableOperatorName ? true : false, message: $t('Form.PleaseSelect') }]"
                            class="OperatorName" />
                        <van-icon name="info-o" class="msgIcon" @click="ShowPrompt" />
                        <!-- Operator Name 操作员名称 2-->
                    </div>
                    <!-- 上传附件 2  -->
                    <div style="display:flex; align-items: center; position: relative;">
                        <van-field v-model="OperatorAttachment_" error readonly
                            :label="$t('SSI.Competence_Certificate_Card')" disabled
                            :required="ApplicableOperatorName ? true : false"
                            :rules="[{ required: ApplicableOperatorName ? true : false, message: $t('Form.upload') }]"
                            class="line_" />
                        <div class="add_upload">
                            <img :src="upload" alt="" class="icon_">
                            <input id="UploadPictures2" type="file" accept="image/*" class="add_upload_file"
                                @change="(e) => fileUpload(e, 2)" />
                        </div>
                    </div>
                    <!-- <p>imgList_2==>>> {{ imgList_2 }}</p> -->
                    <div class="add_upload_imgBox" v-if="fileList_2.length">
                        <div class="add_upload_imgDiv" v-for="(item, index) in fileList_2" :key="item.id + index">
                            <section>
                                <img v-if="item.base64.includes('image')" :src="item.base64"
                                    @click="fileListShow2 = true">
                                <img v-else :src="file_" @click="previewFile(2, item)">
                                <van-image-preview v-model:show="fileListShow2" :images="imgList_2"></van-image-preview>
                            </section>
                            <p style="margin-left:5px; margin-bottom: 15px; font-size: 13px; color: #00a0e1;">{{
                                item.formattedName }}
                            </p>
                            <p class="add_upload_close"
                                @click="ProcessedeleteImage(item.id, 2, item.base64, item.fileName)">
                                <van-icon name="cross" style="color:#828282; position: absolute; left: 0; top: 0;" />
                            </p>
                        </div>
                    </div>
                    <!-- 上传附件 2  -->
                </van-cell-group>
                <van-cell-group inset style=" display: flex; align-items: center; padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableModel" size="16px"
                        @update:model-value="store.setApplicableModel($event)" />
                    <van-field v-model="Model" @update:model-value="store.setModel($event)" error
                        :label="$t('SSI.Model')" :required="ApplicableModel ? true : false"
                        :rules="[{ required: ApplicableModel ? true : false, message: $t('SSI.applicable_model_enter_tips') }]"
                        class="Model" clearable />
                </van-cell-group>
                <van-cell-group inset style="display: flex; align-items: center;padding:0 0 0 14px;">
                    <van-switch v-model="ApplicablePlateNo" size="16px"
                        @update:model-value="store.setApplicablePlateNo($event)" />
                    <van-field v-model="PlateNo" @update:model-value="store.setPlateNo($event)" error
                        :label="$t('SSI.PlateNo')" :required="ApplicablePlateNo ? true : false"
                        :rules="[{ required: ApplicablePlateNo ? true : false, message: $t('SSI.license_plate_number_enter_tips') }]"
                        class="PlateNo" clearable />
                </van-cell-group>
                <van-cell-group inset style="display: flex; align-items: center;padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableSWLOrCapacity" size="16px"
                        @update:model-value="store.setApplicableSWLOrCapacity($event)" />
                    <van-field v-model="SWLOrCapacity" @update:model-value="store.setSWLOrCapacity($event)" error
                        :label="$t('SSI.SWL_Capacity_Tonnes')" :required="ApplicableSWLOrCapacity ? true : false"
                        :rules="[{ required: ApplicableSWLOrCapacity ? true : false, message: $t('SSI.SWL_capacity_enter_tips') }]"
                        class="SWL_Capacity_Tonnes" clearable />
                </van-cell-group>
                <van-cell-group inset style="display: flex; align-items: center;padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableSizeOrDiameter" size="16px"
                        @update:model-value="store.setApplicableSizeOrDiameter($event)" />
                    <van-field v-model="SizeOrDiameter" @update:model-value="store.setSizeOrDiameter($event)" error
                        :label="$t('SSI.Size_Diameter_meter')" :required="ApplicableSizeOrDiameter ? true : false"
                        :rules="[{ required: ApplicableSizeOrDiameter ? true : false, message: $t('SSI.size_diameter_enter_tips') }]"
                        class="Size_Diameter_meter" clearable />
                </van-cell-group>
                <van-cell-group class="cell" inset style=" display: flex; align-items: center;padding:0 0 0 14px;">
                    <van-switch v-model="ApplicableLength" size="16px"
                        @update:model-value="store.setApplicableLength($event)" />
                    <van-field v-model="Length" @update:model-value="store.setLength($event)" error
                        :label="$t('SSI.Length')" :required="ApplicableLength ? true : false"
                        :rules="[{ required: ApplicableLength ? true : false, message: $t('SSI.length_enter_tips') }]"
                        class="Length" clearable />
                </van-cell-group>
                <van-cell-group inset style="padding:0 0 90px 0; ">
                    <van-field v-model="Remarks" @update:model-value="store.setRemarks($event)" clearable rows="3"
                        autosize :label="$t('SSI.Remarks')" type="textarea" input-align="left"
                        :rules="[{ required: false, message: $t('UAUC.description_of_the_problem_unit_input_tips') }]"
                        label-align="left" />
                </van-cell-group>

                <!-- 牵引线 / ------------------------------------------------------------------------------------------------>
                <van-popup v-model:show="isShowPrompt" position="bottom" :style="{ height: '30%' }" closeable>
                    <div class="ShowPromptMsg">
                        <h3 style="text-align:center;margin-bottom: 10px">
                            {{ $t('SSI.OperatorName') }}
                        </h3>
                        <div>{{ $t('SSI.Supplier_User_Role_Required_Tips') }}</div>
                    </div>
                </van-popup>

                <van-popup v-model:show="showPicker" destroy-on-close round position="bottom">
                    <van-picker :model-value="pickerValue" :columns="columns" @cancel="showPicker = false"
                        @confirm="onConfirm" :loading="pickerLoading" />
                </van-popup>
                <van-popup v-model:show="EntryDatePicker" position="bottom">
                    <van-date-picker @confirm="EntryDateConfirm" @cancel="EntryDatePicker = false" type="date"
                        :min-date="minDate" :max-date="maxDate" />
                </van-popup>
                <!--  日历 -->
                <van-popup v-model:show="showTimePicker" position="bottom">
                    <van-date-picker v-model="currentDate" @confirm="timeConfirm" @cancel="showTimePicker = false"
                        type="date" :min-date="minDate2" :max-date="maxDate2" />
                </van-popup>
                <!-- SHOW 日历 -->
                <div class="foot">
                    <van-button class="subbtn-text" color='#00A0E1' block type="primary" native-type="submit"
                        :loading="Loading">
                        {{ $t('CSTC.submit') }}
                    </van-button>
                </div>
            </van-form>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import { onMounted, ref, reactive, watch, onUnmounted, onBeforeUnmount, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { SSI_Submit, OwnerPosition } from '@/service/user'
import { SSI } from '@/store/ssi'
import { useFormData, useLang, useParamsFormatter } from '@/utils';
import { usePostBody, useGetQuery } from '@/utils/request'
import { showNotify } from 'vant';
import heic2any from 'heic2any'
import Compressor from 'compressorjs';
import { url } from '@/api/url';
import { useCore } from '@/store/core/index'
import upload from '@/assets/imgs/upload.png'
import file_ from '@/assets/imgs/file_.png'
import Pdfh5 from 'pdfh5';
import 'pdfh5/css/pdfh5.css';

const core = useCore()
const store = SSI()
const router = useRouter()
const route = useRoute()
const EntryDate = ref(new Date().toISOString().split('T')[0])
const options_ = ref({
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
})
// const EntryDate_ = ref(new Intl.DateTimeFormat(navigator?.language, options_.value).format(Date.parse(new Date())))
const EntryDatePicker = ref(false)
const minDate = new Date(new Date().setMonth(new Date().getMonth()))
const maxDate = new Date(new Date().setMonth(new Date().getMonth() + 6))
const currentDate = ref(new Date().toISOString().split('T')[0].split('-'))
const minDate2 = new Date(2015, 0, 1)
const maxDate2 = new Date(new Date().setMonth(new Date().getMonth() + 1207)) //2125年
const ProjectName = ref('')
const ProjectNo = ref('')
const Contractor = ref('')
const ContractorCode = ref('')
const columns = ref([])
const TemplateID = ref(store.TemplateID || ''); //value: "646985155559493"
const TemplateID_ = ref(store.TemplateID_ || ''); //text: "Test1"
const showPicker = ref(false);
const pickerValue = ref([]);
const Manufacture = ref(store.Manufacture || '')
const DisplayName = ref()
const CalibrationExpiryDate = ref(store.CalibrationExpiryDate || '') //new Date().toISOString().split('T')[0])
const CalibrationExpiryDate_ = ref() //ref(new Intl.DateTimeFormat(navigator?.language, options_.value).format(Date.parse(new Date())))
const showTimePicker = ref(false)
const ApplicableCalibrationExpiryDate = ref(store.ApplicableCalibrationExpiryDate); //默认true
const ApplicableOperatorName = ref(store.ApplicableOperatorName);
const ApplicableModel = ref(store.ApplicableModel);
const ApplicablePlateNo = ref(store.ApplicablePlateNo);
const ApplicableSWLOrCapacity = ref(store.ApplicableSWLOrCapacity);
const ApplicableSizeOrDiameter = ref(store.ApplicableSizeOrDiameter);
const ApplicableLength = ref(store.ApplicableLength);
const Loading = ref(false)
const Model = ref(store.Model || '') //型号
const PlateNo = ref(store.PlateNo || '') //车牌号
const SWLOrCapacity = ref(store.SWLOrCapacity || '') //SWL / 容量
const SizeOrDiameter = ref(store.SizeOrDiameter || '') //尺寸 /直径
const Length = ref(store.Length || '') //模型
const Remarks = ref(store.Remarks || '') // 备注
const SerialNo = ref(store.SerialNo || '') // 序列号
const EquipmentStatus = ref(0)
const EquipmentStatusValue = ref('')
const OperatorName = ref(store.OperatorName || '') //操作员
const OperatorAccount = ref(store.OperatorAccount || '') //操作员邮箱

const CertificateOrOtherAttachment = ref(store.CertificateOrOtherAttachment || '') // 附件 1
const CertificateOrOtherAttachment_ = ref(store.CertificateOrOtherAttachment ? ' ' : '') //'1 ' : '2'
const fileList = ref(store.fileList || []) //证书文档
const imgList = ref(store.imgList || [])
const MAX_SIZE = ref(2 * 1024 * 1024)  // 最大2MB
const fileIds = ref(store.fileIds_ || [])

const OperatorAttachment = ref(store.OperatorAttachment || '') // 附件 2
const OperatorAttachment_ = ref(store.OperatorAttachment ? ' ' : '')
const fileList_2 = ref(store.fileList_2 || []) //证书卡
const imgList_2 = ref(store.imgList_2 || [])
const MAX_SIZE_2 = ref(2 * 1024 * 1024)  // 最大2MB
const fileIds_2 = ref(store.fileIds_2 || [])

const ClientTimeZone = ref(Intl.DateTimeFormat().resolvedOptions().timeZone)

const showEquipmentType = () => {
    showPicker.value = true;
}
const onConfirm = ({ selectedValues, selectedOptions }) => {
    console.log("%c [ selectedOptions ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedOptions)
    pickerValue.value = selectedValues;
    TemplateID_.value = selectedOptions[0].text; //text
    store.TemplateID_ = selectedOptions[0].text;
    TemplateID.value = selectedOptions[0].value; //value
    store.TemplateID = selectedOptions[0].value;
    showPicker.value = false;
};


const EntryDateConfirm = (time) => {
    EntryDate.value = time.selectedValues?.join('/')
    EntryDatePicker.value = false
}

const timeConfirm = (time) => {
    CalibrationExpiryDate.value = time.selectedValues?.join('/')
    store.CalibrationExpiryDate = time.selectedValues?.join('/')
    // CalibrationExpiryDate_.value = new Intl.DateTimeFormat(navigator?.language, options_.value).format(Date.parse(time.selectedValues?.join('/')))
    // console.log("%c [ time.selectedValues ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", time.selectedValues)
    showTimePicker.value = false
}

const uploadFileApi = async (result_img, type, base64, name, formattedName, id) => {
    let formData = new window.FormData()
    formData.append('fileToUpload', result_img)
    console.log("%c [ 最终 上传的图片 result_img ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result_img)
    await usePostBody(url.form.updateAttach, {}, formData, true).then(result => {
        console.log("%c [upload img result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)
        if (result && result?.length > 0) {
            result_img.status = 'done'
            result_img.message = 'Upload completed'
            if (type === 1) {
                fileList.value.push({ id, base64, formattedName, fileName: name, fileId: result[0].fileId, ext: result[0].ext })
                console.log("%c [ --fileList.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileList.value)
                if (base64.includes('image')) {
                    imgList.value.push(base64)
                    store.setImgList([...imgList.value])
                }
                console.log("%c [ imgList.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", imgList.value)
                store.setFileList([...fileList.value])

                fileIds.value.push({ id: result[0].fileId, name })
                store.setFileIds_([...fileIds.value])
                console.log("%c [ fileIds.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileIds.value)
                const newFileIds = fileIds.value.map(item => item.id).join(',')
                CertificateOrOtherAttachment.value = newFileIds
                CertificateOrOtherAttachment_.value = ' ' //'3 '
                store.CertificateOrOtherAttachment = newFileIds
                console.log("%c [ 证书/其他 type === 1 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", CertificateOrOtherAttachment.value)
            } else {
                fileList_2.value.push({ id, base64, formattedName, fileName: name, fileId: result[0].fileId, ext: result[0].ext })
                console.log("%c [ --fileList_2.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileList_2.value)
                if (base64.includes('image')) {
                    imgList_2.value.push(base64)
                    store.setImgList_2([...imgList_2.value])
                }
                store.setFileList_2([...fileList_2.value])
                fileIds_2.value.push({ id: result[0].fileId, name })
                store.setFileIds_2([...fileIds_2.value])
                const newFileIds2 = fileIds_2.value.map(item => item.id).join(',')
                OperatorAttachment.value = newFileIds2
                OperatorAttachment_.value = ' '
                store.OperatorAttachment = newFileIds2
                console.log("%c [ 证书/卡 type === 2 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", OperatorAttachment.value)
            }
        } else {
            result_img.status = 'failed';
            result_img.message = 'Upload failed';
        }
    }).catch(error => {
        result_img.status = 'failed';
        result_img.message = 'Upload failed';
        console.error('error', error)
    })

}
const CompressPictures = (resultFile, imgName, type, name) => {
    console.log("%c [ 压缩前 原图 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", resultFile)
    new Compressor(resultFile, {
        quality: 0.5,
        async success(result_img) {
            console.log("%c [ 压缩后的图片 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result_img)
            console.log("%c [ imgName ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", imgName)
            if (!result_img.name) {
                result_img.name = imgName?.replace(/\.(heic|heif)$/i, '.jpeg')
            }
            // uploadFileApi(result_img, type, '', name)
        },
        error(err) {
            console.log('图片压缩失败', err.message); //第一个参数必须是图像 File 或 Blob 对象。
        },
    });
}
function formatFileName(fileName) {
    const [name, ext] = fileName.split(".");
    if (name.length > 4) {
        return `${name.slice(0, 4)}...${name.slice(-3)}.${ext}`;
    } else {
        return fileName; // 文件名小于 4 位时保持原样
    }
}
const fileUpload = async (event, type) => {
    // event.stopPropagation();
    const targetList = type === 1 ? fileList.value : fileList_2.value
    let file = type === 1 ? document.getElementById('UploadPictures') : document.getElementById('UploadPictures2');
    let fileName = file.value; // 绝对路径 == >>>  C:\fakepath\01.png
    // console.log("%c [ fileName ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", fileName)
    let files = file.files;
    if (fileName == null || fileName == "") {
        console.log('空文件 1')
        showNotify({ message: useLang('UAUC.select_file_tips'), type: 'danger' })
    } else {
        let fileType = fileName.substr(fileName.length - 4, fileName.length);
        let uploadFile = files[0]
        if (fileType) {
            if (uploadFile) {
                const formattedName = formatFileName(uploadFile.name)
                console.log("%c [ formattedName ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", formattedName)
                // 预览图片
                let reader = new FileReader();
                if (fileType === "heic" || fileType === "heif") {
                    reader.onload = function (e) {
                        const heicBlob = e.target.result;
                        console.log("%c [ heicBlob ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", heicBlob)
                        // 使用 heic2any 将 HEIC HEIF 转换为 PNG
                        heic2any({ // 1.先转格式
                            blob: new Blob([heicBlob], { type: uploadFile.type || 'image/heic' }),
                            toType: "image/png"
                        }).then(function (resultBlob) { // resultBlob: { size: 20662230, type: "image/png"}
                            if (uploadFile.size > MAX_SIZE.value) { // 2.压缩
                                console.log("%c [ heif 压缩 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
                                CompressPictures(resultBlob, formattedName, type, uploadFile.name) //未使用
                            }
                            // 生成图片的 URL 并展示预览
                            const url = URL.createObjectURL(resultBlob); // blob:http://localhost:5173/c3f1043c-f3a4-4dc7-8d98-e6be198fe0e0
                            targetList.push({ id: (new Date()).valueOf(), base64: url, name: formattedName, fileName: uploadFile.name }) //e.target.result  base64图片  uploadFile.name
                            if (type === 1) {
                                imgList.value.push(url)
                            } else {
                                imgList_2.value.push(url)
                            }
                        }).catch(function (error) {
                            console.error("Error converting HEIC to PNG:", error);
                        });
                        console.log("%c [ targetList 1111]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", targetList)
                    }
                    reader.readAsArrayBuffer(uploadFile);
                } else {
                    reader.onload = function (e) {
                        uploadFileApi(uploadFile, type, e.target.result, uploadFile.name, formattedName, new Date().valueOf())
                    }
                    reader.readAsDataURL(uploadFile);


                    // if (uploadFile.size > MAX_SIZE.value) { // > 2MB的先压缩
                    //     console.log("%c [ 2 jpeg, png 压缩 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
                    //     CompressPictures(uploadFile, formattedName, type, uploadFile.name) // uploadFile.name,
                    // } else {                                // 小于2MB的直接上传 api 接口
                    //     reader.onload = function (e) {
                    //         targetList.push({ id: (new Date()).valueOf(), base64: e.target.result, name: formattedName }) // uploadFile.name,
                    //         console.log("%c [ 1 --- e.target.result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", e.target.result)
                    //         uploadFileApi(uploadFile, type, e.target.result, uploadFile.name)
                    //         if (type === 1) {
                    //             imgList.value.push(e.target.result)
                    //         } else {
                    //             imgList_2.value.push(e.target.result)
                    //         }
                    //     }
                    //     reader.readAsDataURL(uploadFile);
                    // }

                }
            } else {
                console.log('空文件 2')
                showNotify({ message: useLang('UAUC.select_file_tips'), type: 'danger' })
            }
        } else {
            showNotify({ message: useLang('UAUC.WrongFileTypeUploaded'), type: 'danger' })
        }
    }
}
const fileListShow = ref(false)
const fileListShow2 = ref(false)
const previewFile = (n, item) => {
    router.push({
        path: '/fileRead',
        query: { n, type: item.ext?.replace('.', ''), fileId: item.fileId, formattedName: item.formattedName, next: 'ssiRegister', query: JSON.stringify(route.query) },
        replace: true
    })
}
const ProcessedeleteImage = (id, type, base64, name) => {
    const targetList = type === 1 ? fileList.value : fileList_2.value
    for (let i = 0; i < targetList.length; i += 1) {
        if (targetList[i].id === id) {
            targetList.splice(i, 1);
            break;
        }
    }
    const imgList__ = type === 1 ? imgList.value : imgList_2.value
    imgList__.forEach((item, index) => {
        imgList__.splice(index, 1)
    })
    if (type === 1) {
        store.setFileList([...targetList])
        store.setImgList([...imgList.value])
        fileIds.value = fileIds.value.filter(item => item.name !== name)
        store.setFileIds_([...fileIds.value])
        const newFileIds = fileIds.value.map(item => item.id).join(',')
        console.log("%c [ type 1 newFileIds ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newFileIds)
        CertificateOrOtherAttachment.value = newFileIds
        if (CertificateOrOtherAttachment.value === '') {
            CertificateOrOtherAttachment_.value = ''
        }
    } else {
        store.setFileList_2([...targetList])
        store.setImgList_2([...imgList_2.value])
        fileIds_2.value = fileIds_2.value.filter(item => item.name !== name)
        store.setFileIds_2([...fileIds_2.value])
        const newFileIds = fileIds_2.value.map(item => item.id).join(',')
        OperatorAttachment.value = newFileIds
        if (OperatorAttachment.value === '') {
            OperatorAttachment_.value = ''
        }
    }
}
const isShowPrompt = ref(false)
const ShowPrompt = () => {
    isShowPrompt.value = true
}


const onSubmit = async () => {
    try {
        const params = {
            "ProjectNo": ProjectNo.value,
            "ProjectName": ProjectName.value,
            "TemplateID": TemplateID.value, //"646985155559493",
            "TemplateName": TemplateID_.value,
            "EntryDate": EntryDate.value, //"2025-02-27 00:00:00",
            "Contractor": Contractor.value, //"Linde group",
            "ContractorCode": ContractorCode.value, //"LD01",
            "Manufacture": Manufacture.value, //"2222",
            "SerialNo": SerialNo.value, //"2222",
            "InspectionRequired": true,
            "EquipmentStatus": "0",
            "EquipmentStatusValue": "",
            "ClientTimeZone": ClientTimeZone.value, //时区
            "ApplicableCalibrationExpiryDate": ApplicableCalibrationExpiryDate.value,
            "CalibrationExpiryDate": CalibrationExpiryDate.value,
            "CertificateOrOtherAttachment": CertificateOrOtherAttachment.value,  // 附件 1
            "ApplicableOperatorName": ApplicableOperatorName.value,
            "OperatorAccount": OperatorAccount.value,
            "OperatorName": OperatorName.value,
            "OperatorAttachment": OperatorAttachment.value,  // 附件 2
            "ApplicableModel": ApplicableModel.value,
            "Model": Model.value,
            "ApplicablePlateNo": ApplicablePlateNo.value,
            "PlateNo": PlateNo.value,
            "ApplicableSWLOrCapacity": ApplicableSWLOrCapacity.value,
            "SWLOrCapacity": SWLOrCapacity.value,
            "ApplicableSizeOrDiameter": ApplicableSizeOrDiameter.value,
            "SizeOrDiameter": SizeOrDiameter.value,
            "ApplicableLength": ApplicableLength.value,
            "Length": Length.value,
            "Remarks": Remarks.value,
        }
        Loading.value = true
        try {
            const sub_res = await SSI_Submit(params)
            console.log("%c [ SSI_Submit_res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", sub_res)
            if (sub_res.data.success) {
                Loading.value = false
                showNotify({ message: useLang('UAUC.SubmissionSuccessful'), type: 'success' })
                router.back()
            } else {
                Loading.value = false
                showNotify({ message: useLang('UAUC.SubmissionFailed'), type: 'danger' })
            }
        } catch (error) {
            Loading.value = false
            showNotify({ message: useLang('UAUC.SubmissionFailed'), type: 'danger' })
            console.log("%c [ Submit_SSI_error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
        }
        console.log("%c [ Submit --- params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
    } catch (error) {
        Loading.value = false
        console.log("%c [ OwnerPosition error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
    }
}
const pickerLoading = ref(false)
const POST_EquipmentClassification = async () => { //设备工具分类
    const formdata = useFormData({
        f: 'e30=',
        c: 'WyJJdGVtSWQiLCJOYW1lIl0=', //["ItemId","Name"]
        o: ''
    })
    pickerLoading.value = true
    try {
        const data = await usePostBody('bpm/datasource/527365695868997/table/BDS_EquipmentTemplate', {}, formdata)
        columns.value = data?.map(item => ({
            text: item.Name,
            value: item.ItemId
        })) || []
        pickerLoading.value = false
    } catch (error) {
        pickerLoading.value = false
        console.log("设备工具分类---error", error)
    }
}
onMounted(async () => {
    console.log("%c [ --route.query.fid-- ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", route.query.fid)
    const { user } = await useGetQuery(url.login.getUserInfo);
    DisplayName.value = user?.DisplayName

    if (route.query?.fid) {
        store.projectData.projectName = ''
        store.projectData.projectNo = ''
        store.projectData.supplierName = ''
        store.projectData.supplierCode = ''
        store.TemplateID = ''
        store.TemplateID_ = ''
        store.Manufacture = ''
        store.Model = ''
        store.PlateNo = ''
        store.SWLOrCapacity = ''
        store.SizeOrDiameter = ''
        store.Length = ''
        store.Remarks = ''
        store.SerialNo = ''
        store.ApplicableCalibrationExpiryDate = true
        store.ApplicableOperatorName = true
        store.ApplicableModel = true
        store.ApplicablePlateNo = true
        store.ApplicableSWLOrCapacity = true
        store.ApplicableSizeOrDiameter = true
        store.ApplicableLength = true
        store.CalibrationExpiryDate = ''
        store.CertificateOrOtherAttachment = '' // 附件1
        store.OperatorName = ''
        store.OperatorAccount = ''
        store.OperatorAttachment = '' // 附件2
        store.setFileList([])
        store.setImgList([])
        store.setFileList_2([])
        store.setImgList_2([])
        store.setFileIds_([])
        store.setFileIds_2([])

        ProjectName.value = ''
        ProjectNo.value = ''
        Contractor.value = ''
        TemplateID_.value = ''
        TemplateID.value = ''
        Manufacture.value = ''
        SerialNo.value = ''
        CalibrationExpiryDate.value = ''
        OperatorName.value = ''
        OperatorAccount.value = ''
        Model.value = ''
        PlateNo.value = ''
        SWLOrCapacity.value = ''
        SizeOrDiameter.value = ''
        Length.value = ''
        Remarks.value = ''
        CertificateOrOtherAttachment.value = ''
        OperatorAttachment.value = ''
        CertificateOrOtherAttachment_.value = ''
        OperatorAttachment_.value = ''
        fileList.value = []
        fileList_2.value = []
        imgList.value = []
        imgList_2.value = []
    }

    POST_EquipmentClassification()
    if (store.projectData) {     //项目 \ 办公室名称
        console.log("%c [ store.projectData ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", store.projectData)
        ProjectName.value = store.projectData?.projectName
        ProjectNo.value = store.projectData?.projectNo
        Contractor.value = store.projectData?.supplierName
        ContractorCode.value = store.projectData?.supplierCode
    }
    if (store.OperatorName) {
        OperatorName.value = store.OperatorName
        OperatorAccount.value = store.OperatorAccount
    }
})
onUnmounted(() => {

})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #f9f9f9;
    box-sizing: border-box;

    .fave {
        // background-color: var(--yz-div-background);
    }

    .cell {
        margin-bottom: 3px;
    }

    .foot {
        width: 100%;
        box-sizing: border-box;
        padding: 16px;
        position: fixed;
        bottom: 0;
        z-index: 99;
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ellipsis2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all; //断词
}



::v-deep(.van-cell__title) {
    color: #828282;
}

::v-deep(.van-field__control) {
    color: #00A0E1 !important;
}

::v-deep(.van-field__control:disabled) {
    color: #00a0e1 !important;
    -webkit-text-fill-color: #00a0e1 !important;
}

::v-deep(.van-field__control--error, .van-field__control--error::placeholder) {
    -webkit-text-fill-color: #00a0e1 !important;
}

::v-deep(.van-field__control--error) {
    color: #00A0E1;
}

::v-deep(.van-icon-search) {
    color: #00A0E1;
}

::v-deep(.van-field__label) {
    width: 110px !important;
}

::v-deep(.OperatorName) {
    padding: 9px 6px 9px 9px !important;
}

::v-deep(.OperatorName .van-field__label) {
    width: 90px !important;
}

::v-deep(.CalibrationExpiryDate .van-field__label) {
    width: 85px !important;
    margin-left: -8px;
}

::v-deep(.line_ .van-field__label) {
    width: 250px !important;
}

.msgIcon {
    position: absolute;
    left: 137px;
    top: 12px;
}

.ShowPromptMsg {
    padding: 40px 25px;
}


.add_upload {
    .icon_ {
        color: #00a0e1;
        width: 20px;
        height: 20px;
        position: absolute;
        top: 12px;
        right: 15px;
    }
}

.add_upload .add_upload_icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.add_upload .add_upload_file {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    font-size: 0;
}

.add_upload_imgBox {
    padding: 10px 20px;
}

.add_upload_imgBox .add_upload_imgDiv {
    max-width: 250px;
    height: 1.5rem;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.add_upload_imgBox .add_upload_imgDiv section img {
    width: 1.5rem;
    height: 1.5rem;
}

.add_upload_imgBox .add_upload_close {
    position: absolute;
    right: 33px;
    width: 15px;
    height: 15px;
    margin-bottom: 15px;
}

.add_upload_imgBox .add_upload_close img {
    width: 100%;
    height: 100%;
}
</style>