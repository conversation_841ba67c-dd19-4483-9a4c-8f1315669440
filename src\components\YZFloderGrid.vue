<template>
    <!-- <van-grid-item @click="handlerGridClick(process)">
        <div class="grid-item">
            <div class="grid-icon"
                :style="{ background: (process.isProcess ? (process.IconColor ?? '#5dc663') : '#db6541') }">
                <div
                    :class="['icon-content ' + (process.isProcess ? process.IconCls ?? 'iconfont icon-zhidu' : 'fa fa-folder')]">
                </div>
            </div>
            <span class="grid-text">
                {{ process.text }}
            </span>
        </div>
    </van-grid-item> -->
    <van-grid-item @click="handlerGridClick(process)">
        <div class="grid-item">
            <div class="grid-icon">
                <img :src="process.src" alt="" style="width: 30px; height: 35px" class="iconfont">
            </div>

            <!-- {
    "expanded": false,
    "isProcess": true,
    "fid": 646064554467397,
    "text": "SSI",
    "leaf": false,
    "path": 646064554467397,
    "parentId": "572592747761733",
    "ProcessVersion": null,
    "IsCollection": false,
    "src": 'xxxxxx',
    "type": "流程"
} -->
            <span class="grid-text">
                {{ process.text }}
            </span>
        </div>
    </van-grid-item>
</template>

<script lang="ts" setup>
import { IProcessInfo, ITreeProcessInfo } from '@/logic/home/<USER>';
import { PropType } from 'vue';

defineProps({
    process: {
        type: Object as PropType<ITreeProcessInfo>,
        required: true,
        default: {}
    }
})
const emits = defineEmits<{
    onGridClick: [val: ITreeProcessInfo]
}>()
const handlerGridClick = (process: ITreeProcessInfo) => {
    console.log("%c [ handlerGridClick ---  process ] -2", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", process)
    let fid = process.fid
    if (!fid) {
        process.fid = process.Id
    }
    emits('onGridClick', process)

}
</script>
<style lang="scss" scoped>
.grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.grid-icon {
    // width: 40px;
    // height: 40px;
    width: 30px;
    height: 30px;
    border-radius: 3px;
    text-align: center;

    // .icon-content {
    //     width: 18px;
    //     height: 18px;
    //     margin: 10px auto;
    //     color: #ffffff;
    //     line-height: 18px;
    //     font-size: var(--yz-home-gridicon);
    // }
}

.grid-text {
    font-family: PingFangSC-Regular;
    font-size: var(--yz-home-gridtext);
    color: var(--yz-text-666);
    text-align: center;
    // line-height: 16px;
    font-weight: 400;
    margin-top: 7px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
}
</style>