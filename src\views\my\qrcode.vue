<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                我的二维码
            </template>
        </YZNav>

        <div class="code">
            <div class="code-box">
                <div class="code-head">
                    <div class="code-head-left">
                        <img :src="userImage" alt="">
                    </div>
                    <div class="code-head-right">
                        <div class="code-head-right-name">{{ user.getUserInfo.DisplayName }}</div>
                        <div class="code-head-right-com">公司：Linde</div>
                        <div class="code-head-right-email">邮箱：{{ user.getUserInfo.EMail }}</div>
                    </div>
                </div>
                <div class="code-img">
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from "@/store/users";
const user = useUserStore()

let host = process.env.NODE_ENV === 'development' ? window.webConfig.devhost : window.webConfig.host
const userImage = computed(() => host + user.getUserInfo?.PortraitUrl)

onMounted(() => {
    console.log(user.getUserInfo);
})
</script>

<style lang="scss" scoped>
.page{
    background-color: var(--yz-layout-mian);
    min-height: 100vh;
    width: 100%;

    .code {
            padding: 0 24px;
            margin-top: 100px;
    
            .code-box {
                padding: 24px;
                background: #FFFFFF;
                box-shadow: 0px 0px 8px 1px rgba(215, 213, 213, 0.25);
                border-radius: 10px;
    
                .code-head {
                    display: flex;
                    align-items: center;
    
                    .code-head-left {
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                        margin-right: 10px;
                        border: 1px solid #ddd;
                        overflow: hidden;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
    
                    .code-head-right {
                        flex: 1;
    
                        .code-head-right-name {
                            font-size: 14px;
                            font-weight: 400;
                            color: #444444;
                        }
    
                        .code-head-right-com,
                        .code-head-right-email {
                            font-size: 10px;
                            font-weight: 400;
                            color: #777777;
                            margin-top: 5px;
                        }
                    }
                }
    
                .code-img {
                    margin: 45px auto 0;
                    border: 1px solid #ddd;
                    width: 260px;
                    height: 260px;
                }
            }
        }
}
</style>