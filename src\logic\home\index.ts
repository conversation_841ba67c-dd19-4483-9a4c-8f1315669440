import { url } from "@/api/url"
import { useGetQuery, usePostBody } from "@/utils/request"
import { onMounted, reactive, ref, watch } from "vue"
import { ILibiaryInfo, IProcessInfo, ITreeProcessInfo } from "./homeModel"
import { useHomeStore } from "@/store/home"
import { eventBus } from "@/utils/eventBus"
import { useLang, useParamsFormatter, useFormData } from "@/utils"
import { showNotify } from "vant"
import { useProcessStore } from "@/store/process"
import i18n from '@/locale/index'
import { useRoute, useRouter } from 'vue-router'
import Cookies from 'js-cookie';
import UAUC3X from '@/assets/images/UAUC3X.png'
import Ebooks3X from '@/assets/images/E-books3X.png'
import CSTC3X from '@/assets/images/CSTC3X.png'
import SSI3X from '@/assets/images/SSI3X.png'
interface IBardInfo {
    text: string;
    id: string;
}
function useHome() {
    const route = useRoute()
    const router = useRouter()
    const LibiaryData = ref<Array<ILibiaryInfo>>([])
    const process = ref<Array<IProcessInfo>>([])
    const treeProcess = ref<Array<ITreeProcessInfo>>([])
    const chActiveTab = ref<number>(0)
    const collActives = ref<Array<string>>([''])
    const collActive = ref<Array<string>>(['1', '2'])
    const actionShow = ref<boolean>(false)
    const actions = ref<any>([])
    const planProcess = ref<Array<ITreeProcessInfo>>([])
    const selectObj: Record<string, string> = reactive({
        IsCollection: '',
        processId: '',
        processName: '',
        processVersion: '',
    })
    const homeStore = useHomeStore()
    const porcessStore = useProcessStore()
    const searchKwd = ref<string>('')
    const procesShow = ref<boolean>(false)
    const time = ref<number>(0)
    const defaultPath = ref<IBardInfo[]>([
        { text: i18n.global.t('home.All'), id: '' }
    ])
    const historyData = ref<Array<IProcessInfo>>([])
    const allFolderData = ref<Array<ITreeProcessInfo>>([])
    const allProcessData = ref<Array<ITreeProcessInfo>>([])

    onMounted(() => {
        if (route.query?.type == 'all') { //Linde cf modify 
            addColl()
        }
        sessionStorage.removeItem('appName')
        sessionStorage.removeItem('appId')

        // getUserType()

        rootDirectory() // HSE 根目录 cf modify 暂时隐藏
        myCollection()  // 我的收藏
        // initCount()  //获取待办数量 + 待阅，需要隐藏
        recentProcess() // 最近流程
        initCount()     //待阅数量
    })

    const getUserType = async () => {

        const SubjectType = new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'AAD' : 'B2C'
        localStorage.setItem('SubjectType', SubjectType)
        // const cookies = Cookies.get('PPA.Auth.Cookies')
        // Cookies.set('auth_cookie', cookies, { expires: 30 })
        // console.log("%c [ ~_~ cookies ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", cookies)
        // window.addEventListener('beforeunload', () => {
        //     const currentCookie = Cookies.get('PPA.Auth.Cookies') //('.AspNetCore.Cookies')
        //     if (currentCookie) {
        //         Cookies.set('auth_cookie', currentCookie, { expires: 30 })
        //     }
        // })        
    }
    const recentProcess = async () => {
        if (location.hostname === 'localhost') {
            const res_data = [
                {
                    "Id": 604160195670085,
                    "ProcessName": "UAUC",
                    "IsCollection": false,
                    "IconCls": "iconfont icon-zhidu",
                    "IconColor": "#5dc663",
                    "MobileInitiation": true,
                    "ShortName": ""
                }
            ]
            historyData.value = res_data as Array<IProcessInfo>
        } else {
            const { data } = await useGetQuery(url.home.getTopList) // 最近流程接口 portal/process/commonuse
            if (data)
                historyData.value = data as Array<IProcessInfo>
        }
    }
    const rootDirectory = async () => {
        treeProcess.value = []
        planProcess.value = []
        if (location.hostname === 'localhost') { // local 模式
            const localChildren = [
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 604160195670085,
                    "text": "UAUC",
                    "leaf": false,
                    "path": 604160195670085,
                    "parentId": "572592747761733",
                    "ProcessVersion": "1.0",
                    "IsCollection": true,
                    "src": UAUC3X, //'/src/views/home/<USER>/UAUC3X.png',
                    "type": "流程"
                },
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 606565585551429,
                    "text": "E-books",
                    "leaf": false,
                    "path": 606565585551429,
                    "parentId": "572592747761733",
                    "ProcessVersion": null,
                    "IsCollection": false,
                    "src": Ebooks3X, //'/src/views/home/<USER>/E-books3X.png',
                    "type": "流程"
                },
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 606632759345221,
                    "text": "CSTC",
                    "leaf": false,
                    "path": 606632759345221,
                    "parentId": "572592747761733",
                    "ProcessVersion": null,
                    "IsCollection": false,
                    "src": CSTC3X, //'/src/views/home/<USER>/CSTC3X.png',
                    "type": "流程"
                },
                // 本地的 SSI
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 646064554467397,
                    "text": "SSI",
                    "leaf": false,
                    "path": 646064554467397,
                    "parentId": "572592747761733",
                    "ProcessVersion": null,
                    "IsCollection": false,
                    "src": SSI3X, //'/src/views/home/<USER>/SSI3X.png',
                    "type": "流程"
                }
            ]
            planProcess.value = resovlePlan(localChildren)
            console.log("%c [ 01 本地 planProcess.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", planProcess.value)
            treeProcess.value = localChildren;
        } else {
            const onlineChildren = [ //线上
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 604160195670085,
                    "text": "UAUC",
                    "leaf": false,
                    "path": 604160195670085,
                    "parentId": "572592747761733",
                    "ProcessVersion": "1.0",
                    "IsCollection": true,
                    "src": UAUC3X,
                    "type": "流程"
                },
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 606565585551429,
                    "text": "E-books",
                    "leaf": false,
                    "path": 606565585551429,
                    "parentId": "572592747761733",
                    "ProcessVersion": null,
                    "IsCollection": false,
                    "src": Ebooks3X,
                    "type": "流程"
                },
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 606632759345221,
                    "text": "CSTC",
                    "leaf": false,
                    "path": 606632759345221,
                    "parentId": "572592747761733",
                    "ProcessVersion": null,
                    "IsCollection": false,
                    "src": CSTC3X,
                    "type": "流程"
                },
                // 线上 SSI open
                {
                    "expanded": false,
                    "isProcess": true,
                    "fid": 646064554467397,
                    "text": "SSI",
                    "leaf": false,
                    "path": 646064554467397,
                    "parentId": "572592747761733",
                    "ProcessVersion": null,
                    "IsCollection": false,
                    "src": SSI3X,
                    "type": "流程"
                }
            ]
            planProcess.value = resovlePlan(onlineChildren)
            treeProcess.value = onlineChildren;
            // getTreeFloder接口: {
            //     "success": true,
            //     "errorMessage": "",
            //     "children": [
            //         {
            //             "text": "B2C",
            //             "expanded": true,
            //             "leaf": false,
            //             "fid": "527396994728005",
            //             "path": "527396994728005",
            //             "children": [],
            //             "eico": "UtWozvWv.3b0"
            //         },
            //         {
            //             "text": "HSE",
            //             "expanded": true,
            //             "leaf": false,
            //             "fid": "572592747761733",
            //             "path": "572592747761733",
            //             "children": [],
            //             "eico": "BBbDtWfn.ae4"
            //         },
            //         {
            //             "text": "PE",
            //             "expanded": true,
            //             "leaf": false,
            //             "fid": "566649109291077",
            //             "path": "566649109291077",
            //             "children": [],
            //             "eico": "XCtYMaPE.79c"
            //         },
            //         {
            //             "text": "Procurement",
            //             "expanded": true,
            //             "leaf": false,
            //             "fid": "572594636484677",
            //             "path": "572594636484677",
            //             "children": [],
            //             "eico": "b84jBde6.717"
            //         }
            //     ]
            // }
            // const { success, children } = await useGetQuery(url.home.getTreeFloder, {//根目录 "HSE", "Construction Change Management", "Procurement"
            //     perm: 'Execute',
            //     expand: true,
            //     node: 'root'
            // })
            // if (success) {
            //     planProcess.value = resovlePlan(children)
            // }
            // HSE 相关的顶级目录
            // treeProcess.value = children
            // console.log("%c [ rootDirectory HSE ] -1", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", children)
        }
    }
    // 获取流程库
    const GetProcessLibrary = async () => {
        const libraries = await useGetQuery(url.home.getlibiaryProcess, {
            kwd: searchKwd.value
        })
        LibiaryData.value = []
        collActives.value = []
        treeProcess.value = []
        if (libraries && libraries.data.length > 0) {
            libraries.data.forEach((element: any) => {
                collActives.value.push(element.FolderName)
                LibiaryData.value.push({
                    id: '',
                    libRootFolderId: '',
                    text: element.FolderName,
                    glyph: 0,
                    startApp: '',
                    children: element.Processes
                })
                if (searchKwd.value) {
                    if (element.Processes && element.Processes.length > 0) {
                        element.Processes.forEach((x: any) => {
                            treeProcess.value.push({
                                text: x.ShortName || x.ProcessName,
                                isProcess: true,
                                ...x
                            })
                            //YZ + text: x.ShortName || x.ProcessName, 
                        })
                    }
                } else {
                    rootDirectory()
                }

            });
        }
    }
    // 首页---我的收藏 
    const myCollection = async () => {
        process.value = []
        const { data } = await useGetQuery(url.home.getMyCollection)
        if (data) {
            // data: [{
            //     "Id": 604160195670085,
            //     "ProcessName": "UAUC",
            //     "IsCollection": true,
            //     "IconCls": "c-m-icon fa fa-check-square-o",
            //     "IconColor": "#5dc663",
            //     "MobileInitiation": true,
            //     "ShortName": "UAUC",
            //     "IconImg": "CsU8fW4e.13c"
            // }]
            data.forEach((item: any) => {
                process.value?.push({
                    ProcessName: item.ShortName || item.ProcessName, //ProcessName: "UAUC", ShortName: "UAUC"
                    IconCls: item.IconCls,
                    IsCollection: item.IsCollection,
                    IconColor: item.IconColor,
                    OrderIndex: 0,
                    Id: item.Id,
                    ProcessVersion: item.ProcessVersion
                })
            })
        }
    }
    const initCount = async () => {
        // const { total } = await useGetQuery(url.work.getWorkListCount)
        // console.log("%c [ 获取待办数量 2 total ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", total)
        const { total: ReadCount } = await useGetQuery(url.work.getWorkReadCount)
        console.log("%c [ 首页获取待阅 数量 ReadCount ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", ReadCount)
        // homeStore.setAllCount(total + ReadCount)
        homeStore.setReadCount(ReadCount)
        // homeStore.setWorkCount(total)
    }
    // 平面数据转换
    const resovlePlan = (data: any) => {
        const list: ITreeProcessInfo[] = []
        data.forEach((item: any) => {
            if (item.children && item.children.length > 0) {
                let newChild = item.children.map((el: any) => {
                    return { ...el, parentId: item.fid }
                })
                item.children = newChild
                list.push(item)
                list.push(...resovlePlan(item.children))

            } else {
                list.push(item)
            }
        })
        return list
    }
    const addColl = () => {
        chActiveTab.value = 1
    }
    eventBus.on('reloadColl', {
        cb: function (parasm: any) {
            GetProcessLibrary()
            myCollection()
        }
    })
    const clearSelectObj = () => {
        selectObj['IsCollection'] = ''
        selectObj['processId'] = ''
        selectObj['processName'] = ''
        selectObj['processVersion'] = ''
    }
    const onLongPress = (el: any) => { //长按图标显示添加收藏
        // const process = el.getAttribute('isprocess') as string
        // // if (process) { //YZ need hide 
        // if (process !== '文件夹') { //Linde cf modify
        //     actionShow.value = true
        //     const val = el.getAttribute('atttrue') as string
        //     selectObj['IsCollection'] = val
        //     selectObj['processId'] = el.getAttribute('attid') as string
        //     selectObj['processName'] = el.getAttribute('proname') as string
        //     selectObj['processVersion'] = el.getAttribute('prover') as string
        //     actions.value = []
        //     if (val === 'true') {
        //         actions.value.push({ name: useLang('home.Remove_from_favorites') })
        //     } else {
        //         actions.value.push({ name: useLang('home.Add_to_favorites') })
        //     }
        // }

    }
    const onCancel = () => {
        actionShow.value = false
    }
    const onSelect = async () => {
        console.log("%c [ onSelect ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
        const v = selectObj['IsCollection']
        const id = String(selectObj['processId'])
        let valueUrl = (selectObj['IsCollection'] === 'true' ? url.home.cancleCollection : url.home.addCollection)
        const newUrl = useParamsFormatter(valueUrl, {
            params1: selectObj['processId']
        })
        const { success } = await usePostBody(newUrl, {}, selectObj)
        if (success) {
            showNotify({ type: 'success', message: useLang('home.OperationSuccessful') })
            clearSelectObj()
            GetProcessLibrary()
            myCollection()
            if (v === 'true') {
                // 取消收藏
                const findex = allProcessData.value.findIndex(x => String(x.fid) === id)
                if (findex > -1) {
                    allProcessData.value[findex].IsCollection = false
                }
                const sindex = treeProcess.value.findIndex(x => String(x.fid) === id)
                if (sindex > -1)
                    treeProcess.value[sindex].IsCollection = false
            } else {
                //增加收藏
                const findex = allProcessData.value.findIndex(x => String(x.fid) === id)
                if (findex > -1) {
                    allProcessData.value[findex].IsCollection = true
                }
                const sindex = treeProcess.value.findIndex(x => String(x.fid) === id)
                if (sindex > -1)
                    treeProcess.value[sindex].IsCollection = true
            }
        } else {
            showNotify({ type: 'danger', message: useLang('home.OperationFailed') })
        }
    }
    const onSearch = async () => {
        chActiveTab.value = 1
        console.log(' onSearch 顶部搜索 === >>> ')
        await GetProcessLibrary() //Linde cf modify 获取流程库
        //YZ + 👇
        LibiaryData.value = []
        collActives.value = []
        treeProcess.value = []
        if (searchKwd.value) {
            let newUrl = useParamsFormatter(url.home.getLindeProcess, {
                params1: -1
            })
            const searchData = await useGetQuery(newUrl, {
                kwd: searchKwd.value,
                searchType: "QuickSearch"
            })
            if (searchData && searchData.children.length > 0) {
                searchData.children.forEach((element: any) => {

                    treeProcess.value.push({
                        text: element.ShortName || element.ProcessName,
                        isProcess: true,
                        ...element
                    })
                });
            }
        } else {
            GetProcessLibrary()
        }

        defaultPath.value = [
            { text: i18n.global.t('home.All'), id: '' }
        ]
        //YZ 
    }
    const onOpenForm = (process: IProcessInfo) => { //打开收藏里的内容
        console.log("%c [ 收藏 进入 --->>> process ]", "font-size:13px; background:linear-gradient(to right , #9B63FF,rgb(76, 47, 126)); color:yellow;", process)
        // porcessStore.setActionType(1)
        // porcessStore.setProcessId(process.Id)
        // time.value = new Date().getTime()
        // procesShow.value = true
        if (process.Id === 604160195670085) { //收藏里 去uauc
            router.push({
                path: '/uauc',
                query: {
                    fid: process.Id
                }
            })
        } else {
            if (process.Id === 606565585551429) {
                router.push('/ebook')
            } else if (process.Id === 606632759345221) {
                router.push('/cstc')
            } else {
                // router.push('/ssi')  // todo 收藏里 去ssi
            }
        }
    }
    eventBus.on('onBack', {
        cb: function () {
            procesShow.value = false
            homeStore.setTab(true)
        }
    })
    const onFloderClick = async ({ text, fid, isProcess }: ITreeProcessInfo) => {
        try {
            let HSE = [
                { fid: "572593109565509", text: "e-Books", path: "/ebook" },
                { fid: "604160195670085", text: "UAUC", path: "/uauc" },
                { fid: "572593166979141", text: "CSTC", path: "/cstc" },
                { fid: "646064554467397", text: "SSI", path: "/ssi" }
            ]
            // local本地跳转
            let findItem = HSE.find(x => x.text == text || x.fid == fid)
            if (findItem && location.hostname === 'localhost') {
                console.log("%c [local本地跳转 findItem ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", findItem) //之前改好的代码，现在这段代码又没用了
                if (fid === 604160195670085) { // 去UAUC
                    porcessStore.setActionType(1)
                    porcessStore.setProcessId(fid)
                    time.value = new Date().getTime()
                    procesShow.value = true;
                }
                // if (fid === 572593109565509) {
                //     router.push('/ebook')     //去ebook
                // }
                // if (fid === 572593166979141) {
                //     router.push('/cstc')      //去cstc
                // }
                //  return router.push(findItem.path) 
            }

            // 线上
            if (!isProcess) {
                // 加载面包屑
                treeProcess.value = []
                const fx = defaultPath.value.findIndex(x => x.text === text && x.id === fid) //YZ + && x.id === fid
                if (fx < 0) {
                    defaultPath.value.push({
                        text,
                        id: fid
                    })
                }
                //点击顶层文件夹
                treeProcess.value = childfolder(fid)
                console.log("%c [ 2 treeProcess.value  ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", treeProcess.value)
                //加载 uauc,ebooks,cstc,SSI 的子项
                const process = await loadTreeProcess(fid) // '/bpm/lindeprocess/folder/572592747761733/processes’
                const e_books = {
                    "Id": 606565585551429,
                    "ProcessName": "E-books",
                    "ProcessVersion": null,
                    "ProcessDesc": null,
                    "FolderId": 572592747761733,
                    "OrderIndex": 2,
                    "IconCls": "c-m-icon fa fa-file-pdf-o",
                    "IconColor": "#000000",
                    "RSID": "",
                    "ShortName": null,
                    "MobileInitiation": true,
                    "DefaultMobileForm": null,
                    "MobileColor": null,
                    "IsCollection": false,
                    "IconImg": null,
                    "Type": "MiniApp"
                }
                const ebooks = {
                    IsCollection: true,
                    ProcessVersion: "1.0",
                    expanded: false,
                    fid: 572593109565509, //ebooks //新 606565585551429
                    isProcess: true,
                    leaf: false,
                    parentId: "572592747761733",
                    path: 572593109565509,          //  606565585551429
                    text: "e-Books",
                    type: "流程"
                }
                const cstc = {
                    IsCollection: true,
                    ProcessVersion: "1.0",
                    expanded: false,
                    fid: 572593166979141, //cstc //新 606632759345221
                    isProcess: true,
                    leaf: false,
                    parentId: "572592747761733", //   606632759345221
                    path: 572593166979141,
                    text: "CSTC",
                    type: "流程"
                }
                if (process && process.length > 0) {
                    treeProcess.value.push(...process)
                    console.log("%c [ treeProcess.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", treeProcess.value)
                }
            } else {
                //点击流程
                if (fid === 604160195670085) { //去uauc  
                    router.push({
                        path: '/uauc',
                        query: {
                            fid: fid
                        }
                    })
                    // porcessStore.setActionType(1)
                    // porcessStore.setProcessId(fid)
                    // time.value = new Date().getTime()
                    // procesShow.value = true
                } else {
                    if (fid === 606565585551429) {
                        router.push('/ebook') //去ebook
                    } else if (fid === 606632759345221) {
                        router.push('/cstc') //去cstc
                    } else {
                        router.push({
                            path: '/ssi',
                            query: {
                                fid: fid
                            }
                        })
                        //去ssi
                        console.log("%c [ go-ssI ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;",)
                    }
                }
            }
        } catch (error) {
            console.log("%c [ 流程 --- error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
        }
    }
    const childfolder = (id: string) => {
        return planProcess.value.filter(x => x.parentId === id)
    }
    const loadTreeProcess = async (id: string) => {
        const list: any = []
        const newUrl = useParamsFormatter(url.home.getLindeProcess, {
            params1: id
        })
        allFolderData.value = []
        const { children, success } = await useGetQuery(newUrl)
        if (success && children.length > 0) {
            children.forEach((item: any) => {
                if (item.MobileInitiation) {
                    const json: ITreeProcessInfo = {
                        expanded: false,
                        isProcess: true,
                        fid: item.Id,
                        text: item.ShortName || item.ProcessName,
                        leaf: false,
                        path: item.Id,
                        parentId: id,
                        ProcessVersion: item.ProcessVersion,
                        IsCollection: item.IsCollection,
                        type: '流程'
                    }
                    list.push(json)
                    const index = allProcessData.value.findIndex(x => x.fid === item.Id)
                    if (index < 0)
                        allProcessData.value.push(json)
                }
            })
        }
        return list
    }
    const breadClick = async ({ id, text }: IBardInfo) => {
        if (id) {
            treeProcess.value = []
            const floder = planProcess.value.filter(x => x.parentId === id)
            //加载流程
            const process = allProcessData.value.filter(x => x.parentId === id)
            floder.push(...process)
            treeProcess.value = floder
            buildbread(id)
        } else {
            //全部
            defaultPath.value = [
                { text: i18n.global.t('home.All'), id: '' }
            ]
            await rootDirectory()
        }
    }
    const buildbread = (id: string) => {
        const findex = defaultPath.value.findIndex(x => x.id === id)
        if (findex > -1) {
            defaultPath.value.splice(findex + 1)
        }

    }
    return {
        process,
        LibiaryData,
        chActiveTab,
        collActives,
        collActive,
        addColl,
        actionShow,
        onLongPress,
        actions,
        onSelect,
        onCancel,
        searchKwd,
        onSearch,
        onOpenForm,
        procesShow,
        time,
        historyData,
        treeProcess,
        onFloderClick,
        defaultPath,
        breadClick
    }
}
export {
    useHome
}

