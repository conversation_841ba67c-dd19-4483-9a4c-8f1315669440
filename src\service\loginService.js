import axios from 'axios'
import router from '@/router';
import { useLoingStore } from '@/store/login';
import store from '@/store';
import { useCore } from '@/store/core';
import { showLoadingToast, showNotify } from 'vant';
import Cookies from 'js-cookie';
const loginStore = useLoingStore(store)
const coreStore = useCore(store)


let baseURL = process.env.NODE_ENV === 'development' ? window.webConfig.devhost : window.webConfig.host //window.webConfig.devhost == /api/ ||  window.webConfig.host == ./
// let baseURL = process.env.NODE_ENV === 'development' ? '/api/' : './'

const requestService = axios.create({
	baseURL,
	timeout: 60 * 1000,
	withCredentials: true,
})

requestService.interceptors.request.use(
	(config) => {
		config.headers['Access-Control-Allow-Origin'] = '*'
		config.headers['Authorization'] = "Bearer " + loginStore.getToken
		config.headers['Access-Control-Allow-Credentials'] = true
		config.headers['Accept-Language'] = coreStore.$lang == 'en' ? 'en-US' : coreStore.$lang
		// const savedCookie = Cookies.get('auth_cookie')
		const savedCookie = Cookies.get('auth_cookie')
		if (savedCookie) {
			config.headers['Cookie'] = '.AspNetCore.Cookies=' + savedCookie
			// config.headers['Cookie'] = 'PPA.Auth.Cookies=' + savedCookie
		}
		return config
	},
	(error) => {
		Promise.reject(error)
	}
)

requestService.interceptors.response.use(result => {
	switch (result?.status) {
		case 200:
			if (result.data && result.data.errorCode === 101) {
				localStorage.clear()
				loginStore.restart()
				setTimeout(() => {
					router.push({ path: '/login', replace: true })
				}, 1000)

			} if (result.data.success === false) {
				// showNotify({ message: result.data.errorMessage })
				console.log('error-2', result.data.errorMessage)
				return result
			} else {
				return result
			}
		case 401:
			setTimeout(() => {
				router.push({ path: '/login', replace: true })
			}, 1000)
			break;
		case 501:
			break;
	}
	return result
},
	(error) => {
		console.log("%c [ error ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", error)
		const reuslt = error.response
		if (reuslt?.status === 401) {
			showNotify({ message: '身份认证过期,请重新登录,系统将会在2s后跳转登录' })
			setTimeout(() => {
				loginStore.restart()
				sessionStorage.clear()
				window.location.reload()
			}, 2000)
		} else if (reuslt?.status === 500) {
			return reuslt
		} else if (reuslt?.status === 404) {
			const data = {
				code: 1,
				errorMessage: '接口未找到',
				success: undefined
			}
			reuslt.data = data
			return reuslt
		} else if (reuslt?.status === 400) {
			const data = {
				code: 1,
				errorMessage: '接口入参格式校验失败',
				success: undefined
			}
			reuslt.data = data
			return reuslt
		} else {
			return reuslt
		}
	}
)

export default requestService