<template>
    <div class="">
        <van-nav-bar :title="title" :left-text="$t('Global.Back')" left-arrow @click-left="onClickLeft()" />
        <renderForm ref="renderForm" :taskId="props.reformRead.taskId" :loadType="props.reformRead.loadType"
            :formService="formService" :noShowHeader="noShowHeader" />
    </div>
</template>
<script lang="ts" setup>
const emit = defineEmits(['onBack'])
import { useProcessStore } from "@/store/process";
import { useCore } from '@/store/core/index'
const props = defineProps({
    reformRead: {
        type: Object,
        default: {}
    },
    formService: {
        type: Object,
        default: {}
    },
    title: {
        type: String,
        default: '表单'
    },
    // 外部传入，不要展示header,true不展示；false展示
    noShowHeader: {
        type: Boolean,
        default: false
    }
})

import { useRoute, useRouter } from 'vue-router';
const route = useRoute()
const router = useRouter()
const processStore = useProcessStore()
const core = useCore()
// const renderFormRef = ref<any>(null)
let query = route.query
import { eventBus } from '@/utils/eventBus';
const onClickLeft = async (params?: any) => {
    let taskId = props.formService.formId
    // await core.delFieldsRecordByTid(taskId)
    await core.delFieldsRecordArrByLast()
    emit('onBack', params)
    eventBus.off(`onBackChildForm`)
}
eventBus.on(`onBackChildForm`, {
    cb: function (params) {
        if (!params) {
            onClickLeft()
        } else if (Object.keys(props.formService).length > 0 && props.formService.uuid === params.uuid) {
            eventBus.off(`childFormSave${props.formService.uuid}`)
            onClickLeft(params)
        }

    }
})
// 如果页面突然刷新而且还会丢失需要不隐藏的头部
window.addEventListener('beforeunload', function (event) {
    processStore.setHideTabHeader(false)
});
// const openTaskForm = () => {
//     renderFormRef.value.openTaskForm()
// }
// defineExpose({
//     openTaskForm
// })
</script>
<style lang="">

</style>