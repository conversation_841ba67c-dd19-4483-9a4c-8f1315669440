import { useLang } from "@/utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON>tyle } from "./formModel";
import dayjs from "dayjs";
export class YZDatePickerConfig extends YZConfig {
    private dateShow: boolean;
    private minDate: Date;
    private maxDate: Date;
    private columnType: string[]
    private formatterType: string
    private tabs: string[]
    private nextText: string;
    private pickType: string;
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZDatePicker'
        this.dateShow = false
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        const year: number = new Date().getFullYear()
        this.minDate = new Date(year - 50, 0, 1)
        this.maxDate = new Date(year + 10, 12, 31)
        this.columnType = []
        const type = parmas['extends'].type
        this.formatterType = ''
        this.tabs = ['选择日期']
        this.nextText = '确定'
        this.pickType = type
        //  const value =parmas['__vModel__'].modelValue
        switch (type) {
            case 'Ym':
                this.columnType = ['year', 'month']
                this.formatterType = 'YYYY/MM'
                this.tabs = ['选择日期']
                break;
            case 'Ymd':
                this.columnType = ['year', 'month', 'day']
                this.formatterType = 'YYYY/MM/DD'
                this.tabs = ['选择日期']
                break;
            case 'YmdHi':
                this.tabs = ['选择日期', '选择时间']
                this.formatterType = 'YYYY/MM/DD HH:mm'
                this.nextText = '下一步'
                this.columnType = ['year', 'month', 'day']
                break;
            case 'Hi':
                this.tabs = ['选择时间']
                this.formatterType = 'HH:mm'
                this.columnType = ['year', 'month', 'day']
                break;
            default:
                this.columnType = ['year', 'month', 'day']
                this.formatterType = 'YYYY/MM/DD'
                break
        }
        // if (value) {
        //     parmas['__vModel__'].modelValue =  dayjs(value,this.formatterType) 
        // }
    }
    getShow() {
        return this.dateShow
    }
    setShow(show: boolean) {
        this.dateShow = show
    }
    minDateValue(): Date {
        return this.minDate
    }
    maxDateValue(): Date {
        return this.maxDate
    }
    getFormatter(): any[] {
        return this.columnType
    }
    getFormatterType(): string {
        return this.formatterType
    }
    getTabs(): string[] {
        return this.tabs
    }
    getNextText(): string {
        return this.nextText
    }
    getType(): string {
        return this.pickType
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZDatePickerStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZDatePicker extends YZField {
    private configData?: YZDatePickerConfig;
    constructor(params: any) {
        super(params)
        this.readonly = params['__config__']['readonly']
        const configItem = this.config as YZDatePickerConfig
        if (configItem) {
            let type = configItem.getFormatterType()
            const value = params['__vModel__'].modelValue
            if (value) {
                if (type.indexOf('-') > -1)
                    type = type.replaceAll('-', '/')
                const newvalue = dayjs(value, type)
                const values = newvalue.format(type).toString()
                params['__vModel__'].modelValue = values
                params['__vModel__'].optionValue = values
            }
        }
        this.vModel.value = params['__vModel__'].modelValue
        this.vModel.optionValue = params['__vModel__'].optionValue
    }
    initConfig(config: any): YZConfig {
        this.configData = new YZDatePickerConfig(config)
        return this.configData
    }
    initStyle(style: any): YZStyle<object> {
        return new YZDatePickerStyle(style)
    }
    getConfig() {
        return this.configData
    }
}
