<template>
    <div>
        <van-popup v-model:show="isShow" teleport="body" position="bottom" :style="{ height: '100%' }">

            <van-nav-bar title="选择部门" :left-text="$t('Global.Cancel')" :right-text="$t('Global.Confirm')" left-arrow
                @click-right="rightClick" @click-left="leftClick" />
            <div>
                <!-- <van-search v-model="searchkey" placeholder="查询部门" @search="onSearch" @clear="onClear"
                        style="padding: 0px; width:95%;margin:0px auto" /> -->
            </div>
            <div class="path-line">
                <div class="li-div" v-for="item in pathArrys" :key="item.name" @click="handlerPath(item)">
                    {{ item.name }}
                    <van-icon name="arrow" style="margin-left: -13px;" v-if="item.icon" />
                </div>
            </div>
            <div class="select-user" v-if="selectArrys.length > 0">
                <div class="puser" v-for="(org, index) in selectArrys" :key="index">
                    {{ org.name }}
                    <span @click.stop="onRemove(org.ouid)">
                        <van-icon name="cross" class="badge-icon" />
                    </span>
                </div>
            </div>
            <!-- 部门多选 -->
            <van-checkbox-group v-model="checkBoxSelect" v-if="multiple" @change="onCheckChange">
                <van-cell-group>
                    <div>
                        <van-cell v-for="item in orgItems" :key="item.text">
                            <div class="depart-cell">
                                <van-checkbox :name="item.data?.getOuID()">
                                    {{ item.data?.getName() }}
                                </van-checkbox>
                            </div>
                            <div class="depart-down" @click="departChange(item.data)">
                                查看子部门
                            </div>
                        </van-cell>
                    </div>
                </van-cell-group>
            </van-checkbox-group>
            <!-- 部门单选 -->

            <van-radio-group @change="onRadioChange" v-model="radioSelect" v-else>
                <van-cell-group>
                    <div>
                        <van-cell v-for="item in orgItems" :key="item.text">
                            <div class="depart-cell">
                                <van-radio :name="item.data?.getOuID()">
                                    {{ item.data?.getName() }}
                                </van-radio>
                            </div>
                            <div class="depart-down" @click="departChange(item.data)">
                                查看子部门
                            </div>
                        </van-cell>
                    </div>
                </van-cell-group>
            </van-radio-group>
            <!-- 空数据 -->
            <van-empty description="无子部门" v-show="orgItems.length <= 0" />
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { useGetQuery } from '@/utils/request';
import { ref, computed, onMounted } from 'vue';
import { url } from '@/api/url';
import { OrgModel, OrgModelData } from '@/logic/forms/formModel';
import { useParamsFormatter } from '@/utils';
import { watchEffect } from 'vue';
import { ProcessService } from '@/logic/forms/processHelper';
import i18n from '@/locale/index'
export interface IBarPath {
    name: string;
    ouid: string;
    icon: boolean;
    providerName: string;
    code: string;
    level: string,
    sid: string
}
export interface ISelectOrg {
    name: string;
    code: string;
    ouid: string
}
const props = defineProps({
    show: {
        type: Boolean,
        required: true,
        default: true
    },
    multiple: {
        type: Boolean,
        required: false,
        default: false
    },
    defaultOrgs: {
        type: Array<ISelectOrg>,
        required: false,
        default: []
    }
})
const isShow = computed(() => props.show)
const emit = defineEmits(['update:show', 'onSave'])
const leftClick = () => {
    checkBoxSelect.value = []
    pathArrys.value = []
    radioSelect.value = ''
    emit('update:show', false)

    initOrgTree()
}
const rightClick = () => {
    if (!props.multiple) {
        getOrgMap(selectArrys.value)
    } else emit('onSave', selectArrys.value)
    checkBoxSelect.value = []
    radioSelect.value = ''

    initOrgTree()
    emit('update:show', false)
}
const getOrgMap = async (selectArrys: any[]) => {
    const newUrl = useParamsFormatter(url.org.getOrgMapData, {
        params1: selectArrys[0].ouid,
    })
    const data = await useGetQuery(newUrl)
    emit('onSave', selectArrys, data)
}
const onCheckChange = (data: Array<string>) => {
    selectArrys.value = []
    if (data && data.length > 0) {
        data.forEach(val => {
            buildSelectArrys(val)
        })
    }
}
const onRadioChange = (data: string) => {
    selectArrys.value = []
    buildSelectArrys(data)
}
const buildSelectArrys = (ouid: string) => {
    const item = allOrgItems.value.find(x => x.data?.getOuID() === ouid)
    if (item) {
        const currentData = item.data?.getCurrentData() as OrgModelData
        selectArrys.value.push({
            name: currentData.Name,
            code: currentData.Code,
            ouid: currentData.OUID
        })
    }
}
const searchkey = ref<string>('')
const pathArrys = ref<Array<IBarPath>>([])
const orgItems = ref<Array<OrgModel>>([])
const radioSelect = ref<string>('')
const checkBoxSelect = ref<Array<string>>([])
const selectArrys = ref<Array<ISelectOrg>>([])
const allOrgItems = ref<Array<OrgModel>>([])
const defaultPath = [{ name: i18n.global.t('home.All'), ouid: 'all', icon: false, providerName: '', code: '' }]
onMounted(async () => {
    initOrgTree()
})
const initOrgTree = async () => {
    const data = await useGetQuery(url.org.getOrgs, {}, false)
    builderOrg(data)
    pathArrys.value = [{ name: i18n.global.t('home.All'), ouid: 'all', icon: false, providerName: '', code: '', sid: '', level: '' }]
}
const departChange = async (modelData: OrgModelData | any) => {
    const model = modelData as OrgModelData
    const newurl = useParamsFormatter(url.org.getOrgChildren, {
        params1: model.providerName,
        params2: model.OUID
    })
    const data = await useGetQuery(newurl)
    builderOrg(data)
    buildPath({ name: model.Name, icon: false, ouid: model.OUID, providerName: model.providerName, code: model.Code, sid: model.SID, level: model.Level })
}
const builderOrg = (children: Array<any>) => {
    orgItems.value = []
    if (children && children.length > 0) {
        children.forEach((item: any) => {
            const data = new OrgModel(item, 'ou')
            orgItems.value.push(data)
        })
    }
    allOrgItems.value.push(...orgItems.value)
}
const handlerPath = (model: IBarPath) => {
    if (model.ouid === 'all') {

        initOrgTree()
        pathArrys.value = [{ name: i18n.global.t('home.All'), ouid: 'all', icon: false, providerName: '', code: '', sid: '', level: '' }]
    } else {
        departChange({
            Code: model.code,
            SID: model.sid,
            Name: model.name,
            providerName: model.providerName,
            OUID: model.ouid,
            Level: model.level
        })
    }
}
const buildPath = (model: IBarPath) => {
    const item = pathArrys.value.find(x => x.name === model.name)
    const newArrys: IBarPath[] = []
    if (item) {
        for (let i = 0; i < pathArrys.value.length; i++) {
            if (pathArrys.value[i].name === model.name) {
                newArrys.push({

                    icon: false,
                    name: model.name,
                    ouid: model.ouid,
                    code: model.code,
                    level: model.level,
                    sid: model.sid,
                    providerName: model.providerName
                })
                break;
            } else {
                newArrys.push({
                    icon: true,
                    name: pathArrys.value[i].name,
                    ouid: pathArrys.value[i].ouid,
                    code: pathArrys.value[i].code,
                    level: pathArrys.value[i].level,
                    sid: pathArrys.value[i].sid,
                    providerName: pathArrys.value[i].providerName
                })
            }
        }
        pathArrys.value = newArrys

    } else {
        pathArrys.value.forEach(item => {
            item.icon = true
        })
        pathArrys.value.push(model)
    }
}
const onSearch = async () => {
    const { children } = await useGetQuery(url.org.searchUser, {
        keyword: searchkey.value
    })
    builderOrg(children)
}
const onClear = () => {

    initOrgTree()
}
const onRemove = (org: string): void => {
    const findex = selectArrys.value.findIndex(x => x.ouid === org)
    if (findex > -1)
        selectArrys.value.splice(findex, 1)
    const checkIndex = checkBoxSelect.value.findIndex(x => x === org)
    if (checkIndex > -1)
        checkBoxSelect.value.splice(checkIndex, 1)
    if (org === radioSelect.value)
        radioSelect.value = ''
}
watchEffect(() => {
    if (props.defaultOrgs.length > 0) {
        if (props.multiple) {
            // 多选 
            checkBoxSelect.value = props.defaultOrgs.map(x => x.ouid)
        } else {
            // 单选
            radioSelect.value = props.defaultOrgs[0].ouid
        }
    }
})
</script>
<style lang="scss" scoped>
.path-line {
    height: 30px;
    width: 100%;
    background: #efefef;
    margin-top: 5px;
    line-height: 30px;
    text-indent: 0.4em;
    font-size: var(--yz-com-13);
    color: #918c8c;

    .li-div {
        float: left;
    }
}

.item {
    height: 40px;
    position: relative;

    .item-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: var(--yz-com-22);
        border-radius: 4px;
        overflow: hidden;
        margin-right: 20px;
        float: left;
        color: #76dbb4;

        :deep .van-icon__image {
            width: 100% !important;
            height: 100% !important;
        }

    }

    .item-cont {
        height: 100%;
        overflow: hidden;

        .check {
            width: 20px;
            height: 20px;
            float: right;
            text-align: center;
            line-height: 20px;
            color: #12bc2b;
        }
    }

    .folder-icon {
        float: left;
        margin-right: 13px;
        margin-top: 5px;
        font-size: 30px;
    }


}

.item-cont:before {
    content: "";
    width: 0;
    font-size: 0;
    height: 100%;
    visibility: hidden;
    display: inline-block;
    vertical-align: middle;
}

.item-cont-inner {
    width: 100%;
    display: inline-block;
    vertical-align: middle;
    font-size: var(--yz-com-16);
    color: var(--van-field-label-color);
    text-align: left;
}

.select-user {
    width: 100%;
    overflow-y: scroll;
    padding: 10px 0px;

    .puser {
        padding: 3px;
        height: 30px;
        border: 1px dashed #efefef;
        margin: 5px 5px;
        float: left;
        line-height: 30px;
        font-size: var(--yz-com-12);
        position: relative;

        span {
            width: 20px;
            height: 20px;
            position: absolute;
            top: -7px;
            right: -10px;
            display: block;
            text-align: center;
            background-color: #ff2727;
            border-radius: 50%;
            line-height: 20px;
            color: #fff;
        }
    }
}

.depart-cell {
    width: 70%;
    float: left;
}

.depart-down {
    width: 30%;
    float: left;
    height: 100%;
    text-align: right;
    color: #bcb9b9;
}
</style>