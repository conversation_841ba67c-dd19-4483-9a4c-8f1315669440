// 内置控件，且只有在审批的时候才会渲染

import { YZConfig, YZField, YZStyle } from "./formModel";
import { useLang } from "@/utils";
export class YZTaskIdConfig extends YZConfig {
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZTaskId'
        if (this.required && !this.rules) {
            this.rules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        }
    }
}
export class YZTaskIdStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZTaskId extends YZField {
    constructor(params: any) {
        super(params)
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZTaskIdConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZTaskIdStyle(style)
    }
}