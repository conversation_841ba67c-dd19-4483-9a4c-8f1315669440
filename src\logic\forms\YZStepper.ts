import { Y<PERSON><PERSON><PERSON>fi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
import { useLang } from "@/utils";
export class YZStepperConfig extends YZConfig {
    public thousands: boolean;
    public digt: number;
    public currency: string;
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZStepper'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]

        // console.log(this.required && this.writable,this.label)
        if (this.required && this.writable) {
            this.rules = this.defaultRules
            // console.log(this.label,this.rules) 
        }
        this.thousands = parmas.thousands
        this.digt = parmas.extends['digit']
        this.currency = parmas.extends['currency']
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZ<PERSON>tepperStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
            tst: '111'
        }
    }
}
export class YZStepper extends YZField {
    private step: number;
    constructor(params: any) {
        super(params)
        this.vModel.value = params['__config__'].defaultValue
        if (params['__vModel__'].modelValue != null || params['__vModel__'].modelValue != '' || params['__vModel__'].modelValue != undefined) {
            this.vModel.value = params['__vModel__'].modelValue
        }
        this.vModel.valueType = 'number'
        this.step = params['step']
        this.readonly = params['__config__'].extends.readOnly ?? params.readonly
    }
    initConfig(config: any): YZConfig {
        return new YZStepperConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZStepperStyle(style)
    }
    getStep(): number {

        return this.step
    }

}
