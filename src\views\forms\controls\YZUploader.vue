<template>
    <div>
        <p style="display: none;">
            {{ vModel }}
        </p>
        <van-field v-if="!field.hidden" ref="upRef" :name="field.vModel.model" :required="field.config.required"
            :rules="field.config.rules" :readonly="field.disabled" :label="field.config.label" v-model="optionData">
            <template #input>
                <!-- 如果不是禁用，表示还可以上传图片 -->
                <div>
                    <div class="file-div" @click="onUploadClick(item)" v-for="(item, index) in imageSrc" :key="index">
                        {{ item.item.fileName }}
                        <div class="remove-btn" @click.stop="onRemoveClick(item, index)" v-if="!field.readonly">删除</div>
                    </div>
                    <van-button icon="plus" type="primary" size="small" @click="onUploadBtn"
                        :disabled="field.disabled">上传文件</van-button>
                    <van-uploader ref="fpRef" style="display: none;" :max-size="isOverSize" accept="image/*"
                        v-model="vModel" :multiple="field.getMultipe()" :after-read="afterRead"
                        :before-read="beforeRead">
                        <p> <van-button icon="plus" type="primary" size="mini">上传文件</van-button> </p>
                    </van-uploader>
                    <van-uploader ref="fpFileRef" style="display: none;" :max-size="isOverSize" v-model="vModel"
                        accept=".pdf,.doc,.docx,.xls,.xlsx" :multiple="field.getMultipe()" :after-read="afterRead"
                        :before-read="beforeRead">
                        <p> <van-button icon="plus" type="primary" size="mini">上传文件</van-button> </p>
                    </van-uploader>
                </div>
            </template>
        </van-field>
        <van-image-preview v-model:show="previewShow" :images="imageUrlSrc">
            <template #image="{ src }">
                <img :src="src" style="width: 100%;" />
            </template>
        </van-image-preview>
        <!-- 文件上传 -->
        <van-action-sheet v-model:show="actionShow" :actions="actionData" cancel-text="取消" close-on-click-action
            @cancel="onActionCancel" @select="onActionSelect" />
    </div>
</template>

<script lang="ts" setup>
import { url } from '@/api/url';
import { YZUploader, YZUploaderConfig } from '@/logic/forms/YZUploader';
import { YZField } from '@/logic/forms/formModel';
import { useLoingStore } from '@/store/login';
import { useProcessStore } from '@/store/process';
import { useUserStore } from '@/store/users';
import { useEnviroMent } from '@/utils';
import { eventBus } from '@/utils/eventBus';
import { usePostBody } from '@/utils/request';
import dd from 'dingtalk-jsapi';
import { showNotify, UploaderFileListItem } from 'vant';
import { computed, onMounted, PropType, ref, watch, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
export interface IFileData {
    name: string;
    size: number;
    fileId: string;
}
const instance = getCurrentInstance()
const actionShow = ref<boolean>(false)
const props = defineProps({
    field: {
        type: Object as PropType<YZUploader>,
        required: true
    },
    modelValue: {
        type: Array<UploaderFileListItem>,
        default: []
    },
    optionValue: {
        type: [String],
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const actionData = ref<any>([
    { name: 'Select picture' },
    { name: 'Select file' }
])
const emit = defineEmits(['update:modelValue', 'update:optionValue'])
const yzconfig = props.field.config as YZUploaderConfig
const size = yzconfig.getUnit()
const sizeMax = yzconfig.getSize()
const imageSrc = ref<Array<any>>([])
const loginStore = useLoingStore()
const userStore = useUserStore()
const route = useRoute()
const token = loginStore.getToken
const previewShow = ref<boolean>(false)
const imageUrlSrc = ref<Array<string>>([])
const fpRef = ref<any>(null)
const fpFileRef = ref<any>(null)
const upRef = ref<any>(null)
//    accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
const accpetData = ref<string>('image/*')
defineExpose({
    imageSrc
})
const vModel = computed({
    get() {
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        if (typeof props.modelValue === 'string')
            return []
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const setFun = () => {
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const onUploadBtn = () => {
    actionShow.value = true
}
const optionData = computed({
    get() {
        return props.optionValue
    },
    set(val) {

        emit('update:optionValue', val)
    }
})
const isOverSize = (file: File): boolean => {
    let showValue = ''
    if (size === 'MB') {
        const value = file.size / 1024 / 1024
        const mbValue = Math.round((value) * 100) / 100
        if (sizeMax >= mbValue) {
            return false
        }
        showValue = mbValue + size
    } else if (size === 'GB') {
        const value = file.size / 1024 / 1024 / 1024
        const mbValue = Math.round((value) * 100) / 100
        if (sizeMax >= mbValue) {
            return false
        }
        showValue = mbValue + size
    } else if (size === "KB") {
        const value = file.size / 1024
        const mbValue = Math.round((value) * 100) / 100
        if (sizeMax >= mbValue) {
            return false
        }
        showValue = mbValue + size
    }
    showNotify({
        type: 'danger',
        message: `上传的文件大小超出限制，限制【${sizeMax}${size}】，实际文件【${showValue}】`
    })
    return true

}
const onActionCancel = () => {
    actionShow.value = false
}
const onActionSelect = (type: any) => {
    if (fpRef && fpFileRef) {
        if (type.name === 'Select picture') {
            fpRef.value.chooseFile()
        } else {
            fpFileRef.value.chooseFile()
        }
    }
}
const fileData: Array<IFileData> = []
const afterRead = (file: any): any => {
    const formData = new FormData()
    file.status = 'uploading';
    file.message = '上传中...';
    formData.append('file', file.file)
    props.field.setIsUpload(true)
    usePostBody(url.form.updateAttach, {}, formData, true)
        .then(result => {
            if (result && result.length > 0) {
                result.forEach((item: any) => {
                    file.status = 'done'
                    file.message = '上传完成'
                    const id = item.fileId
                    builderOption(id)
                    builderFileData(file.file.name, file.file.size, id, item.ext)
                })
            } else {
                file.status = 'failed';
                file.message = '上传失败';
            }
            props.field.setIsUpload(false)
        }).catch(error => {
            file.status = 'failed';
            file.message = '上传失败';
            console.error('error', error)
            props.field.setIsUpload(false)
        })
}

const beforeRead = (file: any): any => {
    const item = fileData.find(x => x.name === file.name && x.size === file.size)
    if (item) {
        showNotify({
            type: 'danger',
            message: '请勿重复上传相同的文件'
        })
        return false
    }
    let fileFileType = yzconfig.getFileTypes()
    if (fileFileType && fileFileType != '*.*') {
        //  直接取了名字后缀名，文件类型
        let fileType = file.name.split('.')[file.name.split('.').length - 1]
        // 字符串类似 *.png;*.jpg 的字符串
        let fileFileTypeArr = fileFileType.replaceAll("*.", '').split(';').filter(item => item)
        if (!fileFileTypeArr.includes(fileType)) {
            showNotify({
                type: 'danger',
                message: `因文件类型不符，禁止上传附件：${file.name}此处仅允许上传以下后缀的附件：${fileFileType}`
            })
            return false
        }
    }
    return true
}
const builderOption = (fileId: string) => {
    const option = optionData.value
    if (option) {
        const value = option.split(',')
        value.push(fileId)
        const newFileId = value.join(',')
        emit('update:optionValue', newFileId)

    } else {
        emit('update:optionValue', fileId)
    }
    if (!props.field.disabled)
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
}
const builderFileData = (name: string, size: number, fileId: string, ext: string): void => {
    const json = {
        name: name,
        size: size,
        fileId: fileId,
        ext
    }
    fileData.push(json)
    const srcUrl = `bpm/attachment/file/${fileId}?access_token=${token}`
    const newJson = {
        item: {
            fileId,
            fileName: name,
        },
        url: srcUrl,
        ext
    }
    imageSrc.value.push(newJson)
    const events = new CustomEvent<any>("afterUpload", {
        detail: {
            $refs: instance,
            data: props.field
        }
    })
    document.dispatchEvent(events)
}
const processStore = useProcessStore()
const isFirstLoad = ref<boolean>(true)
onMounted(async () => {
    isFirstLoad.value = false
    const params = await processStore.getAttachmentInfo(props.field.vModel.optionValue)
    if (params && params.length > 0) {
        for (let i = 0; i < params.length; i++) {
            const id = params[i].fileId
            const srcUrl = `bpm/attachment/file/${id}?access_token=${token}`
            const newJson = {
                item: params[i],
                url: srcUrl,
                ext: params[i].ext
            }
            imageSrc.value.push(newJson)
        }
        emit('update:modelValue', imageSrc.value)
        setFun()
    }
})
const onRemoveClick = (img: any, index: number) => {
    const rmItem = img.item
    const findex = imageSrc.value.findIndex(x => x.item.fileId === rmItem.fileId)
    if (findex >= 0)
        imageSrc.value.splice(findex, 1)
    //更新实际数据
    const str = imageSrc.value.map(x => x.item.fileId).join(',')
    // 删除重复文件
    const spIndex = fileData.findIndex(x => x.fileId === img.item.fileId)
    if (spIndex > -1)
        fileData.splice(spIndex, 1)
    // 删除绑定值
    if (vModel.value) {
        const vmList = vModel.value
        const vmIndex = vmList.findIndex(x => x.file?.name === rmItem.fileName)
        if (vmIndex > -1) {
            vmList.splice(vmIndex, 1)
            emit('update:modelValue', vmList)
            setFun()
        }
    }
    emit('update:optionValue', str)
    if (!props.field.disabled)
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
}
const onUploadClick = (item: any) => {
    const ext: Array<string> = ['.png', '.jpg', '.jpeg']
    if (item && ext.indexOf(item.ext) > -1) {
        const url = item.url
        const uitem = imageUrlSrc.value.find(x => x === url)
        if (!uitem)
            imageUrlSrc.value.push(url)
        previewShow.value = true
    } else {
        const env = useEnviroMent()
        const host = window.location.origin
        let downloadUrl = host + '/' + item.url
        if (env.isH5) {
            window.open(downloadUrl)
        } else if (env.isComWeChat) {
            window.wx.previewFile({
                url: downloadUrl,
                name: item.item.fileName,
                size: item.item.size
            });
        } else if (env.isDingTalk) {
            const appKeyName = loginStore.getAppName
            const token = loginStore.getToken
            const user = userStore.getAccount
            // const downurl = `${window.location.origin}/mobile/download.html?token=${token}&key=${appKeyName}&account=${user}&id=${item.item.fileId}`
            dd.biz.util.openLink({
                url: downloadUrl
            })
        } else if (env.isUniApp) {
            // 
            const filePath = downloadUrl
            window.open(filePath)
        } else if (env.isFeiShu) {
            const filePath = downloadUrl
            window.open(filePath)
        }
    }
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = yzconfig.getDefaultRule()
            }
        }
    }
})
// 监听数据MAP
eventBus.on('onMap', { //YZ +
    cb: async function (params) {
        console.log("%c [ params 10]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
            // props.field.vModel.value = params.value
            const file = await processStore.getAttachmentInfo(params.value)
            imageSrc.value = []
            if (file && file.length > 0) {
                for (let i = 0; i < file.length; i++) {
                    const id = file[i].fileId
                    const srcUrl = `bpm/attachment/file/${id}?access_token=${token}`
                    const newJson = {
                        item: file[i],
                        url: srcUrl,
                        ext: file[i].ext
                    }
                    imageSrc.value.push(newJson)
                }
                emit('update:modelValue', imageSrc.value)
                optionData.value = params.value
                setFun()
            }
        }
    }
})
</script>
<style lang="scss">
.form-item-div {
    .van-uploader__input-wrapper {
        width: 100% !important;
    }
}

.file-div {
    width: 100%;
    text-align: left;
    position: relative;

    .remove-btn {
        margin-left: 15px;
        color: red;
        display: inline-block;
    }
}
</style>