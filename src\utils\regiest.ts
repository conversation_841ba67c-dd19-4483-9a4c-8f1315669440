import { App, defineAsyncComponent, defineComponent } from "vue";
import renderFormVue from "@/views/forms/renderForm.vue";
export function regiest(app: App) {
   app.component("renderForm",renderFormVue)
   const files = import.meta.glob('@/views/forms/controls/*.vue')
   loadFile(app, files, 'control')
   const myfiles = import.meta.glob('@/views/my/components/*.vue')
   loadFile(app, myfiles, 'my')
   const btnfiles = import.meta.glob('@/views/forms/btns/*.vue')
   loadFile(app, btnfiles, 'button')
   const topBars = import.meta.glob('@/views/work/components/*.vue')
   loadFile(app, topBars, "topBar")
}
const loadFile = async (app: App, files: any, type: string) => {
   // app.component("renderForm",renderFormVue)
   Object.keys(files).forEach(key => {
      const lastIndex = key.lastIndexOf('/')
      const firstValue = key.substring(lastIndex)
      const pointIndex = firstValue.indexOf('.')
      const componentName = firstValue.substring(1, pointIndex)
      let component: any = {}
      switch (type) {
         case 'control':
            component = defineAsyncComponent(() => import(`@/views/forms/controls/${componentName}.vue`))
            app.component(componentName, component)
            break;
         case 'my':
            component = defineAsyncComponent(() => import(`@/views/my/components/${componentName}.vue`))
            app.component(componentName, component)
            break;
         case 'button':
            component = defineAsyncComponent(() => import(`@/views/forms/btns/${componentName}.vue`))
            app.component(componentName, component)
            break;
         case 'topBar':
            component = defineAsyncComponent(() => import(`@/views/work/components/${componentName}.vue`))
            app.component(componentName, component)
            break;
      }
   })
}
// 加载自定义组件
export const useDymicVue=(Vue:any,Vant:any,app:App<Element>)=>{
   const options = {
      moduleCache: {
        vue: Vue,
        Vant
      },
      async getFile(url:any) {   
        const res = await fetch(url);
        if ( !res.ok )
          throw Object.assign(new Error(res.statusText + ' ' + url), { res });
        return {
          getContentData: (asBinary:any) => asBinary ? res.arrayBuffer() : res.text(),
        }
      },
      addStyle(textContent:any) {
        const style = Object.assign(document.createElement('style'), { textContent });
        const ref = document.head.getElementsByTagName('style')[0] || null;
        document.head.insertBefore(style, ref);
      },
    }
    // 加载动态组件
    const arrys = window.component
    const newWindows = window as any 
    const { loadModule } = newWindows['vue3-sfc-loader'];
    if (arrys && arrys.length>0) {
      for(let i=0;i<arrys.length;i++) {
         const item = arrys[i]
         const url = `${window.webConfig.dymicComponent}${item}.vue`
         app.component(item,defineAsyncComponent(()=>loadModule(url,options)))
      }
    }
}