
// 加载流程提交事件
export const init = async (processName: string) => {
  // if (processName) {
  //   const data = await fetch(`customerjs/${processName}.js?v=`+new Date().getTime())
  //   const text = await data.text()
  //   if(text && text.includes('<!DOCTYPE html>')) return null
  //   if (text) {
  //     const newJsString = text +' \r\n\r\n  return  funOptioin;'
  //     const func = new Function(newJsString);  
  //     return func();
  //   }
  //   return null
  // }
  // return null

  if(processName){
    try {
      const data = await fetch(`customerjs/${processName}.js?v=`+new Date().getTime())
      const text = await data.text()
      if(text && text.includes('<!DOCTYPE html>')) return null
      if(text) {
        const newJsString = text +' \r\n\r\n  return  funOptioin;'
        const func = new Function(newJsString);  
        return func();
      }
    } catch (error) {
      return null
    }
    return null
  }
  return null

}
// 加载高级验证
export const loadValidator =async (processName:string)=>{
  // if (processName) {
  //   const data = await fetch(`customerValidator/${processName}.js?v=`+new Date().getTime())
  //   const text = await data.text()
  //   if(text && text.includes('<!DOCTYPE html>')) return null
  //   if (text) {
  //     const newJsString = text +" \r\n\r\n return validator";
  //     const func = new Function(newJsString);  
  //     return func();
  //   }
  //   return null
  // }
  // return null

  // let returnValue = null
  if(processName){
    try {
      const data = await fetch(`customerValidator/${processName}.js?v=`+new Date().getTime())
      const text = await data.text()
      if(text && text.includes('<!DOCTYPE html>')) return null
      if(text) {
        const newJsString = text +' \r\n\r\n  return  validator;'
        const func = new Function(newJsString);  
        return func();
      }
    } catch (error) {
      return null
    }
  }
  return null
}
// 加载表单自定义函数
export const loadMobileCustomerFun =async (processName:string)=>{
  // if (processName) {
  //   const data = await fetch(`customerForm/${processName}.js?v=`+new Date().getTime())
  //   const text:any = await data.text()
  //   if(text && text.includes('<!DOCTYPE html>')) return null
  //   if (text) {
  //       const newJsString = text + " \r\n\r\n  return funs;"
  //       const func = new Function(newJsString);  
  //        return func();
  //   }
  //   return null
  // }
  // return null
  // let returnValue = null
  if(processName){
    try {
      const data = await fetch(`customerForm/${processName}.js?v=`+new Date().getTime())
      const text = await data.text()
      if(text && text.includes('<!DOCTYPE html>')) return null
      if(text) {
        const newJsString = text +' \r\n\r\n  return  funs;'
        const func = new Function(newJsString);  
        return func();
      }
    } catch (error) {
      return null;
    }
    return null
  }
  return null
}