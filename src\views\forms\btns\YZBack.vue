<template>
    <div>
        <van-button size="small" hairline type="default" :disabled="!btn.enable" icon="revoke" @click="backClick"
            style="height: 32px;padding: 0px 10px;">
            {{ btn.text }}
        </van-button>

        <van-dialog v-model:show="backShow" :title="$t('Form.Retrieve')" @confirm="onBackConfirm"
            :before-close="onBeforeClose" @cancel="onBackCancle" show-cancel-button>
            <div class="dialog-div">
                <van-field name="checkboxGroup" right-icon="search" v-model="backData" :label="$t('Form.GetBack')">
                    <template #input>
                        <van-radio-group v-model="returnBack" direction="horizontal">
                            <van-radio :name="step.StepID" v-for="step in stepData" :key="step.StepID">
                                {{ step.NodeName }}/({{ step.HandlerAccount }})
                            </van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field v-model="message" rows="1" autosize :label="$t('Form.Reason_for_retrieval')" type="textarea"
                    :placeholder="$t('Form.Reason_for_retrieval_tips')" />
            </div>
        </van-dialog>
    </div>
</template>

<script lang="ts" setup>
import { url } from '@/api/url';
import { IBtnModel } from '@/logic/forms/formModel';
import { useProcessStore } from '@/store/process';
import { useUserStore } from '@/store/users';
import { useLang, useParamsFormatter } from '@/utils';
import { eventBus } from '@/utils/eventBus';
import { useGetQuery, usePostBody } from '@/utils/request';
import { e } from 'mathjs';
import { showNotify } from 'vant';
import { PropType, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const backShow = ref<boolean>(false)
const message = ref<string>('')
const route = useRoute()
const userStore = useUserStore()
const router = useRouter()
const processStore = useProcessStore()
const backData = ref<string>()
const stepData = ref<Array<any>>([])
const returnBack = ref<string>('')
const onBackConfirm = async () => {
    if (!message.value) {
        showNotify({ message: useLang('Form.Reason_for_retrieval_tips') })
        return
    }
    if (!returnBack.value) {
        showNotify({ message: useLang('Form.Select_retrieve_tips') })
        return
    }
    const taskid = processStore.getProcLoad.taskId
    const stepid = returnBack.value
    const newUrl = useParamsFormatter(url.process.backToUser, {
        params1: taskid,
        params2: stepid
    })
    const data = await usePostBody(newUrl, {}, { comments: message.value })
    if (data.success) {
        showNotify({ message: useLang('Form.Retrieve_successfully_tips'), type: 'success' })
        onBackCancle()
        eventBus.emit('onBack', true)
    } else {
        showNotify({ message: data.message, type: 'danger' })
    }
}
const onBeforeClose = () => {
    return false
}
const onBackCancle = () => {
    backShow.value = false
    message.value = ''
}
const backClick = async () => {

    const newUrl = useParamsFormatter(url.process.backToNode, {
        params1: processStore.getProcLoad.taskId
    })
    const data = await useGetQuery(newUrl, {
        limit: 100,
        start: 0
    })
    if (data.success) {
        const { children } = data
        stepData.value = children
    } else {
        showNotify({ type: 'danger', message: useLang('Form.Get_retrieval_step_failed') })
    }
    backShow.value = true
}

</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}
</style>