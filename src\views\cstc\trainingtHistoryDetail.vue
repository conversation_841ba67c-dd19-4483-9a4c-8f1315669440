<template>
    <div class="page">
        <YZNav isBack>
            <template #title>
                {{ $t('CSTC.TrainingRecordDetails') }}
            </template>
        </YZNav>

        <div class="grap"></div>

        <div class="list">
            <div class="li">
                <div class="li-name">{{ $t('CSTC.Region') }}：</div>
                <div class="li-info">{{ item?.Region }}</div>
            </div>
            <div class="li">
                <div class="li-name" style="width:11rem;">{{ $t('CSTC.TrainingCategory') }}：</div>
                <div class="li-info">{{ item?.TrainingCategory }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.ProjectLocation') }}：</div>
                <div class="li-info">{{ item?.ProjectNo }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.ProjectName') }}：</div>
                <div class="li-info">{{ item?.ProjectName }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.SupplierCode') }}：</div>
                <div class="li-info">{{ item?.SupplierCode }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.SupplierName') }}：</div>
                <div class="li-info">{{ item?.SupplierName }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.CheckInTime') }}：</div>
                <div class="li-info" style="color:#25a6d8;">
                    {{
                        item?.CheckInDateTime && new Intl.DateTimeFormat(navigator?.language,
                            options_).format(Date.parse(item?.CheckInDateTime))
                    }}
                </div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.CheckInAccount') }}：</div>
                <div class="li-info">{{ item?.CheckInAccount }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.Sign_in_name') }}：</div>
                <div class="li-info">{{ item?.CheckInName }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.CheckOTime') }}：</div>
                <div class="li-info" style="color:#25a6d8;">
                    {{
                        item?.CheckOutDateTime && new Intl.DateTimeFormat(navigator?.language,
                            options_).format(Date.parse(item?.CheckOutDateTime))
                    }}
                </div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.Sign_out_account') }}：</div>
                <div class="li-info">{{ item?.CheckOutAccount }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.Sign_off_name') }}：</div>
                <div class="li-info">{{ item?.CheckOutName }}</div>
            </div>
            <div class="li">
                <div class="li-name">{{ $t('CSTC.CheckInValidationDuration') }}：</div>
                <div class="li-info" style="color:#25a6d8;">{{ item?.CheckOutDateTime ? new
                    Intl.DateTimeFormat(navigator?.language, options).format(new Date(item?.CheckInValidationDuration +
                        'Z'))
                    : '' }}
                </div>
            </div>

        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import YZNav from '@/components/YZNav.vue';
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const item = ref()
const options = ref({
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    timeZoneName: "short"
})

const options_ = ref({
    hour12: false,
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short"
})
onMounted(() => {
    item.value = JSON.parse(route.query.i)
    console.log("%c [ item.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item.value)
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;
            color: #00A0E1;
        }
    }

    .grap {
        width: 100%;
        height: 10px;
        background-color: #f9f9f9;
    }

    .list {
        padding: 10px 12px;

        .li {
            font-size: var(--yz-com-14);
            margin-bottom: 16px;
            display: flex;
            align-items: center;

            .li-name {
                color: #999999;
                width: 145px;
            }

            .li-info {
                color: #333333;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>