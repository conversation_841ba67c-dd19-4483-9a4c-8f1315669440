<template>
    <div>
        <van-popup v-model:show="show" position="right" style="height: 100vh; width: 100%">
            <div class="service-div">
                <van-nav-bar :title="$t(item.name)" :left-text="$t('Global.Back')"
                    @click-left.stop="item.onBack(item.name)" left-arrow />
                <div>
                    <van-cell-group>
                        <van-cell v-for="item in langMenus" @click="handlerClick(item)" :key="item.name"
                            :title="$t(item.name)">
                            <van-icon slot="right-icon" name="success" v-if="item.value === selectId" color="#4caf50"
                                class="van-cell__right-icon"></van-icon>
                        </van-cell>
                    </van-cell-group>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { ILangMenu, IMyMenu } from '@/logic/my/myModel';
import { useGetQuery } from '@/utils/request';
import { url } from '@/api/url'
import { computed, onMounted, PropType, ref } from 'vue';
import { useUserStore } from '@/store/users';
import { NotifyType, showNotify } from 'vant';
import { useChangLang } from '@/utils';
import { useI18n } from 'vue-i18n';
import i18n from "@/locale"
import { useCore } from '@/store/core/index'
const userStore = useUserStore()
const props = defineProps({
    isPop: {
        type: Boolean,
        default: false,
        required: false
    },
    item: {
        type: Object as PropType<IMyMenu>,
        required: true
    }
})
const show = computed(() => props.isPop)
const selectId = ref<string>('')
const core = useCore()
onMounted(() => {
    selectId.value = core.$lang
})
//culture=zh-Hans
// const langMenus = ref<ILangMenu[]>([
//     { name: 'my.simplified', value: 'zh-cn' },
//     { name: 'my.english', value: 'en' }
// ])

const langMenus = ref<ILangMenu[]>([
    { name: 'my.simplified', value: 'zh-CN' },
    { name: 'my.english', value: 'en' }, //'en-US' ??
    { name: 'my.korean', value: 'ko-KR' },
    { name: 'my.german', value: 'de-DE' }
])
const handlerClick = (item: ILangMenu) => {
    selectId.value = item.value
    reloadLang()
}
const reloadLang = () => {
    //切换语言
    useChangLang(selectId.value)
}
</script>
<style scoped>
.service-div {
    width: 100%;
    height: 100vh;

    ::v-deep(.van-cell__title) {
        font-size: var(--yz-com-14);
    }
}
</style>