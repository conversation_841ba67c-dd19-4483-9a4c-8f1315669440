
<template>
  <div>
    <van-field
      v-if="!field.hidden"
      :name="field.vModel.model"
      :required="field.config.required"
      :readonly="field.readonly"
      :disabled="field.disabled"
      :rules="field.config.rules"
      :label="field.config.label"
      v-model="vModel"
    >
      <template #input>
        <div class="history-form-link" @click="openForm()">
          <!-- targetFormId：{{ field.targetFormId }}<br> -->
          <!-- TaskID：{{ historyFormObj.TaskID }}<br>
          uuid:{{ props.field.uuid }}<br>
          值:{{ props.modelValue }}<br>
          下标:{{ props.field.pindex }}<br> -->
          {{
            (vModel && historyFormObj[config.extends?.fieldDisplayText]) ||
            config.extends?.fieldEmptyText
          }}
        </div>
      </template>
    </van-field>
    <van-popup
      teleport="body"
      v-model:show="procesShow"
      style="height: 100vh"
      position="bottom"
    >
      <HistoryFormRead
        :key="time"
        ref="reformReadRef"
        :reformRead="reformRead"
        @onBack="procesShow = false"
        :noShowHeader="true"
      />
    </van-popup>
  </div>
</template>
  
  <script lang="ts" setup>
import {
  YZHistoryFormLink,
  YZHistoryFormLinkConfig,
} from "@/logic/forms/YZHistoryFormLink";
import { eventBus } from "@/utils/eventBus";
import {
  computed,
  onMounted,
  PropType,
  ref,
  watch,
  nextTick,
  reactive,
} from "vue";
import { url } from "@/api/url";
import { useParamsFormatter } from "@/utils";
import { useGetQuery } from "@/utils/request";
import { useRouter } from "vue-router";
import HistoryFormRead from "@/components/HistoryFormRead.vue";
import { useProcessStore } from "@/store/process";
const emit = defineEmits(["update:modelValue"]);
const processStore = useProcessStore();
const props = defineProps({
  field: {
    type: Object as PropType<YZHistoryFormLink>,
    required: true,
  },
  modelValue: {
    type: [String, Number],
    default: 0,
  },
  index: {
    type: Number,
    defualt: -1,
  },
});
const isFirstLoad = ref<boolean>(true);
const vModel = computed({
  get() {
    // props.field.expressTest(props.field, props.index, isFirstLoad.value);
    // props.field.disableTest(props.field, props.index, isFirstLoad.value);
    // props.field.hiddenTest(props.field, props.index, isFirstLoad.value);
    // props.field.advancedValidate(props.field, props.index, isFirstLoad.value);
    return props.modelValue;
  },
  set(val) {
    emit("update:modelValue", val);
  },
});
const setFun = () => {
  props.field.expressTest(props.field, props.index, isFirstLoad.value);
  props.field.disableTest(props.field, props.index, isFirstLoad.value);
  props.field.hiddenTest(props.field, props.index, isFirstLoad.value);
  props.field.advancedValidate(props.field, props.index, isFirstLoad.value);
}
onMounted(async () => {
  if (props.modelValue) await getHistoryFormInfo();
  isFirstLoad.value = false;
});
// const isFirstLoad = ref<boolean>(true)
let historyFormObj = reactive<any>({
  AgentAccount: null,
  AgentDisplayName: null,
  CreateAt: null,
  Description: null,
  FinishAt: null,
  OwnerAccount: null,
  OwnerDisplayName: null,
  OwnerPositionID: null,
  ParentStepID: null,
  ParentStepName: null,
  ParentTaskID: null,
  ProcessId: null,
  ProcessName: null,
  ProcessVersion: null,
  SerialNum: null,
  TaskID: null,
  TaskState: null,
})
const testShow = ref<boolean>(false);
const time = ref<number>(0);
const router = useRouter();
const procesShow = ref<boolean>(false);
const reformRead = reactive({
  taskId: "",
  loadType: "",
  formTitle:'',
});
const reformReadRef = ref(null);
const config = props.field.config as YZHistoryFormLinkConfig;
const resetHistoryData = () => {
  
  Object.keys(historyFormObj).forEach((el:any) => {
    historyFormObj[el] = null
  })
  console.log(historyFormObj,'historyFormObj')
}
const getHistoryFormInfo = async () => {
  resetHistoryData()
  // historyFormObj.value = {};
  if (props.field.vModel.value) {
    const newUrl = useParamsFormatter(url.process.historyFormInfo, {
      params1: props.field.vModel.value,
    });
    const data = await useGetQuery(newUrl);
    // historyFormObj.value = data;
    Object.keys(data).forEach(key => {
      historyFormObj[key] = data[key]
    })

    console.log("historyFormObj.value",props.field.targetFormId, historyFormObj,vModel.value);
  }
};
// 打开表单
const openForm = () => {
  
  // debugger;
  if (!props.field.vModel.value || props.field.vModel.value == 0) return;
  time.value = new Date().getTime();
  reformRead.taskId = props.field.vModel.value;
  reformRead.loadType = "read";
  reformRead.formTitle = "";
  procesShow.value = true;
};
eventBus.on("setFieldValue", {
  cb: async function (params) {
    if (params.uuid === props.field.uuid) {
      props.field.vModel.value = String(params.value);
      setFun()
      await getHistoryFormInfo();
    }
  },
});
eventBus.on("setFieldDisable", {
  cb: function (params) {
    if (params.uuid === props.field.uuid) {
      props.field.disabled = params.value;
    }
  },
});
eventBus.on("setFieldHidden", {
  cb: function (params) {
    if (params.uuid === props.field.uuid) {
      props.field.hidden = params.value;
    }
  },
});
// 监听数据MAP
eventBus.on("onMap", {
  cb: async function (params) {
    if (
      props.field.vModel.model === params.model &&
      props.field.uuid === params.uuid
    ) {
      props.field.vModel.value = params.value;
      setFun()
      await getHistoryFormInfo();
    }
  },
});
</script>
<style lang="scss" scoped>
.history-form-link {
  width: 100%;
  color: #409eff;
}
</style>