<template>
    <div>
        <van-field v-if="!field.hidden" v-model="vModel" is-link :name="field.vModel.model"
            :required="field.config.required" :placeholder="field.placeholder" :disabled="field.disabled"
            :readonly="true" :rules="field.config.rules" :label="field.config.label" @click="onClick" />
        <van-popup v-model:show="pickerShow" position="bottom">
            <van-picker :columns="props.field.getOptions()" @confirm="onConfirm" @cancel="onCancle" />
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { YZSelect, YZSelectConfig, ISelectOption } from '@/logic/forms/YZSelect';
import { parseExpress } from '@/logic/forms/express';
import { YZConfig, YZField } from '@/logic/forms/formModel';
import { eventBus } from '@/utils/eventBus';
import { onMounted, onUnmounted, nextTick } from 'vue';
import { computed, PropType, ref, watch } from 'vue';
import { url } from "@/api/url";
import { useParamsFormatter, useIsChinese, useStrToUnicode, useFormData, decodeBase64 } from "@/utils";
import { usePostBody } from "@/utils/request";
import { formBuilder } from '@/logic/forms/formBuilder';
import { MD5 } from 'crypto-js';
import store from "@/store";
import { useCore } from '@/store/core';
const core = useCore(store)
const props = defineProps({
    field: {
        type: Object as PropType<YZSelect>,
        required: true
    },
    modelValue: {
        type: [String, Number],
        default: ''
    },
    optionValue: {
        type: [String, Number],
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:modelValue', 'update:optionValue'])
const config = props.field.config as YZSelectConfig
const pickerShow = ref<boolean>(config.selectShow)
const uuidValue = ref<number>(0)
const isProcessed = ref(false)

const getOptions = async (filterArr?: any[]) => {
    const options: ISelectOption[] = [];
    const config = props.field.config as YZConfig;
    const arrys: Array<string> = []
    let type: string = ''
    if (config.extends) {
        type = config.extends.use === "options" ? "static" : "dymic";
    }
    const dueTime = [
        {
            "text": "No work until correction",
            "value": "No work until correction",
            "Id": 19,
            "DataType": "UAUC Due Time",
            "Language": "en",
            "Code": "Option_01",
            "DisplayName": "No work until correction",
            "Value": "No work until correction",
            "Sort": 1
        },
        {
            "text": "Within one day",
            "value": "Within one day",
            "Id": 20,
            "DataType": "UAUC Due Time",
            "Language": "en",
            "Code": "Option_02",
            "DisplayName": "Within one day",
            "Value": "Within one day",
            "Sort": 2
        },
        {
            "text": "Other",
            "value": "Other",
            "Id": 21,
            "DataType": "UAUC Due Time",
            "Language": "en",
            "Code": "Option_03",
            "DisplayName": "Other",
            "Value": "Other",
            "Sort": 3
        }
    ]
    if (type !== "static" && config.extends.ds) {
        const { displayField, valueField } = config.extends
        const { dataSourceId, filter, orderBy, tableName, type, esbId } = config.extends.ds;

        let newUrl = ''
        if (type === 'esb') {
            newUrl = useParamsFormatter(url.dataSource.getEsbDataNoPage, {
                params1: esbId
            });
            const result = await usePostBody(newUrl, {}, useFormData({
                f: buildFilter(filter, filterArr)
            }), false, false)

            if (result && result.length > 0) {
                for (let i = 0; i < result.length; i++) {
                    const item = result[i];
                    const json: ISelectOption = {
                        text: "",
                        value: "",
                        ...item
                    };
                    if (item[displayField]) {
                        json.text = item[displayField];
                    }
                    if (item[valueField]) {
                        json.value = item[valueField];
                    }
                    options.push(json);
                }
            }
            // 设置缓存到sessionStroage
            //   core.setCachedData(dsMd5,options)
            props.field.setOption(options)
            // 如果vmodel目前的值存在在options中不更改或者清空
            let hasData = options.find(item => {
                return item.text == vModel.value
            })
            if (!hasData) {
                vModel.value = ''
            }
            //   const value = props.field.vModel.optionValue;
            //   if (value) {
            //     const item = options.find((x) => x.value === value);
            //     if (item) {
            //         vModel.value = item.text
            //     }
            //   }
            // })
        } else if (type === 'table' && !isProcessed.value) {
            isProcessed.value = true
            newUrl = useParamsFormatter(url.dataSource.getTableData, {
                params1: dataSourceId,
                params2: type,
                params3: tableName,
            });
            const result = await usePostBody(
                newUrl,
                {},
                useFormData({
                    f: buildFilter(filter, filterArr),
                    c: btoa(JSON.stringify(arrys)),
                    o: config.extends.ds.orderBy ? decodeBase64(config.extends.ds.orderBy) : '',
                }),
                false,
                false
            )
            // console.log("%c [ result -2]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)

            if (result && result.length > 0) {
                for (let i = 0; i < result.length; i++) {
                    const item = result[i];
                    const json: ISelectOption = {
                        text: "",
                        value: "",
                        ...item
                    };
                    if (item[displayField]) {
                        json.text = item[displayField];
                    }
                    if (item[valueField]) {
                        json.value = item[valueField];
                    }
                    options.push(json);
                    // console.log("%c [ options -2 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", options)
                }
            }
            //   dataOptions.value = options
            // 设置缓存到sessionStroage
            // core.setCachedData(dsMd5,options)
            props.field.setOption(options)
            // 如果vmodel目前的值存在在options中不更改或者清空
            let hasData = options.find(item => {
                return item.text == vModel.value
            })
            if (!hasData) {
                vModel.value = ''
            }
        } else if (type === 'form') { //YZ +
            let ds = config.extends.ds
            newUrl = useParamsFormatter(url.dataSource.getFormNoPage, {
                params1: ds.formId,
                params2: ds.formTable,
            });
            const result = await usePostBody(
                newUrl,
                {},
                useFormData({
                    f: buildFilter(filter, filterArr),
                    c: btoa(JSON.stringify(arrys)),
                    o: config.extends.ds.orderBy ? decodeBase64(config.extends.ds.orderBy) : '',
                }), false, false
            )

            if (result && result.length > 0) {
                for (let i = 0; i < result.length; i++) {
                    const item = result[i];
                    const json: ISelectOption = {
                        text: "",
                        value: "",
                        ...item
                    };
                    if (item[displayField]) {
                        json.text = item[displayField];
                    }
                    if (item[valueField]) {
                        json.value = item[valueField];
                    }
                    options.push(json);
                }
            }
            //   dataOptions.value = options
            // 设置缓存到sessionStroage
            // core.setCachedData(dsMd5,options)
            props.field.setOption(options)
            // 如果vmodel目前的值存在在options中不更改或者清空
            let hasData = options.find(item => {
                return item.text == vModel.value
            })
            if (!hasData) {
                vModel.value = ''
            }
        }
        // setFun()
    }
    if (type === "static") {
        props.field.setOption(config.extends.options)
    }
}
const onClick = () => {
    if (!props.field.disabled) {
        //const ashow =  props.field.config as YZSelectConfig
        // ashow.setShow(true)
        config.selectShow = true
        pickerShow.value = true
    }

}
const onConfirm = ({ selectedOptions }: any) => {
    console.log("%c [ van-picker --- selectedOptions ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", selectedOptions)
    const itemValue = selectedOptions[0]
    config.selectShow = true
    pickerShow.value = false
    emit('update:modelValue', itemValue.text)
    emit('update:optionValue', itemValue.value)
    const mapObj = config.getMap()
    if (mapObj) {

        const mapFields: YZField[] = []
        Object.keys(mapObj).forEach(item => {
            const name = item
            const value = mapObj[name] as string
            let mapKey = value ?? name
            // 获取指定的控件
            let formId = props.field.targetFormId
            let field = parseExpress.getFieldByFid(formId, value) //parseExpress.getField(value)
            if (value.indexOf(".") > -1 || value.indexOf("子表") > -1 || value.split('.').length === 2) {
                let fex = value.split('.')[0]
                let fieldModel = value.split('.')[1]
                field = parseExpress.getFieldByFid(formId, fex) //parseExpress.getField(fex)
                const fieldValue = field?.vModel.value
                if (fieldValue && fieldValue.length > 0) {
                    let newIndex = props.index
                    if (newIndex === -1) {
                        // 表示当前操作的控件是主表，但是要给字表赋值
                        fieldValue.forEach((f: any, index: number) => {
                            const items = fieldValue[index].colums as Array<any>
                            const xfield = items.find(x => x.field.vModel.model === fieldModel)
                            if (xfield) {
                                mapFields.push(xfield.field)
                            }
                        })
                    } else {
                        const items = fieldValue[newIndex].colums as Array<any>
                        const xfield = items.find(x => x.field.vModel.model === fieldModel)
                        if (xfield)
                            mapFields.push(xfield.field)
                    }
                }
            }
            if (field) {
                mapFields.push(field)
            }
            const displayField = config.extends['displayField']
            let keyName = (name === displayField ? 'text' : 'value')
            if (mapFields.length > 0) {
                mapFields.forEach(field => {
                    if (field) {
                        let newValue = itemValue[keyName]
                        if (!newValue) {
                            const vName = name.toLocaleUpperCase()
                            Object.keys(itemValue).forEach(item => {
                                const vItem = item.toUpperCase()
                                if (vItem === vName)
                                    newValue = itemValue[item]
                            })
                        }
                        // eventBus.emit('onMap', true, {
                        //     value: newValue,
                        //     model: field.vModel.model,
                        //     uuid: field.uuid,
                        //     index: props.index
                        // })
                        if (mapObj[item] === field.table) {
                            eventBus.emit('onMap', true, {
                                value: itemValue[item],
                                model: field.vModel.model,
                                uuid: field.uuid,
                                index: props.index
                            })
                        } else if (mapObj[item] === field.vModel.model) {
                            eventBus.emit('onMap', true, {
                                value: itemValue[item],
                                model: field.vModel.model,
                                uuid: field.uuid,
                                index: props.index
                            })
                        }
                    }
                })
            }

        })
        // 获取当前控件的值与文本的映射关系
        // const displayField = config.extends['displayField']
        // if (mapObj) {
        //     Object.keys(mapObj).forEach(item => {
        //         const code = mapObj[item]
        //         var control = parseExpress.getField(code)
        //         if (control)
        //             control.vModel.value = (item === displayField) ? itemValue.text : itemValue.value
        //     })
        // }
    }
    nextTick(() => { //YZ +
        setFun()
    })
    // onChange()
}
const onCancle = () => {
    config.setShow(false)
    pickerShow.value = false
}
const buildFilter = (filter: any, arr?: any) => {
    let str: string = '{'
    if (arr && arr.length > 0) {
        let obj: any = {}
        Object.keys(filter).forEach(key => {
            const bind = filter[key]
            let dataObj = arr.find((item: any) => item.fieldName === bind.field)
            if (dataObj) str += `"${builderFieldChinese(key)}":{"op":"${bind.op}","value":"${builderFieldChinese(dataObj.value)}"},`
        })
    } else {
        const fields = formBuilder.formConfig?.getFieldsRecord()
        Object.keys(filter).forEach(key => {
            const item = filter[key]

            if (fields && fields.length > 0) {
                let valueData = item.value
                if (item.field) {
                    if (item.field.indexOf('.') > -1) {
                        // 读取明细表
                        const fieldItem = fields.find(x => x.tableName == item.field)
                        if (fieldItem) {
                            valueData = fieldItem.vModel.value
                        }
                    } else {
                        const fieldItem = fields.find(x => !x.tableName && x.vModel.model == item.field)
                        if (fieldItem) {
                            if (fieldItem.config.ctype === 'YZUploader') {
                                valueData = fieldItem.vModel.optionValue
                            } else {
                                valueData = fieldItem.vModel.value
                            }

                        }
                    }
                }
                const nkey: string = builderFieldChinese(key)
                str += `"${nkey}":{"op":"${item.op}","value":"${builderFieldChinese(valueData)}"},`
            }
        })
        if (str.lastIndexOf(',') > -1)
            str = str.substring(0, str.lastIndexOf(','))
    }
    str += "}"
    return btoa(str)
}
const builderFieldChinese = (str: string) => {
    // console.log("%c [ builderFieldChinese str ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", str)
    if (useIsChinese(str)) {
        // console.log("%c [ useStrToUnicode(str) ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", useStrToUnicode(str))
        return useStrToUnicode(str)
    }
    return str
}
const isFirstLoad = ref<boolean>(true)
// const getLocationProject = async () => {

// }
onMounted(async () => {
    isFirstLoad.value = false
    await getOptions()
    // getLocationProject()
    let vModelItemCheck = props.field.getOptions().find((item: any) => item.value == props.field.vModel.optionValue)
    const type = props.field.getDateType()
    // console.log("%c [ type ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", type)
    props.field.vModel.value = vModelItemCheck?.text
    if (type === 'static') {
        const item = props.field.getOptions().find((x: any) => x.value === props.field.vModel.optionValue)
        if (item) {
            props.field.vModel.value = item.text
        } else {
            // 设置默认值
            const defaultItem = props.field.getOptions().find((x: any) => x.checked)
            if (defaultItem) {
                props.field.vModel.value = defaultItem.text
                props.field.vModel.optionValue = defaultItem.value
            }
        }
    }
    nextTick(() => {
        setFun()
    })

    // console.log("%c [ props.field.config.label ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", props.field.config.label)
})
// let count = 0
const vModel = computed({
    get() {
        // count ++
        // console.log('props.field',props.field.config.label,'我是下拉走了几次？？？' + count)
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
// eventBus.on('onSetModel', {
//     cb: function (params) {
//         if (props.field.uuid === params['field'].uuid) {
//             vModel.value = params['item'].text
//             setFun()
//         }
//     }
// })
// onUnmounted(() => {
//     eventBus.off('onSetModel', true)
// })
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldValue', {
    cb: async function (params) {
        if (params.uuid === props.field.uuid) {
            await getOptions()
            const item = props.field.getOptions().find((x: any) => String(x.value) === String(params.value))
            if (item) {
                vModel.value = item.text
                setFun()
                emit('update:optionValue', item.value)
            }
        }
    }
})
// 触发过滤
eventBus.on('setSelectFilter', {
    cb: async function (parasm) {
        if (parasm.uuid === props.field.uuid && parasm.index === props.index) {
            await getOptions(parasm.value)
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
eventBus.on('onMap', {
    cb: async function (params) {
        console.log("%c [ params 5555]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
        if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
            await getOptions()
            const item = props.field.getOptions().find((x: any) => String(x.value) === String(params.value))
            console.log("%c [ item 5555]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item)
            if (item) {
                vModel.value = item.text
                setFun()
                emit('update:optionValue', item.value)
            }

        }
    }
})
</script>
<style scoped></style>