import { useLang } from "@/utils";
import { Y<PERSON><PERSON>onfig, Y<PERSON><PERSON><PERSON>, Y<PERSON><PERSON>ty<PERSON> } from "./formModel";
export class Y<PERSON>ButtonConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZButton'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZDescStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZ<PERSON>utton extends YZField {
    private desc: string;
    constructor(params: any) {
        super(params)
        this.desc = params['__config__'].extends['$html']
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZButtonConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZDescStyle(style)
    }
    getDesc() {
        return this.desc
    }
}