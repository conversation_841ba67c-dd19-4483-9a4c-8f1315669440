<template>
    <div>
        <van-field  v-if="!field.hidden" :name="field.vModel.model"
         :required="field.config.required"  
            :readonly="field.disabled" :label="field.config.label">
            <template #input>
                <van-switch v-model="vModel" 
                @change="handleChange"
                :active-color="field.getActiveColor()"
                :active-value="field.getActiveValue()"
                :inactive-color="field.getInActiveColor()"
                :inactive-value="field.getInActiveValue()"
                :disabled="field.disabled"
                size="small"
                 />
            </template>
        </van-field>
    </div>
</template>

<script lang="ts" setup>
import { YZSwitch } from '@/logic/forms/YZSwitch';
import { eventBus } from '@/utils/eventBus';
import { boolean } from 'mathjs';
import { computed, onMounted, PropType, ref, watch } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZSwitch>,
        required: true
    },
    modelValue: {
        type: [Object,Boolean,String,Number],
        default: ''
    },
    index:{
        type:Number,
        default:-1
    }
})
const handleChange=()=>{
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue ? 1: 0
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
eventBus.on('setFieldValue', {
  cb: function (params) {
    if (params.uuid === props.field.uuid) {
      props.field.vModel.value = params.value
    }
  }
})
eventBus.on('setFieldDisable',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.disabled = params.value 
       }
    }
})
eventBus.on('setFieldHidden',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.hidden = params.value 
       }
    }
})
eventBus.on('onMap', {
  cb: function (params) {
    console.log("%c [ params 7]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)
    // console.log(params,props.field)
    if (props.field.vModel.model === params.model && props.field.uuid === params.uuid) {
        props.field.vModel.value = params.value ? 1 : 0
        handleChange()
    }
  }
})
</script>
<style  scoped>

</style>