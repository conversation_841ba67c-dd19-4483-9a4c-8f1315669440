<!DOCTYPE html>
<html lang="en" data-theme="">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
    <meta name="format-detection" content="telephone=yes" />
    <title>文件下载</title>
    <script>
        window.onload = function () {
            adapter()
            setTimeout(() => {
                buildFile()
            }, 1000)
        }
        function adapter() {
            const dip = document.documentElement.clientWidth
            const rootSize = (dip * 100) / 375
            document.documentElement.style.fontSize = rootSize + 'px'
        }
        window.onresize = adapter
        function buildFile() {
            const newUrl = `/sso/login?appKeyName=${useQueryParams("key")}&AccessToken=${useQueryParams("token")}&Account=${useQueryParams("account")}&RedirectUrl=${window.location.href}`
            window.location.href = newUrl
        }
        function useQueryParams(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&")
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return false;
        }
    </script>
</head>

<body>
    <div class="down-load" onclick="onDownLoad()">立即下载</div>
    <script>
        function onDownLoad() {
            const id = useQueryParams("id")
            const token = useQueryParams("token")
            const url = `/bpm/attachment/file/${id}?access_token=${token}`
            const elink = document.createElement('a')
            elink.download = "文件下载"
            elink.style.display = 'none'
            elink.href = url
            elink.click()
            document.body.appendChild(elink)
            URL.revokeObjectURL(elink.href) // 释放URL 对象
        }
    </script>

</body>
<style>
    .down-load {
        height: 0.45rem;
        line-height: 0.45rem;
        text-align: center;
        font-size: 0.16rem;
        background: #4572ed;
        color: #fff;
        border-radius: 5px;
    }
</style>

</html>