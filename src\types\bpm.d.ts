export type OrgType = 'ou' |'user'
export type DisabledType = 'init' | 'change'
export type HiddenType ='init' | 'change'
export type StartType ='Human' | 'Trigger' |'ParentProcess'
export type Status = 'Runnable'|'Suspended'|'Completed'|'Terminating' | 'Terminated' |'Compensating' |'Compensated'
export type TaskInstanceType = 'General ' |  'Inform'|  'Share' | 'CounterSign' | 'AdditionalSign'
export type ProcessInstanceStartType = 'Human' | 'Trigger' | 'ParentProcess' 
export type Priority  = 'Normal' |  'High'
export type $expres = '$express' | '$hidden' |'$disable'
export type OutType ='Period' | 'InOffice' | 'Out'
export type ValueType = 'number' | 'string'