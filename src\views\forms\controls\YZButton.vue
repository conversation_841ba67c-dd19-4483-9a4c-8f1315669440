<template>
    <div class="clearAfter" v-if="!field.hidden">
       <van-button :block="isMainBtn" :disabled="field.disabled"  size="small"
        @click.self="handlerEvent"
       >{{ field.config.label }}</van-button>
    </div>
</template>

<script lang="ts" setup>
import { YZButton  } from '@/logic/forms/YZButton';
import { useMobileEvents } from '@/logic/forms/formsutil';
import { useCore } from '@/store/core';
import { onMounted, PropType, ref ,getCurrentInstance} from 'vue';
import { eventBus } from '@/utils/eventBus';
const props = defineProps({
    field: {
        type: Object as PropType<YZButton>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    },
    isMainBtn:{
        type:Boolean,
        default:true 
    }
})
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    isFirstLoad.value = false
})
const core = useCore()

const handlerEvent =()=>{
    const events =  props.field.events
    const mobileEvent = useMobileEvents()
    if (events && events.handler) {
        if(typeof events.handler ==='string') {
            const funName =events.handler as string
            const btnEvents = mobileEvent.find(x=>x.name ==='btnEvents')
            if (btnEvents &&  btnEvents.fun) {
                const btnFun =btnEvents.fun as any
                const execute:Function =btnFun[funName]
                const formRefs = core.$form
                if (execute)
                    execute(formRefs,core.fields,core.controlsInstance)
            }
        } else {
            events.handler()
        }
    }
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
</script>
<style  lang="scss" scoped>
.clearAfter::after {
    display: none;

}
// .right {
//     text-align: right;
//     float: right;
//     margin: 5px;
//     z-index: 0;
// }
</style>