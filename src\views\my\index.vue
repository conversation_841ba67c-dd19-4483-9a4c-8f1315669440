<template>
  <div>
    <div class="perCenter" @click.prevent>
      <!-- 头部 -->
      <div class="per-head">
        <div class="per-title">
          <!-- Linde  -->
          <div class="back" @click="router.back()">
            <van-icon class="icon" name="arrow-left" />
          </div>
          <!-- Linde  -->
          <!-- <PERSON><PERSON> need hide -->
          <!-- {{ $t('my.UserCenter') }} -->
          <!-- <PERSON><PERSON> need hide -->
        </div>
        <van-row>
          <van-col :span="6">
            <div class="per-image">
              <img :src="userImage" alt="">
            </div>
          </van-col>
          <van-col :span="18">
            <p class="per-user">
              {{ userInfo?.DisplayName }}
            </p>
            <p> <span class="per-dept">
                {{ userInfo?.Account }}
              </span>
              <!-- <PERSON><PERSON> need hide  -->
              <!-- <van-divider vertical style="background: #000;" />
              <span class="per-dept">
                {{ userInfo?.CostCenter }}
              </span> -->
              <!-- <PERSON><PERSON> need hide  -->
            </p>
          </van-col>
        </van-row>
      </div>
      <!-- 内容 -->
      <!-- Linde  -->
      <div class="content">
        <van-cell :title="$t('my.AccountSecurity')" is-link @click="router.push('/account')">
          <template #icon>
            <img class="van-cell__left-icon" src="../../assets/images/anquan.png" alt="">
          </template>
          <template #right-icon>
            <img class="van-cell__left-icon" style="margin-right: 0;" src="../../assets/images/baohu.png" alt="">
          </template>
        </van-cell>
      </div>
      <!-- Linde  -->
      <div class="content">
        <!-- Linde  -->
        <van-cell :title="$t('my.AboutAPP')" is-link @click="router.push('/about')">
          <template #icon>
            <img class="van-cell__left-icon" style="width: 18px;height: 18px;" src="../../assets/images/qiye.png"
              alt="">
          </template>
        </van-cell>
        <!-- Linde  -->
        <van-cell v-for="(item, index) in menu" :key="item.name" :icon="item.iconName" :title="$t(item.name)" is-link
          @click="item.handler(index)">
          <template #icon>
            <van-icon class="van-cell__left-icon" :name="item.iconName" :color="item.color"></van-icon>
          </template>
        </van-cell>
      </div>
      <div v-for="(item, index) in menu" :key="index">
        <component :is="item.component" :item="item" :isPop="item.isPop" />
      </div>
      <van-button type="warning" @click="handlerOut" block class='logoutBtn' style="margin: 0px auto;width: 90%;">
        {{ $t('my.LogOut') }}
      </van-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useMy } from '@/logic/my';
import { useRouter } from 'vue-router'
const router = useRouter()
const { userInfo, handleCallBack, menu, userImage, handlerOut } = useMy()
console.log("%c [ userImage ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", userImage)
console.log("%c [ userInfo ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", userInfo)
</script>
<style lang="scss" scoped>
.perCenter {
  position: absolute;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: var(--van-background-2);

  .per-head {
    height: 168px;
    width: 100%;
    // background-image: url('@/assets/imgs/mybg.jpg'); //YZ need hide
    // background-color: #1D5A91; // #00A0E1; //Linde
    background-color: #00A0E1; //Linde

    background-size: 100% 100%;
    background-repeat: no-repeat;
    overflow: hidden;

    .per-title {
      width: 100%;
      height: 35px;
      line-height: 35px;
      text-align: center;
      font-family: PingFangSC-Medium;
      font-size: var(--yz-com-16);
      // color: #333333; //YZ need hide
      color: #fff; //Linde

      margin-top: 5px;
      font-weight: 500;
      position: relative;

      .back {
        position: absolute;
        top: 0;
        left: 0;
        width: 40px;
        height: 100%;
        padding-right: 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .icon {
          font-size: 20px;
          color: #fff;
        }
      }

      .code {
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 100%;
        padding-right: 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .icon {
          font-size: 20px;
          color: #fff;
        }
      }
    }

    .per-image {
      width: 53px;
      height: 53px;
      border-radius: 50%;

      padding: 20px;
      border-radius: 50%;

      img {
        width: 53px;
        border-radius: 50%;
        height: 53px;
      }
    }

    .per-user {
      font-family: PingFangSC-Medium;
      font-size: var(--yz-com-18);
      // color: #333333; //YZ need hide 
      color: #fff; //Linde

      font-weight: 500;
      margin-top: 24px;
    }

    .per-dept {
      font-family: PingFangSC-Regular;
      font-size: var(--yz-btn-14);
      // color: #606266; //YZ need hide 
      color: #fff; //Linde
      font-weight: 400;
    }
  }

  .content {
    // width: calc(100% - 30px); //YZ need hide 
    border-radius: 10px;
    // margin: -35px auto 30px; //YZ need hide 
    // padding: 30px 5px 20px; //YZ need hide 
    overflow: hidden;
    box-sizing: border-box;

    margin: 10px; //Linde


    .van-cell__left-icon {
      margin-right: 10px;
      font-size: 20px; //Linde
      width: 20px; //Linde
      height: 20px; //Linde
    }

    ::v-deep(.van-cell__title) {
      font-size: var(--yz-com-14);
    }

  }
}

.logoutBtn {
  //Linde
  margin: 0px auto;
  width: calc(100% - 20px);
  background-color: var(--van-search-content-background);
  color: #00A0E1;
  border: none;
  font-size: var(--yz-com-16);
}

.van-cell {
  //Linde
  display: flex;
  align-items: center;
}
</style>