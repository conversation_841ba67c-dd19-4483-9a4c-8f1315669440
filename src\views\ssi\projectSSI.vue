<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div class="btn">
                <!-- <van-icon name="filter-o" @click="rightShow" /> -->
                <van-popup v-model:show="isShow" position="right" :style="{ width: '82%', height: '100%' }">
                    <div>
                        <div class="li_">
                            <span class="name">{{ $t('CSTC.ProjectNo') }}</span>
                            <van-field v-model="p_no" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">{{ $t('CSTC.ProjectName') }}</span>
                            <van-field v-model="p_name" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">{{ $t('SSI.ContractorCode') }}</span>
                            <van-field v-model="SupplierCode" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">{{ $t('SSI.ContractorName') }}</span>
                            <van-field v-model="SupplierName" clearable />
                        </div>
                    </div>
                    <div
                        style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
                        <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset')
                            }}</van-button>
                        <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm">{{
                            $t('Global.Confirm')
                        }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="list">
            <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                :loosing-text="$t('Form.Release_to_refresh')" :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loadData.loading" :finished="loadData.finished"
                    :finished-text="$t('Form.NoMore')" :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div v-for="(i, idx) in viewColsData" :key="idx">
                        <div class="li" @click="toDetailSSI(i)">
                            <div class="li-l">
                                <!-- 项目号 -->
                                <div class="li-n ellipsis"><span class="name">{{ $t('CSTC.ProjectNo') }}</span>
                                    <span class="val_">
                                        {{ i.Project_Number }}
                                    </span>
                                </div>
                                <!-- 项目名称 -->
                                <div class="li-m ellipsis"><span class="name">{{ $t('CSTC.ProjectName') }}</span>
                                    <span class="val_">{{
                                        i.Project_Name }}</span>
                                </div>
                                <div class="li-m ellipsis"><span class="name">{{ $t('SSI.ContractorCode')
                                        }}</span>{{
                                            i.Supplier_Code }}</div>
                                <div class="li-m ellipsis"><span class="name">{{ $t('SSI.ContractorName')
                                        }}</span>{{
                                            i.Supplier_Name }}</div>
                            </div>
                            <div class="li-r">
                                <van-icon name="arrow" size="28" />
                            </div>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed, ref, reactive, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useFormData } from '@/utils'
import { useUserStore } from "@/store/users"
import { usePostBody, useGetQuery } from '@/utils/request'
import { url } from '@/api/url';

const user = useUserStore()
const router = useRouter()
const searchValue = ref('')
const isShow = ref(false)
const p_no = ref('')
const p_name = ref('')
const SupplierCode = ref('')
const SupplierName = ref('')
const searchValue_ = ref('')

const search_ = ref([])
// const config = props.field.config
const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    // pageSize: config.getDataBoweswer()?.pageSize || 10
    pageSize: 20
})
const viewColsData = ref([])
const comeBack = () => {
    router.push({
        path: '/ssiRegister',
        replace: true
    })
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const onSearch = () => {
    console.log("%c [ projectSelectUAUC onSearch ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value)
    loadData.pageIndex = 1
    onLoad()
}
const rest = () => {
    p_no.value = ''
    p_name.value = ''
    SupplierCode.value = ''
    SupplierName.value = ''
    searchValue_.value = ''
}
const searchParams = computed(() => {
    return [
        { name: 'ProjectNumber', op: 'like', value: p_no.value, isAll: false },
        { name: 'ProjectName', op: 'like', value: p_name.value, isAll: false },
        { name: 'SupplierCode', op: 'like', value: SupplierCode.value, isAll: false },
        { name: 'SupplierName', op: 'like', value: SupplierName.value, isAll: false },
    ].filter(param => param.value)
});
const confirm = () => {
    search_.value = searchParams.value;
    loadData.pageIndex = 1
    onLoad()
    isShow.value = false
}

const onRefresh = () => {
    searchValue.value = '' // 清空搜索框
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    viewColsData.value = []
    rest()
    onLoad()
}

const onLoad = async () => {
    const { success, user } = await useGetQuery(url.login.getUserInfo);
    if (success) {
        const SubjectType = new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'Linde' : 'Supplier'
        const ff = window.btoa(JSON.stringify({ "SubjectType": { "op": "=", "value": SubjectType }, "UserAccount": { "op": "=", "value": user?.Account } }))
        const ff_ = window.btoa(JSON.stringify({ "User_Account": { "op": "=", "value": user?.Account }, "Enabled": { "op": "=", "value": true } }))
        const lc_ = window.btoa(JSON.stringify(["ProjectNumber", "ProjectName", "SupplierCode", "SupplierName", "GPSLocation"]))
        const cc_ = window.btoa(JSON.stringify(["Project_Number", "Project_Name", "Supplier_Code", "Supplier_Name"]))//(["Project_Number", "Project_Name", "Supplier_Name", "Supplier_Code"]))
        if (loadData.refreshing) {
            loadData.refreshing = false
            viewColsData.value = []
        }
        const search = [{ "name": "all", "op": "like", "value": searchValue.value, "isAll": true }]
        const search_ = [{ "name": "Keyword", "value": searchValue.value }]
        const formdata = useFormData({
            o: '',
            start: (loadData.pageIndex - 1) * loadData.pageSize,
            page: loadData.pageIndex,
            limit: loadData.pageSize,
            f: 'e30=',// ff_,
            c: cc_,
            lc: 'WyJLZXl3b3JkIl0=', //["Keyword"] // lc_, // 查询列 同上
            s: btoa(window.unescape(encodeURIComponent(JSON.stringify(searchValue.value !== '' ? search_ : [])))) // btoa(window.unescape(encodeURIComponent(JSON.stringify(searchValue.value ? search : search_.value))))
        })
        console.log("%c [ searchValue.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value)

        // console.log("%c [ searchValue.value ? search : search_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value ? search : search_.value)
        try {
            // VW_ProjectUserRole // VW_UAUC_ProjectSupplier
            // const data = await usePostBody(`bpm/datasource/527365695868997/table/VW_ProjectUserRole/paging?_dc=${Date.now()}`, {}, formdata) //项目名称，项目编号的接口
            const data = await usePostBody(`bpm/datasource/esb/678845416267845/paging?_dc=${Date.now()}`, {}, formdata) //项目名称，项目编号的接口
            console.log("%c [ projectSelectSSIdata ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
            if (data.success) {
                viewColsData.value = data.children || []
                loadData.pageIndex++
                loadData.loading = false
                if (data.children.length < loadData.pageSize) {
                    loadData.finished = true
                    console.log("projectSelectUAUC --- 停止刷新")
                }
                rest()
            }
        } catch (error) {
            console.log("projectSelectUAUC---error", error)
        }
    } else {
        console.log('getUserInfo error')
    }
}

const toDetailSSI = (i) => {
    const ProjectSUAUC = {
        ProjectNumber: i.Project_Number,
        ProjectName: i.Project_Name,
        SupplierCode: i.Supplier_Code,
        SupplierName: i.Supplier_Name
    }
    router.push({
        path: '/projectDetailSSI',
        query: {
            ProjectSUAUC: JSON.stringify(ProjectSUAUC)
        },
        replace: true
    })
}

onMounted(() => {
})
onUnmounted(() => {

})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: var(--van-background-2);

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;

            .li_ {
                padding: 20px 13px 0px 13px;

                .van-field {
                    border-radius: 13px;
                    padding: 3px;
                    margin-top: 10px;
                    background: #f8f8f8;
                }

                .name {
                    color: #999999;

                }
            }
        }
    }

    .list {
        margin-top: 10px;
        width: 100%;
        height: 100vh;

        .li {
            border-bottom: 1px solid #DCDFE6;
            background-color: var(--yz-div-background);
            padding: 10px 12px;
            display: flex;
            align-items: center;

            .li-l {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #444444;

                .name {
                    color: #999999;
                    margin-right: 10px;
                }

                .val_ {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .li-m {
                    margin-top: 10px;
                }
            }

            .li-r {
                width: 20px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

::v-deep(.van-field__control) {
    color: #333 !important;
}
</style>