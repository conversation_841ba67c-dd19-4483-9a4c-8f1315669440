<template>
  <div>
    <van-nav-bar :title="formProcessTitle" :left-text="$t('Global.Back')" left-arrow
      v-if="!isThird && !tabHeaderShow && !noShowHeader" @click-left="onClickLeft" />
    <div :class="[
      'form-content',
      { 'hidden-header': Object.keys(formService).length > 0 },
    ]">
      <van-tabs v-model:active="activeTab" @change="onTabChange" class="form-root-tabs">
        <van-tab>
          <template #title>
            <span class="formb-title"> {{ $t("Form.Form") }}</span>
          </template>
          <!-- <van-button @click="closeTest">微信关闭测试</van-button> -->
          <!-- && location.pathname !== '/mobile/' -->
          <van-field v-model="changePostion" v-if="postType === 1" is-link readonly name="picker" label="发起职位"
            :placeholder="$t('Form.Switch_jobs')" @click="pistionShow = true" />
          <van-config-provider :theme-vars="themVarValues">
            <van-form ref="formRef" @submit="onSubmit">
              <div :class="[
                postType === 1 ? 'form-main-start' : 'form-main-audit',
                'form-main',
              ]">
                <div v-for="field in fieldsRefs" :key="field.vModel.model" class="form-item-div">
                  <div>
                    <component :is="field.config.ctype" :field="field" v-model:optionValue="field.vModel.optionValue"
                      v-model="field.vModel.value" :groupIdArr="groupIdArr" />
                  </div>
                </div>
              </div>
            </van-form>
          </van-config-provider>
          <!-- Submit 最终提交-->
          <div class="foot-btn">
            <van-config-provider :theme-vars="themVarValues">
              <van-space>
                <component v-for="btn in btns" :is="btn.component" :btn="btn" :formRef="formRef" />
                <van-button v-if="moreBtns.length > 0" icon="arrow-up" plain size="small" type="primary"
                  @click="onMoreClick">
                  {{ $t("Form.More") }}
                </van-button>
              </van-space>
            </van-config-provider>
          </div>
        </van-tab>
        <!-- 更多按钮 -->
        <van-popup v-model:show="moreShow" closeable close-icon="close" position="bottom"
          :style="{ height: '30%', padding: '30px 20px 0px 20px' }">

          <van-space wrap :size="20">
            <component v-for="btn in moreBtns" :is="btn.component" :btn="btn" :formRef="formRef" />
          </van-space>

        </van-popup>
        <!-- 更多按钮 -->
        <van-tab v-if="postType !== 1">
          <template #title>
            <span class="formb-title"> {{ $t("Form.AuditCord") }}</span>
          </template>
          <van-steps direction="vertical" style="overflow: scroll;" :active="activeId" class="over-time">
            <TimeLine v-for="(history, index) in historyData" :key="index" :hdata="history" />
          </van-steps>
        </van-tab>
      </van-tabs>
    </div>
    <!-- 提交弹出 -->
    <van-popup v-model:show="submitShow" position="top">
      <div class="top-content">
        <div class="tool-bar">
          <van-button size="small" hairline style="padding: 0px 20px; height: 32px;" @click="hadlerPop(1)">{{
            $t('Global.Cancel') }}</van-button>
          <van-button size="small" type="primary" hairline style="padding: 0px 20px; height: 32px;float: right;"
            @click="hadlerPop(2)">
            {{ $t("Global.Confirm") }}
          </van-button>
        </div>
        <div class="content">
          <van-field v-model="opinion" :required="true" type="textarea" :autosize="{ maxHeight: 110, minHeight: 110 }"
            :placeholder="$t('Form.OpinionTips')" />
          <van-cell center :title="$t('Form.AddSign_tips')" v-if="consignEnable">
            <template #right-icon>
              <van-switch v-model="openSign" />
            </template>
          </van-cell>
          <div v-show="consignEnable && openSign">
            <van-divider>
              {{ $t("Form.Signature_information") }}
            </van-divider>
            <YZConsign />
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 提交弹出 -->

    <!-- 表单加载遮罩 -->
    <van-overlay :show="showLoading" class-name="form-loading">
      <div class="wrapper" @click.stop>
        <van-loading type="spinner" />
      </div>
    </van-overlay>
    <!-- 职位切换 -->
    <van-popup v-model:show="pistionShow" position="bottom">
      <van-picker :columns="positionColumns" @confirm="onPostionChange" @cancel="pistionShow = false" />
    </van-popup>
    <!-- 密码输入 -->
    <van-dialog v-model:show="showPass" :title="$t('login.Enter_password_tips')" @confirm="onPassConfirm"
      :before-close="() => false" @cancel="onPassCancel" show-cancel-button>
      <van-field v-model="passBind" :placeholder="$t('login.Enter_password_tips')" />
    </van-dialog>
  </div>
</template>

<script lang="ts">
import { useForm } from "@/logic/forms";
import { useCallBackClose, usePlatForm } from "@/utils";
import { nextTick } from "vue";
import { onMounted } from "vue";
import { getCurrentInstance } from "vue";
import { defineComponent, createApp } from "vue";
import TimeLine from "@/components/TimeLine.vue";
import YZConsign from "@/components/YZConsign.vue";
import { themVarValues } from "@/theamVar";


export default defineComponent({
  props: {
    procesShow: {
      type: Boolean,
      default: () => false,
    },
    taskId: {
      type: String,
      default: "",
    },
    loadType: {
      type: String,
      default: "",
    },
    formService: {
      type: Object,
      default: {},
    },
    // 外部传入，不要展示header,true不展示；false展示
    noShowHeader: {
      type: Boolean,
      default: false,
    },
    reformRead: {
      type: Object,
      default: {},
    }
  },
  setup(props) {
    const instance = getCurrentInstance();
    const {
      formProcessTitle,
      btns,
      submitShow,
      formRef,
      fieldsRefs,
      onSubmit,
      hadlerPop,
      tabTitle,
      headerShow,
      onClickLeft,
      activeTab,
      onTabChange,
      showLoading,
      activeStep,
      fatureData,
      opinion,
      openType,
      recordStep,
      reordData,
      planForm,
      planObj,
      isThird,
      moreBtns,
      moreShow,
      onMoreClick,
      postioinData,
      changePostion,
      pistionShow,
      positionColumns,
      onPostionChange,
      consignEnable,
      headData,
      onRedirectUser,
      tabHeaderShow,
      openSign,
      postType,
      historyData,
      activeId,
      showPass,
      passBind,
      onPassConfirm,
      onPassCancel,
      groupIdArr,
      viewColsData
    } = useForm(instance, props);
    const closeTest = () => {
      useCallBackClose(function () { }, true);
    };
    return {
      closeTest,
      openSign,
      formProcessTitle,
      btns,
      submitShow,
      formRef,
      fieldsRefs,
      onSubmit,
      hadlerPop,
      tabTitle,
      headerShow,
      onClickLeft,
      activeTab,
      onTabChange,
      activeStep,
      fatureData,
      opinion,
      openType,
      recordStep,
      reordData,
      showLoading,
      planForm,
      planObj,
      isThird,
      moreBtns,
      moreShow,
      onMoreClick,
      postioinData,
      changePostion,
      pistionShow,
      positionColumns,
      onPostionChange,
      consignEnable,
      themVarValues,
      headData,
      onRedirectUser,
      tabHeaderShow,
      postType,
      historyData,
      showPass,
      activeId,
      passBind,
      onPassConfirm,
      onPassCancel,
      groupIdArr,
      viewColsData
    };
  },
  components: {
    TimeLine,
    YZConsign,
  },
});
</script>
<style lang="scss" scoped>
::v-deep(.van-nav-bar) {
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
}

::v-deep(.van-field__label--required:before) {
  display: none;
}

::v-deep(.van-field__label--required:after) {
  margin-left: 2px;
  color: var(--van-field-required-mark-color);
  content: "*";
}

.form-head {
  width: 100%;
  height: 46px;
  background: red;
}

.form-content {
  height: calc(100vh - 48px);
  margin: 0px auto;
  overflow: hidden;
  background: var(--yz-form-content);

  .over-time {
    overflow-y: scroll;
    height: calc(100vh - 100px);
    overflow-x: hidden;
  }

  .formb-title {
    font-family: PingFangSC-Medium;
    font-size: 17px;
    color: #333333;
    text-align: center;
    font-weight: 500;
  }

  ::v-deep(.van-tabs__line) {
    width: 80px;
  }

  .form-p {
    margin: 5px 5px;
  }

  .form-main-start {
    height: calc(100vh - 7.5rem - env(safe-area-inset-bottom));
    // height: calc(100vh - 200px - constant(safe-area-inset-bottom)); old
    height: calc(100vh - 7rem - env(safe-area-inset-bottom));
  }

  .form-main-audit {
    height: calc(100vh - 147px - constant(safe-area-inset-bottom));
    height: calc(100vh - 147px - env(safe-area-inset-bottom));
  }

  .form-main {
    margin: 10px;
    border-radius: 6px;
    overflow-x: hidden;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch !important;

    .form-item-div>div {
      position: relative;

      .clearAfter {
        // background: olivedrab;
        display: none; //ucuc信息文字 
      }
    }

    .form-item-div>div::after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: var(--van-padding-md);
      bottom: 0;
      left: var(--van-padding-md);
      border-bottom: 1px solid var(--van-cell-border-color);
      transform: scaleY(0.5);
    }

    // :deep() {
    //   .van-cell {
    //     font-size: var(--yz-com-13);
    //   }
    // }
  }

  .form-main>.form-item-div:nth-child(1) {
    //未展示的图片 hide
    display: none;
    // background: greenyellow;
  }

  .form-main>.form-item-div:nth-child(3) {
    //单号 hide
    display: none;
    // background: yellowgreen;

  }

  .form-main>.form-item-div:nth-child(24) {
    // 整改措施 hide
    // display: none;
    // background: fuchsia;
  }

  .form-main>.form-item-div:nth-child(25) {
    // 整改图片 hide
    // display: none;
    // background: forestgreen;
  }

  .form-main>.form-item-div:nth-child(26) {
    // 处理人 hide
    // display: none;
  }

  .form-main>.form-item-div:nth-child(29) {
    // 复核人姓名 hide
    // display: none;
  }

  .form-main>.form-item-div:nth-child(30) {
    // 复核结果 hide
    // display: none;
  }

  .form-main>.form-item-div:nth-child(31) {
    // 复核意见 hide
    // display: none;
    // background: #333333;
  }

  .form-main>.form-item-div:nth-child(32) {
    // 复核补充附件 hide
    // display: none;
    // background: #26bebe;

  }

  .form-main>.form-item-div:nth-child(41) {
    // 审批历史 hide
    // background: #bdc0c0;
  }

  .foot-btn {
    position: fixed;
    bottom: 0px;
    height: 32px;
    width: 100%;
    padding: 7px 6px;
    background: var(--yz-div-background);
    border-top: 1px solid var(--van-cell-border-color);

    .more {
      font-size: var(--yz-com-13);
      color: #1989fa;
      text-align: center;
    }
  }

  .left-image {
    height: 75px;
    width: 75px;
    border-radius: 50%;
    margin-left: 5px;

    img {
      width: 100%;
      height: 75px;
    }
  }

  .center-div {
    p {
      margin: 5px 0px;
      padding: 0px;
      font-size: var(--yz-font-size);
      font-weight: 600;
    }

    span {
      font-size: var(--yz-com-13);
      color: #bdc0c0;
      display: block;
    }
  }

  // :deep() .van-cell.van-field.van-field--disabled {
  // background-color: var(--yz-disabled);
  // background:transparent; //字段灰色，无法选择。
  // }
}

::v-deep(.van-cell) {
  font-size: var(--yz-com-13);
}

.form-content>div>.van-tabs__content>div>.van-cell--clickable {
  //mark uauc发起职位 hide
  display: none;
}

.top-content {
  width: 100%;

  .tool-bar {
    height: 32px;
    padding: 4px;
    border-bottom: 1px solid #e8e8e8;
  }
}

.btn-div {
  float: left;
  margin-top: 7px;
  margin-right: 3px;
}

.btn-div-start {
  width: 90%;
  margin: 4px 18px;
}

.form-footer {
  height: 50px;
  width: 100%;
  position: fixed;
  bottom: 0px;
  border-top: 1px solid rgb(182, 179, 179);
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}

.form-loading {
  background: #2320208a;
}

.hidden-header :deep .form-main-audit {
  height: calc(100vh - 105px - constant(safe-area-inset-bottom));
  height: calc(100vh - 105px - env(safe-area-inset-bottom));
}

.hidden-header .form-root-tabs :deep() .van-tabs__wrap {
  display: none;
}

::v-deep(.van-tabs__wrap) {
  //mark uauc表单 hide
  display: none;
  // background: #26bebe;
}

::v-deep(.van-field__label) {
  // label(key)字体
  color: #828282;
  width: 81px; //左侧字段 label 宽度
}

::v-deep(.van-field__control) {
  // value字体
  color: #00A0E1;
}

::v-deep(.van-field__right-icon) {
  // 查找🔍icon 
  color: #00A0E1;
}

::v-deep(.van-cell__value) {
  color: #00A0E1;
}

::v-deep(.van-field__control:disabled) {
  color: #00A0E1 !important;
  -webkit-text-fill-color: #00A0E1 !important;
}

::v-deep(.sticky-btn) {
  //添加行 hide
  display: none;
}

::v-deep(.van-step__circle) {
  background: #00a0e1 !important;
  width: 8px !important;
  height: 8px !important;
}
</style>
