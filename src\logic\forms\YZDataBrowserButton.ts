import { useLang } from "@/utils";
import { Y<PERSON><PERSON>onfig, Y<PERSON><PERSON><PERSON>, Y<PERSON><PERSON>ty<PERSON> } from "./formModel";
export class YZDataBrowserButtonConfig extends YZConfig {
    private dataBrowser: any
    private defaultRules: Array<any>;
    private dlgConfig: any
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZDataBrowserButton'

        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.PleaseSelect')}${this.label}` : useLang('Form.Select_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
        this.dataBrowser = parmas['dataBrowser']
        this.dlgConfig = parmas['extends']['dlgConfig']
    }
    getDlgConfig() {
        return this.dlgConfig
    }
    getDataBoweswer() {
        return this.dataBrowser
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZD<PERSON>cStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZDataBrowserButton extends YZField {
    private desc: string;
    constructor(params: any) {
        super(params)
        this.desc = params['__config__'].extends['$html']
    }
    initConfig(config: any): YZConfig {
        const newConfig = new YZDataBrowserButtonConfig(config)
        return newConfig
    }
    initStyle(style: any): YZStyle<object> {

        return new YZDescStyle(style)
    }
    getDesc() {
        return this.desc
    }
}