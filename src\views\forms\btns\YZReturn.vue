<template>
    <div>
        <van-button size="small" hairline type='default' :disabled="!btn.enable" @click="rtOnClick"
            style="padding: 0px 20px; height: 32px;">
            {{ btn.text }}
        </van-button>

        <!-- 退回弹框 -->
        <van-dialog v-model:show="rtShow" :title="$t('Form.TaskReturn')" :closeOnPopstate="false" @confirm="onrtConfirm"
            @cancel="onrtCancle" :before-close="retrunBefore" show-cancel-button>
            <div class="dialog-div">
                <van-field name="checkboxGroup" :label="$t('Form.ReturnTo')">
                    <template #input>
                        <span>{{ $t('Form.SpecificSteps') }}</span>
                        <!-- <van-radio-group v-model="ReturnData.checked" @change="onSelectStep" direction="horizontal">
                            <van-radio :name="1">
                                {{ $t('Form.applicant') }}
                            </van-radio>
                            <van-radio :name="2">
                                {{ $t('Form.SpecificSteps') }}
                            </van-radio>
                        </van-radio-group> -->
                    </template>
                </van-field>
                <van-field name="checkboxGroup" v-model="ReturnData.stepName" :label="$t('Form.SpecificSteps')">
                    <template #input>
                        <van-checkbox-group v-model="ReturnData.stepUser" direction="vertical">
                            <van-checkbox :name="step.StepID" v-for="step in stepData" :key="step.StepID">
                                {{ step.NodeDisplayName }}/({{ step.HandlerDisplayName }})
                            </van-checkbox>
                        </van-checkbox-group>
                    </template>
                </van-field>
                <van-field v-model="ReturnData.message" rows="3" autosize :label="$t('Form.ReturnOpinion')"
                    type="textarea" />
            </div>
        </van-dialog>
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { useUserStore } from '@/store/users';
import { useCallBackClose, useLang, useParamsFormatter } from '@/utils';
import { PropType, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { url } from '@/api/url'
import { useGetQuery, usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { reactive } from 'vue';
import { onMounted } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { useProcessStore } from '@/store/process';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const rtShow = ref<boolean>(false)
const route = useRoute()
const userStore = useUserStore()
const router = useRouter()
const taskList = ref<any[]>([])
const stepData = ref<Array<any>>([])
const taskIdData = ref<string>('')
const stepId = ref<string>('')
const processStore = useProcessStore()
const ReturnData = reactive({
    message: '',
    stepName: '',
    stepUser: []
})
onMounted(() => {
    taskIdData.value = processStore.getProcLoad.taskId
    stepId.value = processStore.getProcLoad.stepId
    getReturnSteps()
})
const getReturnSteps = async () => {
    const newUrl = useParamsFormatter(url.process.getReturnSteps, {
        params1: stepId.value
    })
    const data = await useGetQuery(newUrl)
    if (data.success) {
        stepData.value = data.children
    } else {
        showNotify({ message: '无法获取退回的步骤', type: 'danger' })
    }
}
const rtOnClick = async () => {
    rtShow.value = true
}
const onrtConfirm = async () => {
    if (!ReturnData.message) {
        showNotify({ message: useLang('Form.ReturnOpinion_tips'), type: 'danger' })
        return
    }
    // 退回到指定步骤
    if (ReturnData.stepUser.length == 0) {
        showNotify({ message: useLang('Form.ReturnStep'), type: 'danger' })
    } else {
        const newUrl = useParamsFormatter(url.process.returnToStep, {
            params1: taskIdData.value,
            params2: stepId.value
        })
        const data = await usePostBody(newUrl, {}, {
            comments: ReturnData.message,
            toStepIDs: ReturnData.stepUser
        })
        if (!data.success) {
            showNotify({ message: data.errorMessage, type: 'danger' })
            console.log('error-5')
        } else {
            showNotify({ message: useLang('Form.ReturnSuccess_tips'), type: 'success' })
            onrtCancle()
            eventBus.emit('onBack', true)
        }
    }
}
const onrtCancle = () => {
    ReturnData.message = ''
    ReturnData.stepName = ''
    ReturnData.stepUser = []
    rtShow.value = false
}
const retrunBefore = () => {
    return false
}
// const onSelectStep = async (val: number) => {
//     if (val === 2 && stepData.value.length <= 0) {
//         const newUrl = useParamsFormatter(url.process.getReturnSteps, {
//             params1: stepId.value
//         })
//         const data = await useGetQuery(newUrl)
//         if (data.success) {
//             stepData.value = data.children
//         } else {
//             showNotify({ message: '无法获取退回的步骤', type: 'danger' })
//         }
//     } else {
//         ReturnData.stepUser = ''
//         ReturnData.stepName = ''
//     }
// }
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    min-height: 26vh;
}
</style>