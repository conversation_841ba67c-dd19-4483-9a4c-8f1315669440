// 默认值设置
import store from "@/store";
import { useUserStore } from "@/store/users";
const userStore  = useUserStore(store)
export const useDefaultValueSetting =(code:string):string =>{
   const user =userStore.getUserInfo as any
    let defaultValue = ''
    for(let key in user) {
         const keyName = key.toLocaleLowerCase()
         const codeValue = code.toLocaleLowerCase()
         if (codeValue.indexOf(keyName)>-1) {
            defaultValue = user[key]
            break;
         }
    }
   return defaultValue
}