<template>
    <div>
        <!-- <PERSON><PERSON> need hide -->
        <!-- <YZNav>
            <template #title>
                {{ $t('LayOut.Home') }}
            </template>
</YZNav> -->
        <!-- <div class="search-div">
            <van-search shape="round" v-model="searchKwd" @search="onSearch" :placeholder="$t('home.SearchKeywords')" />
        </div> -->
        <!-- <PERSON><PERSON> need hide -->
        <div class="search-div">
            <div class="back" @click="router.back()"> <van-icon class="icon" name="arrow-left" /></div>
            <div class="search">
                <van-search shape="round" v-model="searchKwd" @search="onSearch" @clear="onSearch"
                    :placeholder="$t('home.SearchKeywords')" />
            </div>
            <div class="btn" @click="onSearch">{{ $t('New.Search') }}</div>
        </div>
        <div class="home-main">
            <div class="home-tab">
                <van-tabs v-model:active="chActiveTab" line-width="20"><!-- line-width="20" Linde-->
                    <!-- 常用流程 隐藏 -->
                    <!-- <van-tab>
                        <template #title>
                            <span class="home-title">{{ $t('home.CommonProcess') }}</span>
                        </template>
                        <div class="tab-content">
                            <van-collapse v-model="collActive">
                                <van-collapse-item name="1">
                                    <template #title>
                                        <div class="tab-title">
                                            {{ $t('home.RecentlyUsed') }}
                                        </div>
                                    </template>
                                    <van-empty :image="emptyimg" image-size="100" v-if="historyData.length <= 0">
                                        <template #description>
                                            <span class="desc-title">
                                                {{ $t('home.Empty_tips') }}
                                            </span>
                                        </template>
                                    </van-empty>
                                    <van-grid square :border="false" v-else>
                                        <YZGrid v-for="item in historyData" :key="item.ProcessName" :process="item"
                                            @onClick="onOpenForm" />
                                    </van-grid>
                                </van-collapse-item>
                                YZ need hide 我的收藏
                                <van-collapse-item name="2">
                                    <template #title>
                                        <div class="tab-title">
                                            {{ $t('home.MyCollection') }}
                                        </div>
                                    </template>
                                    <van-empty :image="emptyimg" image-size="100" v-if="process?.length <= 0">
                                        <template #description>
                                            <span class="desc-title"> {{ $t('New.NoProcess') }}
                                                <span class="desc-add" @click="addColl"> {{ $t('New.Add') }}
                                                </span>
                                            </span>
                                        </template>
                                    </van-empty>
                                    <van-grid square :border="false" v-else>
                                        <YZGrid v-for="item in process" :key="item.ProcessName" @onClick="onOpenForm"
                                            v-longpress="onLongPress" :attid="item.Id" :atttrue="item.IsCollection"
                                            :proname="item.ProcessName" :prover="item.ProcessVersion" :process="item" />
                                    </van-grid>
                                </van-collapse-item>
                                YZ need hide
                            </van-collapse>
                        </div>
                    </van-tab> -->
                    <!-- 全部流程 -->
                    <van-tab>
                        <template #title>
                            <span class="home-title">{{ $t('home.AllProcesses') }}</span>
                        </template>
                        <div class="tab-content">
                            <p class="pathLine">
                                <!-- <span v-for="(b, index) in defaultPath" :key="index" @click="breadClick(b)">
                                    {{ b.text }}<span v-if="index < defaultPath.length - 1"> / </span>
                                </span> -->
                                <span><span>{{ $t('home.All') }}</span> / HSE</span>
                            </p>
                            <van-grid square :border="false">
                                <YZFloderGrid v-for="(item, index) in treeProcess" v-longpress="onLongPress"
                                    :attid="item.fid" :atttrue="item.IsCollection" :isprocess="item.isProcess"
                                    :proname="item.text" :prover="item.ProcessVersion" :key="index" :process="item"
                                    @onGridClick="onFloderClick" />
                            </van-grid>
                            <van-empty :image="emptyimg" image-size="100" v-if="treeProcess?.length <= 0">
                                <template #description>
                                    <span class="desc-title">
                                        {{ $t('home.Empty_tips') }}
                                    </span>
                                </template>
                            </van-empty>
                            <!-- <van-grid square :border="false">
                                <van-grid-item icon="photo-o" text="文字"  v-for="n in 50 " :key="n">
                                    <i class="fa fa-folder" />
                                </van-grid-item>
                                <van-grid-item icon="photo-o" text="文字" />
                                <van-grid-item icon="photo-o" text="文字" />
                                <van-grid-item icon="photo-o" text="文字" />
                            </van-grid> -->
                            <!-- 非多级版   <van-collapse v-model="collActives">
                                <van-collapse-item :name="item.text" v-for="item in LibiaryData" :key="item.id">
                                    <template #title>
                                        <div class="tab-title">
                                            {{ item.text }}
                                        </div>
                                    </template>
                                    <van-empty image="imgs/empty.jpg" image-size="100"
                                        v-if="item.children.length <= 0">
                                        <template #description>
                                            <span class="desc-title">
                                                {{ $t('home.Empty_tips') }}
                                            </span>
                                        </template>
                                    </van-empty>
                                    <van-grid square :border="false" v-else>
                                        <YZGrid v-for="pItem in item.children" v-show="pItem.MobileInitiation" :key="pItem.ProcessName" :process="pItem"
                                            @onClick="onOpenForm" :attid="pItem.Id" :atttrue="pItem.IsCollection"
                                            :proname="pItem.ProcessName" :prover="pItem.ProcessVersion"
                                            v-longpress="onLongPress" />
                                    </van-grid>
                                </van-collapse-item>
                            </van-collapse> -->
                        </div>
                    </van-tab>
                </van-tabs>
            </div>
            <!-- 展开面板 -->
            <van-action-sheet v-model:show="actionShow" :actions="actions" :cancel-text="$t('Global.Cancel')"
                close-on-click-action @cancel="onCancel" @select="onSelect" :close-on-click-overlay="false" />
            <!-- 流程弹出框 -->
            <!-- 流程弹出框 -->
            <van-popup teleport="body" v-model:show="procesShow" style="height: 100vh;" position="bottom">
                <renderForm :key="time" />
            </van-popup>
            <!-- teleport="body" YZ + -->
        </div>

    </div>
</template>

<script lang="ts" setup>
import { useHome } from '@/logic/home/<USER>';
import YZNav from '@/components/YZNav.vue';
import YZGrid from '@/components/YZGrid.vue'
import YZFloderGrid from '@/components/YZFloderGrid.vue'
import emptyimg from '@/assets/imgs/empty.jpg'
import { useRouter } from 'vue-router'
const router = useRouter()

const { process,
    LibiaryData, chActiveTab, collActives, addColl, collActive,
    onLongPress, actionShow, actions, onCancel, onSelect, searchKwd,
    onOpenForm, onSearch, procesShow, time, historyData,
    onFloderClick,
    treeProcess,
    breadClick,
    defaultPath
} = useHome()


</script>
<style lang="scss" scoped>
// .search-div { // YZ need hide
//     width: 100%;
//     height: 54px;
//     background: var(--yz-div-background);
//     margin-top: 10px;
// }

.search-div {
    //Linde
    box-sizing: border-box;
    width: 100%;
    height: 48px;
    background: var(--yz-div-background);
    display: flex;
    position: sticky;
    top: 0;
    z-index: 99;

    .back {
        height: 100%;
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 10px;
        box-sizing: border-box;

        .icon {
            font-size: 20px;
            color: var(--yz-text-666);
        }
    }

    .search {
        flex: 1;
    }

    .btn {
        font-size: 14px;
        width: auto;
        padding-right: 10px;
        display: flex;
        align-items: center;
    }
}

.home-main {
    .home-tab {
        height: 44px;
        width: 100%;
        background: var(--yz-div-background);
        box-shadow: inset 0 -1px 0 0 #D9D9D9;

        .home-title {
            font-family: PingFangSC-Medium;
            font-size: var(--yz-home-title);
            color: var(--yz-text-333);
            text-align: center;
            font-weight: 500;
        }

        ::v-deep(.van-tabs__line) {
            width: 90px;
        }

        .tab-content {
            height: calc(100vh - 205px);
            overflow-x: hidden;
            overflow-y: scroll;
            width: 100%;

            .pm {
                font-family: PingFangSC-Medium;
                font-size: var(--yz-home-tabtitle);
                color: var(--yz-text-333);
                font-weight: 500;
                background: #fff;
                padding: 10px 5px;
                margin: 5px 0px 0px 0px;
            }

            .tab-title {
                font-family: PingFangSC-Medium;
                font-size: var(--yz-home-tabtitle);
                color: var(--yz-text-333);
                font-weight: 500;
            }

            .desc-title {
                font-family: PingFang-SC-ExtraLight;
                font-size: var(--yz-home-desctitle);
                color: #999999;
                text-align: center;
                font-weight: 400;
                margin-top: -25px;
                display: block;

                .desc-add {
                    font-family: PingFang-SC-ExtraLight;
                    font-size: var(--yz-home-desctitle);
                    color: #1989FB;
                    text-align: center;
                    line-height: 16px;
                    font-weight: 400;
                }
            }
        }
    }
}

.pathLine {
    font-size: 13px;
    color: #3f8bd8;
    // padding: 5px 8px 5px 8px;
    padding: 8px 8px 7px 25px; //只有全部流程
}

.van-tab {
    flex: none; //全部流程
}

::v-deep(.van-tab) {
    //全部流程
    flex: none !important;
    margin-left: 24px;
}
</style>