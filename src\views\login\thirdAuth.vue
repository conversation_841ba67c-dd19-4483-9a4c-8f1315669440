<template>
    <div></div>
</template>

<script lang="ts" setup>
import { usePostQuery } from '@/utils/request';
import { showLoadingToast, showNotify } from 'vant';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { url } from '@/api/url'
import { useLoingStore } from '@/store/login';
import { useUserStore } from '@/store/users';
import { useProcessStore } from '@/store/process';
import { useParamsFormatter, useQueryHashParams, useQueryParams } from '@/utils';
const router = useRouter()
const loginStore = useLoingStore()
const userStore = useUserStore()
const porcessStore = useProcessStore()
// let loading: any = {}
onMounted(async () => {
    const loading = showLoadingToast({
        message: '正在进行身份认证...',
        forbidClick: true,
        duration: 0
    });
    if (loginStore.getToken) {
        loading.close()
        console.log('当前获取到身份，准备构建单点', window.location.href)
        buildParamsToForm()
    } else {
        // 进行身份认证，构建单点登录链接
        let account = useQueryParams("account")
        if (!account)
            account = useQueryHashParams("account")
            const newUrl = useParamsFormatter(url.login.getThirdUser, { //third/checkUser/@params1
            params1: account
        })
        console.log("%c [ newUrl 222 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)

        const { success, auth, errorMessage } = await usePostQuery(newUrl)
        if (success) {
            loginStore.ssoLogin(auth)
        } else {
            showNotify({
                type: 'danger',
                message: '身份认证失败: ' + errorMessage
            })
        }
    }

})
const buildParamsToForm = async () => {
    const data = await userStore.setUserInfoAsync();
    const account = useQueryHashParams("account")
    if (data && account) {
        const processId = useQueryHashParams("processId")
        const stepId = useQueryHashParams("stepId")
        const taskid = useQueryHashParams("taskId")
        porcessStore.setHideTabHeader(true)
        if (processId) {
            porcessStore.setProcessId(processId)
            router.push({ path: '/layout/reform', replace: true })
        } else if (stepId) {
            porcessStore.setStepId(stepId)
            router.push({ path: '/layout/reform', replace: true })
        } else if (taskid) {
            const type = useQueryHashParams("type")
            porcessStore.setTaskId(taskid)
            porcessStore.setLoadType(type)
            router.push({ path: '/layout/reform', replace: true })
        }
    }
}
</script>
<style scoped>
.auth-back {
    height: 100vh;
    padding: 0px;
    margin: 0px;
    position: ov;
    background: #efefef;
    overflow: hidden;
}
</style>