import { IProcessInfo } from "@/logic/home/<USER>";
import { IUserInfo } from "@/logic/login/loginModel";
import { ConfigProviderTheme } from "vant";

export interface ILoginState {
   publicKey: string | undefined,
   keyStore: string | undefined,
   accessToken: string;
   tokenValidate: any;
   appName: string;
   applicant: string;
}
export interface IUserState {
   userInfo: IUserInfo | null
}

export interface IOutOffice {
   From: string | null;
   State: string | null
   To: string | null
}
export interface IHomeState {
   activePath: string;
   theme: ConfigProviderTheme;
   showtab: boolean;
   headerShow: boolean;
   allCount: number;
   workCount: number;
   readCount: number
   showFootBar: boolean;
   fontSize: string;
   historyData: Array<IProcessInfo>
}
export interface IProcessState {
   postInfo: any
   tabActive: string;
   procLoad: ILoadInfo;
   consignInfo: any;
   returnUrl: string;
   appUrlName: string;
   appUrlId: string;
   hideTabHeader: boolean;
   oumemberId: string;
}
export interface ILoadInfo {
   processId: string;
   actionType: number;
   instanceId: string;
   taskId: string;
   stepId: string;
   loadType: string;

}
