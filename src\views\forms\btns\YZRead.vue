<template>
    <div>
        <van-button size="small" hairline type="primary" :disabled="!btn.enable" icon="chat-o" @click="onClick"
            style="height: 32px;padding: 0px 20px;">
            {{ btn.text }}
        </van-button>
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { PropType } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/store/users';
import { useCallBackClose, useParamsFormatter } from '@/utils';
import { url } from '@/api/url';
import { usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { ProcessService } from '@/logic/forms/processHelper';
import { useProcessStore } from '@/store/process';
import { eventBus } from '@/utils/eventBus';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const route = useRoute()
const userStrore = useUserStore()
const router = useRouter()
var service = new ProcessService()
const processStore = useProcessStore()
const onClick = async () => {
    const taskId = processStore.getProcLoad.stepId
    const newUrl = useParamsFormatter(url.process.notifyRead, {
        params1: taskId
    })
    const data = await usePostBody(newUrl, {}, { comments: '' })
    if (!data.success) {
        showNotify({ type: 'danger', message: data.errorMessage })
        console.log('error-6')
    } else {
        await service.getWorkCount()
        console.log('2222')
        showNotify({ message: '处理完成', type: 'success' })
        useCallBackClose(() => {
            eventBus.emit('onBack', true)
        }, processStore.getHideTabHeader)
    }
}
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    height: 26vh;
}
</style>