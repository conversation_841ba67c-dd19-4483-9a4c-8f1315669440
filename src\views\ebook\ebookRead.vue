<template>
    <div>
        <YZNav isBack>
            <template #title>
                {{ FileItem.CourseName }}
                <div class="btn" @click="submit" v-if="!FileItem.IsRead">
                    {{ $t('CSTC.submit') }}
                </div>
            </template>
        </YZNav>
        <div class="pad" :class="{ 'pad1': FileItem.IsRead }">
            <div id="demo"></div>
        </div>
        <div class="check" v-if="!FileItem.IsRead">
            <van-checkbox v-model="checked" shape="square" :icon-size="16">{{ $t('CSTC.confirm_Readed')
                }}</van-checkbox>
        </div>
    </div>
</template>

<script setup>
import YZNav from '@/components/YZNav.vue';
import { showNotify } from 'vant';
import { onMounted, onUnmounted, ref, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from "@/store/users";
import { SetEbookRead } from '@/service/user'
import axios from 'axios'
import { useLoingStore } from '@/store/login';
import store from '@/store';
import { useCore } from '@/store/core';
import Pdfh5 from 'pdfh5';
import 'pdfh5/css/pdfh5.css';
import { debounce } from 'lodash'
import { useLang, TodaysDateFormat, formatDate, shortTimezoneName } from "@/utils";

const loginStore = useLoingStore(store)
const coreStore = useCore(store)
const route = useRoute()
const router = useRouter()
const user = useUserStore()
const userInfo = computed(() => user.getUserInfo)
const checked = ref(false)
const FileItem = ref({})
// const currentTime_ = ref(new Date())
// const timeVal = ref(currentTime_.toLocaleString().replace(/(\d{4})[^\d](\d{1,2})[^\d](\d{1,2}).*/, '$1-$2-$3').replace(/-(\d)\b/g, '-0$1')) //(new Date().toISOString().split('T')[0])
const formatOffsetToHhMm = (offsetInMins) => {
    let negative = offsetInMins < 0 ? "+" : "-";
    let positiveMins = Math.abs(offsetInMins);
    let hours = Math.floor(positiveMins / 60);
    let mins = Math.floor((positiveMins - ((hours * 3600)) / 60));
    if (hours < 10) { hours = "0" + hours; }
    if (mins < 10) { mins = "0" + mins; }
    return negative + hours + ':' + mins;
}
const TimezoneOffset = ref(shortTimezoneName(new Date()))  //(formatOffsetToHhMm(new Date().getTimezoneOffset()))
// 已阅读
const submit = debounce(async () => {
    if (!checked.value) return showNotify({ message: useLang('UAUC.Check_reading_tips') });
    let params = {
        "header": {
            "formId": ***************,
            "appId": ***************,
            "formState": "New"
        },
        "formData": {
            "PDF_File": FileItem.value.Id,
            "FileID": FileItem.value.FileId,
            "ApplicationAccount": userInfo.value?.Account, //user?.getAccount,
            "DepartmentNo": '',
            "IsRead": true,
            "TimezoneOffset": TimezoneOffset.value,
            "ApplicationDate": formatDate(new Date()), //TodaysDateFormat()
        }
    }
    // console.log("%c [ /app/instance/***************/rec/-1/appinfo/New/submit --- params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", params)

    await SetEbookRead(params)
    router.back()
}, 300)

const urlPDF_ = ref('')

const getDetail = async (item) => {
    await axios({
        method: 'GET',
        headers: {
            'Authorization': "Bearer " + loginStore.getToken,
            'Accept-Language': coreStore.$lang == 'en' ? 'en-US' : coreStore.$lang
        },
        url: `/bpm/attachment/file/${item.FileId}`,
        responseType: 'blob'
    }).then(res => {
        console.log("%c [ ebook--- res ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res)
        if (res.status === 200) {
            urlPDF_.value = res?.request?.responseURL
            new Pdfh5('#demo', {
                pdfurl: urlPDF_.value
            });
        } else {
            showNotify({ message: 'File does not exist.' })
            return
        }
    }).catch(err => {
        console.log(err)
    })
}

onMounted(() => {
    FileItem.value = JSON.parse(route.query.FileItem)
    if (FileItem.value) getDetail(JSON.parse(route.query.FileItem))
})

onUnmounted(() => {
})

</script>

<style lang="scss" scoped>
.pad {
    width: 100%;
    height: calc(100vh - 48px - 40px);
    box-sizing: border-box;
    overflow-y: auto;
}

.pad1 {
    height: calc(100vh - 48px);
}

.check {
    box-sizing: border-box;
    height: 40px;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 15px;
    box-shadow: 0 0 0 0.06rem rgba(0, 0, 0, 0.1);
    background-color: var(--yz-layout-mian);
    position: relative;
    z-index: 1999;
}

.btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 44px;
    margin-right: 10px;
    height: 100%;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>