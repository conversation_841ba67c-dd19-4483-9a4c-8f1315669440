import { Y<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON>ty<PERSON> } from "./formModel";
import { useLang } from "@/utils";
export class YZPasswordConfig extends YZConfig {
    private defaultRules: Array<any>;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZPassword'
        this.defaultRules = [{ required: true, message: this.label ? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips') }]
        if (this.required && this.writable) {
            this.rules = this.defaultRules
        }
    }
    getDefaultRule(): Array<any> {
        return this.defaultRules
    }
}
export class YZPasswordStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            style,
            tst: '111'
        }
    }
}
export class YZPassword extends YZ<PERSON>ield {
    private showPassword: boolean;
    private maxlength: number;
    private showLimit: boolean;
    private rigthIcon: string;
    constructor(params: any) {
        super(params)
        this.maxlength = 0
        if (!params.maxlength)
            this.maxlength = 15
        this.showPassword = params['show-password'] as boolean
        this.showLimit = params['show-word-limit'] as boolean
        this.rigthIcon = 'eye-o'
    }
    initConfig(config: any): YZConfig {
        return new YZPasswordConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZPasswordStyle(style)
    }
    getShowPass() {
        return this.showPassword
    }
    getMaxLength() {
        return this.maxlength
    }
    getShowlimit() {
        return this.showLimit
    }
    getDefaultIcon() {
        return this.rigthIcon
    }
}