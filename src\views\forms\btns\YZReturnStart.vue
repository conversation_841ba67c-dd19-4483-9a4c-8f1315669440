<template>
    <div>
        <van-button size="small" hairline type='default' :disabled="!btn.enable" @click="rtOnClick"
            style="padding: 0px 20px; height: 32px;">
            {{ btn.text }}
        </van-button>

        <!-- 退回弹框 -->
        <van-dialog v-model:show="rtShow" teleport="#app" :title="$t('Form.TaskReturn')" :closeOnPopstate="false"
            @confirm="onrtConfirm" @cancel="onrtCancle" :before-close="retrunBefore" show-cancel-button>
            <div class="dialog-div">
                <van-field name="checkboxGroup" :label="$t('Form.ReturnTo')">
                    <template #input>
                        <span>{{ $t('Form.applicant') }}</span>
                        <!-- <van-radio-group v-model="ReturnData.checked" @change="onSelectStep" direction="horizontal">
                            <van-radio :name="1">
                                {{ $t('Form.applicant') }}
                            </van-radio>
                            <van-radio :name="2">
                                {{ $t('Form.SpecificSteps') }}
                            </van-radio>
                        </van-radio-group> -->
                    </template>
                </van-field>
                <van-field v-model="ReturnData.message" rows="3" autosize :label="$t('Form.ReturnOpinion')"
                    type="textarea" />
            </div>
        </van-dialog>
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel } from '@/logic/forms/formModel';
import { useUserStore } from '@/store/users';
import { useCallBackClose, useLang, useParamsFormatter } from '@/utils';
import { PropType, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { url } from '@/api/url'
import { useGetQuery, usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { reactive } from 'vue';
import { onMounted } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { useProcessStore } from '@/store/process';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const rtShow = ref<boolean>(false)
const route = useRoute()
const userStore = useUserStore()
const router = useRouter()
const taskList = ref<any[]>([])
const stepData = ref<Array<any>>([])
const taskIdData = ref<string>('')
const stepId = ref<string>('')
const processStore = useProcessStore()
const ReturnData = reactive({
    message: '',
    stepName: '',
    stepUser: ''
})
onMounted(() => {
    taskIdData.value = processStore.getProcLoad.taskId
    stepId.value = processStore.getProcLoad.stepId
})
const rtOnClick = async () => {
    rtShow.value = true
}
const onrtConfirm = async () => {

    if (!ReturnData.message) {
        showNotify({ message: useLang('Form.ReturnOpinion_tips'), type: 'danger' })
        return
    }
    // 退回到申请人
    ///bpm/taskopt/task/421204142456901/step/421204440166469/returntoinitiator
    const newUrl = useParamsFormatter(url.process.returnToUser, {
        params1: taskIdData.value,
        params2: stepId.value
    })
    const data = await usePostBody(newUrl, {}, {
        comments: ReturnData.message,
        toStepIDs: []
    })
    if (!data.success) {
        showNotify({ message: useLang('Form.ReturnFailed'), type: 'danger' })
    } else {
        showNotify({ message: useLang('Form.ReturnSuccess_tips'), type: 'success' })
        onrtCancle()
        useCallBackClose(function () {
            eventBus.emit('onBack', true)
        }, processStore.getHideTabHeader)

    }
}
const onrtCancle = () => {
    ReturnData.message = ''
    ReturnData.stepName = ''
    ReturnData.stepUser = ''
    rtShow.value = false
}
const retrunBefore = () => {
    return false
}
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    // height: 16vh;
}
</style>