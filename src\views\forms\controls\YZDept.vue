<template>
    <div>
        <van-field v-if="!field.hidden" v-model="vModel" v-show="!field.hidden" :name="field.vModel.model"
            :label="field.config.label" :required="field.config.required" @update:model-value="setFun()"
            :key="field.vModel.model" disabled :rules="field.config.rules" :placeholder="field.placeholder" />
    </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/users';
import { YZInitiator } from '@/logic/forms/YZInitiator';
import { computed, PropType, ref, nextTick } from 'vue';
import { onMounted } from 'vue';
import { eventBus } from '@/utils/eventBus';
import { YZDept } from '@/logic/forms/YZDept';
import { useProcessStore } from '@/store/process';
import { useParamsFormatter } from '@/utils';
import { url } from '@/api/url';
import { useGetQuery } from '@/utils/request';
import { useCore } from '@/store/core';
const store = useProcessStore()
const userStore = useUserStore()
const props = defineProps({
    field: {
        type: Object as PropType<YZDept>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
onMounted(() => {

})
const emit = defineEmits(['update:modelValue', 'update:optionValue'],)
eventBus.on('setInitDept', {
    cb: function (params) {
        vModel.value = params
        props.field.expressTest(props.field, props.index, isFirstLoad.value)
        props.field.disableTest(props.field, props.index, isFirstLoad.value)
        props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    }
})
const isFirstLoad = ref<boolean>(true)
//  props.field.getData()
onMounted(() => {
    isFirstLoad.value = false
    getData() //modify
})
const getData = async () => {
    const info = store.getPostInfoData
    if (info) {
        const paramsValue = `${info.providerName}.${info.ouid}`
        const newUrl = useParamsFormatter(url.process.getInitDepart, {
            params1: info.providerName ? paramsValue : info.processInstance.ouid
        })
        // bpm/ou/qualified/bpmou.922
        console.log("%c [ userType 相关 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)

        useGetQuery(newUrl)
            .then(result => {
                //  this.vModel.value = result.OUName
                //  this.vModel.optionValue = result.providerName+"."+result.ouid
                vModel.value = result.OUName
                optionValue.value = result.providerName + "." + result.ouid
                const core = useCore()
                const intance = core.$update as any
                if (intance)
                    intance.update()
                nextTick(() => {
                    setFun()
                })
            }).catch(error => {
                console.error('初始化用户所属部门', error)
            })
    }
}
const vModel = computed({
    get() {
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
const optionValue = computed({
    get() {
        return props.optionValue
    },
    set(val) {
        emit('update:optionValue', val)
    }
})

const setFun = () => {
    // nextTick(() => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
    // })
}
eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
</script>
<style scoped></style>