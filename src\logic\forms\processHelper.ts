import { url } from "@/api/url";
import { useJsonToXml, useParamsFormatter } from "@/utils";
import { useGetQuery, usePostBody, useRequest } from "@/utils/request";
import { buttonProps, showNotify } from "vant";
import { ITaskResult, Y<PERSON><PERSON>ield } from "./formModel";
import { IYZColumn } from "./YZTable";
import { YZStepperConfig } from "@/logic/forms/YZStepper";
import i18n from '@/locale/index'

import {
  Priority,
  ProcessInstanceStartType,
  StartType,
  Status,
  TaskInstanceType,
} from "@/types/bpm";
import { SearchPanel } from "../work/workModel";
import { useHomeStore } from "@/store/home";
const homeStore = useHomeStore()
export interface IHeader {
  ouid: string;
  ownerPosition: string;
}
export interface IProcessForm {
  formData: any;
  header: IHeader;
}
export interface IProcessSubmit {
  processId: string;
  version: string;
  actionName: string;
  ouid: string;
  ownerPosition: string;
  formData: any;
  comments: string;
  consignEnabled: boolean,
  consignReturnType: string,
  consignRoutingType: string,
  consignUsers: Array<string>
}
export interface IProcessTask {
  taskId: string;
  uid: string;
  actionName: string;
  formData: any;
  additionalSignUids?: string[]
  comments?: string;
  stepId: string,
  consignEnabled: boolean,
  consignReturnType: string,
  consignRoutingType: string,
  consignUsers: Array<string>
}
export interface IProcessDarft extends IProcessForm {
  DraftGuid?: string;
}
export abstract class BaseTaskInfo {
  processVersion: string;
  processName: string;
  ownerAccount: string;
  endTime: string;
  processColor: string;
  processShortName: string;
  serialNum: string;
  processDefinitionId: string;
  processInstanceId: string;
  nodeName: string;
  reciveUser: string;
  processTime: string;
  processIcon: string;
  processInstanceOwner: string;
  description: string
  constructor(params: any) {
    this.processVersion = params["ProcessVersion"];
    this.processName = params["ProcessName"];
    this.ownerAccount = params["OwnerAccount"];
    this.endTime = params["endTime"];
    this.processShortName = params["processShortName"] ?? this.processName;
    this.processColor = params["IconColor"];
    this.processIcon = this.vaildIcon(params['ProcessIcon'] || params["IconCls"])
    this.serialNum = params["SerialNum"];
    this.processDefinitionId = params["ProcessId"];
    this.processInstanceId = params["activityId"];
    this.nodeName = params['NodeName']
    this.reciveUser = params['RecipientShortName']
    this.processTime = params['ReceiveAt'] ?? params['CreateAt']
    this.processInstanceOwner = params['OwnerDisplayName']
    this.description = params['Description']
  }
  vaildIcon(iconStr: string) {
    // 如果图标是直接icon-zhidu这种而且没有iconfont 添加iconfont
    if (iconStr && iconStr.includes('icon-') && !iconStr.includes('iconfont')) {
      iconStr = 'iconfont ' + iconStr
    }
    return iconStr
  }
}
export class MyRequestTask extends BaseTaskInfo {
  startType: StartType;
  serialNum: string;
  subject: string;
  startTime: string;
  businessKey: string;
  ouid: string;
  ownerPosition: string;
  initiatorAccount: string;
  aborted: true;
  rejected: true;
  status: Status | string;
  tagItem: Record<string, any>;
  taskId: string;
  constructor(params: any) {
    super(params);

    this.tagItem = {}
    this.startType = params["startType"];
    this.serialNum = params["SerialNum"];
    this.subject = params["subject"];
    this.startTime = params["CreateAt"];
    this.businessKey = params["businessKey"];
    this.ouid = params["ouid"];
    this.ownerPosition = params["ownerPosition"];
    this.initiatorAccount = params["initiatorAccount"];
    this.aborted = params["aborted"];
    this.rejected = params["rejected"];
    this.status = params["State"].State;
    this.taskId = params['TaskID']
    if (this.status === 'running') {
      this.tagItem.type = 'primary'
      this.tagItem.text = i18n.global.t('Work.Running')
    } else if (this.status === 'aborted') {
      this.tagItem.type = 'warning'
      this.tagItem.text = i18n.global.t('Work.Aborted')
    } else if (this.status === 'approved') {
      this.tagItem.type = 'success'
      this.tagItem.text = i18n.global.t('Work.Approved')
    } else if (this.status === 'rejected') {
      this.tagItem.type = 'danger'
      this.tagItem.text = i18n.global.t('Work.Rejected')
    }
  }
}
export class MyWorkTask extends BaseTaskInfo {
  taskId: string;
  stepId: string;
  TaskInstanceType: TaskInstanceType;
  activityName: string;
  activityId: string;
  processInstanceStartType: ProcessInstanceStartType;
  processInstanceSubject: string;
  processInstanceOwner: string;
  processInstanceOwnerShortName: string;
  processInstanceInitiator: string;
  processInstanceInitiatorShortName: string;
  assigneeAccount: string;
  startTime: string;
  dueDate: string;
  remindDate: string;
  durationStandard: 0;
  removeTime: string;
  priority: Priority;
  deleteReason: string;
  share: boolean;
  constructor(params: any) {
    super(params);
    this.TaskInstanceType = params["TaskInstanceType"];
    this.activityName = params["NodeName"];
    this.activityId = params["StepID"];
    this.stepId = params['StepID']
    this.taskId = params["TaskID"];
    this.processInstanceStartType = params["processInstanceStartType"];
    this.processInstanceSubject = params["processInstanceSubject"];
    this.processInstanceOwner = params["OwnerDisplayName"];
    this.processInstanceInitiator = params["OwnerAccount"];
    this.processInstanceOwnerShortName = params["OwnerDisplayName"];
    this.processInstanceInitiatorShortName = params["processInstanceInitiatorShortName"];
    this.assigneeAccount = params["assigneeAccount"];
    this.startTime = params["CreateAt"];
    this.dueDate = params["TimeoutDeadline"];
    this.remindDate = params["remindDate"];
    this.removeTime = params["durationStandard"];
    this.durationStandard = params["durationStandard"];
    this.priority = params["priority"];
    this.deleteReason = params["deleteReason"];
    this.share = params["share"];
  }
}
export class MyProcessedTask extends BaseTaskInfo {
  startType: StartType;
  subject: string;
  startTime: string;
  businessKey: string;
  ouid: string;
  ownerPosition: string;
  initiatorAccount: string;
  aborted: boolean;
  rejected: boolean;
  status: string;
  tagItem: Record<string, any>;
  taskId: string;
  constructor(params: any) {
    super(params);
    this.startType = params["startType"];
    this.subject = params["subject"];
    this.businessKey = params["businessKey"];
    this.ouid = params["ouid"];
    this.startTime = params["CreateAt"];
    this.initiatorAccount = params["initiatorAccount"];
    this.ownerPosition = params["ownerPosition"];
    this.aborted = params["aborted"];
    this.rejected = params["rejected"];
    this.status = params["status"];
    this.taskId = params['TaskID']
    // const item = buildType(this.status,this.aborted,this.rejected);
    this.tagItem = {}
    this.status = params["State"].State;
    if (this.status === 'running') {
      this.tagItem.type = 'primary'
      this.tagItem.text = i18n.global.t('Work.Running')
    } else if (this.status === 'aborted') {
      this.tagItem.type = 'warning'
      this.tagItem.text = i18n.global.t('Work.Aborted')
    } else if (this.status === 'approved') {
      this.tagItem.type = 'success'
      this.tagItem.text = i18n.global.t('Work.Approved')
    } else if (this.status === 'rejected') {
      this.tagItem.type = 'danger'
      this.tagItem.text = i18n.global.t('Work.Rejected')
    }
  }
}
export class MyNotifyTask extends BaseTaskInfo {
  taskId: string;
  TaskInstanceType: TaskInstanceType;
  activityName: string;
  activityId: string;
  processInstanceStartType: ProcessInstanceStartType;
  processInstanceSubject: string;
  processInstanceOwner: string;
  processInstanceInitiator: string;
  processInstanceOwnerShortName: string;
  processInstanceInitiatorShortName: string;
  assigneeAccount: string;
  startTime: string;
  dueDate: string;
  remindDate: string;
  durationStandard: 0;
  removeTime: string;
  priority: Priority;
  deleteReason: string;
  share: true;
  stepId: string;
  constructor(params: any) {
    super(params);
    this.taskId = params["TaskID"];
    this.stepId = params['StepID']
    this.TaskInstanceType = params["TaskInstanceType"];
    this.activityName = params["NodeName"];
    this.activityId = params["StepID"];
    this.processInstanceStartType = params["processInstanceStartType"];
    this.processInstanceSubject = params["processInstanceSubject"];
    this.processInstanceOwner = params["processInstanceOwner"] ?? params['OwnerDisplayName'];
    this.processInstanceInitiator = params["processInstanceInitiator"];
    this.processInstanceOwnerShortName = params["processInstanceOwnerShortName"];
    this.processInstanceInitiatorShortName = params["processInstanceInitiatorShortName"];
    this.assigneeAccount = params["assigneeAccount"];
    this.startTime = params["CreateAt"];
    this.dueDate = params["dueDate"];
    this.remindDate = params["remindDate"];
    this.removeTime = params["durationStandard"];
    this.durationStandard = params["durationStandard"];
    this.priority = params["priority"];
    this.deleteReason = params["deleteReason"];
    this.share = params["share"];
  }
}
export interface TaskCard {
  processIcon: string;
  processName: string;
  processNo: string;
  processTime: string;
}
interface IProcessService {
  /**
   * 保存草稿
   * @param darft 存稿数据
   */
  SaveDarft(darft: IProcessDarft, json: string): Promise<string | undefined>;
  /**
   * 更新存稿
   * @param darft 存稿数据
   */
  UpdateDarft(darft: IProcessDarft, json: string): Promise<void>;
  /**
   * 提交流程
   * @param process 流程发起
   */
  SubmitProcess(postData: IProcessSubmit): Promise<any>;

  /**
   *  审核流程
   * @param postData 流程审核
   */
  ProcessTask(postData: IProcessTask): Promise<any>
  /**
   *  构建表单数据
   * @param fieldsRefs 表单数据
   */
  buildSumbitTable(fieldsRefs: any): {};
  /**
   * 初始化审批预测
   */
  initFature(
    version: string,
    processId: string,
    postdata: any
  ): Promise<any>;
  /**
   * 我的申请打开时，获取的流程的审批预测
   * @param processId 流程实例iD
   * @param postdata 发送数据
   */
  myRequestFature(processId: string, postdata: any): Promise<any>
  /**
   *  我的待办打开时，获取流程的审批预测
   * @param taskid 任务ID
   * @param postdata  发送的数据
   */
  myWorkFature(taskid: string, postdata: any): Promise<any>
  /**
   *  获取我的申请
   * @param search 查询条件
   */
  getMyRequest(search: SearchPanel): Promise<ITaskResult>;

  /**
   *  获取我的代办
   * @param search 查询条件
   */
  getMyWorkList(search: SearchPanel): Promise<ITaskResult>;
  /**
   *  获取我的已办
   * @param search 查询条件
   */
  getMyProcessed(search: SearchPanel): Promise<ITaskResult>;

  /**
   *  获取我的知会
   * @param search 查询条件
   */
  getMyNotifys(search: SearchPanel): Promise<ITaskResult>;
  /**
   * 获取待办、知会 数
   */
  getWorkCount(): Promise<void>;
}
export class ProcessService implements IProcessService {
  async getWorkCount(): Promise<void> {
    // const { total } = await useGetQuery(url.work.getWorkListCount)
    // console.log("%c [ 待阅页面 ==》》 获取待办数量 1 total ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", total)
    // const { total: ReadCount } = await useGetQuery(url.work.getWorkReadCount) //1
    // console.log("%c [ 待阅页面 ==》》 待阅 1 ReadCount ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", ReadCount)
    // homeStore.setAllCount(homeStore.getWorkCount + ReadCount) //2
    // homeStore.setAllCount(total + ReadCount)
    // homeStore.setReadCount(ReadCount) //3
    // homeStore.setWorkCount(total)
    homeStore.setWorkCount(homeStore.getWorkCount) //4
  }
  async SaveDarft(
    darft: IProcessDarft,
    json: string
  ): Promise<string | undefined> {
    return undefined;
  }
  async UpdateDarft(darft: IProcessDarft, json: string): Promise<void> {
    return undefined;
  }
  async SubmitProcess(postData: IProcessSubmit): Promise<any> {
    const newUrl = useParamsFormatter(url.process.submitProcess, {
      params1: postData.processId,
      params2: postData.version,
    });
    const body = {
      formData: postData.formData,
      header: {
        actionName: postData.actionName,
        comments: postData.comments,
        ouid: postData.ouid,
        ownerPosition: postData.ownerPosition,
        consignEnabled: postData.consignEnabled,
        consignReturnType: postData.consignReturnType,
        consignRoutingType: postData.consignRoutingType,
        consignUsers: postData.consignUsers
      }
    }
    const result = await usePostBody(newUrl, {}, body, true);
    return result;
  }
  async ProcessTask(postData: IProcessTask): Promise<any> {
    const newUrl = useParamsFormatter(url.process.processAudit, {
      params1: postData.stepId
    });
    console.log("%c [ 待审批，submit --- newUrl ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", newUrl)
    const result = await usePostBody(newUrl, {}, {
      formData: postData.formData,
      header: {
        actionName: postData.actionName,
        additionalSignUids: postData.additionalSignUids,
        comments: postData.comments,
        consignEnabled: postData.consignEnabled,
        consignReturnType: postData.consignReturnType,
        consignRoutingType: postData.consignRoutingType,
        consignUsers: postData.consignUsers
      }
    }, true);
    console.log("%c [ 待审批，submit --- result ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", result)
    return result;
  }
  async initFature(
    version: string,
    processId: string,
    postdata: any
  ): Promise<any> {
    const newUrl = useParamsFormatter(url.process.getforecastProcess, {
      params1: processId,
      params2: version,
    });
    const result = await usePostBody(newUrl, {}, postdata)
    return result;
  }
  async myRequestFature(
    processId: string,
    postdata: any
  ): Promise<any> {
    const newUrl = useParamsFormatter(url.process.getRequestForecast, {
      params1: processId
    });
    const result = await usePostBody(newUrl, {}, postdata)
    return result;
  }
  async myWorkFature(taskid: string, postdata: any) {
    const newUrl = useParamsFormatter(url.process.getWorkForecast, {
      params1: taskid
    });
    const result = await usePostBody(newUrl, {}, postdata)
    return result;
  }

  buildSumbitTable(fieldsRefs: any): {} {
    const buildTable: any = {};
    if (fieldsRefs && fieldsRefs.length > 0) {
      for (let i = 0; i < fieldsRefs.length; i++) {
        const field = fieldsRefs[i] as YZField;
        if (field.vModel.model) {
          switch (field.config.ctype) {
            case "YZSelect":
            case "YZCheckBox":
            case "YZUploader":
            case "YZImageUpload":
            case "YZStepper":
            case "YZInitiator":
            case "YZOwner":
            case "YZDept":
              buildTable[field.vModel.model] = field.vModel.optionValue;
              break;
            case "YZTable":
              buildTable[field.vModel.model] = [];
              if (field.vModel.value && field.vModel.value.length > 0) {
                field.vModel.value.forEach((rows: any) => {
                  const columns = rows.colums as IYZColumn[];
                  const json: any = {};
                  // 隐藏的控件数据不要加入后台
                  // let noHiddenCols = columns.filter(fcol => !fcol.field.hidden)
                  if (columns.length > 0) {
                    columns.forEach((col) => {
                      if (col.columnCode) {
                        switch (col.field.config.ctype) {
                          case "YZSelect":
                          case "YZCheckBox":
                          case "YZUploader":
                          case "YZImageUpload":
                          case 'YZStepper':
                            json[col.columnCode] = col.field.vModel.optionValue;
                            break;
                          case "YZTable":
                            json[col.columnCode] = this.buildDetailData(
                              col.field.vModel.value
                            );
                            break;
                          case "YZAddress":
                            let addValue = col.field.vModel.value
                            if (col.field.vModel.optionValue)
                              addValue += " " + col.field.vModel.optionValue
                            json[col.columnCode] = addValue
                            break;
                          default:
                            json[col.columnCode] = col.field.vModel.value;
                            break;
                        }
                      }
                    });
                    buildTable[field.vModel.model].push(json);
                  }
                });
              }
              break;
            case "YZAddress":
              let addValue = field.vModel.value
              if (field.vModel.optionValue)
                addValue += " " + field.vModel.optionValue
              buildTable[field.vModel.model] = addValue
              break;
            default:
              buildTable[field.vModel.model] = field.vModel.value;
              break;
          }
        }

      }
    }
    // console.log('表单数据',buildTable)
    return buildTable;
  }
  async getMyRequest(search: SearchPanel): Promise<ITaskResult> {
    const dataArrys: Array<MyRequestTask> = [];
    const result: ITaskResult = {
      total: 0,
      children: dataArrys
    }
    const { children: myRequests, total } = await this.buildTask(
      url.work.getMyRequest,
      search,
    );
    if (myRequests && myRequests.length > 0) {
      myRequests.forEach((item: any) => {
        const requestData = new MyRequestTask(item);
        dataArrys.push(requestData);
      });
    }
    result.total = total
    result.children = dataArrys
    return result;
  }
  async getMyWorkList(search: SearchPanel): Promise<ITaskResult> {
    // console.log("%c [ --search-- ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", search)
    const dataArrys: Array<MyWorkTask> = [];
    const result: ITaskResult = {
      total: 0,
      children: dataArrys
    }
    const { total, children: myRequests } = await this.buildTask(
      url.work.getMyWorks,
      search
    );
    console.log("%c [ 待审批 total 正式接口 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", total)
    homeStore.setWorkCount(total)
    if (myRequests && myRequests.length > 0) {
      myRequests.forEach((item: any) => {
        const requestData = new MyWorkTask(item);
        dataArrys.push(requestData);
      });
    }
    result.total = total
    result.children = dataArrys
    return result;
  }
  async getMyProcessed(search: SearchPanel): Promise<ITaskResult> {
    const dataArrys: Array<MyProcessedTask> = [];
    const result: ITaskResult = {
      total: 0,
      children: dataArrys
    }
    const { children: myRequests, total } = await this.buildTask(
      url.work.getMyProcessed,
      search
    );
    if (myRequests && myRequests.length > 0) {
      myRequests.forEach((item: any) => {
        const requestData = new MyProcessedTask(item);
        dataArrys.push(requestData);
      });
    }
    result.total = total
    result.children = dataArrys
    return result;
  }
  async getMyNotifys(search: SearchPanel): Promise<ITaskResult> {
    const dataArrys: Array<MyNotifyTask> = [];
    const { children: myRequests, total } = await this.buildTask(
      url.work.getMyNotifys,
      search
    );
    // console.log("%c [ 待阅 4 dc 待阅页面 这里没调用 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", total)

    homeStore.setReadCount(total)
    if (myRequests && myRequests.length > 0) {
      myRequests.forEach((item: any) => {
        const requestData = new MyNotifyTask(item);
        dataArrys.push(requestData);
      });
    }
    return {
      total,
      children: dataArrys
    }
  }
  private async buildTask(
    url: string,
    search: SearchPanel
  ): Promise<ITaskResult> {
    const result = await useGetQuery(url, search);
    if (result) {
      return {
        total: result.total,
        children: result['children']
      }
    } else {
      return {
        total: result.total,
        children: []
      }
    }
  }
  private buildDetailData(field: YZField): [] {
    const table: any[] = [];
    field.vModel.value.forEach((rows: any) => {
      const columns = rows.colums as IYZColumn[];
      const json: any = {};
      if (columns.length > 0) {
        columns.forEach((col) => {
          if (col.columnCode) {
            switch (col.field.config.ctype) {
              case "YZSelect":
              case "YZCheckBox":
              case "YZUploader":
                json[col.columnCode] = col.field.vModel.optionValue;
                break;
              case "YZTable":
                json[col.columnCode] = table.push(
                  ...this.buildDetailData(col.field.vModel.value)
                );
                break;
              default:
                json[col.columnCode] = col.field.vModel.value;
                break;
            }
          }
        });
        table.push(json);
      }
    });
    return [];
  }
}
export const buildType = (status: Status, abort: boolean, reject: boolean) => {
  let text = "处理中";
  let type: any = "primary";
  switch (status) {
    case "Completed":
      text = "已批准";
      type = "success";
      break;
    case "Runnable":
      if (abort) {
        text = "已撤销";
        type = "warning";
      } else if (reject) {
        text = "已拒绝";
        type = "danger";
      } else {
        text = "处理中";
        type = "primary";
      }
      break;
    case "Terminated":
      text = "已结束";
      type = "danger";
      break;
    case "Suspended":
      text = "已暂停";
      type = "warning";
      break;
  }
  return {
    text,
    type,
  };
};
// 提取根据控件类型 获取对应的值 目前只有step组件存在问题
export const useFieldOptionValue = (field: YZField) => {
  switch (field.config.ctype) {
    case 'YZStepper':
      return Number(field.vModel.optionValue)
      break;

    default:
      return field.vModel.value
      break;
  }
}
// 币种转换成数字
export const currencyTurnToNum = (value: string | number, stepConfig: YZStepperConfig) => {
  let returnVal;
  if (!value) {
    returnVal = ''
  } else if (typeof value === 'string') {
    //是否开启了币种
    if (stepConfig.currency)
      value = value.replace(stepConfig.currency, '')
    //是否开启了千分位
    if (stepConfig.thousands)
      value = value.replaceAll(',', '')
    if (stepConfig.digt !== -1)
      value = parseFloat(value).toString()
    returnVal = value
  } else {
    returnVal = value
  }
  return returnVal
}
export const currencyFormatValue = (value: string, stepConfig: any, isNotice: boolean = true): string => {
  // voptionModel.value = value
  if (!value && value != '0') {
    return value
  }
  // 判断是否符合数字要求
  const isValidate = parseFloat(value)
  if (isNaN(isValidate)) {
    return ''
  }
  // 判断是否开启小数位
  if (stepConfig.digt == -1 && isNotice || stepConfig.digt == 0 && isNotice) {
    let vaildValue = typeof (value) === 'number' ? String(value) : value
    if (vaildValue.indexOf('.') > -1) {
      showNotify({ message: '不允许输入小数,请开启小数配置', type: 'danger' })
      return ''
    }
  }
  // 如果开启了小数位
  if (stepConfig.digt !== -1) {
    const num: number = parseInt(value)
    if (num !== isValidate) {
      const length = String(value).split('.')[1].length
      const jd = stepConfig.digt //小数精度
      /**  这段被注释了，不太理解为什么小数位数不一样就return了，现在直接修改位数执行了 */
      // if (length > jd) {
      //   showNotify({ message: '仅仅允许保留' + jd + '位小数', type: 'danger' })
      //   return ''
      // }
      // let fex = String(value).split('.')[1]
      // for (let i = length; i < stepConfig.digt; i++) {
      //   fex += '0'
      // }
      // fex = Number(fex).toFixed(stepConfig.digt)
      // value = num + '.' + fex
      value = Number(value).toFixed(jd)
    } else {
      // 不包含小数,根据位数补充
      if (stepConfig.digt > 0) {
        // let fex = ''
        // for (let i = 0; i < stepConfig.digt; i++) {
        //   fex += '0'
        // }
        // value = num + '.' + fex

        value = Number(value).toFixed(stepConfig.digt)
      }

    }
  }
  // 判断是否开启了千分位
  if (stepConfig.thousands) {
    //如果开启了千分位
    const num: any = parseInt(value).toString()
    const resove = [...num].reverse().join('')
    const numarrys: Array<string> = []
    const pushArrys: Array<string> = []
    let j = 1
    for (let i = 0; i < resove.length; i++) {
      const charValue = resove[i]
      pushArrys.push(charValue)
      if (pushArrys.length === num.length) {
        numarrys.push(charValue)
      } else {
        numarrys.push(charValue)
        if (pushArrys.length > 0 && pushArrys.length % 3 === 0) {
          numarrys.push(',')
        }

      }
      // if (j === 3) {
      //   numarrys.push(charValue)
      //   if (resove.length > 3 && i !=resove.length - 1)
      //     numarrys.push(',')
      //   j = 1
      // } else {
      //   numarrys.push(charValue)
      // }
      // j++
    }

    let endValue = numarrys.reverse().join('')
    if (endValue.startsWith(','))
      endValue = endValue.substring(1)

    if (stepConfig.digt !== -1) {
      if (value.indexOf('.') > -1) {
        value = endValue + '.' + value.split('.')[1]
      } else {
        value = endValue
      }

    } else {
      value = endValue
    }
  }
  // 判断是否开启了币种
  if (stepConfig.currency) {
    value = stepConfig.currency + value
  }
  return value
}