<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div class="btn">
                <van-icon name="filter-o" size="20" @click="rightShow" />
                <van-popup v-model:show="isShow" position="right" :style="{ width: '82%', height: '100%' }">
                    <div>
                        <div class="li_">
                            <span class="name">{{ $t('CSTC.EMail') }}</span>
                            <van-field v-model="User_Account" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">{{ $t('SSI.OperatorName') }}</span>
                            <van-field v-model="User_Name" clearable />
                        </div>
                    </div>
                    <div
                        style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
                        <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset')
                            }}</van-button>
                        <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm">{{
                            $t('Global.Confirm')
                            }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="list">
            <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                :loosing-text="$t('Form.Release_to_refresh')" :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loadData.loading" :finished="loadData.finished"
                    :finished-text="$t('Form.NoMore')" :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div v-for="(i, idx) in viewColsData" :key="idx">
                        <div class="li" @click="toOperatorDetail(i)">
                            <div class="li-l">
                                <div class="li-n ellipsis"><span class="name">{{ $t('CSTC.EMail') }}</span>
                                    <span class="val_">
                                        {{ i.User_Account }}
                                    </span>
                                </div>
                                <div class="li-m ellipsis"><span class="name">{{ $t('SSI.OperatorName') }}</span>
                                    <span class="val_">{{
                                        i.User_Name }}</span>
                                </div>
                            </div>
                            <div class="li-r">
                                <van-icon name="arrow" size="20" />
                            </div>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useFormData } from '@/utils'
import { useGetQuery, usePostBody } from '@/utils/request'
import { useUserStore } from "@/store/users"

const route = useRoute()
const user = useUserStore()
const router = useRouter()

const searchValue = ref('')
const searchValue_ = ref('')
const isShow = ref(false)
const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    // pageSize: config.getDataBoweswer()?.pageSize || 10
    pageSize: 20
})
const User_Account = ref('')
const User_Name = ref('')
const viewColsData = ref([])
const search_ = ref([])

const comeBack = () => {
    router.push({
        path: '/ssiRegister',
        replace: true
    })
}
const rest = () => {
    User_Account.value = ''
    User_Name.value = ''
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const onSearch = () => {
    loadData.pageIndex = 1
    onLoad()
}
const searchParams = computed(() => {
    return [
        { name: 'User_Account', op: 'like', value: User_Account.value, isAll: false },
        { name: 'User_Name', op: 'like', value: User_Name.value, isAll: false }
    ].filter(param => param.value)
})
const confirm = () => {
    search_.value = searchParams.value;
    loadData.pageIndex = 1
    onLoad()
    isShow.value = false
}
const onRefresh = () => {
    searchValue.value = '' // 清空搜索框
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    viewColsData.value = []
    rest()
    onLoad()
}
const onLoad = async () => {
    // const res = await useGetQuery(`bpm/datasource/***************/table/VW_ProjectUserRole/params?_dc=${Date.now()}`);
    // console.log("%c [ VW_ProjectUserRole --- params ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", res)

    const SubjectType = new URLSearchParams(window.location?.search)?.get('LoginType') === 'AAD' ? 'Linde' : 'Supplier'
    const ff = window.btoa(JSON.stringify({ "Project_Number": { "op": "=", "value": route.query?.ProjectNo }, "Supplier_Code": { "op": "=", "value": route.query?.ContractorCode }, "Role_Name": { "op": "=", "value": "Contractor Operators" }, "Enabled": { "op": "=", "value": route.query?.ApplicableOperatorName }, "UserType": { "op": "=", "value": SubjectType } }))
    if (loadData.refreshing) {
        loadData.refreshing = false
        viewColsData.value = []
    }
    // if (location.hostname === 'localhost') {
    //     const data_ = [
    //         {
    //             User_Account: '<EMAIL>',
    //             User_Name: 'testxw'
    //         },
    //         {
    //             User_Account: '<EMAIL>',
    //             User_Name: 'Testxw'
    //         }
    //     ]
    //     viewColsData.value = data_
    // }
    const formdata = useFormData({
        f: ff,
        o: '',
        c: 'WyJVc2VyX0FjY291bnQiLCJVc2VyX05hbWUiXQ==', //["User_Account","User_Name"]
        lc: 'WyJVc2VyX0FjY291bnQiLCJVc2VyX05hbWUiXQ==',
        page: 1,
        start: 0,
        limit: 10,
    })
    try {
        const data = await usePostBody(`bpm/datasource/***************/table/VW_ProjectUserRole/paging?_dc=${Date.now()}`, {}, formdata) //项目、办公室 || ProjectNo
        console.log("%c [ 操作员名称 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)

        if (data.success) {
            viewColsData.value = data.children || []
            loadData.pageIndex++
            loadData.loading = false
            if (data.children.length < loadData.pageSize) {
                loadData.finished = true
                console.log("操作员名称选择 Select --- 停止刷新")
            }
            rest()
        }
    } catch (error) {
        console.log("操作员名称---error", error)
    }
}


const toOperatorDetail = (i) => {
    console.log("%c [ i ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", i)
    const Operator = {
        User_Account: i.User_Account,
        User_Name: i.User_Name
    }
    router.push({
        path: '/operatorDetail',
        query: {
            Operator: JSON.stringify(Operator)
        },
        replace: true
    })
}
onMounted(() => {
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #f9f9f9;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;

            .li_ {
                padding: 20px 13px 0px 13px;

                .van-field {
                    border-radius: 13px;
                    padding: 3px;
                    margin-top: 10px;
                    background: #f8f8f8;
                }

                .name {
                    color: #999999;

                }
            }
        }
    }

    .list {
        margin-top: 10px;

        .li {
            border-bottom: 1px solid #DCDFE6;
            background-color: var(--yz-div-background);
            padding: 10px 12px;
            display: flex;
            align-items: center;

            .li-l {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #444444;

                .name {
                    color: #999999;
                    margin-right: 40px;
                }

                .li-m {
                    margin-top: 10px;
                }
            }

            .li-r {
                width: 20px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>