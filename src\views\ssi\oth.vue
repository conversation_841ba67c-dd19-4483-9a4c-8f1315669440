<template>
        <h1>橙某人</h1>
        <button @click="scanner">扫码</button>&nbsp;
        <button @click="close">关闭</button>&nbsp;
        <div class="container">
                <!-- 可以自行提供video标签，也可以不提供，会自动生成 -->
                <!-- <video></video> -->
            </div>
        <h4>识别到的信息：<span style="color: red;">{{ message }}</span></h4>
</template>


<script setup>
import { ref } from "vue";
import Quagga from "quagga";


let message = ref('');


function scanner() {
    Quagga.init(
        {
            // 输入流，定义图形/视频来源
            inputStream: {
                // LiveStream(默认)/ImageStream/VideoStream
                type: "LiveStream",
                target: document.querySelector(".container"),
            },
            // 解码器
            decoder: {
                // 解码器类型: code_128_reader/code_39_reader/upc_reader/i2of5_reader/...
                readers: ["code_128_reader"],
            },
        },
        (err) => {
            if (err) {
                alert('请开启该浏览器的相机权限并重新进行加载。');
                return;
            };
            Quagga.start();
        }
    );
}


// 不断检测条形码位置，为图像/视频每帧不断进行调用
Quagga.onProcessed(result => {
    // 绘制实际检测的捕获框框
    const drawingCtx = Quagga.canvas.ctx.overlay;
    const drawingCanvas = Quagga.canvas.dom.overlay;
    // 下面会实时绘制绿色框、蓝色框、红色线三种图形，你可以对照下方的GIF图进行对比
    if (result) {
        // 绿色框
        if (result.boxes) {
            drawingCtx.clearRect(0, 0, parseInt(drawingCanvas.getAttribute("width")), parseInt(drawingCanvas.getAttribute("height")));
            result.boxes.filter(box => {
                return box !== result.box;
            }).forEach(box => {
                Quagga.ImageDebug.drawPath(box, { x: 0, y: 1 }, drawingCtx, { color: "green", lineWidth: 2 });
            });
        }
        // 蓝色框
        if (result.box) {
            Quagga.ImageDebug.drawPath(result.box, { x: 0, y: 1 }, drawingCtx, { color: "blue", lineWidth: 2 });
        }
        // 红色线
        if (result.codeResult && result.codeResult.code) {
            Quagga.ImageDebug.drawPath(result.line, { x: 'x', y: 'y' }, drawingCtx, { color: 'red', lineWidth: 3 });
        }
    }
});


// 正确定位并解码到条形码时触发
Quagga.onDetected(result => {
    const code = result.codeResult.code;
    if (message.value !== code) {
        message.value = code;
    }
});


function close() {
    Quagga.stop();
}
</script>


<style scoped>
.container {
    width: 100%;
    height: 60vh;
    position: relative;
    margin-top: 10px;
}

.container>video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.container>canvas.drawingBuffer {
    position: absolute;
    left: 0;
    top: 0;
    max-width: 100%;
    width: 100%;
    height: 100%;
}
</style>