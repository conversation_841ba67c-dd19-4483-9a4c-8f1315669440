import { Y<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YZ<PERSON>tyle } from "./formModel";
export class YZStaticImgConfig extends YZConfig {
    private  staticimgWidth:number;
    private staticimgHeight:number;
    private imgSrc:string;
    constructor(parmas: any) {
        super(parmas)
        this.ctype = 'YZStaticImg'
        this.staticimgWidth =parmas['staticimgWidth']
        this.staticimgHeight = parmas['staticimgHeight']
        this.showLabel = parmas['showlable']
        this.imgSrc = parmas['imgSrc']
        
    }
     getWidth():number {
        return this.staticimgWidth
     }
     getHeight():number {
        return this.staticimgHeight
     }
     getImgSrc():string{
        return this.imgSrc
     }
}
export class YZStaticImgStyle extends YZStyle<object> {
    constructor(params: object) {
        super(params)
    }
    initStyle(style: any): object {
        return {
            width: '100%',
            background: 'red',
            ...style,
        }
    }
}
export class YZStaticImg extends <PERSON><PERSON><PERSON><PERSON> {
    constructor(params: any) {
        super(params)
        this.vModel.value = ''
        this.vModel.optionValue = ''
    }
    initConfig(config: any): YZConfig {
        return new YZStaticImgConfig(config)
    }
    initStyle(style: any): YZStyle<object> {
        return new YZStaticImgStyle(style)
    }
}