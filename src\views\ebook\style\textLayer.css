[data-main-rotation="90"]{transform:rotate(90deg) translateY(-100%)}[data-main-rotation="180"]{transform:rotate(180deg) translate(-100%,-100%)}[data-main-rotation="270"]{transform:rotate(270deg) translateX(-100%)}.textLayer{position:absolute;text-align:initial;inset:0;overflow:clip;opacity:1;line-height:1;-webkit-text-size-adjust:none;-moz-text-size-adjust:none;text-size-adjust:none;forced-color-adjust:none;transform-origin:0 0;caret-color:CanvasText;z-index:0}.textLayer.highlighting{touch-action:none}.textLayer :is(span,br){color:transparent;position:absolute;white-space:pre;cursor:text;transform-origin:0% 0%}.textLayer .markedContent span:not(.markedContent),.textLayer>:not(.markedContent){z-index:1}.textLayer span.markedContent{top:0;height:0}.textLayer .highlight{--highlight-bg-color:rgb(180 0 170 / 0.25);--highlight-selected-bg-color:rgb(0 100 0 / 0.25);--highlight-backdrop-filter:none;--highlight-selected-backdrop-filter:none;margin:-1px;padding:1px;background-color:var(--highlight-bg-color);-webkit-backdrop-filter:var(--highlight-backdrop-filter);backdrop-filter:var(--highlight-backdrop-filter);border-radius:4px}@media screen and (forced-colors:active){.textLayer .highlight{--highlight-bg-color:transparent;--highlight-selected-bg-color:transparent;--highlight-backdrop-filter:var(--hcm-highlight-filter);--highlight-selected-backdrop-filter:var(--hcm-highlight-selected-filter)}}.textLayer .highlight.appended{position:initial}.textLayer .highlight.begin{border-radius:4px 0 0 4px}.textLayer .highlight.end{border-radius:0 4px 4px 0}.textLayer .highlight.middle{border-radius:0}.textLayer .highlight.selected{background-color:var(--highlight-selected-bg-color);-webkit-backdrop-filter:var(--highlight-selected-backdrop-filter);backdrop-filter:var(--highlight-selected-backdrop-filter)}.textLayer ::-moz-selection{background:rgba(0 0 255 / .25);background:color-mix(in srgb,AccentColor,transparent 75%)}.textLayer ::selection{background:rgba(0 0 255 / .25);background:color-mix(in srgb,AccentColor,transparent 75%)}.textLayer br::-moz-selection{background:0 0}.textLayer br::selection{background:0 0}.textLayer .endOfContent{display:block;position:absolute;inset:100% 0 0;z-index:0;cursor:default;-webkit-user-select:none;-moz-user-select:none;user-select:none}.textLayer .endOfContent.active{top:0}
