<template>
    <div class="notify-work-wrap">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="flex: 1;overflow-y: auto;">
            <van-list v-model:loading="loading" v-longpress="onListLongPress" :finished="finished"
                :finished-text="$t('Form.NoMore')" @load="onLoad">
                <div class="grap"></div>
                <YZCard v-for="item in Notifylist" :key="item.serialNum" :process="item" @click.stop="onClick(item)">
                    <template #checkbox v-if="showCheck">
                        <div style="padding-left: 10px;">
                            <van-checkbox-group v-model="selectCheck" @click="onCheckSelect(item)">
                                <van-checkbox :name="item.stepId"></van-checkbox>
                            </van-checkbox-group>
                        </div>
                    </template>
                    <template #default>
                        <div class="status-div">
                            {{ item.activityName }}
                        </div>
                    </template>
                </YZCard>
                <YZEmpty v-if="Notifylist.length <= 0" />
            </van-list>
        </van-pull-refresh>
        <div class="select-check-btn" v-if="showCheck">
            <van-button size="small" type="primary" :disabled="selectCheck.length == 0" @click="onBatchRead">{{
                $t('Form.Read')
            }}</van-button>
            <van-button size="small" type="danger" @click="checkCancel">{{ $t('Global.Cancel') }}</van-button>
        </div>
    </div>
    <van-popup teleport="body" v-model:show="procesShow" :style="{ height: '100%' }" position="bottom">
        <renderForm :key="time" />
    </van-popup>
</template>

<script lang="ts" setup>
import { MyNotifyTask } from '@/logic/forms/processHelper';
import { ProcessService } from '@/logic/forms/processHelper';
import { SearchPanel } from '@/logic/work/workModel';
import { useHomeStore } from '@/store/home';
import { useProcessStore } from '@/store/process';
import { useUserStore } from '@/store/users';
import { useLang, useShortTime } from '@/utils';
import { eventBus } from '@/utils/eventBus';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import YZCard from '@/components/YZCard.vue';
import YZEmpty from '@/components/YZEmpty.vue'
import { showConfirmDialog, showNotify } from 'vant';
import { usePostBody } from '@/utils/request';
import { url } from '@/api/url';
const router = useRouter()
const loading = ref<boolean>(false);
const finished = ref<boolean>(false);
const refreshing = ref<boolean>(false);
const pageIndex = ref<number>(1)
const pageSize = ref<number>(10)
const Notifylist = ref<Array<MyNotifyTask>>([])
const service = new ProcessService()
const procesShow = ref<boolean>(false)
const porcessStore = useProcessStore()
const homeStore = useHomeStore()
const time = ref<number>(0)
const searchF = ref<SearchPanel>()
const isSearch = ref<boolean>(false)
const emits = defineEmits<{
    clearKwd: []
}>()
const onRefresh = async () => {
    finished.value = false
    restart()
    isSearch.value = false
    emits('clearKwd')
    await onLoad()

}
const searchInfo = ref<SearchPanel>({
    PostDateType: 'period',
    HistoryTaskType: 'MyRequest',
    byYear: 1,
    Year: new Date().getFullYear(),
    page: 1,
    start: 0,
    limit: 10
})
const restart = () => {
    pageIndex.value = 1
    loading.value = true
    finished.value = false
    Notifylist.value = []
}

const onLoad = async () => {
    let search = new SearchPanel({ limit: 20, start: 0, page: 1 })
    if (refreshing.value) {
        refreshing.value = false
        await service.getWorkCount()
    }
    if (isSearch.value && searchF.value)
        search = searchF.value
    await toSearch(search)
    loading.value = false
}
const onClick = (task: MyNotifyTask) => {
    console.log("%c [ Notify---task ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", task)
    if (showCheck.value) {
        onCheckSelect(task)
    } else {
        if (task.processShortName.includes('SSI')) {
            router.push({ path: '/ssi_apply', query: { next: 'notify', taskId: task.taskId, stepId: task.stepId, serialNum: task.serialNum } }) // 去ssi
        } else {
            porcessStore.setTaskId(task.taskId)
            porcessStore.setStepId(task.stepId)
            time.value = new Date().getTime()
            procesShow.value = true
        }
    }
}
const toSearch = async (search: SearchPanel) => {
    const start = (pageIndex.value - 1) * pageSize.value
    const limit = pageSize.value
    const page = pageIndex.value
    search.page = page
    search.limit = limit
    search.start = start
    const data = await service.getMyNotifys(search)
    console.log("%c [ 正式 待阅 dc total ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)

    if (data.children.length <= 0 || data.total <= Notifylist.value.length) {
        finished.value = true
        pageIndex.value = 1
    } else {
        Notifylist.value.push(...data.children)
        pageIndex.value++
        finished.value = false
        const leftDiv = document.querySelector('.left-div') as any;
        // console.log("%c [ 待阅页面 leftDiv ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", leftDiv)
        if (leftDiv) {
            leftDiv.style.setProperty('margin-bottom', '10px', 'important');
        }
    }
}
const showCheck = ref<boolean>(false)
const selectCheck = ref<any>([])
const onListLongPress = () => {
    if (!procesShow.value) {
        showCheck.value = true
    }
}
const onCheckSelect = (task: any,) => {

    let index = selectCheck.value.findIndex((item: any) => item == task.stepId)
    if (index >= 0) {
        selectCheck.value.splice(index, 1)
    } else {
        selectCheck.value.push(task.stepId)
    }
    // console.log(selectCheck.value)


    // selectPermiss(selectCheck.value,task.stepId)

}
// 批量已阅
const onBatchRead = () => {
    showConfirmDialog({ title: useLang('Form.OperateConfirm'), message: useLang('Form.Select_Confirm_tips') })
        .then(async () => {
            const postData = {
                items: selectCheck.value.map((item: any) => {
                    return {
                        ID: item,
                        StepID: item
                    }
                })
            }
            const data = await usePostBody(url.process.batchApprove, {
                Method: 'BatchApprove',
            }, postData);
            if (data.success) {
                let successStr = data.processedItems.map((el: any) => {
                    return `${el.SerialNum}：${el.Result}`
                }).join('\n')
                // console.log(successStr)
                showNotify({ type: 'success', message: successStr });
                checkCancel()
                restart()
                onLoad()
            }
        })
        .catch(() => {
            // on cancel
        });
}
// 批量已阅取消
const checkCancel = () => {
    showCheck.value = false //选择的checkbox隐藏
    selectCheck.value = [] //选中的数据清空
}
eventBus.on('onBack', {
    cb: function () {
        const appActive = porcessStore.getTabName
        if (appActive === 'NotifyWork') {
            procesShow.value = false
            homeStore.setTab(true)
            Notifylist.value = []
            pageIndex.value = 1
            onLoad()
        }

    }
})
eventBus.on('NotifyWork', {
    cb: function (params) {
        pageIndex.value = 1
        Notifylist.value = []
        if (params['resert']) {
            isSearch.value = false
            emits('clearKwd')
            onLoad()
        } else {
            const search = new SearchPanel(params)
            toSearch(search)
            searchF.value = search
            isSearch.value = true
        }
    }
})


eventBus.on('NotifyWorkReload', {
    cb: function (params) {
        onRefresh()
    }
})


onMounted(() => {
    console.log("%c [ 待阅页面 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")

})
</script>
<style lang="scss" scoped>
.notify-work-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.grap {
    width: 100%;
    height: 10px;
    background-color: #f9f9f9;
}

.status-div {
    width: 51px;
    height: 21px;
    background: #ffeedd;
    border: 0.5px solid #fbab66;
    border-radius: 3px;
    font-family: PingFang-SC-Medium;
    font-size: var(--yz-status-div);
    color: #fbab66;
    line-height: 21px;
    font-weight: 500;
    text-align: center;
    margin-top: 10px;
}

.van-list {
    background-color: var(--yz-background-vanlist);
}

.select-check-btn {
    display: flex;
    padding: 5px;
    align-items: center;

    button {
        padding: 0 15px;

        &:not(:last-child) {
            margin-right: 5px;
        }
    }
}

.van-list__finished-text {
    background: var(--yz-layout-mian) !important;
}
</style>