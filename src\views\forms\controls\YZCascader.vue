<template>
    <div>
        <van-field  v-if="!field.hidden" v-model="vModel" is-link readonly :name="field.vModel.model" :required="field.config.required"
            :placeholder="field.placeholder" :disabled="field.disabled" :rules="field.config.rules"
            :label="field.config.label" @click="onCaseClick" />
        <van-popup v-model:show="caseShow" round position="bottom">
            <van-cascader v-model="cascaderValue" :field-names="field.getProps()" :title="field.placeholder"
                :options="field.getOptions()" @close="onCaseCancle" @finish="onCaseConfirm" />
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import { YZCascader, YZCascaderConfig } from '@/logic/forms/YZCascader';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, PropType, ref } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZCascader>,
        required: true
    },
    modelValue: {
        type: [String],
        default: ''
    },
    optionValue: {
        type: [String],
        default: ''
    },
    index:{
        type:Number,
        default:-1
    }
})
const emit = defineEmits(['update:modelValue', 'update:optionValue'])
const config = props.field.config as YZCascaderConfig
const caseShow = ref<boolean>(config.getShow())
const cascaderValue = ref<string | number>()
const caseProps = props.field.getProps()
const onCaseClick = () => {
    config.setShow(true)
    caseShow.value = true
}
const onCaseConfirm = ({ selectedOptions }: any) => {
    const text = selectedOptions.map((option: any) => option[caseProps.text]).join('/');
    const value = selectedOptions.map((option: any) => option[caseProps.value]).join('/');
    config.setShow(false)
    caseShow.value = false
    emit('update:modelValue', text)
    emit('update:optionValue', value)
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const onCaseCancle = () => {
    config.setShow(false)
    caseShow.value = false
}
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field,props.index,isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})

eventBus.on('setFieldDisable',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.disabled = params.value 
       }
    }
})
eventBus.on('setFieldHidden',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.hidden = params.value 
       }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
             if(params.value ||  params ===1) {
                // 禁用验证
                props.field.config.required =false 
                props.field.config.rules = []
             } else {
                // 启用验证
                props.field.config.required = true 
                props.field.config.rules = config.getDefaultRule()     
             }
        }
    }
})
</script>
<style  scoped>

</style>