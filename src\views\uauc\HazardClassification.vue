<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchValue" @search="onSearch" @clear="onRefresh"
                    :placeholder="$t('New.Search')" />
            </div>
            <div class="btn">
                <van-icon name="filter-o" size="20" @click="rightShow" />
                <van-popup v-model:show="isShow" position="right" :style="{ width: '82%', height: '100%' }">
                    <div>
                        <div class="li_">
                            <span class="name">Code</span>
                            <van-field v-model="Code" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">Display Name</span>
                            <van-field v-model="DisplayName" clearable />
                        </div>
                        <div class="li_">
                            <span class="name">Value</span>
                            <van-field v-model="Value" clearable />
                        </div>
                    </div>
                    <div
                        style="display: flex; padding: 10px; justify-content: center;position: fixed; bottom: 0; left: 0; right: 0;">
                        <van-button type="default" style="width: 47%;" @click="rest">{{ $t('Global.Reset')
                            }}</van-button>
                        <van-button type="success" style="width: 47%;margin-left: 17px;" @click="confirm">{{
                            $t('Global.Confirm')
                            }}</van-button>
                    </div>
                </van-popup>
            </div>
        </div>
        <div class="list">
            <van-pull-refresh v-model="loadData.refreshing" @refresh="onRefresh"
                :loosing-text="$t('Form.Release_to_refresh')" :pulling-text="$t('Form.Pull_down_to_refresh')">
                <van-list v-model:loading="loadData.loading" :finished="loadData.finished"
                    :finished-text="$t('Form.NoMore')" :loading-text="$t('Form.Loading')" @load="onLoad">
                    <div v-for="(i, idx) in viewColsData" :key="idx">
                        <div class="li" @click="toHazardClassificationDetail(i)">
                            <div class="li-l">
                                <div class="li-n ellipsis"><span class="name">Code</span>{{ i.Code }}</div>
                                <div class="li-m ellipsis"><span class="name">Display Name</span>{{ i.DisplayName }}
                                </div>
                                <div class="li-m ellipsis"><span class="name">Value</span>{{ i.Value }}</div>
                            </div>
                            <div class="li-r">
                                <van-icon name="arrow" size="20" />
                            </div>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useFormData } from '@/utils'
import { usePostBody } from '@/utils/request'
import { useCore } from '@/store/core/index'

const router = useRouter()
const core = useCore()
const searchValue = ref(' ')
const isShow = ref(false)
const loadData = reactive({
    refreshing: false,
    loading: false,
    finished: false,
    pageIndex: 1,
    // pageSize: config.getDataBoweswer()?.pageSize || 10
    pageSize: 20
})
const Code = ref('')
const DisplayName = ref('')
const Value = ref('')
const viewColsData = ref([])

const comeBack = () => {
    router.push({
        path: '/uauc',
        replace: true
    })
}
const onSearch = () => {
    loadData.pageIndex = 1
    onLoad()
}
const rightShow = () => {
    rest()
    isShow.value = true
}
const rest = () => {
    Code.value = ''
    DisplayName.value = ''
    Value.value = ''
}
const searchParams = computed(() => {
    return [
        { name: 'Code', op: 'like', value: Code.value, isAll: false },
        { name: 'DisplayName', op: 'like', value: DisplayName.value, isAll: false },
        { name: 'Value', op: 'like', value: Value.value, isAll: false },
    ].filter(param => param.value)
})
const search_ = ref()
const confirm = () => {
    searchValue.value = ''
    search_.value = searchParams.value;
    loadData.pageIndex = 1
    onLoad()
    isShow.value = false
}
const onRefresh = () => {
    searchValue.value = ' ' // 清空搜索框
    loadData.loading = true
    loadData.pageIndex = 1
    loadData.finished = false
    viewColsData.value = []
    rest()
    onLoad()
}
const onLoad = async () => {
    const ff = window.btoa(JSON.stringify({ "DataType": { "op": "=", "value": "Hazard Category" }, "Language": { "op": "=", "value": core.$lang.split('-')[0] } }))
    const cc = window.btoa(JSON.stringify(["Code", "Value", "DisplayName"]))
    if (loadData.refreshing) {
        loadData.refreshing = false
        viewColsData.value = []
    }
    const search = [{ "name": "all", "op": "like", "value": searchValue.value !== ' ' ? searchValue.value.trim() : ' ', "isAll": true }]
    const formdata = useFormData({
        o: '',
        // start: (loadData.pageIndex - 1) * loadData.pageSize,
        // page: loadData.pageIndex,
        // limit: loadData.pageSize,
        f: ff,
        c: cc,
        // lc: cc,
        s: btoa(window.unescape(encodeURIComponent(JSON.stringify(searchValue.value ? search : search_.value))))
    })
    console.log("%c [ searchValue.value ? search : search_.value ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", searchValue.value ? search : search_.value)

    if (location.hostname === 'localhost') {
        const localRes = [
            {
                Code: "Category_01",
                DisplayName: "PPE\u4E2A\u4EBA\u9632\u62A4\u7528\u54C1",
                Value: "Personal Protective Equipment",
                RowNum: 1,
                TotalRows: 16
            },
            {
                Code: "Category_02",
                DisplayName: "\u6587\u660E\u65BD\u5DE5",
                Value: "Housekeeping",
                RowNum: 2,
                TotalRows: 16
            },
            {
                Code: "Category_03",
                DisplayName: "\u9AD8\u5904\u4F5C\u4E1A",
                Value: "Working at Height",
                RowNum: 3,
                TotalRows: 16
            },
            {
                Code: "Category_04",
                DisplayName: "\u811A\u624B\u67B6",
                Value: "Scaffolding",
                RowNum: 4,
                TotalRows: 16
            },
            {
                Code: "Category_05",
                DisplayName: "\u53D7\u9650\u7A7A\u95F4",
                Value: "Confined Space",
                RowNum: 5,
                TotalRows: 16
            },
            {
                Code: "Category_06",
                DisplayName: "\u6316\u6398\u4F5C\u4E1A",
                Value: "Excavation",
                RowNum: 6,
                TotalRows: 16
            },
            {
                Code: "Category_07",
                DisplayName: "\u5DE5\u5177&\u8BBE\u5907",
                Value: "Tools and Equipment",
                RowNum: 7,
                TotalRows: 16
            },
            {
                Code: "Category_08",
                DisplayName: "\u4F5C\u4E1A\u8BB8\u53EF",
                Value: "Work Permit",
                RowNum: 8,
                TotalRows: 16
            },
            {
                Code: "Category_09",
                DisplayName: "\u5371\u5316\u54C1",
                Value: "Hazardous Materials",
                RowNum: 9,
                TotalRows: 16
            },
            {
                Code: "Category_10",
                DisplayName: "\u540A\u88C5&\u8D77\u91CD",
                Value: "Lifting and Rigging",
                RowNum: 10,
                TotalRows: 16
            },
            {
                Code: "Category_11",
                DisplayName: "\u7535\u6C14",
                Value: "Electricity",
                RowNum: 11,
                TotalRows: 16
            },
            {
                Code: "Category_12",
                DisplayName: "\u4EA4\u901A",
                Value: "Traffic",
                RowNum: 12,
                TotalRows: 16
            },
            {
                Code: "Category_13",
                DisplayName: "\u73AF\u5883\u4FDD\u62A4\n",
                Value: "Environmental Protection\n",
                RowNum: 13,
                TotalRows: 16
            },
            {
                Code: "Category_14",
                DisplayName: "\u6D88\u9632",
                Value: "Fire Protection",
                RowNum: 14,
                TotalRows: 16
            },
            {
                Code: "Category_15",
                DisplayName: "\u6ED1\u5012\u3001\u7ECA\u5012\u548C\u8DCC\u5012",
                Value: "Slip, Trip and Fall",
                RowNum: 15,
                TotalRows: 16
            },
            {
                Code: "Category_16",
                DisplayName: "\u5176\u5B83",
                Value: "Other",
                RowNum: 16,
                TotalRows: 16
            }
        ]
        viewColsData.value = localRes
    } else {
        try {
            const data = await usePostBody('bpm/datasource/527365695868997/table/MS_Dictionary_I8N', {}, formdata) //危害分类
            console.log("%c [ 危害分类 data ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", data)
            if (data) {
                viewColsData.value = data || []
                // loadData.pageIndex++
                loadData.loading = false
                if (data.length < loadData.pageSize) {
                    loadData.finished = true
                    console.log("危害分类 --- 停止刷新")
                }
                rest()
            }
        } catch (error) {
            console.log("危害分类---error", error)
        }
    }
}

const toHazardClassificationDetail = (val) => {
    router.push({
        path: '/Hazard_classificationDetail',
        query: {
            list: JSON.stringify(val)
        },
        replace: true
    })
}
onMounted(() => {
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;
    background-color: #f9f9f9;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;

            .li_ {
                padding: 20px 13px 0px 13px;

                .van-field {
                    border-radius: 13px;
                    padding: 3px;
                    margin-top: 10px;
                    background: #f8f8f8;
                }

                .name {
                    color: #999999;

                }
            }
        }
    }

    .list {
        margin-top: 10px;

        .li {
            border-bottom: 1px solid #DCDFE6;
            background-color: var(--yz-div-background);
            padding: 10px 12px;
            display: flex;
            align-items: center;

            .li-l {
                flex: 1;
                font-size: var(--yz-com-14);
                color: #444444;

                .name {
                    color: #999999;
                    margin-right: 10px;
                }

                .li-m {
                    margin-top: 10px;
                }
            }

            .li-r {
                width: 20px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>