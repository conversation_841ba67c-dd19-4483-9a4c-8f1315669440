<template>
    <div>
        <van-button size="small" hairline type='default' :disabled="!btn.enable" icon="clock-o" @click="cbOnClick"
            style="padding: 0px 20px; height: 32px;">
            {{ btn.text }}
        </van-button>

        <van-dialog v-model:show="cbShow" :title="$t('Form.Urging')" :closeOnPopstate="false" @confirm="oncbConfirm"
            @cancel="oncbCancle" :before-close="cbBefore" show-cancel-button>
            <div class="dialog-div">
                <van-field name="checkboxGroup" :label="$t('Form.ExpeditedObject')">
                    <template #input>
                        <van-checkbox-group v-model="remaindData.remandUser" direction="vertical">
                            <van-checkbox :name="cb.StepID" shape="square" style="margin:5px 0px" v-for="cb in taskList"
                                :key="cb.StepID">
                                {{ cb.NodeDisplayName }}({{ cb.ShortName }})
                            </van-checkbox>
                        </van-checkbox-group>
                    </template>
                </van-field>
                <van-field v-model="remaindData.message" rows="1" autosize :label="$t('Form.message')"
                    type="textarea" />
            </div>
        </van-dialog>
    </div>
</template>

<script lang="ts" setup>
import { IBtnModel, IRemandTable } from '@/logic/forms/formModel';
import { useUserStore } from '@/store/users';
import { useLang, useParamsFormatter } from '@/utils';
import { eventBus } from '@/utils/eventBus';
import { PropType, ref } from 'vue';
import { useRoute } from 'vue-router';
import { url } from '@/api/url'
import { useGetQuery, usePostBody } from '@/utils/request';
import { showNotify } from 'vant';
import { MyWorkTask } from '@/logic/forms/processHelper';
import { reactive } from 'vue';
import { cos, re } from 'mathjs';
import { useProcessStore } from '@/store/process';
const props = defineProps({
    btn: {
        type: Object as PropType<IBtnModel>,
        required: true,
        default: {}
    },
    formRef: {
        type: Object,
        default: {}
    }
})
const cbShow = ref<boolean>(false)
const route = useRoute()
const userStore = useUserStore()
const taskList = ref<Array<IRemandTable>>([])
const taskIdRef = ref<string>('')
const remaindData = reactive({
    remandUser: [],
    message: ''

})
const processStore = useProcessStore()
const cbOnClick = async () => {
    const taskId = processStore.getProcLoad.taskId
    taskIdRef.value = taskId
    if (taskId && taskList.value.length <= 0) {
        const newUrl = useParamsFormatter(url.process.remandTask, {
            params1: taskId
        })
        const data = await useGetQuery(newUrl)
        if (data.code === 1) {
            showNotify({ message: data.message, type: 'danger' })
        } else {
            taskList.value = data as Array<IRemandTable>
        }
    }
    cbShow.value = true
}
const oncbConfirm = async () => {
    if (remaindData.remandUser.length <= 0) {
        showNotify({ message: useLang('Form.ExpeditedObject_tips'), type: 'danger' })
        remaindData.remandUser = []
    } else {
        const postJson: any = {
            comments: remaindData.message,
            targets: []
        }
        for (let i = 0; i < remaindData.remandUser.length; i++) {
            const item = remaindData.remandUser[i]
            postJson.targets.push({
                stepid: item,
                uids: taskList.value.filter(x => x.StepID === item).map(x => x.Account)
            })
        }
        const endurl = useParamsFormatter(url.process.remandToUser, {
            params1: taskIdRef.value
        })
        const data = await usePostBody(endurl, {}, postJson)
        if (data.success) {
            showNotify({ message: useLang('Form.ExpeditedSuccess_tips'), type: 'success' })
            cbShow.value = false
        } else {
            showNotify({ message: useLang('Form.ExpeditingFailed_tips'), type: 'danger' })
        }
    }
}
const oncbCancle = () => {
    remaindData.remandUser = []
    remaindData.message = ''
    cbShow.value = false
}
const cbBefore = () => {
    return false
}
</script>
<style lang="scss" scoped>
.submit-btn {
    padding: 0px 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: #1989fa;
    color: #fff;
    font-size: var(--yz-btn-14);
    border-radius: 5px;
}

.dialog-div {
    width: 100%;
    height: 26vh;
}
</style>