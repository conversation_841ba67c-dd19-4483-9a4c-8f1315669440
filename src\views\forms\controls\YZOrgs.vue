<template>
    <div>
        <van-field v-if="!field.hidden" :name="field.vModel.model" :required="field.config.required" is-link
            v-model="vModel" :rules="field.config.rules" :placeholder="field.placeholder" @click="handlerShow"
            :readonly="true" :label="field.config.label">
        </van-field>
        <YZOrgSelect v-model:show="orgShow" @onSave="onSave" :multiple="true" :default-orgs="defaultOrgs" />
    </div>
</template>

<script lang="ts" setup>
import { YZUser } from '@/logic/forms/YZUser';
import { computed, onMounted, PropType, ref } from 'vue';
import YZOrgSelect, { ISelectOrg } from '@/components/YZOrgSelect.vue';
import { eventBus } from '@/utils/eventBus';
import { YZOrgsConfig } from '@/logic/forms/YZOrgs';

const props = defineProps({
    field: {
        type: Object as PropType<YZUser>,
        required: true
    },
    modelValue: {
        type: String,
        default: ''
    },
    optionValue: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        default: -1
    }
})
const emit = defineEmits(['update:optionValue', 'update:modelValue'])
const config = props.field.config as YZOrgsConfig
const defaultOrgs = ref<ISelectOrg[]>([])
const handlerShow = () => {
    if (!props.field.disabled) {
        defaultOrgs.value = []
        if (props.modelValue && props.optionValue) {
            const names = props.optionValue.split(',')
            const ouids = props.modelValue.split(',')
            for (let i = 0; i < names.length; i++) {
                const name = names[i]
                const ouid = ouids[i].split('.')[1]
                defaultOrgs.value.push({
                    name: name,
                    ouid: ouid,
                    code: ''
                })
            }
        }
        orgShow.value = true
    }

}
const isFirstLoad = ref<boolean>(true)

onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        // props.field.expressTest(props.field, props.index, isFirstLoad.value)
        // props.field.disableTest(props.field, props.index, isFirstLoad.value)
        // props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
        // props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
        return props.optionValue
    },
    set(val) {
        emit('update:optionValue', val)
    }
})
const setFun = () => {
    props.field.expressTest(props.field, props.index, isFirstLoad.value)
    props.field.disableTest(props.field, props.index, isFirstLoad.value)
    props.field.hiddenTest(props.field, props.index, isFirstLoad.value)
    props.field.advancedValidate(props.field, props.index, isFirstLoad.value)
}
const orgShow = ref<boolean>(false)
const onSave = (user: ISelectOrg[]) => {
    defaultOrgs.value = []
    if (user && user.length > 0) {
        vModel.value = user.map(x => x.name).join(',')
        const value = user.map(x => 'bpmou.' + x.ouid).join(',')
        emit('update:modelValue', value)
    } else {
        vModel.value = ''
        emit('update:modelValue', '')
    }
    setFun()
}
eventBus.on('setMultipleOrg', {
    cb: function (params) {
        vModel.value = params.map((x: any) => x.text).join(",")
        setFun()
    }
})

eventBus.on('setFieldDisable', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.disabled = params.value
        }
    }
})
eventBus.on('setFieldHidden', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            props.field.hidden = params.value
        }
    }
})
eventBus.on('setFieldRequeired', {
    cb: function (params) {
        if (params.uuid === props.field.uuid) {
            if (params.value || params === 1) {
                // 禁用验证
                props.field.config.required = false
                props.field.config.rules = []
            } else {
                // 启用验证
                props.field.config.required = true
                props.field.config.rules = config.getDefaultRule()
            }
        }
    }
})
</script>
<style scoped></style>