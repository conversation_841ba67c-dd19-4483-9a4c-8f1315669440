 declare   global {
     interface Window {
        webConfig:IWebConfig;
        $ref:any;
        $fieldsRef:any;
        JSEncrypt:any;
        BJCordovaFuncs:any;
        cordova:any;
        FileTransfer:any;
        component:Array<string>;
        wx:any;
        dd:any;
        logOutApp:Function;
        uni:any 
        h5sdk:any;
        tt:any;
    }
}
interface IWebConfig {
    host:string;
    devhost:string;
    openForm:boolean;
    weChat:IWechatConfig;
    dingtalk:IDingTalkConfig;
    feishu:IFeiShuConfig;
    virtualPex:string;
    tokenTime:number;
    dymicComponent:string;
    loginLogoHtml:string
   
}
interface IWechatConfig {
    enable:boolean;
    appid:string;
    agentid:string;
    redirect:string;
}
interface IDingTalkConfig {
    enable:boolean;
    corpId:string;
    agentid:string;
}
interface IH5Login {
    enable:boolean;
}
interface IFeiShuConfig {
    appid:string;
}
export {}
