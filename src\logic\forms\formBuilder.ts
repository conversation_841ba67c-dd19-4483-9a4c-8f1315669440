import { <PERSON><PERSON>onfig, <PERSON><PERSON>rid<PERSON>ode<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./formModel";
import { YZTextBox } from "./YZTextbox";
import { YZTextArea } from "./YZTextArea";
import { YZPassword } from "./YZPassword";
import { YZRadio } from "./YZRadio";
import { YZCheckBox } from "./YZCheckBox";
import { YZSwitch } from "./YZSwitch";
import { YZSelect } from "./YZSelect";
import { YZStepper } from "./YZStepper";
import { YZSlider } from "./YZSlider";
import { YZDatePicker } from "./YZDatePicker";
import { YZRate } from "./YZRate";
import { YZCascader } from "./YZCascader";
import { YZUploader } from "./YZUploader";
import { YZTable } from "./YZTable";
import { YZImageUpload } from "./YZImageUpload";
import { YZUser } from "./YZUser";
import { YZUsers } from "./YZUsers";
import { YZOrg } from "./YZOrg";
import { YZOrgs } from "./YZOrgs";
import { YZAddress } from "./YZAddress";
import { deepCopy, useDeepClone, useLang, useUUID } from "@/utils";
import { YZCreateTime } from "./YZCreateTime";
import { YZSN } from "./YZSN";
import { YZInitiator } from "./YZInitiator";
import { YZDept } from "./YZDept";
import { YZSegmentbar } from "./YZSegmentbar";
import { YZGelocation } from "./YZGelocation";
import { YZDataBower } from "./YZDataBower";
import { YZOwner } from "./YZOwner";
import { YZDesc } from "./YZDesc";
import { YZTaskId } from "./YZTaskId";
import { YZItemId } from "./YZItemId";
import { YZParentItemId } from "./YZParentItemId";
import { YZFormTtile } from "./YZFormTtile";
import { YZValuetodisplay } from "./YZValuetodisplay";
import { DymicPanelBox } from "./DymicPanel";
import { useCore } from "@/store/core";
import { YZOwnerattr } from "./YZOwnerattr";
import { YZButton } from "./YZButton";
import { YZDataBrowserButton } from "./YZDataBrowserButton";
import { YZBarcode } from "./YZBarcode";
import { YZStaticImg } from "./YZStaticImg";
import { YZHistoryFormLink } from "./YZHistoryFormLink";
import { YZChildForm } from './YZChildForm'
import { YZDataView } from './YZDataView'
import { YZHtmleDitorArea } from './YZHtmleDitorArea'
const core = useCore()

export class formBuilder {
   static formConfig: FormConfig | null;
   static copyField: YZField[]
   proptyData: any;
   constructor() {
   }
   /**
    * 
    * @param type 控件类型
    * @param params 控件字段参数
    * @returns 
    */
   static builderField(type: string, params: any): YZField | null {
      const item = ControlRender[type] as Function
      if (item) {
         return item(params)
      } else {
         // 加载自定义动态控件
         return new DymicPanelBox(params)
      }
   }
   /**
    * 将ext控件配置转换为vue可读配置
    * @param data ext控件配置
    * @returns 返回控件配置
    */
   static builderExtField(data: any, targetFormId: string, groupIdArr: any[], viewModel: any): any {
      const mapJson: any = []
      const components = data.components
      const dics = formBuilder.GetLangDictionary()
      Object.keys(components).forEach(field => {
         let item = components[field]
         const json: any = {}
         item = formBuilder.ConvertLable(item, dics)
         switch (item.ctype) {
            case 'text':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "textbox",
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  defualtValue: item['defaultValue'],
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = item.readOnly ?? false;
               json['hidden'] = false
               json['disabled'] = false;
               json['colhidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'textarea':
               json['__XDataBind__'] = ['']
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  ctype: "textarea",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['type'] = 'textarea'
               json['placeholder'] = item.emptyText
               json['autosize'] = {
                  minRows: (Number(item.growMin) - 11) / 20,
                  maxRows: (Number(item.growMax) - 11) / 20,
               }
               json['style'] = {
                  'width': '100%'
               }
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = false
               json['disabled'] = false
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'password':
               json['__config__'] = {
                  label: '密码',
                  showLabel: true,
                  labelWidth: null,
                  changeTag: true,
                  layout: "colFormItem",
                  span: 24,
                  required: true,
                  ctype: "password",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  "prepend": "",
                  "append": ""
               }
               json['placeholder'] = useLang('login.password')
               json['"show-password'] = true
               json['style'] = {
                  'width': '100%'
               }
               json['clearable'] = true
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = false
               json['disabled'] = false
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'checkboxgroup':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  defaultValue: "",
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  optionType: "default",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "checkbox",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               const values = item['items']
               if (values && values.length > 0) {
                  json['__slot__'] = { options: [] }
                  values.forEach((v: any) => {
                     const option = {
                        label: v.boxLabel,
                        value: v.inputValue,
                        checked: v.checked
                     }
                     json['__slot__'].options.push(option)
                  })

               }
               json['style'] = {}
               json['size'] = 'medium'
               json['disabled'] = false;
               json['readonly'] = false;
               json['hidden'] = false
               json['colhidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? [],
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'datetime':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  ctype: "date",
                  showLabel: true,
                  labelWidth: null,
                  span: 24,
                  layout: "colFormItem",
                  required: item['allowBlank'] === false ? true : false,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['placeholder'] = item.emptyText
               json['type'] = 'date'
               json['style'] = {
                  width: '100%'
               }
               json['disabled'] = false

               json['clearable'] = true
               if (item.type === 'Ymd') {
                  json['format'] = 'yyyy-MM-dd'
               }
               json['readonly'] = false
               json['hidden'] = false
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'number':
               json['__config__'] = {
                  label: item.fieldLabel,
                  showLabel: true,
                  changeTag: true,
                  labelWidth: null,
                  span: 24,
                  layout: "colFormItem",
                  ctype: "stepper",
                  required: item['allowBlank'] === false ? true : false,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  thousands: item.thousands,
                  defaultValue: '',
                  formUID: item.uniqueId,
                  validators: item.validators,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr

               }
               json['placeholder'] = item.emptyText
               json['step-strictly'] = false
               json['controls-position'] = ''
               json['disabled'] = false
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['step'] = 1
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'combobox':
               json['__config__'] = {
                  label: item.fieldLabel,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  dataType: "static",
                  ctype: "select",
                  span: 24,
                  required: item['allowBlank'] === false ? true : false,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  map: item['$map'],
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['placeholder'] = item.emptyText
               const selectValue = item['options']
               json['dataType'] = 'dymic'
               if (selectValue && selectValue.length > 0) {
                  json['dataType'] = 'static'
                  json['__slot__'] = { options: [] }
                  json['__slot__'].options = selectValue

               }
               json['style'] = {
                  width: '100%'
               }
               json['disabled'] = false
               json['hidden'] = false
               json['clearable'] = true
               json['filterable'] = false
               json['multiple'] = false
               json['DSConfig'] = item.ds
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'switch':
               json['__config__'] = {
                  label: item.fieldLabel,
                  defaultValue: false,
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "switch",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['style'] = {}
               json['active-text'] = ''
               json['inactive-text'] = ''
               json['active-color'] = ''
               json['disabled'] = false
               json['hidden'] = false
               json['inactive-color'] = ''
               json['active-value'] = 1
               json['inactive-value'] = 0
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'attachment':
               const limit = item.fileSizeLimit
               const value = limit.replace(/[^0-9]/ig, '')
               json['__config__'] = {
                  label: item.fieldLabel,
                  layout: "colFormItem",
                  defaultValue: "",
                  showLabel: true,
                  labelWidth: null,
                  ctype: "upload",
                  span: 24,
                  showTip: false,
                  buttonText: "点击上传",
                  fileSize: parseFloat(value),
                  fileTypes: item.fileTypes,
                  sizeUnit: "MB",
                  required: item['allowBlank'] === false ? true : false,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  formUID: item.uniqueId,
                  validators: item.validators,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  'list-type': true
               }
               json['action'] = '/bpm/attachment/upload'
               json['disabled'] = false
               json['hidden'] = false
               json['accept'] = '.doc,docx,.ppt,.xls,.xlsx,.pdf'
               json['name'] = 'file'
               json['auto-upload'] = true
               json['list-type'] = 'text'
               json['multiple'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'imageattachment':
               json['__config__'] = {
                  label: item.fieldLabel,
                  layout: "colFormItem",
                  defaultValue: "",
                  showLabel: true,
                  labelWidth: null,
                  ctype: "imageUpload",
                  span: 24,
                  showTip: false,
                  buttonText: "点击上传",
                  fileSize: 100,
                  sizeUnit: "MB",
                  required: item['allowBlank'] === false ? true : false,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  'list-type': true
               }
               json['action'] = '/bpm/attachment/upload'
               json['disabled'] = false
               json['accept'] = 'image/*'
               json['name'] = 'file'
               json['auto-upload'] = true
               json['list-type'] = 'img'
               json['multiple'] = false
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'radiogroup':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  defaultValue: "",
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  optionType: "default",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "radio",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               const raidoValues = item['items']
               if (raidoValues && raidoValues.length > 0) {
                  json['__slot__'] = { options: [] }
                  raidoValues.forEach((v: any) => {
                     const option = {
                        label: v.boxLabel,
                        value: v.inputValue,
                        checked: v.checked
                     }
                     json['__slot__'].options.push(option)
                  })

               }
               json['style'] = {}
               json['size'] = 'medium'
               json['disabled'] = false;
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'user':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  defaultValue: "",
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  optionType: "default",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "user",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {}
               json['style'] = {}
               json['size'] = 'medium'
               json['disabled'] = false;
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['placeholder'] = item.fieldDesc
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'users':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  defaultValue: "",
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  optionType: "default",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "users",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {}
               json['style'] = {}
               json['size'] = 'medium'
               json['disabled'] = false;
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'],
                  optionValue: item['optionValue']
               }
               json['targetFormId'] = targetFormId
               json['placeholder'] = item.fieldDesc
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'ou':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  defaultValue: "",
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  optionType: "default",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "ou",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {}
               json['style'] = {}
               json['size'] = 'medium'
               json['disabled'] = false;
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['placeholder'] = item.fieldDesc
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break
            case 'ous':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  defaultValue: "",
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  optionType: "default",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "ous",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {}
               json['style'] = {}
               json['size'] = 'medium'
               json['disabled'] = false;
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['placeholder'] = item.fieldDesc
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'address':
               json['__config__'] = {
                  label: item['fieldLabel'],
                  defaultValue: "",
                  span: 24,
                  showLabel: true,
                  labelWidth: null,
                  layout: "colFormItem",
                  optionType: "default",
                  required: item['allowBlank'] === false ? true : false,
                  ctype: "address",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  hideDetailAddress: item.hideDetailAddress,
                  regionLevel: item.regionLevel,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {}
               json['style'] = {}
               json['size'] = 'medium'
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['placeholder'] = item.fieldDesc
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'starttime':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "createdate",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = useLang('Form.SystemGenerate'), //'系统自动生成'
                  json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'sn':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "sn",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = useLang('Form.SystemGenerate')  //'系统自动生成'
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'segmentbar':
               json['__config__'] = {
                  label: item.title,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "segmentbar",
                  required: true,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = ''
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'initiator':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "initiator",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = useLang('Form.Creator')  //'创建人'
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'] ?? item.fieldLabel,
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'dept':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "dept",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = '所属部门'
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'geolocation':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "geolocation",
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = '请选择您的位置'
               json['style'] = {}
               json['clearable'] = false;
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'databrowser':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "databrowser",
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  formId: new Date().getTime(),
                  renderKey: new Date().getTime(),
                  dataBrowser: item.dataBrowser,
                  enableValueConvert: item.enableValueConvert,
                  valueConvert: item.valueConvert,
                  formUID: item.uniqueId,
                  validators: item.validators,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = ''
               json['style'] = {}
               json['clearable'] = false;
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'owner':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "owner",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = '拥有者'
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'desc':
               json['__config__'] = {
                  label: '说明',
                  labelWidth: null,
                  showLabel: true,
                  ctype: "desc",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  formUID: item.uniqueId,
                  validators: item.validators,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = '说明'
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'formtitle':
               json['__config__'] = {
                  label: item.title,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "formtitle",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = '说明'
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'valuetodisplay':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "valuetodisplay",
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case "button":
               json['__config__'] = {
                  label: item.text,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "button",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['events'] = item.handler
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: field,
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'ownerattr':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "ownerattr",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = useLang('Form.OwnerAttribute') //'拥有者属性'
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case "databrowserbtn":
               json['__config__'] = {
                  label: item.text,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "databrowserbtn",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  dataBrowser: item.dataBrowser,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'barcode':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "barcode",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  barcodeFormat: item.barcodeFormat,
                  barcodeWidth: item.barcodeWidth,
                  barcodeHeight: item.barcodeHeight,
                  showlable: item.ShowLabel,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'staticimg':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "staticimg",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  // validators: item.validators,
                  formUID: item.uniqueId,
                  staticimgWidth: item.ImgWidth,
                  staticimgHeight: item.ImgHeight,
                  imgSrc: item.ImgSrc,
                  showlable: item.ShowLabel,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'historyformlink':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "historyformlink",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  showlable: item.ShowLabel,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'dataview':
               json['__config__'] = {
                  label: item.title,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "dataview",
                  required: false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  showlable: item.ShowLabel,
                  ds: item.ds,
                  enablePaging: item.enablePaging,
                  gridSettingColumns: item.gridSettingColumns,
                  pageSize: item.pageSize,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'childform':
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: "childform",
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  showlable: item.ShowLabel,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            case 'htmleditorarea':
               json['__XDataBind__'] = ['']
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  ctype: "htmleditorarea",
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               // json['type'] = 'textarea'
               // json['placeholder'] = item.emptyText
               json['autosize'] = {
                  minRows: (Number(item.growMin) - 11) / 20,
                  maxRows: (Number(item.growMax) - 11) / 20,
               }
               json['style'] = {
                  'width': '100%'
               }
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = false
               json['disabled'] = false
               json['hidden'] = false
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
            default:
               json['__config__'] = {
                  label: item.fieldLabel,
                  labelWidth: null,
                  showLabel: true,
                  ctype: item.ctype,
                  required: item['allowBlank'] === false ? true : false,
                  layout: "colFormItem",
                  span: 24,
                  formId: item.uniqueId,
                  renderKey: item.uniqueId,
                  validators: item.validators,
                  formUID: item.uniqueId,
                  forceValidation: item.forceValidation ?? false,
                  groupIdArr: groupIdArr
               }
               json['__slot__'] = {
                  prepend: '',
                  append: ''
               }
               json['placeholder'] = item.emptyText
               json['style'] = {}
               json['clearable'] = true;
               json['prefix-icon'] = ''
               json['suffix-icon'] = ''
               json['maxlength'] = ''
               json['show-word-limit'] = false
               json['readonly'] = true;
               json['hidden'] = false
               json['disabled'] = false;
               json['__vModel__'] = {
                  model: item['$bind'],
                  modelValue: item['modelValue'] ?? '',
                  optionValue: item['optionValue'] ?? ''
               }
               json['targetFormId'] = targetFormId
               json['uuid'] = useUUID()
               json['groupIdArr'] = groupIdArr
               json['viewModel'] = viewModel
               break;
         }

         this.buidlerExtendsProp(json, item)
         mapJson.push(json)
      })
      return mapJson
   }
   // 内部使用扩展属性
   private static buidlerExtendsProp(json: any, item: any): any {
      if (json['__config__'] && !json['__config__']['extends']) {
         json['__config__']['extends'] = {}
      }
      const config = json['__config__']
      if (config) {
         const configkeys = Object.keys(config)
         Object.keys(item).forEach(key => {
            const value = configkeys.find(x => x === key)
            if (!value)
               Object.assign(json['__config__']['extends'], {
                  [key]: item[key]
               })
         })
      }

      return json

   }
   static builderExtDetial(data: IGridModel, targetFormId: string, viewModel: any): any {
      const json: any = {}
      const dics = formBuilder.GetLangDictionary()
      data = formBuilder.ConvertLable(data, dics)
      json['__config__'] = {
         "type": "grid",
         "label": data.title,
         "labelWidth": null,
         "showLabel": true,
         "changeTag": false,
         "layout": "colFormItem",
         "ctype": "grid",
         "span": 24,
         "document": "https://element.eleme.cn/#/zh-CN/component/table",
         "isDetailTable": true,
         "hideDefault": true,
         "column": [],
         "dataRowBase": {},
         "defaultValue": [],
         "XDataBindList": [],
         "openPage": false,
         "hideOnSinglePage": false,
         "onLoad": false,
         "isShowGridNo": false,
         "isShowSum": true,
         "formId": new Date().getTime(),
         "renderKey": data.uniqueId,
         "map": data.map,
         "ds": data.ds,
         "extends": {
            $hidden: data.$hidden,
            $express: data.$exress,
            $disable: data.$disable
         },
         "emptyGrid": data.emptyGrid,
         //item.uniqueId
         "formUID": data.uniqueId,
         "tools": [],
         "enablePaging": data.enablePaging,
         "pageSize": data.pageSize
      }
      json['style'] = {
         width: "100%"
      }
      json['readonly'] = false,
         json['disabled'] = false
      json['hidden'] = false
      json['Readable'] = false
      json['targetFormId'] = targetFormId
      json['__vModel__'] = {
         model: data.bind,
         modelValue: [],
         optionValue: ''
      }
      json['clearable'] = true;
      json['uuid'] = useUUID()
      json['viewModel'] = viewModel
      return json
   }
   static setFormConfig(config: FormConfig) {
      this.formConfig = config
   }
   static setCopyField(fields: YZField[]): void {
      this.copyField = fields //useDeepClone(fields)
   }
   static buildSystemField(type: string, value: string) {
      const json: any = {}
      if (type === 'taskid') {
         json['__config__'] = {
            label: '任务ID',
            labelWidth: null,
            showLabel: true,
            ctype: "taskid",
            required: true,
            layout: "colFormItem",
            span: 24,
            formId: new Date().getTime(),
            renderKey: new Date().getTime() * 5,
         }
         json['__slot__'] = {
            prepend: '',
            append: ''
         }
         json['placeholder'] = ''
         json['style'] = {}
         json['clearable'] = true;
         json['prefix-icon'] = ''
         json['suffix-icon'] = ''
         json['maxlength'] = ''
         json['show-word-limit'] = false
         json['readonly'] = true;
         json['hidden'] = false
         json['disabled'] = false;
         json['__vModel__'] = {
            model: 'TaskID',
            modelValue: value,
            optionValue: value
         }
         json['uuid'] = useUUID()
      } else if (type === 'itemid') {
         json['__config__'] = {
            label: '主键ID',
            labelWidth: null,
            showLabel: true,
            ctype: "itemid",
            required: true,
            layout: "colFormItem",
            span: 24,
            formId: new Date().getTime(),
            renderKey: new Date().getTime() * 6,
         }
         json['__slot__'] = {
            prepend: '',
            append: ''
         }
         json['placeholder'] = ''
         json['style'] = {}
         json['clearable'] = true;
         json['prefix-icon'] = ''
         json['suffix-icon'] = ''
         json['maxlength'] = ''
         json['show-word-limit'] = false
         json['readonly'] = true;
         json['hidden'] = false
         json['disabled'] = false;
         json['__vModel__'] = {
            model: 'ItemId',
            modelValue: value,
            optionValue: value
         }
         json['uuid'] = useUUID()
      } else if (type === 'parentid') {
         json['__config__'] = {
            label: '字表表父级ID',
            labelWidth: null,
            showLabel: true,
            ctype: "parentid",
            required: true,
            layout: "colFormItem",
            span: 24,
            formId: new Date().getTime(),
            renderKey: new Date().getTime() * 6,
         }
         json['__slot__'] = {
            prepend: '',
            append: ''
         }
         json['placeholder'] = ''
         json['style'] = {}
         json['clearable'] = true;
         json['prefix-icon'] = ''
         json['suffix-icon'] = ''
         json['maxlength'] = ''
         json['show-word-limit'] = false
         json['readonly'] = true;
         json['hidden'] = false
         json['disabled'] = false;
         json['__vModel__'] = {
            model: 'ParentItemId',
            modelValue: value,
            optionValue: value
         }
         json['uuid'] = useUUID()
      }
      return json
   }

   public static ConvertLable(field: any, dics: any) {
      if (dics) {
         let key = ''
         if (field.fieldLabel) {
            key = `${field.uniqueId}.fieldLabel`
            if (dics[key])
               field.fieldLabel = dics[key]
            // 下拉内部的选值
            if (field.options && field.ctype == 'combobox') {
               field.options.forEach((el: any) => {
                  key = `${field.uniqueId}.option_${el.uniqueId}`
                  if (dics[key]) el.text = dics[key]
               })
            }
            // 单选内部的选值或者多选内部的选值
            if (field.items && field.ctype == 'radiogroup' || field.items && field.ctype == 'checkboxgroup') {
               field.items.forEach((el: any) => {
                  key = `${field.uniqueId}.item_${el.uniqueId}`
                  if (dics[key]) el.boxLabel = dics[key]

               })
            }
         } else if (field.title) {
            key = `${field.uniqueId}.title`
            if (dics[key])
               field.title = dics[key]
         } else if (field.text) {
            key = `${field.uniqueId}.text`
            if (dics[key])
               field.text = dics[key]
         } else if (field.$html) {
            key = `${field.uniqueId}.$html`
            if (dics[key])
               field.$html = dics[key]
         }

      }
      return field
   }
   private static GetLangDictionary() {
      const item = core.getFormLang() //[EnUs:{}, ZhCn:{}] 只有两个
      // console.log("%c [ 审批表的语言 --- item ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", item)
      const lang = core.$lang
      // console.log("%c [ 审批表的语言 --- core.$lang ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", lang)
      // const key = (lang == 'zh-cn' ? 'ZhCn' : 'EnUs') 
      let key;
      if (lang == 'zh-CN') {
         key = 'ZhCn'
      }
      if (lang == 'en') {
         key = 'EnUs'
      }
      if (lang == 'ko-KR') {
         key = 'KoKr'
      }
      if (lang == 'de-DE') {
         key = 'DeDe'
      }
      return item[key]
   }
}

export const ControlRender: any = {
   'textbox': function (params: any): YZField {
      return new YZTextBox(params)
   },
   'textarea': function (params: any): YZField {
      return new YZTextArea(params)
   },
   'password': function (params: any): YZField {
      return new YZPassword(params)
   },
   'radio': function (param: any): YZField {
      return new YZRadio(param)
   },
   'checkbox': function (param: any): YZField {
      return new YZCheckBox(param)
   },
   'switch': function (param: any): YZField {
      return new YZSwitch(param)
   },
   'select': function (param: any): YZField {
      return new YZSelect(param)
   },
   'stepper': function (param: any): YZField {
      return new YZStepper(param)
   },
   'slider': function (param: any): YZField {
      return new YZSlider(param)
   },
   'date': function (param: any): YZField {
      return new YZDatePicker(param)
   },
   'rate': function (param: any): YZField {
      return new YZRate(param)
   },
   'cascader': function (param: any): YZField {
      return new YZCascader(param)
   },
   'upload': function (param: any): YZField {
      return new YZUploader(param)
   },
   'imageUpload': function (param: any): YZField {
      return new YZImageUpload(param)
   },
   'grid': function (param: any): YZField {
      return new YZTable(param)
   },
   'user': function (param: any): YZField {
      return new YZUser(param)
   },
   'users': function (param: any): YZField {
      return new YZUsers(param)
   },
   'ou': function (param: any): YZField {
      return new YZOrg(param)
   },
   'ous': function (param: any): YZField {
      return new YZOrgs(param)
   },
   'address': function (param: any): YZField {
      return new YZAddress(param)
   },
   'createdate': function (param: any): YZField {
      return new YZCreateTime(param)
   },
   'sn': function (param: any): YZField {
      return new YZSN(param)
   },
   'initiator': function (param: any): YZField {
      return new YZInitiator(param)
   },
   'dept': function (params: any): YZField {
      return new YZDept(params)
   },
   'segmentbar': function (param: any): YZField {
      return new YZSegmentbar(param)
   },
   'geolocation': function (param: any): YZField {
      return new YZGelocation(param)
   },
   'databrowser': function (params: any): YZField {
      return new YZDataBower(params)
   },
   'owner': function (params: any): YZField {
      return new YZOwner(params)
   },
   'desc': function (params: any): YZField {
      return new YZDesc(params)
   },
   'taskid': function (params: any): YZField {
      return new YZTaskId(params)
   },
   'itemid': function (params: any): YZField {
      return new YZItemId(params)
   },
   'parentid': function (params: any): YZField {
      return new YZParentItemId(params)
   },
   'formtitle': function (params: any): YZField {
      return new YZFormTtile(params)
   },
   'valuetodisplay': function (param: any): YZField {
      return new YZValuetodisplay(param)
   },
   'ownerattr': function (param: any): YZField {
      return new YZOwnerattr(param)
   },
   'button': function (params: any) {
      return new YZButton(params)
   },
   'databrowserbtn': function (params: any) {
      return new YZDataBrowserButton(params)
   },
   'barcode': function (params: any) {
      return new YZBarcode(params)
   },
   'staticimg': function (param: any) {
      return new YZStaticImg(param)
   },
   'historyformlink': function (params: any) {
      return new YZHistoryFormLink(params)
   },
   'childform': function (params: any) {
      return new YZChildForm(params)
   },
   'dataview': function (params: any) {
      return new YZDataView(params)
   },
   'htmleditorarea': function (params: any) {
      return new YZHtmleDitorArea(params)
   },
}

