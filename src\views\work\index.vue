<template>
    <div>
        <!-- <YZNav isBack>
            <template #title>
                {{ $t('LayOut.Work') }}
            </template>
</YZNav> -->
        <div class="search-div">
            <div class="back" @click="router.back()">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search">
                <van-search shape="round" v-model="searchKwd" @search="onMSearch" @clear="onMSearch"
                    :placeholder="$t('home.SearchKeywords')" />
            </div>
            <div class="btn" @click="onSearch">
                <van-icon class="icon" name="filter-o" />
            </div>
        </div>
        <!-- <div class="grap"></div> -->
        <!-- <div class="work-search">
            <van-row>
                <van-col span="18">
                    <div class="left-search">
                        <van-search v-model="searchKwd" shape="round" :placeholder="$t('home.SearchKeywords')" @search="onMSearch" @clear="onMSearch" />
                    </div>
                </van-col>
                <van-col span="6">
                    <div class="right-btn" @click="onSearch">
                        <img src="@/assets/imgs/filter.png" alt="">
                        {{ $t('Global.Search') }}
                    </div>
                </van-col>
            </van-row>
        </div> -->
        <!-- v-if="route.query.type" -->
        <div class="work-main" id="workmain">
            <div class="work-tab">
                <van-tabs v-model:active="active_" class="queryTab">
                    <van-tab>
                        <template #title>
                            <div>
                                <span class="work-title">{{ $t('SSI.' + 'Pending' + route.query.type + 'Inspection')
                                }}</span>
                                <span class="count-dot">
                                    {{ workCount }}
                                </span>
                            </div>
                        </template>
                    </van-tab>
                </van-tabs>
                <van-tabs v-model:active="active" class="applyTab" @change="onTabChange" safe-area-inset-top
                    line-width="20">
                    <van-tab :name="tab.name" v-for="(tab, index) in topBar" :key="index">
                        <template #title>
                            <div>
                                <span class="work-title"> {{ $t(tab.text) }}</span>
                                <span class="count-dot"
                                    v-if="(tab.name === 'AwaitWork' || tab.name === 'NotifyWork') && (workCount > 0 || readCount > 0)">
                                    {{ tab.name === 'AwaitWork' ? workCount : readCount > 0 ? readCount : '' }}
                                </span>
                            </div>
                        </template>
                        <div class="tab-content">
                            <component :is="tab.name" :key="tab.name" @clearKwd="clearKwd" />
                            <!-- 高级查询  -->
                            <van-popup :show="searchShow" :overlay-style="{ background: 'rgb(24 23 23 / 39%)' }"
                                teleport="#workmain" safe-area-inset-top safe-area-inset-bottom position="top">
                                <div class="search-panel">
                                    <div class="search-title">
                                        {{ $t('Form.Filter') }}
                                        <van-icon name="cross" class="search-con" @click="onCloseSearch" />
                                    </div>
                                    <SearchFilter @onSelectChange="onSelectChange" :select-key="perType" />
                                    <div>
                                        <van-field :label="$t('Work.Year')" v-show="perType !== 'Date'">
                                            <template #input>
                                                <van-stepper v-model="panelData.year" />
                                            </template>
                                        </van-field>
                                        <van-field :label="$t('Work.Month')" v-show="perType === 'Month'">
                                            <template #input>
                                                <van-stepper v-model="panelData.month" :max="12" />
                                            </template>
                                        </van-field>
                                        <van-field :label="$t('Work.Quarter')" v-show="perType === 'Quarter'">
                                            <template #input>
                                                <van-stepper v-model="panelData.quto" :max="4" />
                                            </template>
                                        </van-field>

                                        <van-field :label="$t('Work.Date')" readonly @click="timeClick"
                                            v-model="panelData.day" v-show="perType === 'Date'">
                                        </van-field>
                                        <van-field :label="$t('Work.ProcessName')" v-model="panelData.processname"
                                            v-show="!queryType" />
                                        <!-- <van-field :label="$t('Work.TaskNum')" v-model="panelData.taskid"
                                            v-show="!queryType" /> -->
                                        <van-field :label="$t('SSI.SerialNo')" v-model="panelData.sn"
                                            v-show="!queryType" />
                                        <van-field :label="$t('Work.Initiator')" v-model="panelData.postUser" readonly
                                            is-link @click="SelectUser()" />
                                        <!-- 新增3个查询 -->
                                        <!-- <van-field :label="$t('CSTC.ProjectNo_Name')" v-model="panelData.pn" @click="selectPN()"
                                            readonly is-link /> -->
                                        <!-- <van-field :label="$t('SSI.ContractorName')" v-model="panelData.contractor"
                                            v-show="queryType" is-link /> -->
                                        <!-- todo  接口缺 Contractor Name 承包商名称 -->
                                        <!-- <van-field :label="$t('SSI.Equipment')" v-model="panelData.equipment"
                                            @click="selectEquipment()" readonly is-link /> -->
                                        <!-- 新增3个查询 -->
                                        <van-field :label="$t('Work.Keyword')" v-model="panelData.kwd" />
                                    </div>
                                    <van-row :gutter="20">
                                        <van-col :span="12">
                                            <div class="rest-btn" @click="onResetClick">
                                                {{ $t('Global.Reset') }}
                                            </div>
                                        </van-col>
                                        <van-col :span="12">
                                            <div class="search-btn" @click="onSearchClick">
                                                {{ $t('Global.Search') }}
                                            </div>
                                        </van-col>
                                    </van-row>
                                </div>
                            </van-popup>
                            <!-- 高级查询  -->
                        </div>
                    </van-tab>
                </van-tabs>
            </div>
        </div>
        <!-- YZ hide safe-area-inset-bottom -->
        <van-popup v-model:show="timeShow" safe-area-inset-bottom position="bottom">
            <van-date-picker v-model="currentDate" @confirm="onTimeConfirm" @cancel="timeShow = false" />
        </van-popup>
        <!-- YZUserSelect YZ + -->
        <YZUserSelect v-model:show="showUserCom" :multiple="false" @on-save="userSelectSave" />
        <!-- YZUserSelect YZ + -->
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useWork } from '@/logic/work';
import YZNav from '@/components/YZNav.vue';
import SearchFilter from './components/SearchFilter.vue';
import YZUserSelect, { ISelectUser } from '@/components/YZUserSelect.vue';
import { eventBus } from "@/utils/eventBus";
import { useRoute, useRouter } from 'vue-router'
import i18n from '@/locale/index'

const router = useRouter()
const route = useRoute()
const { active, topBar, workCount,
    searchShow, currentDate, onSearch,
    panelData,
    perType,
    readCount,
    onSelectChange, timeShow, timeClick, onTimeConfirm,
    onTabChange,
    onMSearch,
    searchKwd,
    onCloseSearch,
    onSearchClick,
    onResetClick,
    clearKwd,
    SelectUser, //YZ +
    selectPN,
    selectEquipment,
    showUserCom, //YZ +
    userSelectSave //YZ +
} = useWork()
const active_ = ref('Preliminary')
const queryType = ref(route?.query.type && route?.query.type !== 'ApplyWork')
onMounted(() => {
    console.log('route===>>>', route?.fullPath) // layout/work or /layout/work?type=ApplyWork

    // const ApplyWork: any = document.querySelector('.van-tab--line:nth-child(4)')
    // if (ApplyWork) {
    //     ApplyWork.style.display = 'none'
    // }
    const queryType: any = route?.query.type
    // console.log("%c [ queryType ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", queryType)
    if (queryType && queryType !== 'ApplyWork') {
        console.log("%c [ 1111 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
        const applyTab: any = document.querySelector('.applyTab > .van-tabs__wrap')//('.van-tabs__wrap')
        if (applyTab) {
            applyTab.style.display = 'none'
        }
        const tab__text: any = document.querySelector('.van-tab__text')
        if (tab__text) {
            tab__text.style.setProperty('margin-left', '-77px', 'important');
        }
        const tabs__line: any = document.querySelector('.van-tabs__line')
        if (tabs__line) {
            console.log("%c [ i18n.global.locale ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", i18n.global.locale)
            if (i18n.global.locale === 'zh-CN') {
                tabs__line.style.setProperty('margin-left', '-38px', 'important');
            } else {
                tabs__line.style.setProperty('margin-left', '-55px', 'important');
            }
        }
    } else if (queryType === 'ApplyWork') {
        console.log("%c [ 2222 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
        const queryTab: any = document.querySelector('.queryTab')
        if (queryTab) {
            queryTab.style.display = 'none'
        }
        const applyTab: any = document.querySelector('.applyTab > .van-tabs__wrap')
        if (applyTab) {
            applyTab.style.display = 'none'
        }
        // const LeftDev: any = document.querySelector('.left-div')
        // if (LeftDev) {
        //     LeftDev.style.setProperty('margin-bottom', '10px', 'important');
        // }
    } else {
        const applyTab: any = document.querySelector('.applyTab > .van-tabs__wrap')
        const queryTab: any = document.querySelector('.queryTab')
        console.log("%c [ 3333 ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;")
        if (applyTab) {
            applyTab.style.display = 'block'
            if (queryTab) {
                queryTab.style.display = 'none'
            }
            const ApplyWork: any = document.querySelector('.van-tab--line:nth-child(4)')
            if (ApplyWork) {
                ApplyWork.style.display = 'none'
            }
        }
    }
})


</script>
<style lang="scss" scoped>
.search-div {
    box-sizing: border-box;
    width: 100%;
    height: 48px;
    background: var(--yz-div-background);
    display: flex;
    position: sticky;
    top: 0;
    z-index: 99;

    .back {
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 10px;
        box-sizing: border-box;

        .icon {
            font-size: 20px;
            color: var(--yz-text-666);
        }
    }

    .search {
        flex: 1;
    }

    .btn {
        font-size: 14px;
        width: 40px;
        display: flex;
        align-items: center;

        .icon {
            font-size: 20px;
            color: var(--yz-text-666);
        }
    }
}

.grap {
    width: 100%;
    height: 10px;
    background-color: #f9f9f9;
}

.work-search {
    width: 100%;
    height: 54px;
    background: var(--yz-div-background);
    margin-top: 10px;

    .left-search {
        height: 50px;
    }

    .right-btn {
        height: 34px;
        margin: 8px;
        border: 0.5px solid rgba(200, 200, 200, 1);
        border-radius: 17px;
        text-align: center;
        line-height: 34px;
        color: var(--yz-text-333);

        img {
            width: 17px;
            height: 17px;
            padding-right: 3px;
        }
    }
}

.work-tab {
    height: 44px;
    width: 100%;
    background: var(--yz-div-background);
    box-shadow: inset 0 -1px 0 0 #D9D9D9;

    .work-title {
        font-family: PingFangSC-Medium;
        font-size: var(--yz-work-title);
        // font-size: 14px;
        color: var(--yz-text-333);
        text-align: center;
        font-weight: 500;
    }

    .count-dot {
        font-family: PingFangSC-Medium;
        font-size: var(--yz-work-dot);
        color: #FFFFFF;
        background: #F11C1C;
        border-radius: 7px;
        font-weight: 500;
        min-width: 24px;
        height: 17px;
        position: absolute;
        top: 5px;
        text-align: center;
        line-height: 17px;
        padding: 0 2px;
    }

    ::v-deep(.van-tabs__line) {
        width: 80px;
    }

    .tab-content {
        height: calc(100vh - 128px);
        overflow-x: hidden;
        overflow-y: scroll;
        width: 100%;
    }
}

::v-deep(.van-popup--right) {
    top: 54%
}

.search-panel {
    background: var(--yz-div-background);
    padding-bottom: 20px;

    .search-title {
        border-bottom: 0.5px solid rgba(232, 232, 232, 1);
        height: 44px;
        background: var(--yz-div-background);
        font-family: PingFangSC-Medium;
        font-size: var(--yz-work-searchtitle);
        color: var(--yz-text-333);
        font-weight: 500;
        line-height: 44px;
        text-indent: 1em;

        .search-con {
            margin: 14px;
            float: right;
            color: #999999;
        }
    }

    .rest-btn {
        border: 0.5px solid rgba(11, 115, 243, 1);
        border-radius: 4px;
        width: 60%;
        height: 36px;
        text-align: center;
        font-family: PingFangSC-Regular;
        font-size: var(--yz-work-restbtn);
        color: #0B73F3;
        text-align: center;
        font-weight: 400;
        line-height: 36px;
        float: right;
    }

    .search-btn {
        background: #0B73F3;
        border: 0.5px solid rgba(243, 248, 255, 1);
        border-radius: 4px;
        width: 60%;
        height: 36px;
        text-align: center;
        font-family: PingFangSC-Regular;
        font-size: var(--yz-work-restbtn);
        color: #FFFFFF;
        text-align: center;
        font-weight: 400;
        line-height: 36px;
        float: left
    }
}

::v-deep(.van-stepper__input) {
    width: 60%;
}
</style>