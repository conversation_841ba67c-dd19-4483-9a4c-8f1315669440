export interface ISwiper {
    autoplay: number;
    dataSrcType: string;
    duration: number;
    isOpen: boolean;
    loop: boolean;
    showIndicators: boolean;
    vertical: boolean;
}
export interface IImage {
    FILE64: string
}
export class APPInfo<T> {
    border: boolean;
    children: Array<T>;
    columns: number;
    type: number
    constructor(params: any) {
        this.border = true
        this.children = params['children']
        this.columns = 4
        this.type = params['type']
    }
}
export interface IAppInfo {
    ProcessName: string;
    Version: string;
    Name: string;
    EnName: string;
    FormPath: string;
    Type: string;
    Icon: string;
    Color?: string;
    Url?: string;
}
export interface IAppManager {
    title: string;
    hasChildren: boolean
}
export interface ILibiaryInfo {
    id: string;
    libRootFolderId: string;
    glyph: number;
    startApp: string;
    text: string;
    children: Array<IProcessInfo>
}
export interface IProcessInfo {
    IconCls: string;
    IconColor: string;
    Id: string;
    IsCollection: boolean;
    OrderIndex: number;
    ProcessName: string;
    ProcessVersion: string;
    ShortName?: string;
    MobileInitiation?: boolean
}

export interface ITreeProcessInfo {
    expanded: boolean;
    fid: string;
    leaf: boolean;
    path: string;
    text: string;
    isProcess:boolean;
    IconCls?:string | null;
    IconColor?:string;
    parentId:string;
    IsCollection:boolean;
    ProcessVersion:string;
    type:string;
    Id:string //搜索只会返回fid 这个跟fid一样

}