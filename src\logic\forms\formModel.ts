
import { DisabledType, HiddenType, OrgType, ValueType } from "../../types/bpm";
import { parseExpress } from "./express";
import { formBuilder } from "./formBuilder";
import { eventBus } from "@/utils/eventBus";
import { useMath } from "@/utils";
import { IYZColumn, YZTable, YZTableConfig } from "./YZTable";
import { YZTextBox } from "./YZTextbox";
import { effect } from "vue";
import { formpt } from "./formp";
import { isNaN } from "mathjs";
import { useFieldOptionValue } from '@/logic/forms/processHelper'
import { YZSelect, YZSelectConfig } from "./YZSelect";
import { YZRadio } from "./YZRadio";
import { YZCheckBox } from "./YZCheckBox";
import { YZDatePickerConfig } from "./YZDatePicker";
import { throttle } from 'lodash'
import { useProcessStore } from "@/store/process";
import { useCore } from "@/store/core";
import store from "@/store";
import { YZDataViewConfig } from "./YZDataView";
import { hasValidationGroup } from "./formsutil";
const maths = useMath()
const core = useCore(store)
export abstract class YZConfig {
  [x: string]: any;
  required: boolean;
  showLabel: boolean;
  label: string;
  formId: number;
  ctype: string;
  rules: any | object | []
  extends: any;
  validators: Array<any>;
  formUID: string;
  tools: Array<any>
  writable: boolean;
  forceValidation: boolean;
  constructor(params: any) {
    this.writable = typeof params.writable == 'boolean' ? params.writable : true // 如果不是布尔值就直接true
    this.required = params?.required;
    this.showLabel = params?.showLabel;
    this.label = params?.label
    this.formId = params?.formId
    this.ctype = params?.ctype
    this.rules = params?.rules
    this.extends = (params?.extends === undefined ? {} : params?.extends)
    this.validators = params['validators'] ?? []
    this.formUID = params["formUID"]
    this.tools = params['tools'] ?? []
    this.forceValidation = params.forceValidation

  }
  // private defaultRule():Array<any> {
  //   // 不推荐在父类进行设置
  //     return [
  //     {
  //         required: true, message: this.label? `${useLang('Form.Enter_tips')}${this.label}` : useLang('Form.Enter_Required_fields_tips')
  //     }]
  // }
}
export abstract class YZStyle<T extends object> {
  style: T
  constructor(params: T) {
    this.style = this.initStyle(params)
  }
  abstract initStyle(style: any): T;
}
export abstract class YZOption<T extends any> {
  options: T;
  constructor(params: any) {
    this.options = this.initOption(params)
  }
  abstract initOption(params: any): T;
}
export class vModel {
  model: string;
  value: any;
  optionValue: any;
  valueType: ValueType;
  optionValueType: ValueType;
  constructor(params: any) {
    this.model = params.model
    this.value = params.modelValue ?? ''
    this.optionValue = params.optionValue ?? ''
    this.valueType = 'string'
    this.optionValueType = 'string'
  }
}
export abstract class YZField {
  clearable: boolean;
  disabled: boolean;
  placeholder: string;
  readonly: boolean;
  config: YZConfig;
  style: YZStyle<object>;
  vModel: vModel;
  hidden: boolean;
  events: Record<string, Function>;
  uuid: string;
  table?: string
  tableName?: string;
  pindex: number;
  colhidden: boolean;
  targetFormId: string;
  groupIdArr: any[];
  writable: boolean;
  viewModel: any[]
  constructor(params: any) {
    this.clearable = params.clearable
    // this.disabled = params.disabled
    this.placeholder = params.placeholder
    // this.readonly = params.readonly
    // this.hidden = params.hidden
    this.uuid = params['uuid']
    this.config = this.initConfig(params['__config__'])
    this.writable = typeof params.writable == 'boolean' ? params.writable : true // 如果不是布尔值就直接true
    this.hidden = this.$returnSeltHidden(this, params['pindex'], params.hidden, params.viewModel)
    this.readonly = this.$returnSeltDisabled(this, params['pindex'], params.disabled, params.viewModel)
    this.disabled = this.$returnSeltDisabled(this, params['pindex'], params.disabled, params.viewModel)
    this.viewModel = params.viewModel
    if (this.config.forceValidation && this.disabled) {
      this.config.rules = this.config.defaultRules ?? []
    } else if (this.disabled && !this.config.forceValidation) {
      this.config.rules = []
    }
    // console.log(this.disabled,'???',this.config.label)
    this.colhidden = params.colhidden
    this.style = this.initStyle(params['style'])
    this.vModel = new vModel(params['__vModel__'])
    this.targetFormId = params["targetFormId"]
    this.events = {
      'handler': params['events']
    }
    //this.events['handler'] = params['events']
    this.table = params['table'] ?? ''
    this.tableName = params['tableName'] ?? ''
    this.pindex = params['pindex'] ?? -1
    this.groupIdArr = params['groupIdArr']
  }
  abstract initConfig(config: any): YZConfig;
  abstract initStyle(style: any): YZStyle<object>;
  expressTest(field: YZField, index: number = -1, firstLoad: boolean = true) {
    // if(field.vModel.model === '价税合计') {
    this.$noSelfExpress(field, index)
    if (field.config.extends['$express'] && firstLoad) {
      this.$selfExpress(field, index)
    }
    // }
  }
  disableTest(field: YZField, index: number = -1, firstLoad: boolean = true) {
    this.$noSelfDiable(field, index)
    if (field.config.extends['$disable'] && firstLoad) {
      this.$selfDisable(field, index)
    }
  }
  hiddenTest(field: YZField, index: number = -1, firstLoad: boolean = true) {
    let $hideenText = field.config.extends['$hidden']
    if (!$hideenText) {
      $hideenText = field.config.extends['$columnHidden']
    }
    this.$noSelfHidden(field, index)

    if ($hideenText && firstLoad) {
      this.$seltHidden(field, index)
    }
  }
  advancedValidate(field: YZField, index: number = -1, firstLoad: boolean = true) {
    // console.log(field.config.label,index,'???')
    this.$noselfAdvanceValidate(field, index)
    if (field.config.validators.length > 0 && firstLoad) {
      this.$selfAdvanceValidate(field, index)
    }
  }
  private $noSelfExpress(field: YZField, index: number = -1) {
    // 获取当前表单中所有的控件
    const newAllFields = this.getFormFields()
    // 获取当前表单上所有字表
    const childTable = this.getChildTable()
    // 检查当前数据改变的控件，是否在其他控件的表达式中存在
    const checkItems = this.getRefencesField(newAllFields, field, '$express')
    //执行与当前控件有关的表达式
    for (let i = 0; i < checkItems.length; i++) {
      const item = checkItems[i]
      const exp = item.config.extends['$express']
      if (exp) {
        // 存放提取表达式变量的值
        let $varValues: Array<any> = []
        // 计算当前控件的表达式
        const vp = new formpt(exp)
        if (vp.exp.indexOf('sum') > -1) {
          // 表达式中包含合计
          $varValues = this.expressSum(item, vp)
        } else {
          // 提取计算后的表达式变量
          if (vp.vars && vp.vars.length > 0) {
            for (let v = 0; v < vp.vars.length; v++) {
              const vitem = vp.vars[v]
              if (vitem) {
                if (item.tableName) {
                  // 当前改变的控件是明细控件,提取当前明细控件的值       
                  let fieldModel: YZField | undefined = undefined;
                  if (vitem.indexOf('.') > -1) {
                    const fieldName = vitem.replace(item.tableName + '.', '')
                    fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === item.pindex)
                  } else {
                    fieldModel = newAllFields.find(x => x.vModel.model === vitem)
                  }
                  if (fieldModel) {
                    const vs = this.getFieldValue(fieldModel)
                    $varValues.push(...vs)  //this.getFieldValue(fieldModel)
                    // if (fieldModel.config.ctype === 'YZStepper') {
                    //   const nValue = Number(fieldModel.vModel.value)
                    //   if (isNaN(nValue)) {
                    //     $varValues.push(fieldModel.vModel.optionValue)
                    //   } else {
                    //     $varValues.push(nValue)
                    //   }

                    // } else if (fieldModel.config.ctype === 'YZDatePicker') {
                    //   if (fieldModel.vModel.value) {
                    //     $varValues.push(new Date(fieldModel.vModel.value))
                    //   }
                    // } else {
                    //   $varValues.push(fieldModel.vModel.value)
                    // }
                  }
                } else {
                  // 当前改变的控件是主表控件
                  const fieldModel = newAllFields.find(x => x.vModel.model === vitem && x.table == '' && x.tableName == '')
                  if (fieldModel) {
                    const vs = this.getFieldValue(fieldModel)
                    $varValues.push(...vs)  // = this.getFieldValue(fieldModel)
                    // if (fieldModel.config.ctype === 'YZStepper') {
                    //   const nValue = Number(fieldModel.vModel.value)
                    //   if (isNaN(nValue)) {
                    //     $varValues.push(fieldModel.vModel.optionValue)
                    //   } else {
                    //     $varValues.push(nValue)
                    //   }
                    // } else if (fieldModel.config.ctype === 'YZDatePicker') {
                    //   if (fieldModel.vModel.value) {
                    //     $varValues.push(new Date(fieldModel.vModel.value))
                    //   }
                    // } else {
                    //   $varValues.push(fieldModel.vModel.value)
                    // }

                  }
                }
              }
            }
          }
        }
        // 进行计算
        let endValues = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
        this.setFieldValue(item.uuid, endValues, item.pindex)
      }
    }
    this.ReloadTable(field, childTable)
    // 设置下拉框自动映射过滤字段的值
    this.SelectFilter(field, newAllFields, index)
    // 用于dataView过滤的更新操作
    this.dataViewFilterReload(field, newAllFields)
  }
  private dataViewFilterReload(field: YZField, newAllFields: YZField[]) {
    // 过滤每个字表的数据源过滤条件
    let newValue = null
    if (field.vModel.optionValue) {
      newValue = field.vModel.optionValue
    } else if (!newValue && field.vModel.value) {
      newValue = field.vModel.value
    }
    for (let i = 0; i < newAllFields.length; i++) {
      const child = newAllFields[i]
      const tconfig = child.config as YZDataViewConfig
      const filterData: string[] = []
      if (tconfig.ctype === 'YZDataView') {
        let ds = tconfig.ds
        if (tconfig && ds && ds.filter) {
          Object.keys(ds.filter).forEach((ft: any) => {
            const fild = ds.filter[ft].field
            if (fild) {
              filterData.push(fild)
            }
          })
          let haveF: any = null;
          if (field.table && field.tableName) {
            haveF = filterData.find(x => x === field.tableName)
          } else {
            haveF = filterData.find(x => x === field.vModel.model)
          }
          if (haveF) {
            this.setDataViewReload(child.uuid, newValue, 0)
          }
        }
      }
    }


  }
  private ReloadTable(field: YZField, newAllFields: YZField[]) {
    // 过滤每个字表的数据源过滤条件
    let newValue = null
    if (field.vModel.optionValue) {
      newValue = field.vModel.optionValue
    } else if (!newValue && field.vModel.value) {
      newValue = field.vModel.value
    }
    for (let i = 0; i < newAllFields.length; i++) {
      const child = newAllFields[i]
      const tconfig = child.config as YZTableConfig
      const ds = tconfig.getDs()
      const filterData: string[] = []
      // 判断当前改变的控件是否在明细的过滤条件中被依赖
      if (tconfig && ds && ds.filter) {
        Object.keys(ds.filter).forEach((ft: any) => {
          const fild = ds.filter[ft].field
          if (fild) {
            filterData.push(fild)
          }
        })
        let haveF: any = null;
        if (field.table && field.tableName) {
          haveF = filterData.find(x => x === field.tableName)
        } else {
          haveF = filterData.find(x => x === field.vModel.model)
        }
        if (haveF)
          this.setTableReload(child.uuid, newValue, 0)
      }
    }
    //  // 表示字表控件的某些功能依赖了主表控件，执行初始化加载
    //  let newValue =''
    //  if(field.vModel.value)
    //      newValue = field.vModel.value
    //  if (field.vModel.optionValue)
    //      newValue = field.vModel.optionValue
    //  const titem =  newAllFields.find(x=>x.vModel.model === item.tableName)
    //  if (titem)        
    //  {
    //    const tconfig = titem.config as YZTableConfig
    //    const ds =  tconfig.getDs()
    //    const filterData:string[] =[]
    //    // 判断当前改变的控件是否在明细的过滤条件中被依赖
    //    if(tconfig && ds &&  ds.filter) {
    //        Object.keys(ds.filter).forEach((ft:any)=>{
    //           const fild = ds.filter[ft].field
    //           if (fild) {
    //            filterData.push(fild)
    //           }
    //        })
    //    }
    //    const haveF =  filterData.find(x=>x === field.vModel.model)
    //    if(item.table && field.table && haveF) 
    //        this.setTableReload(titem.uuid,newValue,index)
    //  }
  }
  private SelectFilter(field: YZField, newAllFields: YZField[], index: number = -1) {
    newAllFields.forEach(item => {
      let config = item.config as YZConfig
      let ds = config.extends.ds
      const filterData: any[] = []
      if (ds && ds.filter) {
        Object.keys(ds.filter).forEach((ft: any) => {
          const fild = ds.filter[ft].field
          // if (fild) {
          //   filterData.push(fild)
          // }
          if (fild) {
            if (fild.indexOf('.') > -1) {
              //当前变量与明细表有关，提取字段名称
              const fieldName = fild.split('.')[1]
              //提取当前变量的值
              var fieldModel: any = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
              let value = this.getFieldValue(fieldModel)[0]
              filterData.push({
                fieldName: fild,
                value
              })
            } else {
              let curField = newAllFields.find(el => el.vModel.model == fild) as YZField

              let value = this.getFieldValue(curField)[0]
              filterData.push({
                fieldName: fild,
                value
              })
            }

          }
        })
        let haveF: any = null
        if (field.table && field.tableName) {
          haveF = filterData.find(x => x.fieldName === field.table)
        } else {
          haveF = filterData.find(x => x.fieldName === field.vModel.model)
        }
        if (haveF)
          this.setSelectFilter(item.uuid, filterData, index)
      }
    })
  }
  private $selfExpress(field: YZField, index: number) {
    let $varValues: any = []
    const newAllFields = this.getFormFields()
    //提取自身表达式
    const exp = field.config.extends['$express']
    //执行表达式解析
    const vp = new formpt(exp)
    if (exp.indexOf('sum') > -1) {
      return
    }
    if (vp.vars && vp.vars.length > 0) {
      for (let v = 0; v < vp.vars.length; v++) {
        const vitem = vp.vars[v]
        if (vitem) {
          if (vitem.indexOf('.') > -1) {
            //当前变量与明细表有关，提取字段名称
            const fieldName = vitem.split('.')[1]
            //提取当前变量的值
            var fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
            if (fieldModel) {
              const vs = this.getFieldValue(fieldModel)
              $varValues.push(...vs)//    = this.getFieldValue(fieldModel)
              // if (fieldModel.config.ctype === 'YZStepper') {
              //   const nValue = Number(fieldModel.vModel.value)
              //   if (isNaN(nValue)) {
              //     $varValues.push(fieldModel.vModel.optionValue)
              //   } else {
              //     $varValues.push(nValue)
              //   }
              // } else if (fieldModel.config.ctype === 'YZDatePicker') {
              //   if (fieldModel.vModel.value) {
              //     $varValues.push(new Date(fieldModel.vModel.value))
              //   }
              // } else {
              //   $varValues.push(fieldModel.vModel.value)
              // }
            }
          } else {
            //当前变量与明细表无关，直接提取值
            const fieldModel = newAllFields.find(x => x.vModel.model === vitem)
            if (fieldModel) {
              const vs = this.getFieldValue(fieldModel)
              $varValues.push(...vs)  //= // this.getFieldValue(fieldModel)
              // if (fieldModel.config.ctype === 'YZStepper') {
              //   const nvalue = Number(fieldModel.vModel.value)
              //   if (isNaN(nvalue)) {
              //     $varValues.push(fieldModel.vModel.optionValue)
              //   } else {
              //     $varValues.push(nvalue)
              //   }
              // }else if(fieldModel.config.ctype === 'YZDatePicker') {
              //    if (fieldModel.vModel.value) {
              //     $varValues.push(new Date(fieldModel.vModel.value))
              //    } 
              // } else {
              //   $varValues.push(fieldModel.vModel.value)
              // }
            }
          }
        }
      }
    }
    console.log(exp, $varValues, vp.vars, index);

    // 进行计算
    let endValues = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
    if (String(endValues) !== String(field.vModel.value))
      // 计算完毕后，设置当前表达式所属控件的值
      this.setFieldValue(field.uuid, endValues, index)

  }
  private getFieldValue(fieldModel: YZField) {
    const $varValues: any = []
    if (fieldModel.config.ctype === 'YZStepper') {
      let vModel = Number(fieldModel.vModel.value)
      const nValue = isNaN(vModel) ? 0 : vModel
      if (isNaN(nValue)) {
        // 这边注释是未识别为数字不要反悔nan，返回''
        // $varValues.push(fieldModel.vModel.optionValue)
        $varValues.push(fieldModel.vModel.optionValue)
      } else {
        $varValues.push(nValue)
      }
    } else if (fieldModel.config.ctype === 'YZDatePicker') {
      if (fieldModel.vModel.value) {
        const dcofig = fieldModel.config as YZDatePickerConfig
        if (dcofig.getFormatterType() === 'HH:mm') {
          $varValues.push(fieldModel.vModel.value)
        } else {
          $varValues.push(new Date(fieldModel.vModel.value))
        }

      }
    } else if (fieldModel.config.ctype === 'YZSelect') {
      if (fieldModel.vModel.optionValue) {
        $varValues.push(fieldModel.vModel.optionValue)
      } else {
        const yzSelect = fieldModel as YZSelect
        if (yzSelect) {
          const defaultOptions = yzSelect.getOptions();
          if (defaultOptions && defaultOptions.length > 0) {
            const option = defaultOptions.find(x => x.checked)
            if (option)
              $varValues.push(option.value)
          }
        }
      }
    } else if (fieldModel.config.ctype === 'YZRadio') {
      if (fieldModel.vModel.value) {
        $varValues.push(fieldModel.vModel.value)
      } else {
        const yzRadio = fieldModel as YZRadio
        if (yzRadio) {
          const defaultOptions = yzRadio.getOptions();
          const checkOption = defaultOptions.find(x => x.checked)
          if (checkOption) {
            $varValues.push(checkOption.value)
          }
        }
      }
    } else if (fieldModel.config.ctype === 'YZCheckBox') {
      if (fieldModel.vModel.value) {
        $varValues.push(fieldModel.vModel.optionValue)
      } else {
        const yzCheckBox = fieldModel as YZCheckBox
        if (yzCheckBox) {
          const defaultOptions = yzCheckBox.getOptions();
          const checkOption = defaultOptions.find(x => x.checked)
          if (checkOption) {
            $varValues.push(checkOption.value)
          }
        }
      }
    } else {
      $varValues.push(fieldModel.vModel.value)
    }
    return $varValues
  }

  private $noSelfDiable(field: YZField, index: number = -1) {
    // 获取当前表单中所有的控件
    let $varValues: Array<any> = []
    const newAllFields = this.getFormFields()
    // 检查当前数据改变的控件，是否在其他控件的表达式中存在
    const checkItems = this.getRefencesField(newAllFields, field, '$disable')
    for (let i = 0; i < checkItems.length; i++) {
      const item = checkItems[i]
      // 提取当前有关控件的表达式
      const exp = item.config.extends['$disable']
      if (exp && this.processExp(exp)) {
        this.processDisabled(exp, item, index)
      } else if (exp) {
        //解析当前表达式
        const vp = new formpt(exp)
        if (vp.vars && vp.vars.length > 0) {
          vp.vars.forEach(item => {
            //检查当前变量是否跟明细有关
            const vitem = item
            if (vitem.indexOf('.') > -1) {
              //当前变量与明细表有关，提取字段名称
              const fieldName = vitem.split('.')[1]
              //提取当前变量的值
              var fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index && vitem == x.table)
              if (fieldModel) {
                $varValues.push(...this.getFieldValue(fieldModel))
                // if (fieldModel.config.ctype === 'YZStepper') {
                //   const nValue = Number(fieldModel.vModel.value)
                //   if (isNaN(nValue)) {
                //     $varValues.push(fieldModel.vModel.optionValue)
                //   } else {
                //     $varValues.push(nValue)
                //   }
                // } else if (fieldModel.config.ctype === 'YZDatePicker') {
                //   if (fieldModel.vModel.value) {
                //     $varValues.push(new Date(fieldModel.vModel.value))
                //   }
                // } else {
                //   $varValues.push(fieldModel.vModel.value)
                // }
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              }
            } else {
              //当前变量与明细表无关，直接提取值
              const fieldModel = newAllFields.find(x => x.vModel.model === vitem)
              if (fieldModel) {
                $varValues.push(...this.getFieldValue(fieldModel))
                // if (fieldModel.config.ctype === 'YZStepper') {
                //   const nvalue = Number(fieldModel.vModel.value)
                //   if (isNaN(nvalue)) {
                //     $varValues.push(fieldModel.vModel.optionValue)
                //   } else {
                //     $varValues.push(nvalue)
                //   }
                // }else if(fieldModel.config.ctype === 'YZDatePicker') {
                //    if (fieldModel.vModel.value) {
                //     $varValues.push(new Date(fieldModel.vModel.value))
                //    } 
                // } else {
                //   $varValues.push(fieldModel.vModel.value)
                // }
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              }
            }

            // const vitem = item
            // if (vitem) {
            //   if (item.tableName) {
            //     // 当前改变的控件是明细控件,提取当前明细控件的值
            //     const fieldName = vitem.replace(item.tableName + '.', '')
            //     const fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
            //     if (fieldModel) {
            //       $varValues.push(fieldModel.vModel.value)
            //     }
            //   } else {
            //     // 当前改变的控件是主表控件
            //     const fieldModel = newAllFields.find(x => x.vModel.model === vitem)
            //     if (fieldModel) {
            //       $varValues.push(fieldModel.vModel.value)
            //     }
            //   }
            // }
          })
        }
        // 计算表达式
        const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
        const sendValue = Number(value) > 0 ? true : false
        if (item.writable) {
          if (sendValue && !item.config.forceValidation) {
            item.config.rules = []
          } else {
            let config = field.config
            field.config.rules = field.config.defaultRules ?? []
          }
          this.setFieldDisable(item.uuid, sendValue, index)
        }
      }
    }
  }
  private $selfDisable(field: YZField, index: number = -1) {
    const $varValues: any = []
    const newAllFields = this.getFormFields()
    //提取自身表达式
    const exp = field.config.extends['$disable']
    //执行表达式解析
    const vp = new formpt(exp)
    if (vp.vars && vp.vars.length > 0) {
      vp.vars.forEach(item => {
        //检查当前变量是否跟明细有关
        const vitem = item
        if (vitem) {
          if (item.tableName) {
            // 当前改变的控件是明细控件,提取当前明细控件的值
            const fieldName = vitem.replace(item.tableName + '.', '')
            const fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
            if (fieldModel) {
              $varValues.push(fieldModel.vModel.value)
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              const processStore = useProcessStore();
              const postInfoData = processStore.getPostInfoData
              $varValues.push(postInfoData[processStr])
            }
          } else {
            // 当前改变的控件是主表控件
            const fieldModel = newAllFields.find(x => x.vModel.model === vitem)
            if (fieldModel) {
              $varValues.push(fieldModel.vModel.value)
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              const processStore = useProcessStore();
              const postInfoData = processStore.getPostInfoData
              $varValues.push(postInfoData[processStr])
            }
          }
        }
      })
    }
    // 执行计算
    const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
    const sendValue = Number(value) > 0 ? true : false
    if (field.writable) {
      if (sendValue && !field.config.forceValidation) {
        field.config.rules = []
      } else {
        field.config.rules = field.config.defaultRules ?? []
      }
      this.setFieldDisable(field.uuid, sendValue, index)
    }
  }
  private $noSelfHidden(field: YZField, index: number = -1) {
    // 获取当前表单中所有的控件
    const $varValues: Array<any> = []
    const newAllFields = this.getFormFields()
    // 检查当前数据改变的控件，是否在其他控件的表达式中存在
    let checkItems: any = []
    let hiddenField = this.getRefencesField(newAllFields, field, '$hidden')
    let columnHidden = this.getRefencesField(newAllFields, field, '$columnHidden')
    checkItems = [...hiddenField, ...columnHidden]
    // let checkItems = this.getRefencesField(newAllFields, field, '$hidden')
    // if (checkItems.length <= 0)
    //   checkItems = this.getRefencesField(newAllFields, field, '$columnHidden')
    // 我这边做个处理 如果存在下表index >= 0的情况下要过滤里面的存在的明细子项
    // if(checkItems.length > 1 && index >= 0){
    //    checkItems = [checkItems.filter(item => item.table)[index]].filter(item => item)
    // }
    for (let i = 0; i < checkItems.length; i++) {
      $varValues.length = 0
      const item = checkItems[i]
      // 提取当前有关控件的表达式
      const exp = item.config.extends['$hidden'] ?? item.config.extends['$columnHidden']
      if (exp && this.processExp(exp)) {
        this.processHidden(exp, item, index)
      } else if (exp) {
        //解析当前表达式
        const vp = new formpt(exp)
        if (vp.vars && vp.vars.length > 0) {
          vp.vars.forEach(vitem => {
            //检查当前变量是否跟明细有关
            // const vitem = item
            if (vitem) {
              if (item.tableName) {
                // 当前改变的控件是明细控件,提取当前明细控件的值
                const fieldName = vitem.replace(item.tableName + '.', '')
                const fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
                if (fieldModel) {
                  this.getFieldValue(fieldModel)
                  $varValues.push(this.getFieldValue(fieldModel))
                } else if (this.processExp(vitem)) {
                  let processStr = vitem.replace('$', '')
                  const processStore = useProcessStore();
                  const postInfoData = processStore.getPostInfoData
                  $varValues.push(postInfoData[processStr])
                }
              } else {
                // 当前改变的控件是主表控件
                const fieldModel = newAllFields.find(x => x.vModel.model === vitem && x.pindex === index)
                if (fieldModel) {
                  $varValues.push(this.getFieldValue(fieldModel))
                } else if (this.processExp(vitem)) {
                  let processStr = vitem.replace('$', '')
                  const processStore = useProcessStore();
                  const postInfoData = processStore.getPostInfoData
                  $varValues.push(postInfoData[processStr])
                }
              }
            }
          })
        }
        // 计算表达式
        const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
        const sendValue = Number(value) > 0 ? true : false
        this.setFieldHidden(item.uuid, sendValue, index)
      }
    }
  }
  private $returnSeltHidden(field: YZField, index: number = -1, fieldHidden: boolean, viewModel: any) {
    let exp = field.config.extends['$hidden'] ?? field.config.extends['$columnHidden']
    let falg = true
    if (this.processExp(exp) && exp) {
      // $isPost 是否发起；$isProcess 是否审批中；$isRead 是否只读；$isInform 是否知会
      // $activityId 和 $activityName 另外判断
      const processStore = useProcessStore();
      const loadInfo = processStore.getProcLoad
      const postInfoData = processStore.getPostInfoData
      let taskInstance = sessionStorage.getItem('taskInstance')
      // // console.log(exp,field,loadInfo,'processHidden')
      if (postInfoData) {
        // console.log(viewModel,field)
        const initAllData = viewModel?.data
        let $varValues: Array<any> = []
        const vp = new formpt(exp)
        if (vp.vars && vp.vars.length > 0) {
          vp.vars.forEach(item => {
            //检查当前变量是否跟明细有关
            const vitem = item
            if (vitem.indexOf('.') > -1) {
              // console.log(field,index)
              //当前变量与明细表有关，提取字段名称
              const tableName = vitem.split('.')[0]
              const fieldName = vitem.split('.')[1]
              //提取当前变量的值
              var fieldModel: any = initAllData[tableName][index] && initAllData[tableName][index][fieldName]
              if (fieldModel) {
                $varValues.push(fieldModel)
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              } else {
                falg = false
              }
            } else {
              //当前变量与明细表无关，直接提取值
              const fieldModel = Object.keys(initAllData).find(x => x == vitem)
              // // console.log('newAllFields', fieldModel, vitem, processStore.getPostInfoData)
              if (fieldModel) {
                $varValues.push(initAllData[fieldModel])
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                let processFalg = processStore.getPostInfoData[processStr]
                if (processFalg != undefined) {
                  $varValues.push(processFalg)
                } else {
                  if (loadInfo) {
                    // 发起
                    if (loadInfo.processId && vitem === '$isPost') {
                      // this.setFieldHidden(field.uuid, true, index)
                      fieldHidden = true
                    } else
                      // 审批
                      if (loadInfo.stepId && vitem === '$isProcess' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Process' ||
                        loadInfo.stepId && vitem === '$isProcess' && taskInstance && JSON.parse(taskInstance).stepFinished == false
                      ) {
                        // this.setFieldHidden(field.uuid, true, index)
                        fieldHidden = true
                      } else
                        // 只读
                        if (loadInfo.taskId && loadInfo.loadType === 'read' && vitem === '$isRead' ||
                          loadInfo.taskId && vitem === '$isRead') {
                          // this.setFieldHidden(field.uuid, true, index)
                          fieldHidden = true
                        } else
                          // 知会
                          if (loadInfo.stepId && vitem === '$isInform' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Inform') {
                            // this.setFieldHidden(field.uuid, true, index)
                            fieldHidden = true
                          }
                  }
                  $varValues.push(fieldHidden)
                }
              } else {
                falg = false
              }
            }
          })
        } else if (exp == '1') $varValues.push('1')
        if (!falg && $varValues.length == 0) {
          return fieldHidden
        }
        // 计算表达式
        const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
        const sendValue = Number(value) > 0 ? true : false
        // // console.log(sendValue,exp, $varValues, vp.vars,'结果')
        fieldHidden = sendValue

        // console.log(fieldHidden,field.config.label,exp, $varValues, vp.vars,'结果')
      }
      if (loadInfo) {
        // 发起
        if (loadInfo.processId && exp === '$isPost') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
        // 审批
        if (loadInfo.stepId && exp === '$isProcess' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Process') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
        // 只读
        if (loadInfo.taskId && loadInfo.loadType === 'read' && exp === '$isRead') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
        // 知会
        if (loadInfo.stepId && exp === '$isInform' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Inform') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
      }
    } else {
      if (exp) {
        const initAllData = viewModel.data
        let $varValues: Array<any> = []
        //执行表达式解析
        const vp = new formpt(exp)
        if (vp.vars && vp.vars.length > 0) {
          vp.vars.forEach(item => {
            //检查当前变量是否跟明细有关
            const vitem = item
            if (vitem.indexOf('.') > -1) {
              // console.log(field,index)
              //当前变量与明细表有关，提取字段名称
              const tableName = vitem.split('.')[0]
              const fieldName = vitem.split('.')[1]
              //提取当前变量的值
              var fieldModel: any = initAllData[tableName][index] && initAllData[tableName][index][fieldName]
              if (fieldModel) {
                $varValues.push(fieldModel)
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              }
            } else {
              //当前变量与明细表无关，直接提取值
              const fieldModel = Object.keys(initAllData).find(x => x === vitem)
              if (fieldModel) {
                $varValues.push(initAllData[fieldModel])
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              }
            }
          })
        } else if (exp == '1') $varValues.push('1')

        // 执行计算
        const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)

        const sendValue = Number(value) > 0 ? true : false
        // // console.log(sendValue,exp, $varValues, vp.vars,'结果')

        // console.log(fieldHidden,field.config.label,exp, $varValues, vp.vars,'结果')
        fieldHidden = sendValue
      }
    }
    return fieldHidden
  }
  public $returnSeltDisabled(field: YZField, index: number = -1, fieldHidden: boolean, viewModel: any, dyTableData?: any) {

    // console.log(2)
    // let exp = field.config.extends['$hidden'] ?? field.config.extends['$columnHidden']
    const exp = field.config.extends['$disable']
    if (this.processExp(exp) && exp) {
      // $isPost 是否发起；$isProcess 是否审批中；$isRead 是否只读；$isInform 是否知会
      // $activityId 和 $activityName 另外判断
      const processStore = useProcessStore();
      const loadInfo = processStore.getProcLoad
      const postInfoData = processStore.getPostInfoData
      let taskInstance = sessionStorage.getItem('taskInstance')
      // // console.log(exp,field,loadInfo,'processHidden')
      if (postInfoData) {
        // console.log(viewModel,field)
        const initAllData = viewModel?.data
        let $varValues: Array<any> = []
        const vp = new formpt(exp)
        if (vp.vars && vp.vars.length > 0) {
          vp.vars.forEach(item => {
            //检查当前变量是否跟明细有关
            const vitem = item
            if (vitem.indexOf('.') > -1) {
              // console.log(dyTableData,'dyTableData',field.config.label,vitem)
              // console.log(field,index)
              //当前变量与明细表有关，提取字段名称
              const tableName = vitem.split('.')[0]
              const fieldName = vitem.split('.')[1]
              //提取当前变量的值
              var fieldModel: any = initAllData[tableName][index] && initAllData[tableName][index][fieldName]
              if (fieldModel) {
                $varValues.push(fieldModel)
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              }
            } else {
              //当前变量与明细表无关，直接提取值
              const fieldModel = Object.keys(initAllData).find(x => x == vitem)
              // // console.log('newAllFields', fieldModel, vitem, processStore.getPostInfoData)
              if (fieldModel) {
                $varValues.push(initAllData[fieldModel])
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                let processFalg = processStore.getPostInfoData[processStr]
                // $varValues.push(processFalg)
                if (processFalg != undefined) {
                  $varValues.push(processFalg)
                } else {
                  if (loadInfo) {
                    // 发起
                    if (loadInfo.processId && vitem === '$isPost') {
                      // this.setFieldHidden(field.uuid, true, index)
                      fieldHidden = true
                    }
                    // 审批
                    if (loadInfo.stepId && vitem === '$isProcess' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Process') {
                      // this.setFieldHidden(field.uuid, true, index)
                      fieldHidden = true
                    }
                    // 只读
                    if (loadInfo.taskId && loadInfo.loadType === 'read' && exp === '$isRead') {
                      // this.setFieldHidden(field.uuid, true, index)
                      fieldHidden = true
                    }
                    // 知会
                    if (loadInfo.stepId && exp === '$isInform' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Inform') {
                      // this.setFieldHidden(field.uuid, true, index)
                      fieldHidden = true
                    }
                  }
                  $varValues.push(fieldHidden)
                }
              }
            }
          })
        }
        // 计算表达式
        const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
        const sendValue = Number(value) > 0 ? true : false
        // // console.log(sendValue,exp, $varValues, vp.vars,'结果')
        fieldHidden = sendValue

        // console.log(fieldHidden,field.config.label,exp, $varValues, vp.vars,'结果')
      }
      if (loadInfo) {
        // 发起
        if (loadInfo.processId && exp === '$isPost') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
        // 审批
        if (loadInfo.stepId && exp === '$isProcess' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Process') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
        // 只读
        if (loadInfo.taskId && loadInfo.loadType === 'read' && exp === '$isRead') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
        // 知会
        if (loadInfo.stepId && exp === '$isInform' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Inform') {
          // this.setFieldHidden(field.uuid, true, index)
          fieldHidden = true
        }
      }
    } else {
      if (exp) {
        const initAllData = viewModel.data
        let $varValues: Array<any> = []
        //执行表达式解析
        const vp = new formpt(exp)
        if (vp.vars && vp.vars.length > 0) {
          vp.vars.forEach(item => {
            //检查当前变量是否跟明细有关
            const vitem = item
            if (vitem.indexOf('.') > -1) {
              // console.log(dyTableData,'dyTableData',field.config.label,vitem,index)
              // console.log(dyTableData && dyTableData[index],vitem,field,index)
              // console.log(field,index)
              //当前变量与明细表有关，提取字段名称
              const tableName = vitem.split('.')[0]
              const fieldName = vitem.split('.')[1]
              //提取当前变量的值
              var fieldModel: any = initAllData[tableName][index] && initAllData[tableName][index][fieldName] || dyTableData && dyTableData[index][vitem]
              if (fieldModel) {
                $varValues.push(fieldModel)
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              }
            } else {
              //当前变量与明细表无关，直接提取值
              const fieldModel = Object.keys(initAllData).find(x => x === vitem)
              if (fieldModel) {
                $varValues.push(initAllData[fieldModel])
              } else if (this.processExp(vitem)) {
                let processStr = vitem.replace('$', '')
                const processStore = useProcessStore();
                const postInfoData = processStore.getPostInfoData
                $varValues.push(postInfoData[processStr])
              }
            }
          })
        }

        // 执行计算
        const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)

        const sendValue = Number(value) > 0 ? true : false
        // // console.log(sendValue,exp, $varValues, vp.vars,'结果')

        // console.log(fieldHidden,field.config.label,exp, $varValues, vp.vars,'结果')
        fieldHidden = sendValue
      }
    }
    // console.log(exp,'$returnSeltDisabled',field.config.label,fieldHidden)
    return fieldHidden
  }
  private $seltHidden(field: YZField, index: number = -1) {
    let $varValues: any = []
    const newAllFields = this.getFormFields()
    //提取自身表达式
    const exp = field.config.extends['$hidden'] ?? field.config.extends['$columnHidden']
    if (this.processExp(exp) && exp) {
      this.processHidden(exp, field, index)
    } else {
      //执行表达式解析
      const vp = new formpt(exp)
      if (vp.vars && vp.vars.length > 0) {
        vp.vars.forEach(item => {
          //检查当前变量是否跟明细有关
          const vitem = item
          if (vitem.indexOf('.') > -1) {
            //当前变量与明细表有关，提取字段名称
            const fieldName = vitem.split('.')[1]
            //提取当前变量的值
            var fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
            if (fieldModel) {
              $varValues = this.getFieldValue(fieldModel)
              // if (fieldModel.config.ctype === 'YZStepper') {
              //   const nValue = Number(fieldModel.vModel.value)
              //   if (isNaN(nValue)) {
              //     $varValues.push(fieldModel.vModel.optionValue)
              //   } else {
              //     $varValues.push(nValue)
              //   }
              // } else if (fieldModel.config.ctype === 'YZDatePicker') {
              //   if (fieldModel.vModel.value) {
              //     $varValues.push(new Date(fieldModel.vModel.value))
              //   }
              // } else {
              //   $varValues.push(fieldModel.vModel.value)
              // }
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              const processStore = useProcessStore();
              const postInfoData = processStore.getPostInfoData
              $varValues.push(postInfoData[processStr])
            }
          } else {
            //当前变量与明细表无关，直接提取值
            const fieldModel = newAllFields.find(x => x.vModel.model === vitem && x.pindex === index)
            if (fieldModel) {
              $varValues = this.getFieldValue(fieldModel)
              // if (fieldModel.config.ctype === 'YZStepper') {
              //   const nvalue = Number(fieldModel.vModel.value)
              //   if (isNaN(nvalue)) {
              //     $varValues.push(fieldModel.vModel.optionValue)
              //   } else {
              //     $varValues.push(nvalue)
              //   }
              // }else if(fieldModel.config.ctype === 'YZDatePicker') {
              //    if (fieldModel.vModel.value) {
              //     $varValues.push(new Date(fieldModel.vModel.value))
              //    } 
              // } else {
              //   $varValues.push(fieldModel.vModel.value)
              // }
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              const processStore = useProcessStore();
              const postInfoData = processStore.getPostInfoData
              $varValues.push(postInfoData[processStr])
            }
          }
          // if (vitem) {
          //   if (item.tableName) {
          //     // 当前改变的控件是明细控件,提取当前明细控件的值
          //     const fieldName = vitem.replace(item.tableName + '.', '')
          //     const fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
          //     if (fieldModel) {
          //       $varValues.push(fieldModel.vModel.value)
          //     }
          //   } else {
          //     // 当前改变的控件是主表控件
          //     const fieldModel = newAllFields.find(x => x.vModel.model === vitem)
          //     if (fieldModel) {
          //       $varValues.push(fieldModel.vModel.value)
          //     }
          //   }
          // }
        })
      }
      // 执行计算
      const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
      const sendValue = Number(value) > 0 ? true : false
      this.setFieldHidden(field.uuid, sendValue, index)
    }
  }
  private $selfAdvanceValidate(field: YZField, index: number = -1) {
    const $varValues: any = []
    const newAllFields = this.getFormFields()
    const validates = field.config.validators
    if (validates.length > 0) {
      validates.forEach(expitem => {
        //提取自身表达式
        //执行表达式解析
        const exp = expitem['$disable']
        // console.log(exp,expitem)
        // if(exp && expitem.validationGroup &&  && expitem.type == 'required'){

        // }
        // console.log(expitem.type)
        if ((exp && (expitem.validationGroup && hasValidationGroup(expitem.validationGroup, field.groupIdArr)) && field.writable && expitem.type == 'required') || (exp && !expitem.validationGroup && field.writable) && expitem.type == 'required') {
          const vp = new formpt(exp)
          if (exp.indexOf('sum') > -1)
            return
          if (vp.vars && vp.vars.length > 0) {
            for (let v = 0; v < vp.vars.length; v++) {
              const vitem = vp.vars[v]
              if (vitem) {
                if (vitem.indexOf('.') > -1) {
                  //当前变量与明细表有关，提取字段名称
                  const fieldName = vitem.split('.')[1]
                  //提取当前变量的值
                  var fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)

                  if (fieldModel) {
                    if (fieldModel.config.ctype === 'YZStepper') {
                      const nValue = Number(fieldModel.vModel.value)
                      if (isNaN(nValue)) {
                        $varValues.push(fieldModel.vModel.optionValue)
                      } else {
                        $varValues.push(nValue)
                      }
                    } else {
                      $varValues.push(fieldModel.vModel.value)
                    }
                  } else if (this.processExp(vitem)) {
                    let processStr = vitem.replace('$', '')
                    const processStore = useProcessStore();
                    const postInfoData = processStore.getPostInfoData
                    $varValues.push(postInfoData[processStr])
                  }
                } else {
                  //当前变量与明细表无关，直接提取值
                  const fieldModel = newAllFields.find(x => x.vModel.model === vitem)
                  if (fieldModel) {
                    if (fieldModel.config.ctype === 'YZStepper') {
                      const nvalue = Number(fieldModel.vModel.value)
                      if (isNaN(nvalue)) {
                        $varValues.push(fieldModel.vModel.optionValue)
                      } else {
                        $varValues.push(nvalue)
                      }
                    } else {
                      $varValues.push(fieldModel.vModel.value)
                    }
                  } else if (this.processExp(vitem)) {
                    let processStr = vitem.replace('$', '')
                    const processStore = useProcessStore();
                    const postInfoData = processStore.getPostInfoData
                    $varValues.push(postInfoData[processStr])
                  }
                }
              }
            }
          }
          // 进行计算
          let endValues = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
          if (isNaN(endValues))
            endValues = 0
          if (String(endValues) !== String(field.vModel.value))
            this.setFieldRequeired(field.uuid, endValues, index)
        }
      })
    }

  }
  private $noselfAdvanceValidate(field: YZField, index: number) {
    // 获取当前表单中所有的控件
    const newAllFields = this.getFormFields()
    // 检查当前数据改变的控件，是否在其他控件的高级验证中存在
    const checkItems = this.getRefrenceValidateField(newAllFields, field)
    //执行与当前控件有关的表达式
    for (let i = 0; i < checkItems.length; i++) {
      const item = checkItems[i]
      // if (item.table && item.pindex !== index) {
      //   continue
      // }
      if (item.config.validators.length > 0) {
        item.config.validators.forEach(expitem => {
          const exp = expitem['$disable']
          if ((exp && (expitem.validationGroup && hasValidationGroup(expitem.validationGroup, field.groupIdArr)) && item.writable && expitem.type == 'required') || (exp && !expitem.validationGroup && item.writable && expitem.type == 'required')) {
            // 存放提取表达式变量的值
            let $varValues: Array<any> = []
            // 计算当前控件的表达式
            const vp = new formpt(exp)
            if (vp.exp.indexOf('sum') > -1) {
              // 表达式中包含合计
              $varValues = this.expressSum(item, vp)
            } else {
              // 提取计算后的表达式变量
              if (vp.vars && vp.vars.length > 0) {
                for (let v = 0; v < vp.vars.length; v++) {
                  const vitem = vp.vars[v]
                  if (vitem) {
                    if (item.tableName && vitem.indexOf('.') > -1) {
                      // 当前改变的控件是明细控件,提取当前明细控件的值
                      const fieldName = vitem.replace(item.tableName + '.', '')
                      const fieldModel = newAllFields.find(x => x.vModel.model === fieldName && x.pindex === index)
                      if (fieldModel) {
                        if (fieldModel.config.ctype === 'YZStepper') {
                          const nValue = Number(fieldModel.vModel.value)
                          if (isNaN(nValue)) {
                            $varValues.push(fieldModel.vModel.optionValue)
                          } else {
                            $varValues.push(nValue)
                          }

                        } else {
                          $varValues.push(fieldModel.vModel.value)
                        }
                      } else if (this.processExp(vitem)) {
                        let processStr = vitem.replace('$', '')
                        const processStore = useProcessStore();
                        const postInfoData = processStore.getPostInfoData
                        $varValues.push(postInfoData[processStr])
                      }
                    } else {
                      // 当前改变的控件是主表控件
                      const fieldModel = newAllFields.find(x => x.vModel.model === vitem && x.pindex === index)
                      if (fieldModel) {
                        if (fieldModel.config.ctype === 'YZStepper') {
                          const nValue = Number(fieldModel.vModel.value)
                          if (isNaN(nValue)) {
                            $varValues.push(fieldModel.vModel.optionValue)
                          } else {
                            $varValues.push(nValue)
                          }
                        } else {
                          $varValues.push(fieldModel.vModel.value)
                        }

                      } else if (this.processExp(vitem)) {
                        let processStr = vitem.replace('$', '')
                        const processStore = useProcessStore();
                        const postInfoData = processStore.getPostInfoData
                        $varValues.push(postInfoData[processStr])
                      }
                    }
                  }
                }
              }
            }
            // 进行计算
            let endValues = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
            if (isNaN(endValues))
              endValues = 0
            // 计算完毕后，设置当前表达式所属控件的值
            this.setFieldRequeired(item.uuid, endValues, item.pindex)
          } else if (!exp && item.writable && expitem.type == 'required') {
            this.setFieldRequeired(item.uuid, false, item.pindex)
          }

        })
      }

    }
  }
  private getFormFields() {

    let fomrId = this.targetFormId
    const allFields = formBuilder.formConfig?.getFieldsRecord()
    //const allFields = formBuilder.formConfig?.getFields()
    const newAllFields: YZField[] = []
    if (allFields && allFields.length > 0) {
      for (let i = 0; i < allFields.length; i++) {
        const item = allFields[i]
        if (item.config.ctype === 'YZTable') {
          // 如果当前控件是明细表，那么提取明细表的字段信息
          // 如果当前明细有行数据，才进行提取，否则默认明细无任何字段加载
          if (item.vModel.value && item.vModel.value.length > 0) {
            const rows = item.vModel.value as Array<any>
            rows.forEach(item => {
              const cols = item.colums
              if (cols && cols.length > 0) {
                for (let j = 0; j < cols.length; j++) {
                  const field = cols[j].field as YZField
                  if (field) {
                    const addItem = newAllFields.find(x => x.uuid === field.uuid)
                    if (!addItem)
                      newAllFields.push(field)
                  }
                }
              }
            })
          }
          // 如果明细表中存在tools 提取tools中的字段
          let tools = item.config.tools
          if (tools && tools.length > 0) {
            for (let ti = 0; ti < tools.length; ti++) {
              const tl = tools[ti];
              // 转化成字段
              const newItem = formBuilder.builderField(
                tl["__config__"].ctype,
                tl
              ) as YZField;
              newAllFields.push(newItem)
            }
          }
          // 提取当前明细表本身，因为在某些情况下明细表也需要配置表达式
          newAllFields.push(item)
        } else {
          // 如果当前控件是普通字段，那么直接提取
          //field
          const addItem = newAllFields.find(x => x.uuid === item.uuid)
          if (!addItem)
            newAllFields.push(item)
        }
      }
    }
    return newAllFields
  }
  private getChildTable() {
    let fomrId = this.targetFormId
    const allFields = formBuilder.formConfig?.getFieldsRecord() //formBuilder.formConfig?.getFields()
    const newAllFields: YZField[] = []
    if (allFields && allFields.length > 0) {
      for (let i = 0; i < allFields.length; i++) {
        const item = allFields[i]
        if (item.config.ctype === 'YZTable') {
          // 提取当前明细表本身，因为在某些情况下明细表也需要配置表达式
          newAllFields.push(item)
        }
      }
    }
    return newAllFields
  }
  private getRefencesField(newAllFields: YZField[], field: YZField, type: string) {
    const checkItems: YZField[] = []
    for (let i = 0; i < newAllFields.length; i++) {
      const item = newAllFields[i]
      if (item.config.extends[type]) {
        const expressInfo = item.config.extends[type]
        // 查找到表达式是否与当前控件的model相同，相同就把它放出去 因为存在名字相同的情况
        // if (expressInfo.indexOf(field.vModel.model) > -1) {
        //   checkItems.push(item)
        // }
        // 调整存在同名造成的问题，可能主表和字表存在相同的所以要区分
        const vp = new formpt(expressInfo)
        if (vp.vars && vp.vars.length > 0) {
          vp.vars.forEach(itvp => {

            if (field.table && field.tableName && itvp === field.table) {
              checkItems.push(item)
            } else
              if (itvp == field.vModel.model && !field.table && !field.tableName) {
                checkItems.push(item)
              }
          })
        }
      }
    }
    return checkItems
  }
  private getRefrenceValidateField(newAllFields: YZField[], field: YZField) {
    const checkItems: YZField[] = []
    for (let i = 0; i < newAllFields.length; i++) {
      const item = newAllFields[i]
      if (item.config.validators.length > 0) {
        const validates = item.config.validators
        validates.forEach(v => {
          if (v.type === 'required' && v['$disable'] && v['$disable'].indexOf(field.vModel.model) > -1 || v.type === 'required') {
            checkItems.push(item)
          }
        })
      }
    }
    return checkItems
  }
  private expressSum(field: YZField, pt: formpt) {
    const $varValues: any = []
    if (pt.vars && pt.vars.length > 0) {
      pt.vars.forEach(item => {
        if (item.indexOf('.') > -1) {
          //如果变量中包含明细，那么表示要进行明细合计，提取明细合计的列
          const table = item.split('.')[0]
          const fieldName = item.split('.')[1]
          // 提取明细表控件
          const allfields = this.getFormFields()
          if (allfields && allfields.length > 0) {
            var field = allfields.filter(x => x.table === item)
            if (field && field.length > 0) {
              // var values = field.map(x => Number(x.vModel.value))
              var values = field.map(x => useFieldOptionValue(x))
              $varValues.push(values)
            }
          }
        } else {
          // 主表
          const allfields = this.getFormFields()
          if (allfields && allfields.length > 0) {
            var field = allfields.filter(el => !el.table && el.vModel.model === item)
            if (field && field.length > 0) {
              // var values = field.map(x => Number(x.vModel.value))
              var values = field.map(x => useFieldOptionValue(x))
              $varValues.push(values)
            }
          }
        }
      })
    }

    return $varValues
  }
  // 验证流程变量
  private processExp(str: string) {
    const regex = /\$activityId|\$activityName|\$isPost|\$isProcess|\$isRead|\$isInform/;
    return regex.test(str)
  }
  // 验证流程变量判断
  private processHidden(exp: string, field: YZField, index: number) {
    // $isPost 是否发起；$isProcess 是否审批中；$isRead 是否只读；$isInform 是否知会
    // $activityId 和 $activityName 另外判断
    const processStore = useProcessStore();
    const loadInfo = processStore.getProcLoad
    const postInfoData = processStore.getPostInfoData
    let taskInstance = sessionStorage.getItem('taskInstance')
    // // console.log(exp,field,loadInfo,'processHidden')
    if (postInfoData) {
      const newAllFields = this.getFormFields()
      let $varValues: Array<any> = []
      const vp = new formpt(exp)
      if (vp.vars && vp.vars.length > 0) {
        vp.vars.forEach(item => {
          //检查当前变量是否跟明细有关
          const vitem = item
          if (vitem.indexOf('.') > -1) {
            //当前变量与明细表有关，提取字段名称
            const fieldName = vitem.split('.')[1]
            //提取当前变量的值
            var fieldModel = newAllFields.find(x => x.vModel.model == fieldName && x.pindex === index)
            if (fieldModel) {
              // console.log('newAllFields',newAllFields,vitem,processStore.getPostInfoData)
              $varValues.push(...this.getFieldValue(fieldModel))
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              let processFalg = processStore.getPostInfoData[processStr]
              $varValues.push(processFalg)
            }
          } else {
            //当前变量与明细表无关，直接提取值
            const fieldModel = newAllFields.find(x => x.vModel.model == vitem)
            if (fieldModel) {
              $varValues.push(...this.getFieldValue(fieldModel))
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              let processFalg = processStore.getPostInfoData[processStr]
              $varValues.push(processFalg)
            }
          }
        })
      }
      // console.log(exp, $varValues, vp.vars)
      // 计算表达式
      const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
      const sendValue = Number(value) > 0 ? true : false
      // console.log(field, sendValue, index)
      this.setFieldHidden(field.uuid, sendValue, index)
      // if(exp.includes('$activityId')){
      //   const result = eval(exp.replace(/\$activityId/g, "'" + postInfoData.activityId + "'"));
      //   this.setFieldHidden(field.uuid, result, index)
      // }else if(exp.includes('$activityName')){
      //   const result = eval(exp.replace(/\$activityName/g, "'" + postInfoData.activityName + "'"));
      //   this.setFieldHidden(field.uuid, result, index)
      // }
    }
    if (loadInfo) {
      // 发起
      if (loadInfo.processId && exp === '$isPost') {
        this.setFieldHidden(field.uuid, true, index)
      }
      // 审批
      if (loadInfo.stepId && exp === '$isProcess' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Process') {
        this.setFieldHidden(field.uuid, true, index)
      }
      // 只读
      if (loadInfo.taskId && loadInfo.loadType === 'read' && exp === '$isRead') {
        this.setFieldHidden(field.uuid, true, index)
      }
      // 知会
      if (loadInfo.stepId && exp === '$isInform' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Inform') {
        this.setFieldHidden(field.uuid, true, index)
      }
    }
  }
  private processDisabled(exp: string, field: YZField, index: number) {
    // $isPost 是否发起；$isProcess 是否审批中；$isRead 是否只读；$isInform 是否知会
    // $activityId 和 $activityName 另外判断
    const processStore = useProcessStore();
    const loadInfo = processStore.getProcLoad
    const postInfoData = processStore.getPostInfoData
    let taskInstance = sessionStorage.getItem('taskInstance')
    // // console.log(exp,field.config.label,loadInfo,'processDisabled')
    if (postInfoData) {
      const newAllFields = this.getFormFields()
      let $varValues: Array<any> = []
      const vp = new formpt(exp)
      if (vp.vars && vp.vars.length > 0) {
        vp.vars.forEach(item => {
          //检查当前变量是否跟明细有关
          const vitem = item
          if (vitem.indexOf('.') > -1) {
            //当前变量与明细表有关，提取字段名称
            const fieldName = vitem.split('.')[1]
            //提取当前变量的值
            var fieldModel = newAllFields.find(x => x.vModel.model == fieldName && x.pindex === index && x.table === vitem)
            if (fieldModel) {
              // console.log('newAllFields',newAllFields,vitem,processStore.getPostInfoData)
              $varValues.push(...this.getFieldValue(fieldModel))
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              let processFalg = processStore.getPostInfoData[processStr]
              $varValues.push(processFalg)
            }
          } else {
            //当前变量与明细表无关，直接提取值
            const fieldModel = newAllFields.find(x => x.vModel.model == vitem)
            if (fieldModel) {
              $varValues.push(...this.getFieldValue(fieldModel))
            } else if (this.processExp(vitem)) {
              let processStr = vitem.replace('$', '')
              let processFalg = processStore.getPostInfoData[processStr]
              $varValues.push(processFalg)
            }
          }
        })
      }
      // console.log(exp, $varValues, vp.vars)
      // 计算表达式
      const value = parseExpress.ExecuteExpress(exp, $varValues, vp.vars)
      const sendValue = Number(value) > 0 ? true : false
      // console.log(field, sendValue, index)
      this.setFieldDisable(field.uuid, sendValue, index)
      // if(exp.includes('$activityId')){
      //   const result = eval(exp.replace(/\$activityId/g, "'" + postInfoData.activityId + "'"));
      //   this.setFieldHidden(field.uuid, result, index)
      // }else if(exp.includes('$activityName')){
      //   const result = eval(exp.replace(/\$activityName/g, "'" + postInfoData.activityName + "'"));
      //   this.setFieldHidden(field.uuid, result, index)
      // }
    }
    if (loadInfo) {
      // 发起
      if (loadInfo.processId && exp === '$isPost') {
        this.setFieldDisable(field.uuid, true, index)
      }
      // 审批
      if (loadInfo.stepId && exp === '$isProcess' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Process') {
        this.setFieldDisable(field.uuid, true, index)
      }
      // 只读
      if (loadInfo.taskId && loadInfo.loadType === 'read' && exp === '$isRead') {
        this.setFieldDisable(field.uuid, true, index)
      }
      // 知会
      if (loadInfo.stepId && exp === '$isInform' && taskInstance && JSON.parse(taskInstance).taskInstanceType === 'Inform') {
        this.setFieldDisable(field.uuid, true, index)
      }
    }
  }
  private setFieldValue(uuid: string, value: any, index: number = -1) {
    eventBus.emit('setFieldValue', false, {
      uuid,
      value,
      index
    })
  }
  private setFieldDisable(uuid: string, value: any, index: number = -1) {
    eventBus.emit('setFieldDisable', false, {
      uuid,
      value,
      index
    })
  }
  private setFieldHidden(uuid: string, value: any, index: number = -1) {
    eventBus.emit('setFieldHidden', false, {
      uuid,
      value,
      index
    })
  }
  private setFieldRequeired(uuid: string, value: any, index: number = -1) {
    eventBus.emit('setFieldRequeired', false, {
      uuid,
      value,
      index
    })
  }
  private setTableReload(uuid: string, value: any, index: number = -1) {
    eventBus.emit('setTableReload', false, {
      uuid,
      value,
      index
    })
  }
  private setSelectFilter(uuid: string, value: any, index: number = -1) {
    eventBus.emit('setSelectFilter', false, {
      uuid,
      value,
      index
    })
  }
  private setDataViewReload(uuid: string, value: any, index: number = -1) {
    eventBus.emit('setDataViewReload', false, {
      uuid,
      value,
      index
    })
  }
}
export class FormConfig {
  private title: FormTitle;
  private span: number;
  private size: string;
  private paddingHorizontal: number;
  private labelWidth: number;
  private labelPosition: string;
  private isWrap: number;
  private gutter: number;
  private formRules: string;
  private formRef: string;
  private formModel: string;
  private formBtns: boolean;
  private disabled: boolean;
  private dataSourcesIds: Array<any>;
  private fields: Array<YZField>;
  constructor(config: any) {
    this.title = config.title;
    this.span = config.span;
    this.formBtns = config.formBtns;
    this.size = config.size;
    this.paddingHorizontal = config.paddingHorizontal;
    this.labelPosition = config.labelPosition;
    this.labelWidth = config.labelWidth;
    this.isWrap = config.isWrap;
    this.gutter = config.gutter;
    this.formRules = config.formRules;
    this.formRef = config.formRef;
    this.formModel = config.formModel;
    this.disabled = config.disabled;
    this.dataSourcesIds = config.dataSourcesIdss
    this.fields = []
  }
  setFields(fields: YZField[]) {
    this.fields = fields
  }
  getFields() {
    return this.fields
  }
  // setFieldsRecord(formId:string,fields:Array<YZField>){
  //   core.setFieldsRecord(formId,fields)
  // }
  /* 存储到队列中 因为存在相同表单这个formid也就失去作用了 */
  setFieldsRecord(fields: Array<YZField>) {
    core.setFieldsRecord(fields)
  }
  getTitle() {
    return this.title
  }
  getFormAllFields() {
    const allFields = this.getFields()
    const newAllFields: YZField[] = []
    if (allFields && allFields.length > 0) {
      for (let i = 0; i < allFields.length; i++) {
        const item = allFields[i]
        if (item.config.ctype === 'YZTable') {
          // 如果当前控件是明细表，那么提取明细表的字段信息
          // 如果当前明细有行数据，才进行提取，否则默认明细无任何字段加载
          if (item.vModel.value && item.vModel.value.length > 0) {
            const rows = item.vModel.value as Array<any>
            rows.forEach(item => {
              const cols = item.colums
              if (cols && cols.length > 0) {
                for (let j = 0; j < cols.length; j++) {
                  const field = cols[j].field as YZField
                  if (field) {
                    const addItem = newAllFields.find(x => x.uuid === field.uuid)
                    if (!addItem)
                      newAllFields.push(field)
                  }
                }
              }
            })
          }
          // 提取当前明细表本身，因为在某些情况下明细表也需要配置表达式
          newAllFields.push(item)
        } else {
          // 如果当前控件是普通字段，那么直接提取
          //field
          const addItem = newAllFields.find(x => x.uuid === item.uuid)
          if (!addItem)
            newAllFields.push(item)
        }
      }
    }
    return newAllFields
  }
  // getFieldsRecord(formId:string){
  //   let fields = core.getFieldsRecord(formId)
  //   return fields
  // }
  getFieldsRecord() {
    let fields = core.getFieldsRecord()
    return fields
  }
  getFormAllFieldsRecord(formId: string) {
    // const allFields = this.getFieldsRecord(formId)
    const allFields = this.getFieldsRecord()
    const newAllFields: YZField[] = []
    if (allFields && allFields.length > 0) {
      for (let i = 0; i < allFields.length; i++) {
        const item = allFields[i]
        if (item.config.ctype === 'YZTable') {
          // 如果当前控件是明细表，那么提取明细表的字段信息
          // 如果当前明细有行数据，才进行提取，否则默认明细无任何字段加载
          if (item.vModel.value && item.vModel.value.length > 0) {
            const rows = item.vModel.value as Array<any>
            rows.forEach(item => {
              const cols = item.colums
              if (cols && cols.length > 0) {
                for (let j = 0; j < cols.length; j++) {
                  const field = cols[j].field as YZField
                  if (field) {
                    const addItem = newAllFields.find(x => x.uuid === field.uuid)
                    if (!addItem)
                      newAllFields.push(field)
                  }
                }
              }
            })
          }
          // 提取当前明细表本身，因为在某些情况下明细表也需要配置表达式
          newAllFields.push(item)
        } else {
          // 如果当前控件是普通字段，那么直接提取
          //field
          const addItem = newAllFields.find(x => x.uuid === item.uuid)
          if (!addItem)
            newAllFields.push(item)
        }
      }
    }
    return newAllFields
  }
  // delFieldsRecordByTid(formId:string){
  //   core.delFieldsRecordByTid(formId)
  // }
  delFieldsRecordArrByLast() {
    core.delFieldsRecordArrByLast()
  }

}
export interface IFormTitle {
  color: string;
  fontSize: number;
  fontWeight: string | number;
  paddingBottom: number;
  paddingTop: number;
  position: string;
  value: string;
}
export class FormTitle implements IFormTitle {
  color: string;
  fontSize: number;
  fontWeight: string | number;
  paddingBottom: number;
  paddingTop: number;
  position: string;
  value: string;
  private processName: string;
  constructor(title: FormTitle) {
    this.color = title.color;
    this.fontSize = title.fontSize;
    this.fontWeight = title.fontWeight;
    this.paddingBottom = title.paddingBottom;
    this.paddingTop = title.paddingTop;
    this.position = title.position;
    this.value = title.value
    this.processName = ''
  }
  private setProcessName(name: string) {
    this.processName = name
  }
}

export interface IBtnModel {
  text: string;
  component: string;
  enable: boolean;
  groupId?: string;
  submitConfirm?: string;
  promptMessage?: string;
  formService?: any
}
export interface ISubmitForm {
  formData: any;
  opinion?: string;
  action: string;
  draftId?: string;
}
export class OrgModel {
  expandable: boolean;
  glyph: boolean;
  id: string;
  leaf: boolean;
  public text: string;
  type: import('../../types/bpm').OrgType;
  data?: OrgData;
  constructor(params: any, type: OrgType) {
    this.expandable = params['expandable']
    this.glyph = params['glyph']
    this.id = params['id']
    this.leaf = params['leaf']
    this.text = params['text']
    this.type = type
    if (type === 'ou') {
      this.data = new OrgModelData(params)
      this.text = this.data.getName()
    } else {
      const userValue = new OrgUserModel(params)
      this.data = userValue
    }
  }
}
export abstract class OrgData {
  abstract getName(): string;
  abstract getCode(): string;
  abstract getCurrentData(): this;
  abstract getOuID(): string;
}
export class OrgUserModel extends OrgData {
  account: string;
  shortName: string;
  providerName: string;
  sid: string;
  constructor(params: any) {
    super()
    this.account = params['account']
    this.shortName = params['displayName']
    this.providerName = params['providerName']
    this.sid = params['sid']
  }
  override getName(): string {
    return this.shortName
  }
  override getCurrentData(): this {
    return this
  }
  override getCode(): string {
    return this.account
  }
  override getOuID(): string {
    return ''
  }
}
export class OrgModelData extends OrgData {
  public Code: string;
  public OUID: string;
  public Level: string;
  public Name: string;
  public SID: string;
  public providerName: string;
  constructor(params: any) {
    super()
    this.Code = params['code']
    this.Level = params['ouLevel']
    this.Name = params['text'] ?? params['name']
    this.SID = params['sid']
    this.OUID = String(params['ouid'])
    this.providerName = params['providerName']
  }
  override getName(): string {
    return this.Name
  }
  override getCurrentData(): this {
    return this
  }
  override getCode(): string {
    return this.getCode()
  }
  override getOuID(): string {
    return this.OUID
  }
}
export interface IGridModel {
  gridName?: string;
  ctype?: string;
  colums?: any[];
  tools?: any[];
  bind?: string;
  title?: string;
  uniqueId?: string;
  $hidden?: string
  $exress?: string;
  $disable?: string;
  ds?: any;
  map?: any;
  enablePaging?: boolean
  pageSize?: number
  emptyGrid?: string
}
export interface IFatureModel {
  name: string;
  ownerAccount: string;
  ownerName: string;
  id: string;
  startTime: string;
  endTime: string;
  handlerAccount: string;
  handlerShortName: string;
}
export interface IRemandTable {
  Account: string;
  ElapsedMinutes: string;
  NodeDisplayName: string;
  ShortName: string;
  StepID: string;
}
export interface ITaskResult {
  total: number;
  children: Array<any>
}
export interface IFormEvent {
  name: string;
  fun: Function
}
export interface IPostionData {
  memberId: string;
  ouid: string;
  providerName: string;
  qualifiedId: string;
  text: string;
  userAccount: string;
}
export interface IHeaderData {
  CustomerId: string;
  HeaderUrl: string;
  IdCard: string;
  Name: string;
  RedirctUrl: string;
  TaskID: string;
}
export interface ILocalizationSetting {
  cultures: string[]
  enable: boolean;
  language: Ilanguages;
}
export interface Ilanguages {
  EnUs: Record<string, object>;
  ZhCn: Record<string, object>;
}
export interface IFormValidator {
  type: string;
  validationGroup: string;
  $express: string;
  message: string;
  $disable: string
  field: YZField
}
export interface IChildFormBtn {
  text: string;
  component: string;
  enable: boolean;
  formService: any
}