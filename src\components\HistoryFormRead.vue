<template>
    <div class="">
        <van-nav-bar :title="title" :left-text="$t('Global.Back')" left-arrow @click-left="onClickLeft()" />
        <renderForm ref="renderForm" :taskId="props.reformRead.taskId" :loadType="props.reformRead.loadType" :reformRead="reformRead" :noShowHeader="noShowHeader"  />
    </div>
</template>
<script lang="ts" setup>
const emit = defineEmits(['onBack'])
import { useProcessStore } from "@/store/process";
const props = defineProps({
    reformRead:{
        type:Object,
        default:{}
    },
    title:{
        type:String,
        default:'表单'
    },
    // 外部传入，不要展示header,true不展示；false展示
    noShowHeader:{
      type:Boolean,
      default:false
    }
})

import { useRoute,useRouter } from 'vue-router';
import { useCore } from '@/store/core/index'
const route = useRoute()
const router = useRouter()
const processStore = useProcessStore()
const core = useCore()
// const renderFormRef = ref<any>(null)
let query = route.query
import { eventBus } from '@/utils/eventBus';
const onClickLeft = (params?:any) => {
    // let taskId = props.reformRead.taskId
    core.delFieldsRecordArrByLast()
    emit('onBack',params)
}
eventBus.on('onBackChildForm', {
    cb: function (params) {
        // if(Object.keys(props.formService).length > 0){
        //     eventBus.off('childFormSave')
        // }
        onClickLeft(params)
    }
})
</script>
<style lang="">
    
</style>