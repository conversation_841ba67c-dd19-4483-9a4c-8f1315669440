{"name": "eflowmobile", "private": true, "version": "1.0.0", "type": "module", "scripts": {"d": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@dcloudio/uni-app": "2.0.2-4020420240722004", "@microsoft/signalr": "^7.0.5", "@rollup/rollup-darwin-arm64": "^4.12.0", "@tato30/vue-pdf": "^1.11.1", "@tinymce/tinymce-vue": "^5.1.1", "@vitejs/plugin-legacy": "^5.1.3", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pptx": "^1.0.1", "axios": "^1.3.1", "compressorjs": "^1.2.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "dingtalk-jsapi": "^3.0.25", "heic2any": "^0.0.4", "html5-qrcode": "^2.3.8", "js-cookie": "^3.0.5", "lint": "^0.8.19", "lodash": "^4.17.21", "mathjs": "^11.8.0", "mitt": "^3.0.0", "pdfh5": "^1.4.9", "pinia": "^2.0.30", "quagga": "^0.12.1", "rollup-plugin-esbuild": "^5.0.0", "terser": "^5.28.1", "tinymce": "^7.2.1", "upng-js": "^2.1.0", "vant": "^4.8.5", "vant-table": "^0.0.1", "vconsole": "^3.15.1", "vue": "^3.4.21", "vue-doc-preview": "^0.3.2", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vuedraggable": "^4.1.0", "x2js": "^3.4.4"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/lodash": "^4.14.202", "@types/node": "^18.11.18", "@vitejs/plugin-vue": "^4.0.0", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.14", "postcss-pxtorem": "^6.1.0", "sass": "^1.58.0", "typescript": "^4.9.3", "unplugin-vue-define-options": "^1.2.1", "vite": "^5.1.3", "vue-tsc": "^1.0.11"}}