<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search"></div>
            <div class="btn" @click="confirm">
                {{ $t('Global.Selected') }}
            </div>
        </div>

        <div class="grap"></div>

        <div class="list">
            <div class="li">
                <div class="li-name">User Account</div>
                <div class="li-info">{{ persons?.UserAccount }}</div>
            </div>
            <div class="li">
                <div class="li-name">User Name</div>
                <div class="li-info">{{ persons?.UserName }}</div>
            </div>
            <div class="li">
                <div class="li-name">Role Name</div>
                <div class="li-info">{{ persons?.RoleName }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { HSE } from '@/store/hse'

const store = HSE()
const router = useRouter()
const route = useRoute()

const persons = ref()
const comeBack = () => {
    router.push({
        path: '/personSelect',
        replace: true
    })
}
// const checkedPersons = ref([])
const confirm = () => {
    // checkedPersons.value.push(persons.value)
    // store.checkedPersons = checkedPersons.value
    // console.log("%c [ store.checkedPersons ]", "font-size:13px; background:linear-gradient(to right , #9B63FF, #462188); color:yellow;", store.checkedPersons)
    router.push({
        path: '/personSelect',
        query: {
            Persons_: JSON.stringify(persons.value)
        },
        replace: true
    }) 
}
onMounted(() => {
    persons.value = JSON.parse(route?.query?.persons)
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;
            color: #00A0E1;
        }
    }

    .grap {
        width: 100%;
        height: 10px;
        background-color: #f9f9f9;
    }

    .list {
        padding: 10px 12px;

        .li {
            font-size: var(--yz-com-14);
            margin-bottom: 16px;

            .li-name {
                color: #999999;
            }

            .li-info {
                color: #444444;
                margin-top: 8px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>