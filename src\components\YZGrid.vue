<template>
    <van-grid-item @click="$emit('onClick', process)">
        <div class="grid-item">
            <div class="grid-icon" :style="{ background: process.IconColor }">
                <div :class="['icon-content ' + process.IconCls]">
                </div>
            </div><span class="grid-text">
                <!-- {{ formatter(process.ProcessName) }} -->
                {{ process.ShortName || process.ProcessName }}
            </span>
        </div>
    </van-grid-item>
</template>

<script lang="ts" setup>
import { IProcessInfo } from '@/logic/home/<USER>';
import { PropType } from 'vue';

defineProps({
    process: {
        type: Object as PropType<IProcessInfo>,
        required: true,
        default: {}
    }
})
defineEmits<{
    onClick: [val: IProcessInfo]
}>()
// const formatter = (name: string) => {
//     return name = name.length > 4 ? name.substring(0, 4) + '..' : name
// }
</script>
<style lang="scss" scoped>
.grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.grid-icon {
    width: 40px;
    height: 40px;
    border-radius: 3px;
    text-align: center;

    .icon-content {
        width: 18px;
        height: 18px;
        margin: 10px auto;
        color: #ffffff;
        line-height: 18px;
        font-size: var(--yz-home-gridicon);
    }
}

.grid-text {
    font-family: PingFangSC-Regular;
    font-size: var(--yz-home-gridtext);
    color: var(--yz-text-666);
    text-align: center;
    // line-height: 16px;
    font-weight: 400;
    margin-top: 7px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
}
</style>