<template>
    <div class="page">
        <div class="search-div">
            <div class="back" @click="comeBack">
                <van-icon class="icon" name="arrow-left" />
            </div>
            <div class="search"></div>
            <div class="btn" @click="confirm">
                {{ $t('Global.Selected') }}
            </div>
        </div>

        <div class="grap"></div>

        <div class="list">
            <div class="li">
                <div class="li-name">Code</div>
                <div class="li-info">{{ list?.Code }}</div>
            </div>
            <div class="li">
                <div class="li-name">Display Name</div>
                <div class="li-info">{{ list?.DisplayName }}</div>
            </div>
            <div class="li">
                <div class="li-name">Value</div>
                <div class="li-info">{{ list?.Value }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { HSE } from '@/store/hse'

const store = HSE()
const router = useRouter()
const route = useRoute()

const list = ref()
const comeBack = () => {
    router.push({
        path: '/processTimeSelect',
        replace: true
    })
}
const confirm = () => {
    store.processTime = list.value?.Value
    store.processTime_name = list.value?.DisplayName
    router.push({ path: '/uauc', replace: true })
}
onMounted(() => {
    list.value = JSON.parse(route?.query?.list)
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    min-height: 100vh;

    .search-div {
        box-sizing: border-box;
        width: 100%;
        height: 48px;
        background: var(--yz-div-background);
        display: flex;
        position: sticky;
        top: 0;
        z-index: 99;

        .back {
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            box-sizing: border-box;

            .icon {
                font-size: 20px;
                color: var(--yz-text-666);
            }
        }

        .search {
            flex: 1;
        }

        .btn {
            font-size: 14px;
            width: auto;
            padding-right: 10px;
            display: flex;
            align-items: center;
            color: #00A0E1;
        }
    }

    .grap {
        width: 100%;
        height: 10px;
        background-color: #f9f9f9;
    }

    .list {
        padding: 10px 12px;

        .li {
            font-size: var(--yz-com-14);
            margin-bottom: 16px;

            .li-name {
                color: #999999;
            }

            .li-info {
                color: #444444;
                margin-top: 8px;
            }
        }
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>