<template>
    <div class="clearAfter">
        <van-field readonly
        style="display: none;"
        v-model="vModel"  :name="field.vModel.model" :label="field.config.label"  :key="field.vModel.model" :placeholder="field.placeholder" />
    </div>
</template>
<script lang="ts" setup>
import { YZItemId } from '@/logic/forms/YZItemId';
import { eventBus } from '@/utils/eventBus';
import { computed, onMounted, PropType, ref } from 'vue';
const props = defineProps({
    field: {
        type: Object as PropType<YZItemId>,
        required: true
    },
    modelValue:{
        type:String,
        default:''
    },
    index:{
        type:Number,
        default:-1
    }
})
const emit = defineEmits(['update:modelValue'])
const isFirstLoad = ref<boolean>(true)
onMounted(() => {
    isFirstLoad.value = false
})
const vModel = computed({
    get() {
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue',val)  
    }
})

eventBus.on('setFieldDisable',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.disabled = params.value 
       }
    }
})
eventBus.on('setFieldHidden',{
    cb:function(params) {
       if (params.uuid === props.field.uuid) {
           props.field.hidden = params.value 
       }
    }
})
</script>
<style  scoped>
.clearAfter::after{
    display: none;
}
</style>