<template>
    <div class="main">
        <!-- 路由 -->
        <RouterView />
        <!-- 底部关掉 YZ need hide-->
        <!-- <van-tabbar @change="onChange" v-model="activeName" v-show="tabShow">
            <van-tabbar-item v-for="item in tabBarData" :key="item.CODE" :style="{ background: colorInfo.BGCOLOR }"
                :name="item.TOURL">
                <template #icon>
                    <van-badge class="bottom-badge" :content="countNum" v-if="item.CODE === 'work' && countNum > 0">
                        <i :class="[item.ICON && item.ICON !== '' ? item.ICON : 'fa fa-bookmark-o']"
                            style="font-size: 16px"></i>
                    </van-badge>
                    <span v-else>
                        <i :class="[item.ICON && item.ICON !== '' ? item.ICON : 'fa fa-bookmark-o']"
                            style="font-size: 16px"></i>
                    </span>
                </template>
{{ $t('LayOut.' + item.TITLECN) }}
</van-tabbar-item>
</van-tabbar> -->
        <!-- 底部关掉 YZ need hide-->
    </div>
</template>

<script lang="ts" setup>
import { useLayout } from '@/logic/layout/index'
const { activeName, onChange, colorInfo, tabBarData, countNum, tabShow } = useLayout()
</script>
<style scoped>
.main {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    background: var(--yz-layout-mian);
}

::v-deep(.van-tabbar-item__text) {
    font-size: 13.3px;
}
</style>